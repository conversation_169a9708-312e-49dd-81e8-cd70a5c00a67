using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace AredooPOS.Models.Reports
{
    /// <summary>
    /// نموذج تقرير الأرباح
    /// يحتوي على جميع البيانات المتعلقة بتقارير الأرباح والخسائر
    /// </summary>
    public class ProfitReport
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم التقرير
        /// </summary>
        public int ReportID { get; set; }

        /// <summary>
        /// نوع التقرير
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ReportType { get; set; }

        /// <summary>
        /// تاريخ بداية التقرير
        /// </summary>
        [Required]
        public DateTime FromDate { get; set; }

        /// <summary>
        /// تاريخ نهاية التقرير
        /// </summary>
        [Required]
        public DateTime ToDate { get; set; }

        /// <summary>
        /// تاريخ إنشاء التقرير
        /// </summary>
        public DateTime GeneratedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// من أنشأ التقرير
        /// </summary>
        [Required]
        [StringLength(50)]
        public string GeneratedBy { get; set; }

        #endregion

        #region الإيرادات

        /// <summary>
        /// إجمالي المبيعات
        /// </summary>
        public decimal TotalSales { get; set; }

        /// <summary>
        /// المرتجعات
        /// </summary>
        public decimal TotalReturns { get; set; }

        /// <summary>
        /// الخصومات
        /// </summary>
        public decimal TotalDiscounts { get; set; }

        /// <summary>
        /// صافي المبيعات
        /// </summary>
        public decimal NetSales => TotalSales - TotalReturns - TotalDiscounts;

        /// <summary>
        /// إيرادات أخرى
        /// </summary>
        public decimal OtherRevenue { get; set; }

        /// <summary>
        /// إجمالي الإيرادات
        /// </summary>
        public decimal TotalRevenue => NetSales + OtherRevenue;

        #endregion

        #region التكاليف

        /// <summary>
        /// تكلفة البضاعة المباعة
        /// </summary>
        public decimal CostOfGoodsSold { get; set; }

        /// <summary>
        /// إجمالي الربح
        /// </summary>
        public decimal GrossProfit => NetSales - CostOfGoodsSold;

        /// <summary>
        /// نسبة إجمالي الربح
        /// </summary>
        public decimal GrossProfitMargin => NetSales > 0 ? (GrossProfit / NetSales) * 100 : 0;

        #endregion

        #region المصاريف التشغيلية

        /// <summary>
        /// مصاريف البيع
        /// </summary>
        public decimal SellingExpenses { get; set; }

        /// <summary>
        /// المصاريف الإدارية
        /// </summary>
        public decimal AdministrativeExpenses { get; set; }

        /// <summary>
        /// مصاريف التسويق
        /// </summary>
        public decimal MarketingExpenses { get; set; }

        /// <summary>
        /// إجمالي المصاريف التشغيلية
        /// </summary>
        public decimal TotalOperatingExpenses => SellingExpenses + AdministrativeExpenses + MarketingExpenses;

        /// <summary>
        /// الربح التشغيلي
        /// </summary>
        public decimal OperatingProfit => GrossProfit - TotalOperatingExpenses;

        /// <summary>
        /// نسبة الربح التشغيلي
        /// </summary>
        public decimal OperatingProfitMargin => NetSales > 0 ? (OperatingProfit / NetSales) * 100 : 0;

        #endregion

        #region المصاريف الأخرى

        /// <summary>
        /// المصاريف المالية
        /// </summary>
        public decimal FinancialExpenses { get; set; }

        /// <summary>
        /// مصاريف أخرى
        /// </summary>
        public decimal OtherExpenses { get; set; }

        /// <summary>
        /// إجمالي المصاريف الأخرى
        /// </summary>
        public decimal TotalOtherExpenses => FinancialExpenses + OtherExpenses;

        #endregion

        #region صافي الربح

        /// <summary>
        /// الربح قبل الضريبة
        /// </summary>
        public decimal ProfitBeforeTax => OperatingProfit - TotalOtherExpenses;

        /// <summary>
        /// الضرائب
        /// </summary>
        public decimal Taxes { get; set; }

        /// <summary>
        /// صافي الربح
        /// </summary>
        public decimal NetProfit => ProfitBeforeTax - Taxes;

        /// <summary>
        /// نسبة صافي الربح
        /// </summary>
        public decimal NetProfitMargin => NetSales > 0 ? (NetProfit / NetSales) * 100 : 0;

        #endregion

        #region قوائم التفاصيل

        /// <summary>
        /// تفاصيل الأرباح حسب المنتج
        /// </summary>
        public List<ProductProfitDetail> ProductProfitDetails { get; set; } = new List<ProductProfitDetail>();

        /// <summary>
        /// تفاصيل الأرباح حسب الفئة
        /// </summary>
        public List<CategoryProfitDetail> CategoryProfitDetails { get; set; } = new List<CategoryProfitDetail>();

        /// <summary>
        /// تفاصيل الأرباح اليومية
        /// </summary>
        public List<DailyProfitDetail> DailyProfitDetails { get; set; } = new List<DailyProfitDetail>();

        /// <summary>
        /// تفاصيل الأرباح الشهرية
        /// </summary>
        public List<MonthlyProfitDetail> MonthlyProfitDetails { get; set; } = new List<MonthlyProfitDetail>();

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// فترة التقرير بالأيام
        /// </summary>
        public int ReportPeriodDays => (ToDate - FromDate).Days + 1;

        /// <summary>
        /// متوسط الربح اليومي
        /// </summary>
        public decimal DailyAverageProfit => ReportPeriodDays > 0 ? NetProfit / ReportPeriodDays : 0;

        /// <summary>
        /// أفضل منتج ربحاً
        /// </summary>
        public ProductProfitDetail MostProfitableProduct => ProductProfitDetails?.OrderByDescending(p => p.TotalProfit).FirstOrDefault();

        /// <summary>
        /// أفضل فئة ربحاً
        /// </summary>
        public CategoryProfitDetail MostProfitableCategory => CategoryProfitDetails?.OrderByDescending(c => c.TotalProfit).FirstOrDefault();

        /// <summary>
        /// أفضل يوم ربحاً
        /// </summary>
        public DailyProfitDetail MostProfitableDay => DailyProfitDetails?.OrderByDescending(d => d.NetProfit).FirstOrDefault();

        /// <summary>
        /// معدل العائد على المبيعات
        /// </summary>
        public decimal ReturnOnSales => TotalSales > 0 ? (NetProfit / TotalSales) * 100 : 0;

        /// <summary>
        /// نقطة التعادل
        /// </summary>
        public decimal BreakEvenPoint
        {
            get
            {
                var variableCostRatio = NetSales > 0 ? CostOfGoodsSold / NetSales : 0;
                var contributionMargin = 1 - variableCostRatio;
                return contributionMargin > 0 ? TotalOperatingExpenses / contributionMargin : 0;
            }
        }

        #endregion

        #region الدوال المساعدة

        /// <summary>
        /// حساب الإجماليات من التفاصيل
        /// </summary>
        public void CalculateTotals()
        {
            if (ProductProfitDetails?.Any() == true)
            {
                TotalSales = ProductProfitDetails.Sum(p => p.TotalSales);
                CostOfGoodsSold = ProductProfitDetails.Sum(p => p.TotalCost);
                TotalDiscounts = ProductProfitDetails.Sum(p => p.TotalDiscount);
            }

            if (DailyProfitDetails?.Any() == true)
            {
                TotalReturns = DailyProfitDetails.Sum(d => d.TotalReturns);
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            if (FromDate > ToDate)
                return false;

            if (string.IsNullOrWhiteSpace(ReportType))
                return false;

            if (string.IsNullOrWhiteSpace(GeneratedBy))
                return false;

            return true;
        }

        /// <summary>
        /// الحصول على ملخص التقرير
        /// </summary>
        /// <returns>ملخص التقرير</returns>
        public string GetSummary()
        {
            return $"تقرير الأرباح من {FromDate:dd/MM/yyyy} إلى {ToDate:dd/MM/yyyy}\n" +
                   $"إجمالي المبيعات: {TotalSales:C}\n" +
                   $"إجمالي الربح: {GrossProfit:C}\n" +
                   $"صافي الربح: {NetProfit:C}\n" +
                   $"نسبة صافي الربح: {NetProfitMargin:F2}%";
        }

        /// <summary>
        /// تحليل الاتجاه
        /// </summary>
        /// <returns>تحليل الاتجاه</returns>
        public string GetTrendAnalysis()
        {
            if (MonthlyProfitDetails?.Count >= 2)
            {
                var lastMonth = MonthlyProfitDetails.OrderBy(m => m.Year).ThenBy(m => m.Month).Last();
                var previousMonth = MonthlyProfitDetails.OrderBy(m => m.Year).ThenBy(m => m.Month).Skip(MonthlyProfitDetails.Count - 2).First();
                
                var growth = previousMonth.NetProfit != 0 ? ((lastMonth.NetProfit - previousMonth.NetProfit) / previousMonth.NetProfit) * 100 : 0;
                
                if (growth > 0)
                    return $"نمو في الأرباح بنسبة {growth:F2}%";
                else if (growth < 0)
                    return $"انخفاض في الأرباح بنسبة {Math.Abs(growth):F2}%";
                else
                    return "استقرار في الأرباح";
            }
            
            return "لا توجد بيانات كافية للتحليل";
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// تفاصيل ربح المنتج
    /// </summary>
    public class ProductProfitDetail
    {
        public int ProductID { get; set; }
        public string ProductName { get; set; }
        public string ProductCode { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalCost { get; set; }
        public decimal TotalProfit { get; set; }
        public decimal TotalDiscount { get; set; }
        public decimal QuantitySold { get; set; }
        public decimal ProfitMargin => TotalSales > 0 ? (TotalProfit / TotalSales) * 100 : 0;
        public decimal ProfitPerUnit => QuantitySold > 0 ? TotalProfit / QuantitySold : 0;
    }

    /// <summary>
    /// تفاصيل ربح الفئة
    /// </summary>
    public class CategoryProfitDetail
    {
        public int CategoryID { get; set; }
        public string CategoryName { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalCost { get; set; }
        public decimal TotalProfit { get; set; }
        public decimal ProfitMargin => TotalSales > 0 ? (TotalProfit / TotalSales) * 100 : 0;
        public int ProductCount { get; set; }
        public decimal AverageProfitPerProduct => ProductCount > 0 ? TotalProfit / ProductCount : 0;
    }

    /// <summary>
    /// تفاصيل الربح اليومي
    /// </summary>
    public class DailyProfitDetail
    {
        public DateTime ProfitDate { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalCost { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal OperatingExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal TotalReturns { get; set; }
        public decimal ProfitMargin => TotalSales > 0 ? (NetProfit / TotalSales) * 100 : 0;
        public string DayName => ProfitDate.ToString("dddd");
    }

    /// <summary>
    /// تفاصيل الربح الشهري
    /// </summary>
    public class MonthlyProfitDetail
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalCost { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal OperatingExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal ProfitMargin => TotalSales > 0 ? (NetProfit / TotalSales) * 100 : 0;
        public decimal GrowthPercentage { get; set; }
        public int WorkingDays { get; set; }
        public decimal DailyAverageProfit => WorkingDays > 0 ? NetProfit / WorkingDays : 0;
    }

    #endregion

    #region التعدادات

    /// <summary>
    /// أنواع تقارير الأرباح
    /// </summary>
    public static class ProfitReportTypes
    {
        public const string Daily = "Daily";
        public const string Weekly = "Weekly";
        public const string Monthly = "Monthly";
        public const string Quarterly = "Quarterly";
        public const string Yearly = "Yearly";
        public const string ByProduct = "ByProduct";
        public const string ByCategory = "ByCategory";
        public const string Comparative = "Comparative";
        public const string Trend = "Trend";
    }

    #endregion
}
