using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using AredooPOS.Models;
using Microsoft.Extensions.Logging;

namespace AredooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات لأنشطة المستخدمين
    /// تحتوي على جميع العمليات المتعلقة بقاعدة البيانات لأنشطة المستخدمين
    /// </summary>
    public class UserActivityDAL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly string _connectionString;
        private readonly ILogger<UserActivityDAL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة الوصول للبيانات لأنشطة المستخدمين
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public UserActivityDAL(string connectionString = null, ILogger<UserActivityDAL> logger = null)
        {
            _connectionString = connectionString ?? DatabaseConfig.GetConnectionString();
            _logger = logger;
        }

        #endregion

        #region العمليات الأساسية

        /// <summary>
        /// إضافة نشاط مستخدم جديد
        /// </summary>
        /// <param name="activity">بيانات النشاط</param>
        /// <returns>رقم النشاط الجديد</returns>
        public int AddUserActivity(UserActivity activity)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_AddUserActivity", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                // إضافة المعاملات
                command.Parameters.AddWithValue("@UserID", activity.UserID);
                command.Parameters.AddWithValue("@SessionID", activity.SessionID ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ActivityType", activity.ActivityType);
                command.Parameters.AddWithValue("@Description", activity.Description);
                command.Parameters.AddWithValue("@ActivityDateTime", activity.ActivityDateTime);
                command.Parameters.AddWithValue("@Module", activity.Module ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Action", activity.Action ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@TargetObject", activity.TargetObject ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@TargetObjectID", activity.TargetObjectID ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@OldValues", activity.OldValues ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@NewValues", activity.NewValues ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Status", activity.Status);
                command.Parameters.AddWithValue("@IsSuccessful", activity.IsSuccessful);
                command.Parameters.AddWithValue("@ErrorMessage", activity.ErrorMessage ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ExecutionTimeMs", activity.ExecutionTimeMs ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@IPAddress", activity.IPAddress ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@UserAgent", activity.UserAgent ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@DeviceName", activity.DeviceName ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Severity", activity.Severity);
                command.Parameters.AddWithValue("@IsSecuritySensitive", activity.IsSecuritySensitive);
                command.Parameters.AddWithValue("@RequiresReview", activity.RequiresReview);
                command.Parameters.AddWithValue("@AdditionalData", activity.AdditionalData ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Notes", activity.Notes ?? (object)DBNull.Value);

                // معامل الإخراج
                var outputParam = new SqlParameter("@NewActivityID", SqlDbType.Int)
                {
                    Direction = ParameterDirection.Output
                };
                command.Parameters.Add(outputParam);

                connection.Open();
                command.ExecuteNonQuery();

                var activityId = Convert.ToInt32(outputParam.Value);
                return activityId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إضافة نشاط المستخدم");
                throw;
            }
        }

        /// <summary>
        /// تحديث بيانات النشاط
        /// </summary>
        /// <param name="activity">بيانات النشاط المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateUserActivity(UserActivity activity)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_UpdateUserActivity", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                // إضافة المعاملات
                command.Parameters.AddWithValue("@ActivityID", activity.ActivityID);
                command.Parameters.AddWithValue("@Status", activity.Status);
                command.Parameters.AddWithValue("@IsSuccessful", activity.IsSuccessful);
                command.Parameters.AddWithValue("@ErrorMessage", activity.ErrorMessage ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ExecutionTimeMs", activity.ExecutionTimeMs ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@IsReviewed", activity.IsReviewed);
                command.Parameters.AddWithValue("@ReviewedBy", activity.ReviewedBy ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ReviewedDate", activity.ReviewedDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ReviewNotes", activity.ReviewNotes ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Notes", activity.Notes ?? (object)DBNull.Value);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم تحديث النشاط {activity.ActivityID}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث النشاط {activity.ActivityID}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على نشاط بالرقم
        /// </summary>
        /// <param name="activityId">رقم النشاط</param>
        /// <returns>بيانات النشاط</returns>
        public UserActivity GetActivityById(int activityId)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetActivityById", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@ActivityID", activityId);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapActivityFromReader(reader);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على النشاط {activityId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على أنشطة المستخدم
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="activityType">نوع النشاط</param>
        /// <param name="pageSize">حجم الصفحة</param>
        /// <param name="pageNumber">رقم الصفحة</param>
        /// <returns>قائمة أنشطة المستخدم</returns>
        public List<UserActivity> GetUserActivities(int userId, DateTime? fromDate = null, DateTime? toDate = null, 
            string activityType = null, int pageSize = 50, int pageNumber = 1)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetUserActivities", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@UserID", userId);
                command.Parameters.AddWithValue("@FromDate", fromDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ToDate", toDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ActivityType", activityType ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PageSize", pageSize);
                command.Parameters.AddWithValue("@PageNumber", pageNumber);

                connection.Open();
                using var reader = command.ExecuteReader();

                var activities = new List<UserActivity>();
                while (reader.Read())
                {
                    activities.Add(MapActivityFromReader(reader));
                }

                return activities;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على أنشطة المستخدم {userId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على الأنشطة حسب النوع
        /// </summary>
        /// <param name="activityType">نوع النشاط</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="pageSize">حجم الصفحة</param>
        /// <param name="pageNumber">رقم الصفحة</param>
        /// <returns>قائمة الأنشطة</returns>
        public List<UserActivity> GetActivitiesByType(string activityType, DateTime? fromDate = null, DateTime? toDate = null,
            int pageSize = 50, int pageNumber = 1)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetActivitiesByType", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@ActivityType", activityType);
                command.Parameters.AddWithValue("@FromDate", fromDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ToDate", toDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PageSize", pageSize);
                command.Parameters.AddWithValue("@PageNumber", pageNumber);

                connection.Open();
                using var reader = command.ExecuteReader();

                var activities = new List<UserActivity>();
                while (reader.Read())
                {
                    activities.Add(MapActivityFromReader(reader));
                }

                return activities;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الأنشطة من النوع {activityType}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على الأنشطة الحساسة أمنياً
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="reviewedOnly">المراجعة فقط</param>
        /// <returns>قائمة الأنشطة الحساسة</returns>
        public List<UserActivity> GetSecuritySensitiveActivities(DateTime? fromDate = null, DateTime? toDate = null, bool? reviewedOnly = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetSecuritySensitiveActivities", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@FromDate", fromDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ToDate", toDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ReviewedOnly", reviewedOnly ?? (object)DBNull.Value);

                connection.Open();
                using var reader = command.ExecuteReader();

                var activities = new List<UserActivity>();
                while (reader.Read())
                {
                    activities.Add(MapActivityFromReader(reader));
                }

                return activities;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على الأنشطة الحساسة أمنياً");
                throw;
            }
        }

        /// <summary>
        /// البحث في الأنشطة
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="userId">رقم المستخدم (اختياري)</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="pageSize">حجم الصفحة</param>
        /// <param name="pageNumber">رقم الصفحة</param>
        /// <returns>قائمة الأنشطة المطابقة</returns>
        public List<UserActivity> SearchActivities(string searchTerm, int? userId = null, DateTime? fromDate = null, 
            DateTime? toDate = null, int pageSize = 50, int pageNumber = 1)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_SearchActivities", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@SearchTerm", searchTerm ?? string.Empty);
                command.Parameters.AddWithValue("@UserID", userId ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@FromDate", fromDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ToDate", toDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PageSize", pageSize);
                command.Parameters.AddWithValue("@PageNumber", pageNumber);

                connection.Open();
                using var reader = command.ExecuteReader();

                var activities = new List<UserActivity>();
                while (reader.Read())
                {
                    activities.Add(MapActivityFromReader(reader));
                }

                return activities;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في البحث في الأنشطة");
                throw;
            }
        }

        #endregion

        #region عمليات الصيانة

        /// <summary>
        /// تنظيف الأنشطة القديمة
        /// </summary>
        /// <param name="olderThanDays">أقدم من عدد الأيام</param>
        /// <param name="keepSecuritySensitive">الاحتفاظ بالأنشطة الحساسة أمنياً</param>
        /// <returns>عدد الأنشطة المحذوفة</returns>
        public int CleanupOldActivities(int olderThanDays = 365, bool keepSecuritySensitive = true)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_CleanupOldActivities", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@OlderThanDays", olderThanDays);
                command.Parameters.AddWithValue("@KeepSecuritySensitive", keepSecuritySensitive);

                // معامل الإخراج
                var outputParam = new SqlParameter("@DeletedCount", SqlDbType.Int)
                {
                    Direction = ParameterDirection.Output
                };
                command.Parameters.Add(outputParam);

                connection.Open();
                command.ExecuteNonQuery();

                var deletedCount = Convert.ToInt32(outputParam.Value);
                _logger?.LogInformation($"تم حذف {deletedCount} نشاط قديم");
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تنظيف الأنشطة القديمة");
                throw;
            }
        }

        /// <summary>
        /// مراجعة نشاط
        /// </summary>
        /// <param name="activityId">رقم النشاط</param>
        /// <param name="reviewedBy">من راجع النشاط</param>
        /// <param name="reviewNotes">ملاحظات المراجعة</param>
        /// <returns>true إذا تمت المراجعة بنجاح</returns>
        public bool ReviewActivity(int activityId, string reviewedBy, string reviewNotes = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_ReviewActivity", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@ActivityID", activityId);
                command.Parameters.AddWithValue("@ReviewedBy", reviewedBy);
                command.Parameters.AddWithValue("@ReviewNotes", reviewNotes ?? (object)DBNull.Value);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تمت مراجعة النشاط {activityId} بواسطة {reviewedBy}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في مراجعة النشاط {activityId}");
                throw;
            }
        }

        #endregion

        #region الإحصائيات

        /// <summary>
        /// الحصول على إحصائيات الأنشطة
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>إحصائيات الأنشطة</returns>
        public ActivityStatistics GetActivityStatistics(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetActivityStatistics", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@FromDate", fromDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ToDate", toDate ?? (object)DBNull.Value);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return new ActivityStatistics
                    {
                        TotalActivities = reader.GetInt32("TotalActivities"),
                        SuccessfulActivities = reader.GetInt32("SuccessfulActivities"),
                        FailedActivities = reader.GetInt32("FailedActivities"),
                        SecuritySensitiveActivities = reader.GetInt32("SecuritySensitiveActivities"),
                        UnreviewedActivities = reader.GetInt32("UnreviewedActivities"),
                        TodayActivities = reader.GetInt32("TodayActivities"),
                        AverageExecutionTime = reader.GetDouble("AverageExecutionTime")
                    };
                }

                return new ActivityStatistics();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إحصائيات الأنشطة");
                throw;
            }
        }

        /// <summary>
        /// الحصول على إحصائيات الأنشطة حسب النوع
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>إحصائيات الأنشطة حسب النوع</returns>
        public List<ActivityTypeStatistics> GetActivityStatisticsByType(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetActivityStatisticsByType", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@FromDate", fromDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ToDate", toDate ?? (object)DBNull.Value);

                connection.Open();
                using var reader = command.ExecuteReader();

                var statistics = new List<ActivityTypeStatistics>();
                while (reader.Read())
                {
                    statistics.Add(new ActivityTypeStatistics
                    {
                        ActivityType = reader.GetString("ActivityType"),
                        Count = reader.GetInt32("Count"),
                        SuccessRate = reader.GetDouble("SuccessRate"),
                        AverageExecutionTime = reader.GetDouble("AverageExecutionTime")
                    });
                }

                return statistics;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إحصائيات الأنشطة حسب النوع");
                throw;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// تحويل بيانات القارئ إلى كائن نشاط
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن النشاط</returns>
        private UserActivity MapActivityFromReader(SqlDataReader reader)
        {
            return new UserActivity
            {
                ActivityID = reader.GetInt32("ActivityID"),
                UserID = reader.GetInt32("UserID"),
                SessionID = reader.IsDBNull("SessionID") ? null : reader.GetInt32("SessionID"),
                ActivityType = reader.GetString("ActivityType"),
                Description = reader.GetString("Description"),
                ActivityDateTime = reader.GetDateTime("ActivityDateTime"),
                Module = reader.IsDBNull("Module") ? null : reader.GetString("Module"),
                Action = reader.IsDBNull("Action") ? null : reader.GetString("Action"),
                TargetObject = reader.IsDBNull("TargetObject") ? null : reader.GetString("TargetObject"),
                TargetObjectID = reader.IsDBNull("TargetObjectID") ? null : reader.GetInt32("TargetObjectID"),
                OldValues = reader.IsDBNull("OldValues") ? null : reader.GetString("OldValues"),
                NewValues = reader.IsDBNull("NewValues") ? null : reader.GetString("NewValues"),
                Status = reader.GetString("Status"),
                IsSuccessful = reader.GetBoolean("IsSuccessful"),
                ErrorMessage = reader.IsDBNull("ErrorMessage") ? null : reader.GetString("ErrorMessage"),
                ExecutionTimeMs = reader.IsDBNull("ExecutionTimeMs") ? null : reader.GetInt32("ExecutionTimeMs"),
                IPAddress = reader.IsDBNull("IPAddress") ? null : reader.GetString("IPAddress"),
                UserAgent = reader.IsDBNull("UserAgent") ? null : reader.GetString("UserAgent"),
                DeviceName = reader.IsDBNull("DeviceName") ? null : reader.GetString("DeviceName"),
                Severity = reader.GetString("Severity"),
                IsSecuritySensitive = reader.GetBoolean("IsSecuritySensitive"),
                RequiresReview = reader.GetBoolean("RequiresReview"),
                IsReviewed = reader.GetBoolean("IsReviewed"),
                ReviewedBy = reader.IsDBNull("ReviewedBy") ? null : reader.GetString("ReviewedBy"),
                ReviewedDate = reader.IsDBNull("ReviewedDate") ? null : reader.GetDateTime("ReviewedDate"),
                ReviewNotes = reader.IsDBNull("ReviewNotes") ? null : reader.GetString("ReviewNotes"),
                AdditionalData = reader.IsDBNull("AdditionalData") ? null : reader.GetString("AdditionalData"),
                Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes"),
                CreatedDate = reader.GetDateTime("CreatedDate")
            };
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// إحصائيات الأنشطة
    /// </summary>
    public class ActivityStatistics
    {
        public int TotalActivities { get; set; }
        public int SuccessfulActivities { get; set; }
        public int FailedActivities { get; set; }
        public int SecuritySensitiveActivities { get; set; }
        public int UnreviewedActivities { get; set; }
        public int TodayActivities { get; set; }
        public double AverageExecutionTime { get; set; }
    }

    /// <summary>
    /// إحصائيات الأنشطة حسب النوع
    /// </summary>
    public class ActivityTypeStatistics
    {
        public string ActivityType { get; set; }
        public int Count { get; set; }
        public double SuccessRate { get; set; }
        public double AverageExecutionTime { get; set; }
    }

    #endregion
}
