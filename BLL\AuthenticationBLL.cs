using System;
using System.Collections.Generic;
using System.Linq;
using AredooPOS.Models;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.BLL
{
    /// <summary>
    /// طبقة منطق الأعمال للمصادقة
    /// تحتوي على جميع العمليات المتعلقة بتسجيل الدخول والخروج والمصادقة
    /// </summary>
    public class AuthenticationBLL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly UserDAL _userDAL;
        private readonly UserSessionDAL _sessionDAL;
        private readonly UserActivityDAL _activityDAL;
        private readonly ILogger<AuthenticationBLL> _logger;

        // إعدادات الأمان
        private const int MaxFailedAttempts = 5;
        private const int SessionDurationHours = 8;
        private const int MaxConcurrentSessions = 3;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة منطق الأعمال للمصادقة
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public AuthenticationBLL(string connectionString = null, ILogger<AuthenticationBLL> logger = null)
        {
            _userDAL = new UserDAL(connectionString, null);
            _sessionDAL = new UserSessionDAL(connectionString, null);
            _activityDAL = new UserActivityDAL(connectionString, null);
            _logger = logger;
        }

        #endregion

        #region تسجيل الدخول

        /// <summary>
        /// تسجيل دخول المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <param name="ipAddress">عنوان IP</param>
        /// <param name="userAgent">معلومات المتصفح</param>
        /// <param name="deviceName">اسم الجهاز</param>
        /// <returns>نتيجة تسجيل الدخول</returns>
        public LoginResult Login(string username, string password, string ipAddress, string userAgent = null, string deviceName = null)
        {
            try
            {
                // التحقق من صحة المدخلات
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    return new LoginResult
                    {
                        IsSuccessful = false,
                        ErrorMessage = "اسم المستخدم وكلمة المرور مطلوبان"
                    };
                }

                // البحث عن المستخدم
                var user = _userDAL.GetUserByUsername(username);
                if (user == null)
                {
                    // تسجيل محاولة دخول فاشلة
                    LogFailedLoginAttempt(username, ipAddress, "مستخدم غير موجود");
                    
                    return new LoginResult
                    {
                        IsSuccessful = false,
                        ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة"
                    };
                }

                // التحقق من حالة المستخدم
                var statusCheck = CheckUserStatus(user);
                if (!statusCheck.CanLogin)
                {
                    // تسجيل محاولة دخول فاشلة
                    LogFailedLoginAttempt(username, ipAddress, statusCheck.Reason);
                    
                    return new LoginResult
                    {
                        IsSuccessful = false,
                        ErrorMessage = statusCheck.Reason,
                        User = user
                    };
                }

                // التحقق من كلمة المرور
                if (!PasswordHelper.VerifyPassword(password, user.PasswordHash, user.PasswordSalt))
                {
                    // تسجيل محاولة دخول فاشلة
                    _userDAL.RecordFailedLogin(user.UserID, ipAddress);
                    LogFailedLoginAttempt(username, ipAddress, "كلمة مرور خاطئة");
                    
                    return new LoginResult
                    {
                        IsSuccessful = false,
                        ErrorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة",
                        User = user
                    };
                }

                // التحقق من عدد الجلسات المتزامنة
                var activeSessions = _sessionDAL.GetUserSessions(user.UserID, true);
                if (activeSessions.Count >= MaxConcurrentSessions)
                {
                    // إنهاء أقدم جلسة
                    var oldestSession = activeSessions.OrderBy(s => s.StartTime).First();
                    _sessionDAL.EndUserSession(oldestSession.SessionID, "تجاوز الحد الأقصى للجلسات المتزامنة");
                }

                // إنشاء جلسة جديدة
                var session = new UserSession(user.UserID, ipAddress, userAgent, SessionDurationHours)
                {
                    DeviceName = deviceName,
                    DeviceType = DetectDeviceType(userAgent),
                    OperatingSystem = DetectOperatingSystem(userAgent)
                };

                var sessionId = _sessionDAL.AddUserSession(session);
                session.SessionID = sessionId;

                // تسجيل دخول ناجح
                _userDAL.RecordSuccessfulLogin(user.UserID, ipAddress);

                // تسجيل النشاط
                LogActivity(user.UserID, ActivityTypes.Login, $"تسجيل دخول ناجح من {ipAddress}", 
                    SystemModules.Authentication, SystemActions.Login, username, sessionId, ipAddress, userAgent, deviceName);

                _logger?.LogInformation($"تسجيل دخول ناجح للمستخدم {username} من {ipAddress}");

                return new LoginResult
                {
                    IsSuccessful = true,
                    User = user,
                    Session = session,
                    MustChangePassword = user.ShouldChangePassword()
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تسجيل دخول المستخدم {username}");
                
                return new LoginResult
                {
                    IsSuccessful = false,
                    ErrorMessage = "حدث خطأ في النظام، يرجى المحاولة مرة أخرى"
                };
            }
        }

        /// <summary>
        /// التحقق من صحة الجلسة
        /// </summary>
        /// <param name="sessionToken">رمز الجلسة</param>
        /// <returns>نتيجة التحقق من الجلسة</returns>
        public SessionValidationResult ValidateSession(string sessionToken)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(sessionToken))
                {
                    return new SessionValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "رمز الجلسة مطلوب"
                    };
                }

                var session = _sessionDAL.GetSessionByToken(sessionToken);
                if (session == null)
                {
                    return new SessionValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "جلسة غير صحيحة"
                    };
                }

                // التحقق من انتهاء صلاحية الجلسة
                if (session.IsExpired())
                {
                    _sessionDAL.EndUserSession(session.SessionID, SessionEndReasons.Expired);
                    
                    return new SessionValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "انتهت صلاحية الجلسة"
                    };
                }

                // التحقق من نشاط الجلسة
                if (!session.IsActiveSession())
                {
                    _sessionDAL.EndUserSession(session.SessionID, SessionEndReasons.Timeout);
                    
                    return new SessionValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "انتهت مهلة الجلسة"
                    };
                }

                // الحصول على بيانات المستخدم
                var user = _userDAL.GetUserById(session.UserID);
                if (user == null || !user.CanLogin())
                {
                    _sessionDAL.EndUserSession(session.SessionID, SessionEndReasons.SecurityBreach);
                    
                    return new SessionValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "المستخدم غير مخول للوصول"
                    };
                }

                // تحديث آخر نشاط
                _sessionDAL.UpdateLastActivity(sessionToken);

                return new SessionValidationResult
                {
                    IsValid = true,
                    User = user,
                    Session = session
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في التحقق من الجلسة {sessionToken}");
                
                return new SessionValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "حدث خطأ في التحقق من الجلسة"
                };
            }
        }

        #endregion

        #region تسجيل الخروج

        /// <summary>
        /// تسجيل خروج المستخدم
        /// </summary>
        /// <param name="sessionToken">رمز الجلسة</param>
        /// <param name="reason">سبب تسجيل الخروج</param>
        /// <returns>true إذا تم تسجيل الخروج بنجاح</returns>
        public bool Logout(string sessionToken, string reason = SessionEndReasons.NormalLogout)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(sessionToken))
                    return false;

                var session = _sessionDAL.GetSessionByToken(sessionToken);
                if (session == null)
                    return false;

                var user = _userDAL.GetUserById(session.UserID);
                
                // إنهاء الجلسة
                var success = _sessionDAL.EndUserSessionByToken(sessionToken, reason);

                if (success && user != null)
                {
                    // تسجيل النشاط
                    LogActivity(user.UserID, ActivityTypes.Logout, $"تسجيل خروج - {reason}", 
                        SystemModules.Authentication, SystemActions.Logout, user.Username, session.SessionID);

                    _logger?.LogInformation($"تسجيل خروج المستخدم {user.Username}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تسجيل خروج الجلسة {sessionToken}");
                return false;
            }
        }

        /// <summary>
        /// تسجيل خروج جميع جلسات المستخدم
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="reason">سبب تسجيل الخروج</param>
        /// <param name="excludeSessionId">استثناء جلسة معينة</param>
        /// <returns>عدد الجلسات المنتهية</returns>
        public int LogoutAllSessions(int userId, string reason = "تسجيل خروج من جميع الأجهزة", int? excludeSessionId = null)
        {
            try
            {
                var user = _userDAL.GetUserById(userId);
                if (user == null)
                    return 0;

                var endedCount = _sessionDAL.EndAllUserSessions(userId, reason, excludeSessionId);

                if (endedCount > 0)
                {
                    // تسجيل النشاط
                    LogActivity(userId, ActivityTypes.Security, $"تم إنهاء {endedCount} جلسة للمستخدم", 
                        SystemModules.Security, "إنهاء جميع الجلسات", user.Username, null, null, null, null, true);

                    _logger?.LogInformation($"تم إنهاء {endedCount} جلسة للمستخدم {user.Username}");
                }

                return endedCount;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إنهاء جلسات المستخدم {userId}");
                return 0;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// التحقق من حالة المستخدم
        /// </summary>
        /// <param name="user">المستخدم</param>
        /// <returns>نتيجة فحص الحالة</returns>
        private UserStatusCheck CheckUserStatus(User user)
        {
            if (!user.IsActive)
            {
                return new UserStatusCheck
                {
                    CanLogin = false,
                    Reason = "الحساب غير نشط"
                };
            }

            if (user.IsLocked)
            {
                return new UserStatusCheck
                {
                    CanLogin = false,
                    Reason = $"الحساب مقفل - {user.LockReason}"
                };
            }

            if (!user.CanAccessSystem)
            {
                return new UserStatusCheck
                {
                    CanLogin = false,
                    Reason = "غير مخول للوصول للنظام"
                };
            }

            if (user.IsPasswordExpired())
            {
                return new UserStatusCheck
                {
                    CanLogin = false,
                    Reason = "انتهت صلاحية كلمة المرور"
                };
            }

            return new UserStatusCheck
            {
                CanLogin = true,
                Reason = "مخول للدخول"
            };
        }

        /// <summary>
        /// اكتشاف نوع الجهاز
        /// </summary>
        /// <param name="userAgent">معلومات المتصفح</param>
        /// <returns>نوع الجهاز</returns>
        private string DetectDeviceType(string userAgent)
        {
            if (string.IsNullOrWhiteSpace(userAgent))
                return DeviceTypes.Unknown;

            userAgent = userAgent.ToLower();

            if (userAgent.Contains("mobile") || userAgent.Contains("android") || userAgent.Contains("iphone"))
                return DeviceTypes.Mobile;

            if (userAgent.Contains("tablet") || userAgent.Contains("ipad"))
                return DeviceTypes.Tablet;

            return DeviceTypes.Desktop;
        }

        /// <summary>
        /// اكتشاف نظام التشغيل
        /// </summary>
        /// <param name="userAgent">معلومات المتصفح</param>
        /// <returns>نظام التشغيل</returns>
        private string DetectOperatingSystem(string userAgent)
        {
            if (string.IsNullOrWhiteSpace(userAgent))
                return "غير معروف";

            userAgent = userAgent.ToLower();

            if (userAgent.Contains("windows"))
                return "Windows";
            if (userAgent.Contains("mac"))
                return "macOS";
            if (userAgent.Contains("linux"))
                return "Linux";
            if (userAgent.Contains("android"))
                return "Android";
            if (userAgent.Contains("ios") || userAgent.Contains("iphone") || userAgent.Contains("ipad"))
                return "iOS";

            return "غير معروف";
        }

        /// <summary>
        /// تسجيل محاولة دخول فاشلة
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="ipAddress">عنوان IP</param>
        /// <param name="reason">سبب الفشل</param>
        private void LogFailedLoginAttempt(string username, string ipAddress, string reason)
        {
            try
            {
                var activity = new UserActivity
                {
                    UserID = 0, // مستخدم غير معروف
                    ActivityType = ActivityTypes.Login,
                    Description = $"محاولة دخول فاشلة لاسم المستخدم: {username} - السبب: {reason}",
                    Module = SystemModules.Authentication,
                    Action = "محاولة دخول فاشلة",
                    IsSuccessful = false,
                    ErrorMessage = reason,
                    IPAddress = ipAddress,
                    IsSecuritySensitive = true,
                    RequiresReview = true,
                    Severity = ActivitySeverity.High
                };

                _activityDAL.AddUserActivity(activity);
                _logger?.LogWarning($"محاولة دخول فاشلة لاسم المستخدم {username} من {ipAddress}: {reason}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تسجيل محاولة الدخول الفاشلة");
            }
        }

        /// <summary>
        /// تسجيل نشاط المصادقة
        /// </summary>
        private void LogActivity(int userId, string activityType, string description, string module, string action, 
            string username, int? sessionId = null, string ipAddress = null, string userAgent = null, 
            string deviceName = null, bool isSecuritySensitive = false)
        {
            try
            {
                var activity = new UserActivity(userId, activityType, description, module, action)
                {
                    SessionID = sessionId,
                    IPAddress = ipAddress,
                    UserAgent = userAgent,
                    DeviceName = deviceName,
                    IsSecuritySensitive = isSecuritySensitive,
                    RequiresReview = isSecuritySensitive,
                    Severity = isSecuritySensitive ? ActivitySeverity.High : ActivitySeverity.Medium
                };

                _activityDAL.AddUserActivity(activity);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تسجيل نشاط المصادقة");
            }
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// نتيجة تسجيل الدخول
    /// </summary>
    public class LoginResult
    {
        public bool IsSuccessful { get; set; }
        public string ErrorMessage { get; set; }
        public User User { get; set; }
        public UserSession Session { get; set; }
        public bool MustChangePassword { get; set; }
    }

    /// <summary>
    /// نتيجة التحقق من الجلسة
    /// </summary>
    public class SessionValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        public User User { get; set; }
        public UserSession Session { get; set; }
    }

    /// <summary>
    /// فحص حالة المستخدم
    /// </summary>
    public class UserStatusCheck
    {
        public bool CanLogin { get; set; }
        public string Reason { get; set; }
    }

    #endregion
}
