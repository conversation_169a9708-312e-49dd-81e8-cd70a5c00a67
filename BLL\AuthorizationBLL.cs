using System;
using System.Collections.Generic;
using System.Linq;
using AredooPOS.Models;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.BLL
{
    /// <summary>
    /// طبقة منطق الأعمال للتخويل والصلاحيات
    /// تحتوي على جميع العمليات المتعلقة بفحص الصلاحيات والتخويل
    /// </summary>
    public class AuthorizationBLL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly UserDAL _userDAL;
        private readonly RoleDAL _roleDAL;
        private readonly UserActivityDAL _activityDAL;
        private readonly ILogger<AuthorizationBLL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة منطق الأعمال للتخويل
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public AuthorizationBLL(string connectionString = null, ILogger<AuthorizationBLL> logger = null)
        {
            _userDAL = new UserDAL(connectionString, null);
            _roleDAL = new RoleDAL(connectionString, null);
            _activityDAL = new UserActivityDAL(connectionString, null);
            _logger = logger;
        }

        #endregion

        #region فحص الصلاحيات

        /// <summary>
        /// التحقق من صلاحية المستخدم
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="permission">اسم الصلاحية</param>
        /// <param name="logAccess">تسجيل محاولة الوصول</param>
        /// <returns>true إذا كان المستخدم يملك الصلاحية</returns>
        public bool HasPermission(int userId, string permission, bool logAccess = true)
        {
            try
            {
                var user = _userDAL.GetUserById(userId);
                if (user == null)
                {
                    if (logAccess)
                        LogAccessAttempt(userId, permission, false, "مستخدم غير موجود");
                    return false;
                }

                // التحقق من حالة المستخدم
                if (!user.IsActive || user.IsLocked || !user.CanAccessSystem)
                {
                    if (logAccess)
                        LogAccessAttempt(userId, permission, false, "مستخدم غير نشط أو مقفل");
                    return false;
                }

                // التحقق من الصلاحية
                var hasPermission = user.HasPermission(permission);

                if (logAccess)
                    LogAccessAttempt(userId, permission, hasPermission, hasPermission ? "تم السماح" : "تم الرفض");

                return hasPermission;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في فحص صلاحية {permission} للمستخدم {userId}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صلاحية المستخدم مع رمز الجلسة
        /// </summary>
        /// <param name="sessionToken">رمز الجلسة</param>
        /// <param name="permission">اسم الصلاحية</param>
        /// <param name="logAccess">تسجيل محاولة الوصول</param>
        /// <returns>نتيجة فحص الصلاحية</returns>
        public PermissionCheckResult CheckPermission(string sessionToken, string permission, bool logAccess = true)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(sessionToken))
                {
                    return new PermissionCheckResult
                    {
                        HasPermission = false,
                        ErrorMessage = "رمز الجلسة مطلوب"
                    };
                }

                // التحقق من صحة الجلسة
                var authBLL = new AuthenticationBLL();
                var sessionValidation = authBLL.ValidateSession(sessionToken);
                
                if (!sessionValidation.IsValid)
                {
                    return new PermissionCheckResult
                    {
                        HasPermission = false,
                        ErrorMessage = sessionValidation.ErrorMessage
                    };
                }

                var user = sessionValidation.User;
                var hasPermission = HasPermission(user.UserID, permission, logAccess);

                return new PermissionCheckResult
                {
                    HasPermission = hasPermission,
                    User = user,
                    Session = sessionValidation.Session,
                    ErrorMessage = hasPermission ? null : "ليس لديك صلاحية للوصول لهذه الميزة"
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في فحص صلاحية {permission} للجلسة {sessionToken}");
                
                return new PermissionCheckResult
                {
                    HasPermission = false,
                    ErrorMessage = "حدث خطأ في فحص الصلاحية"
                };
            }
        }

        /// <summary>
        /// التحقق من صلاحيات متعددة
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="permissions">قائمة الصلاحيات</param>
        /// <param name="requireAll">هل يجب توفر جميع الصلاحيات</param>
        /// <returns>true إذا كان المستخدم يملك الصلاحيات المطلوبة</returns>
        public bool HasPermissions(int userId, string[] permissions, bool requireAll = true)
        {
            try
            {
                if (permissions == null || permissions.Length == 0)
                    return true;

                var user = _userDAL.GetUserById(userId);
                if (user == null || !user.IsActive || user.IsLocked || !user.CanAccessSystem)
                    return false;

                if (requireAll)
                {
                    return permissions.All(permission => user.HasPermission(permission));
                }
                else
                {
                    return permissions.Any(permission => user.HasPermission(permission));
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في فحص الصلاحيات للمستخدم {userId}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من مستوى الصلاحية
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="requiredLevel">المستوى المطلوب</param>
        /// <returns>true إذا كان مستوى المستخدم أعلى أو يساوي المطلوب</returns>
        public bool HasPermissionLevel(int userId, int requiredLevel)
        {
            try
            {
                var user = _userDAL.GetUserById(userId);
                if (user == null || !user.IsActive || user.IsLocked || !user.CanAccessSystem)
                    return false;

                return user.PermissionLevel >= requiredLevel;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في فحص مستوى الصلاحية للمستخدم {userId}");
                return false;
            }
        }

        #endregion

        #region إدارة الأدوار

        /// <summary>
        /// إضافة دور جديد
        /// </summary>
        /// <param name="role">بيانات الدور</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>رقم الدور الجديد</returns>
        public int AddRole(Role role, string currentUser)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateRole(role, true);

                // التحقق من عدم تكرار اسم الدور
                if (_roleDAL.RoleNameExists(role.RoleName))
                {
                    throw new InvalidOperationException("اسم الدور موجود مسبقاً");
                }

                // تعيين معلومات النظام
                role.CreatedBy = currentUser;
                role.ModifiedBy = currentUser;
                role.CreatedDate = DateTime.Now;
                role.ModifiedDate = DateTime.Now;

                // إضافة الدور
                var roleId = _roleDAL.AddRole(role);

                // تسجيل النشاط
                LogActivity(0, ActivityTypes.Create, $"تم إضافة دور جديد: {role.RoleName}", 
                    SystemModules.UserManagement, SystemActions.Add, currentUser, true);

                _logger?.LogInformation($"تم إضافة دور جديد: {role.RoleName} برقم {roleId}");
                return roleId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إضافة الدور {role?.RoleName}");
                throw;
            }
        }

        /// <summary>
        /// تحديث بيانات الدور
        /// </summary>
        /// <param name="role">بيانات الدور المحدثة</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateRole(Role role, string currentUser)
        {
            try
            {
                // التحقق من وجود الدور
                var existingRole = _roleDAL.GetRoleById(role.RoleID);
                if (existingRole == null)
                {
                    throw new InvalidOperationException("الدور غير موجود");
                }

                // التحقق من عدم تعديل الأدوار النظامية
                if (existingRole.IsSystemRole)
                {
                    throw new InvalidOperationException("لا يمكن تعديل الأدوار النظامية");
                }

                // التحقق من صحة البيانات
                ValidateRole(role, false);

                // التحقق من عدم تكرار اسم الدور
                if (_roleDAL.RoleNameExists(role.RoleName, role.RoleID))
                {
                    throw new InvalidOperationException("اسم الدور موجود مسبقاً");
                }

                // تعيين معلومات التعديل
                role.ModifiedBy = currentUser;
                role.ModifiedDate = DateTime.Now;
                role.CreatedBy = existingRole.CreatedBy;
                role.CreatedDate = existingRole.CreatedDate;
                role.IsSystemRole = existingRole.IsSystemRole;

                // تحديث الدور
                var success = _roleDAL.UpdateRole(role);

                if (success)
                {
                    // تسجيل النشاط
                    LogActivity(0, ActivityTypes.Update, $"تم تحديث الدور: {role.RoleName}", 
                        SystemModules.UserManagement, SystemActions.Edit, currentUser, true);

                    _logger?.LogInformation($"تم تحديث الدور: {role.RoleName}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث الدور {role?.RoleID}");
                throw;
            }
        }

        /// <summary>
        /// حذف دور
        /// </summary>
        /// <param name="roleId">رقم الدور</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public bool DeleteRole(int roleId, string currentUser)
        {
            try
            {
                var role = _roleDAL.GetRoleById(roleId);
                if (role == null)
                {
                    throw new InvalidOperationException("الدور غير موجود");
                }

                // التحقق من عدم حذف الأدوار النظامية
                if (role.IsSystemRole)
                {
                    throw new InvalidOperationException("لا يمكن حذف الأدوار النظامية");
                }

                // التحقق من إمكانية الحذف
                if (!_roleDAL.CanDeleteRole(roleId))
                {
                    throw new InvalidOperationException("لا يمكن حذف الدور لأنه مرتبط بمستخدمين");
                }

                var success = _roleDAL.DeleteRole(roleId, currentUser);

                if (success)
                {
                    // تسجيل النشاط
                    LogActivity(0, ActivityTypes.Delete, $"تم حذف الدور: {role.RoleName}", 
                        SystemModules.UserManagement, SystemActions.Delete, currentUser, true);

                    _logger?.LogInformation($"تم حذف الدور: {role.RoleName}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حذف الدور {roleId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع الأدوار
        /// </summary>
        /// <param name="includeInactive">تضمين الأدوار غير النشطة</param>
        /// <returns>قائمة الأدوار</returns>
        public List<Role> GetAllRoles(bool includeInactive = false)
        {
            try
            {
                return _roleDAL.GetAllRoles(includeInactive);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على جميع الأدوار");
                throw;
            }
        }

        /// <summary>
        /// الحصول على دور بالرقم
        /// </summary>
        /// <param name="roleId">رقم الدور</param>
        /// <returns>بيانات الدور</returns>
        public Role GetRoleById(int roleId)
        {
            try
            {
                return _roleDAL.GetRoleById(roleId);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الدور {roleId}");
                throw;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// التحقق من صحة بيانات الدور
        /// </summary>
        /// <param name="role">بيانات الدور</param>
        /// <param name="isNew">هل الدور جديد</param>
        private void ValidateRole(Role role, bool isNew)
        {
            if (role == null)
                throw new ArgumentNullException(nameof(role));

            if (string.IsNullOrWhiteSpace(role.RoleName))
                throw new ArgumentException("اسم الدور مطلوب");

            if (role.RoleName.Length < 2)
                throw new ArgumentException("اسم الدور يجب أن يكون حرفين على الأقل");

            if (role.PermissionLevel < 1 || role.PermissionLevel > 10)
                throw new ArgumentException("مستوى الصلاحية يجب أن يكون بين 1 و 10");

            if (role.MaxDiscountPercentage < 0 || role.MaxDiscountPercentage > 100)
                throw new ArgumentException("نسبة الخصم الأقصى يجب أن تكون بين 0 و 100");
        }

        /// <summary>
        /// تسجيل محاولة الوصول
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="permission">الصلاحية</param>
        /// <param name="granted">هل تم السماح</param>
        /// <param name="reason">السبب</param>
        private void LogAccessAttempt(int userId, string permission, bool granted, string reason)
        {
            try
            {
                var activity = new UserActivity(userId, ActivityTypes.Security, 
                    $"محاولة وصول للصلاحية {permission} - {reason}", 
                    SystemModules.Security, "فحص صلاحية")
                {
                    IsSuccessful = granted,
                    IsSecuritySensitive = !granted,
                    RequiresReview = !granted,
                    Severity = granted ? ActivitySeverity.Low : ActivitySeverity.Medium,
                    AdditionalData = $"{{\"permission\":\"{permission}\",\"granted\":{granted.ToString().ToLower()}}}"
                };

                _activityDAL.AddUserActivity(activity);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تسجيل محاولة الوصول");
            }
        }

        /// <summary>
        /// تسجيل نشاط التخويل
        /// </summary>
        private void LogActivity(int userId, string activityType, string description, string module, 
            string action, string performedBy, bool isSecuritySensitive = false)
        {
            try
            {
                var activity = new UserActivity(userId, activityType, description, module, action)
                {
                    IsSecuritySensitive = isSecuritySensitive,
                    RequiresReview = isSecuritySensitive,
                    Severity = isSecuritySensitive ? ActivitySeverity.High : ActivitySeverity.Medium
                };

                _activityDAL.AddUserActivity(activity);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تسجيل نشاط التخويل");
            }
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// نتيجة فحص الصلاحية
    /// </summary>
    public class PermissionCheckResult
    {
        public bool HasPermission { get; set; }
        public string ErrorMessage { get; set; }
        public User User { get; set; }
        public UserSession Session { get; set; }
    }

    #endregion
}
