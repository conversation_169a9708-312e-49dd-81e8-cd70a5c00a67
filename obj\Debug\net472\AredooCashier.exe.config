<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
  <connectionStrings>
    <add name="AridooPOS" connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=AridooPOS;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <appSettings>
    <!-- إعدادات التطبيق العامة -->
    <add key="CompanyName" value="أريدوو" />
    <add key="CompanyAddress" value="المملكة العربية السعودية" />
    <add key="CompanyPhone" value="+966 XX XXX XXXX" />
    <add key="CompanyEmail" value="<EMAIL>" />
    <add key="TaxNumber" value="*********" />
    <!-- إعدادات الفواتير -->
    <add key="DefaultTaxRate" value="15" />
    <add key="InvoicePrefix" value="INV" />
    <add key="AutoGenerateInvoiceNumber" value="true" />
    <!-- إعدادات الطباعة -->
    <add key="DefaultPrinter" value="" />
    <add key="ThermalPrinterWidth" value="80" />
    <add key="PrinterType" value="Thermal" />
    <!-- إعدادات النسخ الاحتياطي -->
    <add key="BackupPath" value="C:\AridooPOS\Backup\" />
    <add key="AutoBackup" value="true" />
    <add key="BackupInterval" value="24" />
    <!-- إعدادات الأمان -->
    <add key="RequireLogin" value="false" />
    <add key="SessionTimeout" value="480" />
    <!-- إعدادات الواجهة -->
    <add key="Language" value="ar-SA" />
    <add key="Theme" value="Default" />
    <add key="ShowWelcomeScreen" value="true" />
  </appSettings>
  <system.globalization>
    <culture uiCulture="ar-SA" culture="ar-SA" />
  </system.globalization>
</configuration>