using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.IO.Ports;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Printing
{
    /// <summary>
    /// مدير الطباعة الحرارية
    /// يدعم الطباعة على الطابعات الحرارية بأوامر ESC/POS
    /// متوافق مع Windows 7+
    /// </summary>
    public class ThermalPrintManager
    {
        #region المتغيرات والخصائص

        private readonly ILogger<ThermalPrintManager> _logger;
        private readonly ThermalPrinterSettings _settings;

        /// <summary>
        /// حالة الطابعة
        /// </summary>
        public PrinterStatus Status { get; private set; }

        /// <summary>
        /// هل الطابعة متصلة
        /// </summary>
        public bool IsConnected { get; private set; }

        #endregion

        #region الأحداث

        /// <summary>
        /// حدث بدء الطباعة
        /// </summary>
        public event EventHandler<PrintStartedEventArgs> PrintStarted;

        /// <summary>
        /// حدث اكتمال الطباعة
        /// </summary>
        public event EventHandler<PrintCompletedEventArgs> PrintCompleted;

        /// <summary>
        /// حدث فشل الطباعة
        /// </summary>
        public event EventHandler<PrintFailedEventArgs> PrintFailed;

        /// <summary>
        /// حدث تغيير حالة الطابعة
        /// </summary>
        public event EventHandler<PrinterStatusChangedEventArgs> StatusChanged;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ مدير الطباعة الحرارية
        /// </summary>
        /// <param name="settings">إعدادات الطابعة</param>
        /// <param name="logger">مسجل الأحداث</param>
        public ThermalPrintManager(ThermalPrinterSettings settings, ILogger<ThermalPrintManager> logger = null)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            _logger = logger;
            Status = PrinterStatus.Disconnected;
        }

        /// <summary>
        /// تهيئة الطابعة
        /// </summary>
        /// <returns>true إذا تم التهيئة بنجاح</returns>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger?.LogInformation("بدء تهيئة الطابعة الحرارية");

                // فحص الاتصال
                var connectionResult = await TestConnectionAsync();
                if (!connectionResult)
                {
                    Status = PrinterStatus.Error;
                    return false;
                }

                // تهيئة الطابعة
                await SendInitializationCommandsAsync();

                Status = PrinterStatus.Ready;
                IsConnected = true;

                _logger?.LogInformation("تم تهيئة الطابعة الحرارية بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تهيئة الطابعة الحرارية");
                Status = PrinterStatus.Error;
                IsConnected = false;
                return false;
            }
        }

        #endregion

        #region طباعة الفواتير

        /// <summary>
        /// طباعة فاتورة
        /// </summary>
        /// <param name="invoice">بيانات الفاتورة</param>
        /// <returns>true إذا تم الطباعة بنجاح</returns>
        public async Task<bool> PrintInvoiceAsync(InvoiceData invoice)
        {
            if (!IsConnected)
            {
                _logger?.LogWarning("الطابعة غير متصلة");
                return false;
            }

            try
            {
                Status = PrinterStatus.Printing;
                PrintStarted?.Invoke(this, new PrintStartedEventArgs { DocumentType = "Invoice", DocumentId = invoice.InvoiceNumber });

                _logger?.LogInformation($"بدء طباعة الفاتورة {invoice.InvoiceNumber}");

                // بناء محتوى الطباعة
                var printContent = BuildInvoicePrintContent(invoice);

                // إرسال البيانات للطابعة
                var result = await SendToPrinterAsync(printContent);

                if (result)
                {
                    Status = PrinterStatus.Ready;
                    PrintCompleted?.Invoke(this, new PrintCompletedEventArgs { DocumentType = "Invoice", DocumentId = invoice.InvoiceNumber });
                    _logger?.LogInformation($"تم طباعة الفاتورة {invoice.InvoiceNumber} بنجاح");
                }
                else
                {
                    Status = PrinterStatus.Error;
                    PrintFailed?.Invoke(this, new PrintFailedEventArgs { DocumentType = "Invoice", DocumentId = invoice.InvoiceNumber, Error = "فشل في إرسال البيانات للطابعة" });
                }

                return result;
            }
            catch (Exception ex)
            {
                Status = PrinterStatus.Error;
                _logger?.LogError(ex, $"خطأ في طباعة الفاتورة {invoice.InvoiceNumber}");
                PrintFailed?.Invoke(this, new PrintFailedEventArgs { DocumentType = "Invoice", DocumentId = invoice.InvoiceNumber, Error = ex.Message });
                return false;
            }
        }

        /// <summary>
        /// بناء محتوى طباعة الفاتورة
        /// </summary>
        /// <param name="invoice">بيانات الفاتورة</param>
        /// <returns>محتوى الطباعة</returns>
        private byte[] BuildInvoicePrintContent(InvoiceData invoice)
        {
            var content = new List<byte>();

            // تهيئة الطابعة
            content.AddRange(ESCPOSCommands.Initialize);

            // تعيين الترميز العربي
            content.AddRange(ESCPOSCommands.SetCodePage(ESCPOSCommands.CodePage.CP1256));

            // طباعة رأس الفاتورة
            if (_settings.PrintStoreLogo && !string.IsNullOrEmpty(_settings.LogoPath))
            {
                content.AddRange(PrintLogo(_settings.LogoPath));
            }

            // معلومات المتجر
            if (_settings.PrintStoreInfo)
            {
                content.AddRange(PrintStoreHeader(invoice.StoreInfo));
            }

            // خط فاصل
            content.AddRange(ESCPOSCommands.PrintLine("=", _settings.PaperWidth));

            // معلومات الفاتورة
            content.AddRange(PrintInvoiceHeader(invoice));

            // معلومات العميل
            if (_settings.PrintCustomerInfo && invoice.Customer != null)
            {
                content.AddRange(PrintCustomerInfo(invoice.Customer));
            }

            // خط فاصل
            content.AddRange(ESCPOSCommands.PrintLine("-", _settings.PaperWidth));

            // تفاصيل المنتجات
            content.AddRange(PrintInvoiceItems(invoice.Items));

            // خط فاصل
            content.AddRange(ESCPOSCommands.PrintLine("-", _settings.PaperWidth));

            // المجاميع
            content.AddRange(PrintInvoiceTotals(invoice));

            // معلومات الدفع
            if (_settings.PrintPaymentMethod)
            {
                content.AddRange(PrintPaymentInfo(invoice.PaymentInfo));
            }

            // رسالة الشكر
            if (_settings.PrintThankYouMessage)
            {
                content.AddRange(PrintThankYouMessage());
            }

            // الباركود
            if (_settings.PrintBarcode)
            {
                content.AddRange(PrintBarcode(invoice.InvoiceNumber));
            }

            // قطع الورق
            content.AddRange(ESCPOSCommands.CutPaper);

            return content.ToArray();
        }

        /// <summary>
        /// طباعة شعار المتجر
        /// </summary>
        private byte[] PrintLogo(string logoPath)
        {
            var content = new List<byte>();

            try
            {
                if (File.Exists(logoPath))
                {
                    // تحويل الصورة إلى تنسيق مناسب للطباعة
                    using var image = Image.FromFile(logoPath);
                    var logoData = ConvertImageToESCPOS(image);
                    content.AddRange(logoData);
                    content.AddRange(ESCPOSCommands.LineFeed);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطأ في طباعة الشعار");
            }

            return content.ToArray();
        }

        /// <summary>
        /// طباعة رأس المتجر
        /// </summary>
        private byte[] PrintStoreHeader(StoreInfo storeInfo)
        {
            var content = new List<byte>();

            // اسم المتجر
            content.AddRange(ESCPOSCommands.SetAlignment(ESCPOSCommands.Alignment.Center));
            content.AddRange(ESCPOSCommands.SetTextSize(2, 2));
            content.AddRange(ESCPOSCommands.SetBold(true));
            content.AddRange(Encoding.GetEncoding(1256).GetBytes(storeInfo.StoreName));
            content.AddRange(ESCPOSCommands.LineFeed);

            // معلومات الاتصال
            content.AddRange(ESCPOSCommands.SetTextSize(1, 1));
            content.AddRange(ESCPOSCommands.SetBold(false));

            if (!string.IsNullOrEmpty(storeInfo.Phone))
            {
                content.AddRange(Encoding.GetEncoding(1256).GetBytes($"هاتف: {storeInfo.Phone}"));
                content.AddRange(ESCPOSCommands.LineFeed);
            }

            if (!string.IsNullOrEmpty(storeInfo.Address))
            {
                content.AddRange(Encoding.GetEncoding(1256).GetBytes(storeInfo.Address));
                content.AddRange(ESCPOSCommands.LineFeed);
            }

            content.AddRange(ESCPOSCommands.LineFeed);
            return content.ToArray();
        }

        /// <summary>
        /// طباعة رأس الفاتورة
        /// </summary>
        private byte[] PrintInvoiceHeader(InvoiceData invoice)
        {
            var content = new List<byte>();

            content.AddRange(ESCPOSCommands.SetAlignment(ESCPOSCommands.Alignment.Left));
            content.AddRange(ESCPOSCommands.SetBold(true));

            // رقم الفاتورة
            content.AddRange(Encoding.GetEncoding(1256).GetBytes($"رقم الفاتورة: {invoice.InvoiceNumber}"));
            content.AddRange(ESCPOSCommands.LineFeed);

            // التاريخ والوقت
            content.AddRange(Encoding.GetEncoding(1256).GetBytes($"التاريخ: {invoice.InvoiceDate:dd/MM/yyyy HH:mm}"));
            content.AddRange(ESCPOSCommands.LineFeed);

            // الكاشير
            if (!string.IsNullOrEmpty(invoice.CashierName))
            {
                content.AddRange(Encoding.GetEncoding(1256).GetBytes($"الكاشير: {invoice.CashierName}"));
                content.AddRange(ESCPOSCommands.LineFeed);
            }

            content.AddRange(ESCPOSCommands.SetBold(false));
            content.AddRange(ESCPOSCommands.LineFeed);

            return content.ToArray();
        }

        /// <summary>
        /// طباعة معلومات العميل
        /// </summary>
        private byte[] PrintCustomerInfo(CustomerInfo customer)
        {
            var content = new List<byte>();

            content.AddRange(ESCPOSCommands.SetBold(true));
            content.AddRange(Encoding.GetEncoding(1256).GetBytes("معلومات العميل:"));
            content.AddRange(ESCPOSCommands.LineFeed);
            content.AddRange(ESCPOSCommands.SetBold(false));

            content.AddRange(Encoding.GetEncoding(1256).GetBytes($"الاسم: {customer.Name}"));
            content.AddRange(ESCPOSCommands.LineFeed);

            if (!string.IsNullOrEmpty(customer.Phone))
            {
                content.AddRange(Encoding.GetEncoding(1256).GetBytes($"الهاتف: {customer.Phone}"));
                content.AddRange(ESCPOSCommands.LineFeed);
            }

            content.AddRange(ESCPOSCommands.LineFeed);
            return content.ToArray();
        }

        /// <summary>
        /// طباعة عناصر الفاتورة
        /// </summary>
        private byte[] PrintInvoiceItems(List<InvoiceItem> items)
        {
            var content = new List<byte>();

            // رأس الجدول
            content.AddRange(ESCPOSCommands.SetBold(true));
            var header = FormatTableRow("المنتج", "الكمية", "السعر", "المجموع");
            content.AddRange(Encoding.GetEncoding(1256).GetBytes(header));
            content.AddRange(ESCPOSCommands.LineFeed);
            content.AddRange(ESCPOSCommands.SetBold(false));

            // العناصر
            foreach (var item in items)
            {
                var row = FormatTableRow(
                    item.ProductName,
                    item.Quantity.ToString("N2"),
                    item.UnitPrice.ToString("N2"),
                    item.TotalPrice.ToString("N2")
                );
                content.AddRange(Encoding.GetEncoding(1256).GetBytes(row));
                content.AddRange(ESCPOSCommands.LineFeed);
            }

            return content.ToArray();
        }

        /// <summary>
        /// طباعة مجاميع الفاتورة
        /// </summary>
        private byte[] PrintInvoiceTotals(InvoiceData invoice)
        {
            var content = new List<byte>();

            content.AddRange(ESCPOSCommands.SetAlignment(ESCPOSCommands.Alignment.Right));

            // المجموع الفرعي
            content.AddRange(Encoding.GetEncoding(1256).GetBytes($"المجموع الفرعي: {invoice.Subtotal:N2}"));
            content.AddRange(ESCPOSCommands.LineFeed);

            // الخصم
            if (invoice.DiscountAmount > 0)
            {
                content.AddRange(Encoding.GetEncoding(1256).GetBytes($"الخصم: {invoice.DiscountAmount:N2}"));
                content.AddRange(ESCPOSCommands.LineFeed);
            }

            // الضريبة
            if (invoice.TaxAmount > 0)
            {
                content.AddRange(Encoding.GetEncoding(1256).GetBytes($"الضريبة: {invoice.TaxAmount:N2}"));
                content.AddRange(ESCPOSCommands.LineFeed);
            }

            // الإجمالي
            content.AddRange(ESCPOSCommands.SetBold(true));
            content.AddRange(ESCPOSCommands.SetTextSize(2, 1));
            content.AddRange(Encoding.GetEncoding(1256).GetBytes($"الإجمالي: {invoice.TotalAmount:N2}"));
            content.AddRange(ESCPOSCommands.LineFeed);
            content.AddRange(ESCPOSCommands.SetTextSize(1, 1));
            content.AddRange(ESCPOSCommands.SetBold(false));

            content.AddRange(ESCPOSCommands.SetAlignment(ESCPOSCommands.Alignment.Left));
            return content.ToArray();
        }

        /// <summary>
        /// طباعة معلومات الدفع
        /// </summary>
        private byte[] PrintPaymentInfo(PaymentInfo paymentInfo)
        {
            var content = new List<byte>();

            content.AddRange(ESCPOSCommands.LineFeed);
            content.AddRange(Encoding.GetEncoding(1256).GetBytes($"طريقة الدفع: {paymentInfo.PaymentMethod}"));
            content.AddRange(ESCPOSCommands.LineFeed);

            if (paymentInfo.AmountPaid > 0)
            {
                content.AddRange(Encoding.GetEncoding(1256).GetBytes($"المبلغ المدفوع: {paymentInfo.AmountPaid:N2}"));
                content.AddRange(ESCPOSCommands.LineFeed);
            }

            if (paymentInfo.Change > 0)
            {
                content.AddRange(Encoding.GetEncoding(1256).GetBytes($"الباقي: {paymentInfo.Change:N2}"));
                content.AddRange(ESCPOSCommands.LineFeed);
            }

            return content.ToArray();
        }

        /// <summary>
        /// طباعة رسالة الشكر
        /// </summary>
        private byte[] PrintThankYouMessage()
        {
            var content = new List<byte>();

            content.AddRange(ESCPOSCommands.LineFeed);
            content.AddRange(ESCPOSCommands.SetAlignment(ESCPOSCommands.Alignment.Center));

            var message = !string.IsNullOrEmpty(_settings.CustomThankYouMessage) 
                ? _settings.CustomThankYouMessage 
                : "شكراً لزيارتكم";

            content.AddRange(Encoding.GetEncoding(1256).GetBytes(message));
            content.AddRange(ESCPOSCommands.LineFeed);
            content.AddRange(ESCPOSCommands.LineFeed);

            content.AddRange(ESCPOSCommands.SetAlignment(ESCPOSCommands.Alignment.Left));
            return content.ToArray();
        }

        /// <summary>
        /// طباعة الباركود
        /// </summary>
        private byte[] PrintBarcode(string data)
        {
            var content = new List<byte>();

            content.AddRange(ESCPOSCommands.SetAlignment(ESCPOSCommands.Alignment.Center));
            content.AddRange(ESCPOSCommands.PrintBarcode(data, ESCPOSCommands.BarcodeType.CODE128));
            content.AddRange(ESCPOSCommands.LineFeed);
            content.AddRange(ESCPOSCommands.SetAlignment(ESCPOSCommands.Alignment.Left));

            return content.ToArray();
        }

        #endregion

        #region دوال مساعدة

        /// <summary>
        /// فحص الاتصال بالطابعة
        /// </summary>
        /// <returns>true إذا كان الاتصال متاح</returns>
        private async Task<bool> TestConnectionAsync()
        {
            try
            {
                switch (_settings.ConnectionType)
                {
                    case PrinterConnectionType.USB:
                        return TestUSBConnection();

                    case PrinterConnectionType.Serial:
                        return await TestSerialConnectionAsync();

                    case PrinterConnectionType.Network:
                        return await TestNetworkConnectionAsync();

                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فحص الاتصال بالطابعة");
                return false;
            }
        }

        /// <summary>
        /// فحص اتصال USB
        /// </summary>
        private bool TestUSBConnection()
        {
            try
            {
                // فحص وجود الطابعة في قائمة الطابعات المثبتة
                foreach (string printerName in PrinterSettings.InstalledPrinters)
                {
                    if (printerName.Equals(_settings.PrinterName, StringComparison.OrdinalIgnoreCase))
                    {
                        return true;
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// فحص الاتصال التسلسلي
        /// </summary>
        private async Task<bool> TestSerialConnectionAsync()
        {
            try
            {
                using var serialPort = new SerialPort(_settings.SerialPort, _settings.BaudRate);
                serialPort.Open();
                await Task.Delay(100);
                return serialPort.IsOpen;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// فحص اتصال الشبكة
        /// </summary>
        private async Task<bool> TestNetworkConnectionAsync()
        {
            try
            {
                using var client = new TcpClient();
                await client.ConnectAsync(_settings.IPAddress, _settings.Port);
                return client.Connected;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إرسال أوامر التهيئة
        /// </summary>
        private async Task SendInitializationCommandsAsync()
        {
            var commands = new List<byte>();
            commands.AddRange(ESCPOSCommands.Initialize);
            commands.AddRange(ESCPOSCommands.SetCodePage(ESCPOSCommands.CodePage.CP1256));

            await SendToPrinterAsync(commands.ToArray());
        }

        /// <summary>
        /// إرسال البيانات للطابعة
        /// </summary>
        /// <param name="data">البيانات</param>
        /// <returns>true إذا تم الإرسال بنجاح</returns>
        private async Task<bool> SendToPrinterAsync(byte[] data)
        {
            try
            {
                switch (_settings.ConnectionType)
                {
                    case PrinterConnectionType.USB:
                        return await SendToUSBPrinterAsync(data);

                    case PrinterConnectionType.Serial:
                        return await SendToSerialPrinterAsync(data);

                    case PrinterConnectionType.Network:
                        return await SendToNetworkPrinterAsync(data);

                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إرسال البيانات للطابعة");
                return false;
            }
        }

        /// <summary>
        /// إرسال البيانات لطابعة USB
        /// </summary>
        private async Task<bool> SendToUSBPrinterAsync(byte[] data)
        {
            try
            {
                // استخدام RawPrinterHelper للطباعة المباشرة
                return await Task.Run(() => RawPrinterHelper.SendBytesToPrinter(_settings.PrinterName, data));
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الطباعة على USB");
                return false;
            }
        }

        /// <summary>
        /// إرسال البيانات لطابعة تسلسلية
        /// </summary>
        private async Task<bool> SendToSerialPrinterAsync(byte[] data)
        {
            try
            {
                using var serialPort = new SerialPort(_settings.SerialPort, _settings.BaudRate);
                serialPort.Open();
                await serialPort.BaseStream.WriteAsync(data, 0, data.Length);
                await serialPort.BaseStream.FlushAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الطباعة التسلسلية");
                return false;
            }
        }

        /// <summary>
        /// إرسال البيانات لطابعة الشبكة
        /// </summary>
        private async Task<bool> SendToNetworkPrinterAsync(byte[] data)
        {
            try
            {
                using var client = new TcpClient();
                await client.ConnectAsync(_settings.IPAddress, _settings.Port);
                using var stream = client.GetStream();
                await stream.WriteAsync(data, 0, data.Length);
                await stream.FlushAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في طباعة الشبكة");
                return false;
            }
        }

        /// <summary>
        /// تنسيق صف الجدول
        /// </summary>
        private string FormatTableRow(string col1, string col2, string col3, string col4)
        {
            var width = _settings.PaperWidth / _settings.CharactersPerLine;
            var col1Width = width * 40 / 100; // 40%
            var col2Width = width * 15 / 100; // 15%
            var col3Width = width * 20 / 100; // 20%
            var col4Width = width * 25 / 100; // 25%

            return $"{col1.PadRight(col1Width)}{col2.PadLeft(col2Width)}{col3.PadLeft(col3Width)}{col4.PadLeft(col4Width)}";
        }

        /// <summary>
        /// تحويل الصورة إلى تنسيق ESC/POS
        /// </summary>
        private byte[] ConvertImageToESCPOS(Image image)
        {
            // تحويل الصورة إلى أبيض وأسود وتحويلها لتنسيق الطابعة
            // هذا تنفيذ مبسط - يمكن تحسينه
            var content = new List<byte>();
            
            try
            {
                // تصغير الصورة لتناسب عرض الطابعة
                var targetWidth = Math.Min(image.Width, _settings.PaperWidth);
                var targetHeight = (int)(image.Height * ((double)targetWidth / image.Width));
                
                using var resizedImage = new Bitmap(image, targetWidth, targetHeight);
                
                // تحويل إلى أبيض وأسود
                for (int y = 0; y < resizedImage.Height; y += 8)
                {
                    for (int x = 0; x < resizedImage.Width; x++)
                    {
                        byte pixelByte = 0;
                        for (int bit = 0; bit < 8 && (y + bit) < resizedImage.Height; bit++)
                        {
                            var pixel = resizedImage.GetPixel(x, y + bit);
                            var brightness = (pixel.R + pixel.G + pixel.B) / 3;
                            if (brightness < 128) // أسود
                            {
                                pixelByte |= (byte)(1 << (7 - bit));
                            }
                        }
                        content.Add(pixelByte);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطأ في تحويل الصورة");
            }

            return content.ToArray();
        }

        #endregion

        #region تنظيف الموارد

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            try
            {
                IsConnected = false;
                Status = PrinterStatus.Disconnected;
                _logger?.LogInformation("تم تنظيف موارد مدير الطباعة الحرارية");
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطأ في تنظيف موارد الطباعة");
            }
        }

        #endregion
    }
}
