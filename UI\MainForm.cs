using System;
using System.Drawing;
using System.Windows.Forms;
using AridooPOS.DAL;

namespace AridooPOS.UI
{
    public partial class MainForm : Form
    {
        public MainForm()
        {
            InitializeComponent();
            InitializeArabicUI();
            CheckDatabaseConnection();
        }

        private void InitializeComponent()
        {
            this.menuStrip1 = new MenuStrip();
            this.invoicesToolStripMenuItem = new ToolStripMenuItem();
            this.newInvoiceToolStripMenuItem = new ToolStripMenuItem();
            this.searchInvoicesToolStripMenuItem = new ToolStripMenuItem();
            this.toolStripSeparator1 = new ToolStripSeparator();
            this.returnInvoiceToolStripMenuItem = new ToolStripMenuItem();
            this.productsToolStripMenuItem = new ToolStripMenuItem();
            this.customersToolStripMenuItem = new ToolStripMenuItem();
            this.reportsToolStripMenuItem = new ToolStripMenuItem();
            this.settingsToolStripMenuItem = new ToolStripMenuItem();
            this.statusStrip1 = new StatusStrip();
            this.statusLabel = new ToolStripStatusLabel();
            this.dateTimeLabel = new ToolStripStatusLabel();
            this.panel1 = new Panel();
            this.welcomeLabel = new Label();
            this.quickAccessPanel = new Panel();
            this.btnNewInvoice = new Button();
            this.btnSearchInvoices = new Button();
            this.btnProducts = new Button();
            this.btnCustomers = new Button();
            
            this.menuStrip1.SuspendLayout();
            this.statusStrip1.SuspendLayout();
            this.panel1.SuspendLayout();
            this.quickAccessPanel.SuspendLayout();
            this.SuspendLayout();

            // menuStrip1
            this.menuStrip1.Items.AddRange(new ToolStripItem[] {
                this.invoicesToolStripMenuItem,
                this.productsToolStripMenuItem,
                this.customersToolStripMenuItem,
                this.reportsToolStripMenuItem,
                this.settingsToolStripMenuItem});
            this.menuStrip1.Location = new Point(0, 0);
            this.menuStrip1.Name = "menuStrip1";
            this.menuStrip1.Size = new Size(1200, 24);
            this.menuStrip1.TabIndex = 0;
            this.menuStrip1.Text = "menuStrip1";

            // invoicesToolStripMenuItem
            this.invoicesToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
                this.newInvoiceToolStripMenuItem,
                this.searchInvoicesToolStripMenuItem,
                this.toolStripSeparator1,
                this.returnInvoiceToolStripMenuItem});
            this.invoicesToolStripMenuItem.Name = "invoicesToolStripMenuItem";
            this.invoicesToolStripMenuItem.Size = new Size(60, 20);
            this.invoicesToolStripMenuItem.Text = "الفواتير";

            // newInvoiceToolStripMenuItem
            this.newInvoiceToolStripMenuItem.Name = "newInvoiceToolStripMenuItem";
            this.newInvoiceToolStripMenuItem.Size = new Size(152, 22);
            this.newInvoiceToolStripMenuItem.Text = "فاتورة جديدة";
            this.newInvoiceToolStripMenuItem.Click += new EventHandler(this.newInvoiceToolStripMenuItem_Click);

            // searchInvoicesToolStripMenuItem
            this.searchInvoicesToolStripMenuItem.Name = "searchInvoicesToolStripMenuItem";
            this.searchInvoicesToolStripMenuItem.Size = new Size(152, 22);
            this.searchInvoicesToolStripMenuItem.Text = "البحث عن الفواتير";
            this.searchInvoicesToolStripMenuItem.Click += new EventHandler(this.searchInvoicesToolStripMenuItem_Click);

            // toolStripSeparator1
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new Size(149, 6);

            // returnInvoiceToolStripMenuItem
            this.returnInvoiceToolStripMenuItem.Name = "returnInvoiceToolStripMenuItem";
            this.returnInvoiceToolStripMenuItem.Size = new Size(152, 22);
            this.returnInvoiceToolStripMenuItem.Text = "إرجاع فاتورة";
            this.returnInvoiceToolStripMenuItem.Click += new EventHandler(this.returnInvoiceToolStripMenuItem_Click);

            // productsToolStripMenuItem
            this.productsToolStripMenuItem.Name = "productsToolStripMenuItem";
            this.productsToolStripMenuItem.Size = new Size(60, 20);
            this.productsToolStripMenuItem.Text = "المنتجات";

            // customersToolStripMenuItem
            this.customersToolStripMenuItem.Name = "customersToolStripMenuItem";
            this.customersToolStripMenuItem.Size = new Size(50, 20);
            this.customersToolStripMenuItem.Text = "العملاء";

            // reportsToolStripMenuItem
            this.reportsToolStripMenuItem.Name = "reportsToolStripMenuItem";
            this.reportsToolStripMenuItem.Size = new Size(50, 20);
            this.reportsToolStripMenuItem.Text = "التقارير";

            // settingsToolStripMenuItem
            this.settingsToolStripMenuItem.Name = "settingsToolStripMenuItem";
            this.settingsToolStripMenuItem.Size = new Size(60, 20);
            this.settingsToolStripMenuItem.Text = "الإعدادات";

            // statusStrip1
            this.statusStrip1.Items.AddRange(new ToolStripItem[] {
                this.statusLabel,
                this.dateTimeLabel});
            this.statusStrip1.Location = new Point(0, 678);
            this.statusStrip1.Name = "statusStrip1";
            this.statusStrip1.Size = new Size(1200, 22);
            this.statusStrip1.TabIndex = 1;
            this.statusStrip1.Text = "statusStrip1";

            // statusLabel
            this.statusLabel.Name = "statusLabel";
            this.statusLabel.Size = new Size(35, 17);
            this.statusLabel.Text = "جاهز";

            // dateTimeLabel
            this.dateTimeLabel.Name = "dateTimeLabel";
            this.dateTimeLabel.Size = new Size(100, 17);
            this.dateTimeLabel.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm");

            // panel1
            this.panel1.Controls.Add(this.welcomeLabel);
            this.panel1.Dock = DockStyle.Top;
            this.panel1.Location = new Point(0, 24);
            this.panel1.Name = "panel1";
            this.panel1.Size = new Size(1200, 80);
            this.panel1.TabIndex = 2;
            this.panel1.BackColor = Color.FromArgb(41, 128, 185);

            // welcomeLabel
            this.welcomeLabel.AutoSize = true;
            this.welcomeLabel.Font = new Font("Tahoma", 18F, FontStyle.Bold);
            this.welcomeLabel.ForeColor = Color.White;
            this.welcomeLabel.Location = new Point(500, 25);
            this.welcomeLabel.Name = "welcomeLabel";
            this.welcomeLabel.Size = new Size(200, 29);
            this.welcomeLabel.TabIndex = 0;
            this.welcomeLabel.Text = "مرحباً بك في أريدوو";

            // quickAccessPanel
            this.quickAccessPanel.Controls.Add(this.btnNewInvoice);
            this.quickAccessPanel.Controls.Add(this.btnSearchInvoices);
            this.quickAccessPanel.Controls.Add(this.btnProducts);
            this.quickAccessPanel.Controls.Add(this.btnCustomers);
            this.quickAccessPanel.Dock = DockStyle.Fill;
            this.quickAccessPanel.Location = new Point(0, 104);
            this.quickAccessPanel.Name = "quickAccessPanel";
            this.quickAccessPanel.Size = new Size(1200, 574);
            this.quickAccessPanel.TabIndex = 3;
            this.quickAccessPanel.BackColor = Color.FromArgb(236, 240, 241);

            // btnNewInvoice
            this.btnNewInvoice.BackColor = Color.FromArgb(46, 204, 113);
            this.btnNewInvoice.FlatStyle = FlatStyle.Flat;
            this.btnNewInvoice.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            this.btnNewInvoice.ForeColor = Color.White;
            this.btnNewInvoice.Location = new Point(50, 50);
            this.btnNewInvoice.Name = "btnNewInvoice";
            this.btnNewInvoice.Size = new Size(200, 100);
            this.btnNewInvoice.TabIndex = 0;
            this.btnNewInvoice.Text = "فاتورة جديدة\nF2";
            this.btnNewInvoice.UseVisualStyleBackColor = false;
            this.btnNewInvoice.Click += new EventHandler(this.btnNewInvoice_Click);

            // btnSearchInvoices
            this.btnSearchInvoices.BackColor = Color.FromArgb(52, 152, 219);
            this.btnSearchInvoices.FlatStyle = FlatStyle.Flat;
            this.btnSearchInvoices.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            this.btnSearchInvoices.ForeColor = Color.White;
            this.btnSearchInvoices.Location = new Point(300, 50);
            this.btnSearchInvoices.Name = "btnSearchInvoices";
            this.btnSearchInvoices.Size = new Size(200, 100);
            this.btnSearchInvoices.TabIndex = 1;
            this.btnSearchInvoices.Text = "البحث عن الفواتير\nF3";
            this.btnSearchInvoices.UseVisualStyleBackColor = false;
            this.btnSearchInvoices.Click += new EventHandler(this.btnSearchInvoices_Click);

            // btnProducts
            this.btnProducts.BackColor = Color.FromArgb(155, 89, 182);
            this.btnProducts.FlatStyle = FlatStyle.Flat;
            this.btnProducts.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            this.btnProducts.ForeColor = Color.White;
            this.btnProducts.Location = new Point(550, 50);
            this.btnProducts.Name = "btnProducts";
            this.btnProducts.Size = new Size(200, 100);
            this.btnProducts.TabIndex = 2;
            this.btnProducts.Text = "المنتجات\nF4";
            this.btnProducts.UseVisualStyleBackColor = false;

            // btnCustomers
            this.btnCustomers.BackColor = Color.FromArgb(230, 126, 34);
            this.btnCustomers.FlatStyle = FlatStyle.Flat;
            this.btnCustomers.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            this.btnCustomers.ForeColor = Color.White;
            this.btnCustomers.Location = new Point(800, 50);
            this.btnCustomers.Name = "btnCustomers";
            this.btnCustomers.Size = new Size(200, 100);
            this.btnCustomers.TabIndex = 3;
            this.btnCustomers.Text = "العملاء\nF5";
            this.btnCustomers.UseVisualStyleBackColor = false;

            // MainForm
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 700);
            this.Controls.Add(this.quickAccessPanel);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.menuStrip1);
            this.MainMenuStrip = this.menuStrip1;
            this.Name = "MainForm";
            this.Text = "أريدوو - نظام نقاط البيع";
            this.WindowState = FormWindowState.Maximized;
            this.KeyPreview = true;
            this.KeyDown += new KeyEventHandler(this.MainForm_KeyDown);

            this.menuStrip1.ResumeLayout(false);
            this.menuStrip1.PerformLayout();
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.quickAccessPanel.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void InitializeArabicUI()
        {
            // تعيين الخط العربي
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // تشغيل مؤقت لتحديث الوقت
            Timer timer = new Timer();
            timer.Interval = 1000;
            timer.Tick += (s, e) => dateTimeLabel.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            timer.Start();
        }

        private void CheckDatabaseConnection()
        {
            try
            {
                if (DatabaseConnection.TestConnection())
                {
                    statusLabel.Text = "متصل بقاعدة البيانات";
                    statusLabel.ForeColor = Color.Green;
                }
                else
                {
                    statusLabel.Text = "خطأ في الاتصال بقاعدة البيانات";
                    statusLabel.ForeColor = Color.Red;
                    MessageBox.Show("تعذر الاتصال بقاعدة البيانات. يرجى التحقق من الإعدادات.", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                statusLabel.Text = "خطأ في الاتصال بقاعدة البيانات";
                statusLabel.ForeColor = Color.Red;
                MessageBox.Show($"خطأ في الاتصال بقاعدة البيانات:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void MainForm_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.F2:
                    btnNewInvoice_Click(sender, e);
                    break;
                case Keys.F3:
                    btnSearchInvoices_Click(sender, e);
                    break;
                case Keys.F4:
                    // btnProducts_Click(sender, e);
                    break;
                case Keys.F5:
                    // btnCustomers_Click(sender, e);
                    break;
            }
        }

        private void newInvoiceToolStripMenuItem_Click(object sender, EventArgs e)
        {
            btnNewInvoice_Click(sender, e);
        }

        private void btnNewInvoice_Click(object sender, EventArgs e)
        {
            try
            {
                InvoiceForm invoiceForm = new InvoiceForm();
                invoiceForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج الفاتورة:\n{ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void searchInvoicesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            btnSearchInvoices_Click(sender, e);
        }

        private void btnSearchInvoices_Click(object sender, EventArgs e)
        {
            try
            {
                InvoiceSearchForm searchForm = new InvoiceSearchForm();
                searchForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج البحث:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void returnInvoiceToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // سيتم تطوير نموذج إرجاع الفواتير لاحقاً
                MessageBox.Show("سيتم تطوير هذه الميزة قريباً", "قيد التطوير", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #region Designer Variables
        private MenuStrip menuStrip1;
        private ToolStripMenuItem invoicesToolStripMenuItem;
        private ToolStripMenuItem newInvoiceToolStripMenuItem;
        private ToolStripMenuItem searchInvoicesToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator1;
        private ToolStripMenuItem returnInvoiceToolStripMenuItem;
        private ToolStripMenuItem productsToolStripMenuItem;
        private ToolStripMenuItem customersToolStripMenuItem;
        private ToolStripMenuItem reportsToolStripMenuItem;
        private ToolStripMenuItem settingsToolStripMenuItem;
        private StatusStrip statusStrip1;
        private ToolStripStatusLabel statusLabel;
        private ToolStripStatusLabel dateTimeLabel;
        private Panel panel1;
        private Label welcomeLabel;
        private Panel quickAccessPanel;
        private Button btnNewInvoice;
        private Button btnSearchInvoices;
        private Button btnProducts;
        private Button btnCustomers;
        #endregion
    }
}
