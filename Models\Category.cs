using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AredooPOS.Models
{
    /// <summary>
    /// نموذج فئة المنتجات
    /// يمثل تصنيف المنتجات في النظام
    /// </summary>
    public class Category
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم الفئة في قاعدة البيانات (مفتاح أساسي)
        /// </summary>
        public int CategoryID { get; set; }

        /// <summary>
        /// كود الفئة
        /// </summary>
        [Required(ErrorMessage = "كود الفئة مطلوب")]
        [StringLength(20, ErrorMessage = "كود الفئة لا يجب أن يتجاوز 20 حرف")]
        public string CategoryCode { get; set; }

        /// <summary>
        /// اسم الفئة
        /// </summary>
        [Required(ErrorMessage = "اسم الفئة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الفئة لا يجب أن يتجاوز 100 حرف")]
        public string CategoryName { get; set; }

        /// <summary>
        /// اسم الفئة باللغة الإنجليزية
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم الفئة بالإنجليزية لا يجب أن يتجاوز 100 حرف")]
        public string CategoryNameEn { get; set; }

        /// <summary>
        /// وصف الفئة
        /// </summary>
        [StringLength(500, ErrorMessage = "وصف الفئة لا يجب أن يتجاوز 500 حرف")]
        public string Description { get; set; }

        #endregion

        #region التسلسل الهرمي

        /// <summary>
        /// رقم الفئة الأب (للفئات الفرعية)
        /// </summary>
        public int? ParentCategoryID { get; set; }

        /// <summary>
        /// اسم الفئة الأب
        /// </summary>
        public string ParentCategoryName { get; set; }

        /// <summary>
        /// مستوى الفئة في التسلسل الهرمي
        /// </summary>
        public int Level { get; set; } = 1;

        /// <summary>
        /// المسار الكامل للفئة
        /// </summary>
        [StringLength(500, ErrorMessage = "مسار الفئة لا يجب أن يتجاوز 500 حرف")]
        public string CategoryPath { get; set; }

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int SortOrder { get; set; }

        #endregion

        #region الصور والأيقونات

        /// <summary>
        /// مسار صورة الفئة
        /// </summary>
        [StringLength(500, ErrorMessage = "مسار الصورة لا يجب أن يتجاوز 500 حرف")]
        public string ImagePath { get; set; }

        /// <summary>
        /// أيقونة الفئة
        /// </summary>
        [StringLength(50, ErrorMessage = "الأيقونة لا يجب أن تتجاوز 50 حرف")]
        public string Icon { get; set; }

        /// <summary>
        /// لون الفئة (للعرض)
        /// </summary>
        [StringLength(20, ErrorMessage = "اللون لا يجب أن يتجاوز 20 حرف")]
        public string Color { get; set; }

        #endregion

        #region الإعدادات والخصائص

        /// <summary>
        /// هل الفئة نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل الفئة مرئية في الواجهة
        /// </summary>
        public bool IsVisible { get; set; } = true;

        /// <summary>
        /// هل يمكن إضافة منتجات لهذه الفئة مباشرة
        /// </summary>
        public bool AllowProducts { get; set; } = true;

        /// <summary>
        /// نسبة الربح الافتراضية للفئة
        /// </summary>
        [Range(0, 1000, ErrorMessage = "نسبة الربح يجب أن تكون بين 0 و 1000")]
        public decimal DefaultProfitMargin { get; set; }

        /// <summary>
        /// نسبة الضريبة الافتراضية للفئة
        /// </summary>
        [Range(0, 100, ErrorMessage = "نسبة الضريبة يجب أن تكون بين 0 و 100")]
        public decimal DefaultTaxRate { get; set; } = 15;

        /// <summary>
        /// وحدة القياس الافتراضية للفئة
        /// </summary>
        [StringLength(20, ErrorMessage = "وحدة القياس لا يجب أن تتجاوز 20 حرف")]
        public string DefaultUnit { get; set; } = "قطعة";

        #endregion

        #region الملاحظات والمعلومات الإضافية

        /// <summary>
        /// ملاحظات عامة
        /// </summary>
        [StringLength(1000, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 1000 حرف")]
        public string Notes { get; set; }

        /// <summary>
        /// كلمات مفتاحية للبحث
        /// </summary>
        [StringLength(500, ErrorMessage = "الكلمات المفتاحية لا يجب أن تتجاوز 500 حرف")]
        public string Keywords { get; set; }

        #endregion

        #region معلومات النظام

        /// <summary>
        /// منشئ السجل
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المنشئ لا يجب أن يتجاوز 50 حرف")]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ إنشاء السجل
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// آخر من عدل السجل
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المعدل لا يجب أن يتجاوز 50 حرف")]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime ModifiedDate { get; set; }

        #endregion

        #region القوائم المرتبطة

        /// <summary>
        /// الفئة الأب
        /// </summary>
        public Category ParentCategory { get; set; }

        /// <summary>
        /// الفئات الفرعية
        /// </summary>
        public List<Category> SubCategories { get; set; }

        /// <summary>
        /// المنتجات في هذه الفئة
        /// </summary>
        public List<Product> Products { get; set; }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ الفئة مع التهيئة الافتراضية
        /// </summary>
        public Category()
        {
            // تهيئة القوائم
            SubCategories = new List<Category>();
            Products = new List<Product>();

            // تهيئة التواريخ
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;

            // تهيئة القيم الافتراضية
            IsActive = true;
            IsVisible = true;
            AllowProducts = true;
            Level = 1;
            SortOrder = 0;
            DefaultTaxRate = 15;
            DefaultUnit = "قطعة";
            DefaultProfitMargin = 0;
        }

        #endregion

        #region العمليات والحسابات

        /// <summary>
        /// التحقق من صحة بيانات الفئة
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(CategoryCode) &&
                   !string.IsNullOrWhiteSpace(CategoryName);
        }

        /// <summary>
        /// التحقق من كون الفئة فئة رئيسية
        /// </summary>
        /// <returns>true إذا كانت فئة رئيسية</returns>
        public bool IsRootCategory()
        {
            return !ParentCategoryID.HasValue;
        }

        /// <summary>
        /// التحقق من وجود فئات فرعية
        /// </summary>
        /// <returns>true إذا كان لها فئات فرعية</returns>
        public bool HasSubCategories()
        {
            return SubCategories != null && SubCategories.Any();
        }

        /// <summary>
        /// التحقق من وجود منتجات
        /// </summary>
        /// <returns>true إذا كان لها منتجات</returns>
        public bool HasProducts()
        {
            return Products != null && Products.Any();
        }

        /// <summary>
        /// حساب عدد المنتجات في الفئة
        /// </summary>
        /// <returns>عدد المنتجات</returns>
        public int GetProductCount()
        {
            return Products?.Count ?? 0;
        }

        /// <summary>
        /// حساب عدد المنتجات النشطة في الفئة
        /// </summary>
        /// <returns>عدد المنتجات النشطة</returns>
        public int GetActiveProductCount()
        {
            return Products?.Count(p => p.IsActive) ?? 0;
        }

        /// <summary>
        /// حساب إجمالي قيمة المخزون في الفئة
        /// </summary>
        /// <returns>قيمة المخزون</returns>
        public decimal GetTotalStockValue()
        {
            return Products?.Where(p => p.IsActive).Sum(p => p.GetStockValue()) ?? 0;
        }

        /// <summary>
        /// الحصول على المنتجات منخفضة المخزون
        /// </summary>
        /// <returns>قائمة المنتجات منخفضة المخزون</returns>
        public List<Product> GetLowStockProducts()
        {
            return Products?.Where(p => p.IsActive && p.IsLowStock()).ToList() ?? new List<Product>();
        }

        /// <summary>
        /// الحصول على المنتجات نافدة المخزون
        /// </summary>
        /// <returns>قائمة المنتجات نافدة المخزون</returns>
        public List<Product> GetOutOfStockProducts()
        {
            return Products?.Where(p => p.IsActive && p.IsOutOfStock()).ToList() ?? new List<Product>();
        }

        /// <summary>
        /// بناء المسار الكامل للفئة
        /// </summary>
        public void BuildCategoryPath()
        {
            if (ParentCategory != null)
            {
                CategoryPath = $"{ParentCategory.CategoryPath} > {CategoryName}";
                Level = ParentCategory.Level + 1;
            }
            else
            {
                CategoryPath = CategoryName;
                Level = 1;
            }
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// إضافة فئة فرعية
        /// </summary>
        /// <param name="subCategory">الفئة الفرعية</param>
        public void AddSubCategory(Category subCategory)
        {
            if (subCategory != null)
            {
                subCategory.ParentCategoryID = this.CategoryID;
                subCategory.ParentCategory = this;
                subCategory.BuildCategoryPath();
                SubCategories.Add(subCategory);
                ModifiedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// إضافة منتج للفئة
        /// </summary>
        /// <param name="product">المنتج</param>
        public void AddProduct(Product product)
        {
            if (product != null && AllowProducts)
            {
                product.CategoryID = this.CategoryID;
                product.Category = this;
                product.CategoryName = this.CategoryName;
                
                // تطبيق الإعدادات الافتراضية للفئة
                if (product.TaxRate == 0)
                    product.TaxRate = this.DefaultTaxRate;
                
                if (string.IsNullOrWhiteSpace(product.Unit))
                    product.Unit = this.DefaultUnit;
                
                if (product.ProfitMargin == 0)
                    product.ProfitMargin = this.DefaultProfitMargin;

                Products.Add(product);
                ModifiedDate = DateTime.Now;
            }
        }

        #endregion

        #region عمليات النسخ والتحويل

        /// <summary>
        /// تحويل الفئة إلى نص وصفي
        /// </summary>
        /// <returns>وصف نصي للفئة</returns>
        public override string ToString()
        {
            return $"{CategoryCode} - {CategoryName}";
        }

        /// <summary>
        /// إنشاء نسخة من الفئة
        /// </summary>
        /// <returns>نسخة من الفئة</returns>
        public Category Clone()
        {
            return new Category
            {
                CategoryCode = this.CategoryCode + "_نسخة",
                CategoryName = this.CategoryName + " (نسخة)",
                CategoryNameEn = this.CategoryNameEn,
                Description = this.Description,
                ParentCategoryID = this.ParentCategoryID,
                DefaultProfitMargin = this.DefaultProfitMargin,
                DefaultTaxRate = this.DefaultTaxRate,
                DefaultUnit = this.DefaultUnit,
                IsActive = this.IsActive,
                IsVisible = this.IsVisible,
                AllowProducts = this.AllowProducts,
                SortOrder = this.SortOrder + 1,
                Color = this.Color,
                Icon = this.Icon
            };
        }

        #endregion
    }
}
