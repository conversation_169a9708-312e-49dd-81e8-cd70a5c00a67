using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using AredooPOS.Database;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AredooPOS.Services
{
    /// <summary>
    /// خدمة المزامنة بين قاعدة البيانات المحلية والخادم
    /// تدعم المزامنة التلقائية وحل التعارضات
    /// </summary>
    public class SyncService
    {
        #region المتغيرات والخصائص

        private readonly OfflineDatabase _offlineDb;
        private readonly string _serverConnectionString;
        private readonly ILogger<SyncService> _logger;
        private readonly SyncSettings _settings;

        /// <summary>
        /// حالة المزامنة الحالية
        /// </summary>
        public SyncStatus CurrentStatus { get; private set; }

        /// <summary>
        /// هل المزامنة قيد التشغيل
        /// </summary>
        public bool IsSyncing { get; private set; }

        #endregion

        #region الأحداث

        /// <summary>
        /// حدث بدء المزامنة
        /// </summary>
        public event EventHandler<SyncStartedEventArgs> SyncStarted;

        /// <summary>
        /// حدث تقدم المزامنة
        /// </summary>
        public event EventHandler<SyncProgressEventArgs> SyncProgress;

        /// <summary>
        /// حدث اكتمال المزامنة
        /// </summary>
        public event EventHandler<SyncCompletedEventArgs> SyncCompleted;

        /// <summary>
        /// حدث فشل المزامنة
        /// </summary>
        public event EventHandler<SyncFailedEventArgs> SyncFailed;

        /// <summary>
        /// حدث اكتشاف تعارض
        /// </summary>
        public event EventHandler<ConflictDetectedEventArgs> ConflictDetected;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ خدمة المزامنة
        /// </summary>
        /// <param name="offlineDb">قاعدة البيانات المحلية</param>
        /// <param name="serverConnectionString">نص الاتصال بالخادم</param>
        /// <param name="settings">إعدادات المزامنة</param>
        /// <param name="logger">مسجل الأحداث</param>
        public SyncService(
            OfflineDatabase offlineDb,
            string serverConnectionString,
            SyncSettings settings = null,
            ILogger<SyncService> logger = null)
        {
            _offlineDb = offlineDb ?? throw new ArgumentNullException(nameof(offlineDb));
            _serverConnectionString = serverConnectionString ?? throw new ArgumentNullException(nameof(serverConnectionString));
            _settings = settings ?? new SyncSettings();
            _logger = logger;

            CurrentStatus = SyncStatus.Idle;
        }

        #endregion

        #region المزامنة الرئيسية

        /// <summary>
        /// تنفيذ المزامنة الكاملة
        /// </summary>
        /// <param name="direction">اتجاه المزامنة</param>
        /// <returns>نتيجة المزامنة</returns>
        public async Task<SyncResult> SyncAsync(SyncDirection direction = SyncDirection.Bidirectional)
        {
            if (IsSyncing)
            {
                throw new InvalidOperationException("المزامنة قيد التشغيل بالفعل");
            }

            var syncResult = new SyncResult
            {
                StartTime = DateTime.Now,
                Direction = direction
            };

            try
            {
                IsSyncing = true;
                CurrentStatus = SyncStatus.InProgress;

                // إثارة حدث بدء المزامنة
                SyncStarted?.Invoke(this, new SyncStartedEventArgs { Direction = direction });

                _logger?.LogInformation($"بدء المزامنة - الاتجاه: {direction}");

                // فحص الاتصال بالخادم
                if (!await CheckServerConnectionAsync())
                {
                    throw new Exception("لا يمكن الاتصال بالخادم");
                }

                // تحديد الجداول للمزامنة
                var tablesToSync = GetTablesToSync();
                var totalTables = tablesToSync.Count;
                var currentTable = 0;

                foreach (var table in tablesToSync)
                {
                    currentTable++;
                    ReportProgress(currentTable, totalTables, $"مزامنة جدول {table}");

                    try
                    {
                        var tableResult = await SyncTableAsync(table, direction);
                        syncResult.TableResults.Add(table, tableResult);

                        syncResult.TotalRecordsProcessed += tableResult.RecordsProcessed;
                        syncResult.TotalConflicts += tableResult.ConflictsDetected;
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, $"خطأ في مزامنة جدول {table}");
                        syncResult.TableResults.Add(table, new TableSyncResult
                        {
                            TableName = table,
                            IsSuccessful = false,
                            ErrorMessage = ex.Message
                        });
                        syncResult.HasErrors = true;
                    }
                }

                // تنظيف البيانات المتزامنة
                await CleanupSyncedDataAsync();

                syncResult.EndTime = DateTime.Now;
                syncResult.Duration = syncResult.EndTime - syncResult.StartTime;
                syncResult.IsSuccessful = !syncResult.HasErrors;

                CurrentStatus = syncResult.IsSuccessful ? SyncStatus.Completed : SyncStatus.Failed;

                // إثارة حدث اكتمال المزامنة
                SyncCompleted?.Invoke(this, new SyncCompletedEventArgs { Result = syncResult });

                _logger?.LogInformation($"اكتملت المزامنة - النتيجة: {(syncResult.IsSuccessful ? "نجح" : "فشل")}");

                return syncResult;
            }
            catch (Exception ex)
            {
                syncResult.EndTime = DateTime.Now;
                syncResult.Duration = syncResult.EndTime - syncResult.StartTime;
                syncResult.IsSuccessful = false;
                syncResult.ErrorMessage = ex.Message;

                CurrentStatus = SyncStatus.Failed;

                // إثارة حدث فشل المزامنة
                SyncFailed?.Invoke(this, new SyncFailedEventArgs { Error = ex, Result = syncResult });

                _logger?.LogError(ex, "فشلت المزامنة");

                return syncResult;
            }
            finally
            {
                IsSyncing = false;
            }
        }

        /// <summary>
        /// مزامنة جدول واحد
        /// </summary>
        /// <param name="tableName">اسم الجدول</param>
        /// <param name="direction">اتجاه المزامنة</param>
        /// <returns>نتيجة مزامنة الجدول</returns>
        private async Task<TableSyncResult> SyncTableAsync(string tableName, SyncDirection direction)
        {
            var result = new TableSyncResult
            {
                TableName = tableName,
                StartTime = DateTime.Now
            };

            try
            {
                switch (direction)
                {
                    case SyncDirection.ToServer:
                        await SyncToServerAsync(tableName, result);
                        break;

                    case SyncDirection.FromServer:
                        await SyncFromServerAsync(tableName, result);
                        break;

                    case SyncDirection.Bidirectional:
                        await SyncToServerAsync(tableName, result);
                        await SyncFromServerAsync(tableName, result);
                        break;
                }

                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;
                result.IsSuccessful = true;

                return result;
            }
            catch (Exception ex)
            {
                result.EndTime = DateTime.Now;
                result.Duration = result.EndTime - result.StartTime;
                result.IsSuccessful = false;
                result.ErrorMessage = ex.Message;

                throw;
            }
        }

        /// <summary>
        /// مزامنة البيانات إلى الخادم
        /// </summary>
        /// <param name="tableName">اسم الجدول</param>
        /// <param name="result">نتيجة المزامنة</param>
        private async Task SyncToServerAsync(string tableName, TableSyncResult result)
        {
            // الحصول على التغييرات المحلية غير المتزامنة
            var localChanges = GetLocalChangesForTable(tableName);

            if (!localChanges.Any())
            {
                _logger?.LogDebug($"لا توجد تغييرات محلية للجدول {tableName}");
                return;
            }

            using var serverConnection = new SqlConnection(_serverConnectionString);
            await serverConnection.OpenAsync();

            foreach (var change in localChanges)
            {
                try
                {
                    await ProcessLocalChangeAsync(change, serverConnection);
                    result.RecordsProcessed++;

                    // تحديد التغيير كمتزامن
                    _offlineDb.MarkChangesAsSynced(new[] { change.ChangeID });
                }
                catch (ConflictException conflictEx)
                {
                    result.ConflictsDetected++;
                    
                    // إثارة حدث اكتشاف التعارض
                    var conflictArgs = new ConflictDetectedEventArgs
                    {
                        TableName = tableName,
                        LocalChange = change,
                        ServerData = conflictEx.ServerData,
                        ConflictType = conflictEx.ConflictType
                    };

                    ConflictDetected?.Invoke(this, conflictArgs);

                    // حل التعارض حسب الإعدادات
                    await ResolveConflictAsync(conflictArgs);
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, $"خطأ في معالجة التغيير {change.ChangeID}");
                    result.ErrorsCount++;
                }
            }
        }

        /// <summary>
        /// مزامنة البيانات من الخادم
        /// </summary>
        /// <param name="tableName">اسم الجدول</param>
        /// <param name="result">نتيجة المزامنة</param>
        private async Task SyncFromServerAsync(string tableName, TableSyncResult result)
        {
            // الحصول على آخر تاريخ مزامنة للجدول
            var lastSyncDate = GetLastSyncDate(tableName);

            // الحصول على التغييرات من الخادم
            var serverChanges = await GetServerChangesAsync(tableName, lastSyncDate);

            if (!serverChanges.Any())
            {
                _logger?.LogDebug($"لا توجد تغييرات في الخادم للجدول {tableName}");
                return;
            }

            foreach (var change in serverChanges)
            {
                try
                {
                    await ProcessServerChangeAsync(change);
                    result.RecordsProcessed++;
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, $"خطأ في معالجة تغيير الخادم للجدول {tableName}");
                    result.ErrorsCount++;
                }
            }

            // تحديث تاريخ آخر مزامنة
            UpdateLastSyncDate(tableName, DateTime.Now);
        }

        #endregion

        #region معالجة التغييرات

        /// <summary>
        /// معالجة تغيير محلي
        /// </summary>
        /// <param name="change">التغيير</param>
        /// <param name="serverConnection">اتصال الخادم</param>
        private async Task ProcessLocalChangeAsync(ChangeLogEntry change, SqlConnection serverConnection)
        {
            // فحص وجود تعارض
            var serverRecord = await GetServerRecordAsync(change.TableName, change.RecordID, serverConnection);
            
            if (serverRecord != null && HasConflict(change, serverRecord))
            {
                throw new ConflictException("تعارض في البيانات", serverRecord, ConflictType.UpdateConflict);
            }

            // تطبيق التغيير على الخادم
            switch (change.OperationType.ToUpper())
            {
                case "INSERT":
                    await ExecuteServerInsertAsync(change, serverConnection);
                    break;

                case "UPDATE":
                    await ExecuteServerUpdateAsync(change, serverConnection);
                    break;

                case "DELETE":
                    await ExecuteServerDeleteAsync(change, serverConnection);
                    break;
            }
        }

        /// <summary>
        /// معالجة تغيير من الخادم
        /// </summary>
        /// <param name="change">التغيير</param>
        private async Task ProcessServerChangeAsync(ServerChange change)
        {
            // تطبيق التغيير على قاعدة البيانات المحلية
            switch (change.OperationType.ToUpper())
            {
                case "INSERT":
                    await ExecuteLocalInsertAsync(change);
                    break;

                case "UPDATE":
                    await ExecuteLocalUpdateAsync(change);
                    break;

                case "DELETE":
                    await ExecuteLocalDeleteAsync(change);
                    break;
            }
        }

        #endregion

        #region حل التعارضات

        /// <summary>
        /// حل التعارض
        /// </summary>
        /// <param name="conflictArgs">بيانات التعارض</param>
        private async Task ResolveConflictAsync(ConflictDetectedEventArgs conflictArgs)
        {
            switch (_settings.ConflictResolutionStrategy)
            {
                case ConflictResolutionStrategy.ServerWins:
                    await ResolveWithServerDataAsync(conflictArgs);
                    break;

                case ConflictResolutionStrategy.ClientWins:
                    await ResolveWithClientDataAsync(conflictArgs);
                    break;

                case ConflictResolutionStrategy.Manual:
                    // انتظار تدخل المستخدم
                    await WaitForManualResolutionAsync(conflictArgs);
                    break;

                case ConflictResolutionStrategy.Timestamp:
                    await ResolveByTimestampAsync(conflictArgs);
                    break;
            }
        }

        /// <summary>
        /// حل التعارض لصالح بيانات الخادم
        /// </summary>
        private async Task ResolveWithServerDataAsync(ConflictDetectedEventArgs conflictArgs)
        {
            // تحديث البيانات المحلية ببيانات الخادم
            var serverData = JsonConvert.DeserializeObject<Dictionary<string, object>>(conflictArgs.ServerData);
            await UpdateLocalRecordAsync(conflictArgs.TableName, conflictArgs.LocalChange.RecordID, serverData);

            // تحديد التغيير المحلي كمحلول
            _offlineDb.MarkChangesAsSynced(new[] { conflictArgs.LocalChange.ChangeID });
        }

        /// <summary>
        /// حل التعارض لصالح البيانات المحلية
        /// </summary>
        private async Task ResolveWithClientDataAsync(ConflictDetectedEventArgs conflictArgs)
        {
            // تطبيق التغيير المحلي على الخادم بقوة
            using var serverConnection = new SqlConnection(_serverConnectionString);
            await serverConnection.OpenAsync();

            await ProcessLocalChangeAsync(conflictArgs.LocalChange, serverConnection);
            _offlineDb.MarkChangesAsSynced(new[] { conflictArgs.LocalChange.ChangeID });
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// فحص الاتصال بالخادم
        /// </summary>
        /// <returns>true إذا كان الاتصال متاح</returns>
        private async Task<bool> CheckServerConnectionAsync()
        {
            try
            {
                using var connection = new SqlConnection(_serverConnectionString);
                await connection.OpenAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "فشل الاتصال بالخادم");
                return false;
            }
        }

        /// <summary>
        /// الحصول على قائمة الجداول للمزامنة
        /// </summary>
        /// <returns>قائمة أسماء الجداول</returns>
        private List<string> GetTablesToSync()
        {
            return new List<string>
            {
                "Products",
                "Customers",
                "Invoices",
                "InvoiceDetails"
            };
        }

        /// <summary>
        /// الحصول على التغييرات المحلية للجدول
        /// </summary>
        /// <param name="tableName">اسم الجدول</param>
        /// <returns>قائمة التغييرات</returns>
        private List<ChangeLogEntry> GetLocalChangesForTable(string tableName)
        {
            return _offlineDb.GetUnsyncedChanges()
                .Where(c => c.TableName.Equals(tableName, StringComparison.OrdinalIgnoreCase))
                .OrderBy(c => c.ChangeDate)
                .ToList();
        }

        /// <summary>
        /// تقرير تقدم المزامنة
        /// </summary>
        /// <param name="current">العنصر الحالي</param>
        /// <param name="total">إجمالي العناصر</param>
        /// <param name="message">الرسالة</param>
        private void ReportProgress(int current, int total, string message)
        {
            var progressPercentage = (int)((double)current / total * 100);
            
            SyncProgress?.Invoke(this, new SyncProgressEventArgs
            {
                ProgressPercentage = progressPercentage,
                CurrentItem = current,
                TotalItems = total,
                Message = message
            });
        }

        /// <summary>
        /// تنظيف البيانات المتزامنة
        /// </summary>
        private async Task CleanupSyncedDataAsync()
        {
            try
            {
                // حذف سجلات التغييرات المتزامنة القديمة
                var cleanupQuery = @"
                    DELETE FROM ChangeLog 
                    WHERE IsSynced = 1 
                    AND datetime(ChangeDate) < datetime('now', '-7 days')";

                _offlineDb.ExecuteNonQuery(cleanupQuery);

                _logger?.LogDebug("تم تنظيف البيانات المتزامنة");
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطأ في تنظيف البيانات المتزامنة");
            }
        }

        #endregion
    }
}
