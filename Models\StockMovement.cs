using System;
using System.ComponentModel.DataAnnotations;

namespace AredooPOS.Models
{
    /// <summary>
    /// نموذج حركة المخزون
    /// يمثل حركة واردة أو صادرة للمخزون
    /// </summary>
    public class StockMovement
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم حركة المخزون في قاعدة البيانات (مفتاح أساسي)
        /// </summary>
        public int StockMovementID { get; set; }

        /// <summary>
        /// رقم المنتج (مفتاح خارجي)
        /// </summary>
        [Required(ErrorMessage = "رقم المنتج مطلوب")]
        public int ProductID { get; set; }

        /// <summary>
        /// كود المنتج (للعرض)
        /// </summary>
        public string ProductCode { get; set; }

        /// <summary>
        /// اسم المنتج (للعرض)
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// رقم مرجعي للحركة
        /// </summary>
        [StringLength(50, ErrorMessage = "الرقم المرجعي لا يجب أن يتجاوز 50 حرف")]
        public string ReferenceNumber { get; set; }

        #endregion

        #region تفاصيل الحركة

        /// <summary>
        /// تاريخ ووقت الحركة
        /// </summary>
        [Required(ErrorMessage = "تاريخ الحركة مطلوب")]
        public DateTime MovementDate { get; set; }

        /// <summary>
        /// نوع الحركة (وارد، صادر، تسوية، إلخ)
        /// </summary>
        [Required(ErrorMessage = "نوع الحركة مطلوب")]
        [StringLength(20, ErrorMessage = "نوع الحركة لا يجب أن يتجاوز 20 حرف")]
        public string MovementType { get; set; }

        /// <summary>
        /// الكمية (موجبة للوارد، سالبة للصادر)
        /// </summary>
        [Required(ErrorMessage = "الكمية مطلوبة")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// وحدة القياس
        /// </summary>
        [StringLength(20, ErrorMessage = "وحدة القياس لا يجب أن تتجاوز 20 حرف")]
        public string Unit { get; set; } = "قطعة";

        /// <summary>
        /// الكمية قبل الحركة
        /// </summary>
        public decimal StockBefore { get; set; }

        /// <summary>
        /// الكمية بعد الحركة
        /// </summary>
        public decimal StockAfter { get; set; }

        #endregion

        #region التكلفة والقيمة

        /// <summary>
        /// سعر التكلفة للوحدة وقت الحركة
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "سعر التكلفة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal UnitCost { get; set; }

        /// <summary>
        /// إجمالي قيمة الحركة
        /// </summary>
        public decimal TotalValue => Math.Abs(Quantity) * UnitCost;

        /// <summary>
        /// العملة
        /// </summary>
        [StringLength(10, ErrorMessage = "العملة لا يجب أن تتجاوز 10 أحرف")]
        public string Currency { get; set; } = "ريال";

        #endregion

        #region مصدر الحركة

        /// <summary>
        /// سبب الحركة
        /// </summary>
        [Required(ErrorMessage = "سبب الحركة مطلوب")]
        [StringLength(200, ErrorMessage = "سبب الحركة لا يجب أن يتجاوز 200 حرف")]
        public string Reason { get; set; }

        /// <summary>
        /// رقم المستند المرتبط (فاتورة، إذن استلام، إلخ)
        /// </summary>
        [StringLength(50, ErrorMessage = "رقم المستند لا يجب أن يتجاوز 50 حرف")]
        public string DocumentNumber { get; set; }

        /// <summary>
        /// نوع المستند
        /// </summary>
        [StringLength(30, ErrorMessage = "نوع المستند لا يجب أن يتجاوز 30 حرف")]
        public string DocumentType { get; set; }

        /// <summary>
        /// رقم المورد (للحركات الواردة)
        /// </summary>
        public int? SupplierID { get; set; }

        /// <summary>
        /// اسم المورد (للعرض)
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم المورد لا يجب أن يتجاوز 100 حرف")]
        public string SupplierName { get; set; }

        /// <summary>
        /// رقم العميل (للحركات الصادرة)
        /// </summary>
        public int? CustomerID { get; set; }

        /// <summary>
        /// اسم العميل (للعرض)
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم العميل لا يجب أن يتجاوز 100 حرف")]
        public string CustomerName { get; set; }

        #endregion

        #region معلومات إضافية

        /// <summary>
        /// رقم الدفعة (للمنتجات ذات الدفعات)
        /// </summary>
        [StringLength(50, ErrorMessage = "رقم الدفعة لا يجب أن يتجاوز 50 حرف")]
        public string BatchNumber { get; set; }

        /// <summary>
        /// تاريخ انتهاء الصلاحية (للمنتجات ذات الصلاحية)
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// رقم المخزن أو الموقع
        /// </summary>
        [StringLength(50, ErrorMessage = "رقم المخزن لا يجب أن يتجاوز 50 حرف")]
        public string WarehouseLocation { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 500 حرف")]
        public string Notes { get; set; }

        #endregion

        #region حالة الحركة

        /// <summary>
        /// حالة الحركة (معلقة، مؤكدة، ملغاة)
        /// </summary>
        [StringLength(20, ErrorMessage = "حالة الحركة لا يجب أن تتجاوز 20 حرف")]
        public string Status { get; set; } = MovementStatus.Confirmed;

        /// <summary>
        /// هل الحركة مؤكدة
        /// </summary>
        public bool IsConfirmed => Status == MovementStatus.Confirmed;

        /// <summary>
        /// هل الحركة ملغاة
        /// </summary>
        public bool IsCancelled => Status == MovementStatus.Cancelled;

        /// <summary>
        /// تاريخ التأكيد
        /// </summary>
        public DateTime? ConfirmedDate { get; set; }

        /// <summary>
        /// من أكد الحركة
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المؤكد لا يجب أن يتجاوز 50 حرف")]
        public string ConfirmedBy { get; set; }

        #endregion

        #region معلومات النظام

        /// <summary>
        /// منشئ السجل
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المنشئ لا يجب أن يتجاوز 50 حرف")]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ إنشاء السجل
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// آخر من عدل السجل
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المعدل لا يجب أن يتجاوز 50 حرف")]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime ModifiedDate { get; set; }

        #endregion

        #region القوائم المرتبطة

        /// <summary>
        /// المنتج المرتبط
        /// </summary>
        public Product Product { get; set; }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ حركة المخزون مع التهيئة الافتراضية
        /// </summary>
        public StockMovement()
        {
            // تهيئة التواريخ
            MovementDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;

            // تهيئة القيم الافتراضية
            Unit = "قطعة";
            Currency = "ريال";
            Status = MovementStatus.Confirmed;
            UnitCost = 0;
            StockBefore = 0;
            StockAfter = 0;
        }

        #endregion

        #region العمليات والتحقق

        /// <summary>
        /// التحقق من صحة بيانات الحركة
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return ProductID > 0 &&
                   !string.IsNullOrWhiteSpace(MovementType) &&
                   Quantity != 0 &&
                   !string.IsNullOrWhiteSpace(Reason);
        }

        /// <summary>
        /// التحقق من كون الحركة واردة
        /// </summary>
        /// <returns>true إذا كانت حركة واردة</returns>
        public bool IsInbound()
        {
            return MovementType == StockMovementTypes.StockIn ||
                   MovementType == StockMovementTypes.Return ||
                   (MovementType == StockMovementTypes.Adjustment && Quantity > 0);
        }

        /// <summary>
        /// التحقق من كون الحركة صادرة
        /// </summary>
        /// <returns>true إذا كانت حركة صادرة</returns>
        public bool IsOutbound()
        {
            return MovementType == StockMovementTypes.StockOut ||
                   MovementType == StockMovementTypes.Damage ||
                   MovementType == StockMovementTypes.Loss ||
                   (MovementType == StockMovementTypes.Adjustment && Quantity < 0);
        }

        /// <summary>
        /// تأكيد الحركة
        /// </summary>
        /// <param name="confirmedBy">من أكد الحركة</param>
        public void Confirm(string confirmedBy)
        {
            if (Status == MovementStatus.Pending)
            {
                Status = MovementStatus.Confirmed;
                ConfirmedDate = DateTime.Now;
                ConfirmedBy = confirmedBy;
                ModifiedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// إلغاء الحركة
        /// </summary>
        /// <param name="cancelledBy">من ألغى الحركة</param>
        public void Cancel(string cancelledBy)
        {
            if (Status != MovementStatus.Cancelled)
            {
                Status = MovementStatus.Cancelled;
                ModifiedBy = cancelledBy;
                ModifiedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// حساب التأثير على المخزون
        /// </summary>
        /// <returns>التأثير على المخزون (موجب للزيادة، سالب للنقصان)</returns>
        public decimal GetStockImpact()
        {
            if (!IsConfirmed)
                return 0;

            return IsInbound() ? Math.Abs(Quantity) : -Math.Abs(Quantity);
        }

        /// <summary>
        /// إنشاء رقم مرجعي تلقائي
        /// </summary>
        public void GenerateReferenceNumber()
        {
            var prefix = MovementType switch
            {
                StockMovementTypes.StockIn => "IN",
                StockMovementTypes.StockOut => "OUT",
                StockMovementTypes.Adjustment => "ADJ",
                StockMovementTypes.Transfer => "TRF",
                StockMovementTypes.Return => "RET",
                StockMovementTypes.Damage => "DMG",
                StockMovementTypes.Loss => "LOSS",
                _ => "MOV"
            };

            ReferenceNumber = $"{prefix}-{DateTime.Now:yyyyMMdd}-{DateTime.Now.Ticks.ToString().Substring(10)}";
        }

        #endregion

        #region عمليات النسخ والتحويل

        /// <summary>
        /// تحويل الحركة إلى نص وصفي
        /// </summary>
        /// <returns>وصف نصي للحركة</returns>
        public override string ToString()
        {
            var direction = IsInbound() ? "وارد" : "صادر";
            return $"{ReferenceNumber} - {ProductCode} - {direction} {Math.Abs(Quantity)} {Unit}";
        }

        #endregion
    }

    #region الثوابت والتعدادات

    /// <summary>
    /// أنواع حركات المخزون
    /// </summary>
    public static class StockMovementTypes
    {
        public const string StockIn = "وارد";
        public const string StockOut = "صادر";
        public const string Adjustment = "تسوية";
        public const string Transfer = "تحويل";
        public const string Return = "مرتجع";
        public const string Damage = "تالف";
        public const string Loss = "فقدان";
    }

    /// <summary>
    /// حالات حركة المخزون
    /// </summary>
    public static class MovementStatus
    {
        public const string Pending = "معلقة";
        public const string Confirmed = "مؤكدة";
        public const string Cancelled = "ملغاة";
    }

    /// <summary>
    /// أنواع المستندات
    /// </summary>
    public static class DocumentTypes
    {
        public const string PurchaseInvoice = "فاتورة شراء";
        public const string SalesInvoice = "فاتورة بيع";
        public const string ReceiptVoucher = "إذن استلام";
        public const string IssueVoucher = "إذن صرف";
        public const string AdjustmentVoucher = "إذن تسوية";
        public const string TransferVoucher = "إذن تحويل";
        public const string ReturnVoucher = "إذن مرتجع";
    }

    #endregion
}
