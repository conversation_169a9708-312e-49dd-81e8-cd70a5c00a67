using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AredooPOS.Database
{
    /// <summary>
    /// مدير قاعدة البيانات المحلية للعمل أوفلاين
    /// يستخدم SQLite لتخزين البيانات محلياً مع إمكانية المزامنة
    /// </summary>
    public class OfflineDatabase
    {
        #region المتغيرات والخصائص

        private readonly string _connectionString;
        private readonly string _databasePath;
        private readonly ILogger<OfflineDatabase> _logger;
        private readonly object _lockObject = new object();

        /// <summary>
        /// مسار قاعدة البيانات المحلية
        /// </summary>
        public string DatabasePath => _databasePath;

        /// <summary>
        /// حالة الاتصال بقاعدة البيانات
        /// </summary>
        public bool IsConnected { get; private set; }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ مدير قاعدة البيانات المحلية
        /// </summary>
        /// <param name="databasePath">مسار قاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public OfflineDatabase(string databasePath = null, ILogger<OfflineDatabase> logger = null)
        {
            _databasePath = databasePath ?? Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "AredooPOS", "offline.db");

            _connectionString = $"Data Source={_databasePath};Version=3;";
            _logger = logger;

            // إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
            var directory = Path.GetDirectoryName(_databasePath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            InitializeDatabase();
        }

        /// <summary>
        /// تهيئة قاعدة البيانات المحلية
        /// </summary>
        private void InitializeDatabase()
        {
            try
            {
                lock (_lockObject)
                {
                    if (!File.Exists(_databasePath))
                    {
                        SQLiteConnection.CreateFile(_databasePath);
                        _logger?.LogInformation($"تم إنشاء قاعدة البيانات المحلية: {_databasePath}");
                    }

                    CreateTables();
                    IsConnected = true;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تهيئة قاعدة البيانات المحلية");
                IsConnected = false;
                throw;
            }
        }

        /// <summary>
        /// إنشاء الجداول المطلوبة
        /// </summary>
        private void CreateTables()
        {
            var createTableQueries = new[]
            {
                // جدول المنتجات المحلي
                @"CREATE TABLE IF NOT EXISTS Products (
                    ProductID INTEGER PRIMARY KEY,
                    ProductCode TEXT NOT NULL UNIQUE,
                    ProductName TEXT NOT NULL,
                    Barcode TEXT,
                    CategoryID INTEGER,
                    CostPrice REAL DEFAULT 0,
                    SalePrice REAL DEFAULT 0,
                    CurrentStock REAL DEFAULT 0,
                    MinimumStock REAL DEFAULT 0,
                    IsActive INTEGER DEFAULT 1,
                    LastModified TEXT DEFAULT CURRENT_TIMESTAMP,
                    IsSynced INTEGER DEFAULT 0,
                    SyncAction TEXT DEFAULT 'NONE'
                )",

                // جدول العملاء المحلي
                @"CREATE TABLE IF NOT EXISTS Customers (
                    CustomerID INTEGER PRIMARY KEY,
                    CustomerCode TEXT NOT NULL UNIQUE,
                    CustomerName TEXT NOT NULL,
                    Phone TEXT,
                    Email TEXT,
                    Address TEXT,
                    CreditLimit REAL DEFAULT 0,
                    CurrentBalance REAL DEFAULT 0,
                    IsActive INTEGER DEFAULT 1,
                    LastModified TEXT DEFAULT CURRENT_TIMESTAMP,
                    IsSynced INTEGER DEFAULT 0,
                    SyncAction TEXT DEFAULT 'NONE'
                )",

                // جدول الفواتير المحلية
                @"CREATE TABLE IF NOT EXISTS Invoices (
                    InvoiceID INTEGER PRIMARY KEY,
                    InvoiceNumber TEXT NOT NULL UNIQUE,
                    CustomerID INTEGER,
                    InvoiceDate TEXT NOT NULL,
                    TotalAmount REAL NOT NULL,
                    TaxAmount REAL DEFAULT 0,
                    DiscountAmount REAL DEFAULT 0,
                    NetAmount REAL NOT NULL,
                    PaymentMethod TEXT,
                    PaymentStatus TEXT DEFAULT 'Pending',
                    Notes TEXT,
                    UserID INTEGER,
                    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
                    LastModified TEXT DEFAULT CURRENT_TIMESTAMP,
                    IsSynced INTEGER DEFAULT 0,
                    SyncAction TEXT DEFAULT 'INSERT'
                )",

                // جدول تفاصيل الفواتير
                @"CREATE TABLE IF NOT EXISTS InvoiceDetails (
                    DetailID INTEGER PRIMARY KEY,
                    InvoiceID INTEGER NOT NULL,
                    ProductID INTEGER NOT NULL,
                    Quantity REAL NOT NULL,
                    UnitPrice REAL NOT NULL,
                    TotalPrice REAL NOT NULL,
                    DiscountPercentage REAL DEFAULT 0,
                    TaxPercentage REAL DEFAULT 0,
                    LastModified TEXT DEFAULT CURRENT_TIMESTAMP,
                    IsSynced INTEGER DEFAULT 0,
                    SyncAction TEXT DEFAULT 'INSERT',
                    FOREIGN KEY (InvoiceID) REFERENCES Invoices(InvoiceID)
                )",

                // جدول تتبع التغييرات
                @"CREATE TABLE IF NOT EXISTS ChangeLog (
                    ChangeID INTEGER PRIMARY KEY AUTOINCREMENT,
                    TableName TEXT NOT NULL,
                    RecordID TEXT NOT NULL,
                    OperationType TEXT NOT NULL,
                    OldData TEXT,
                    NewData TEXT,
                    ChangeDate TEXT DEFAULT CURRENT_TIMESTAMP,
                    UserID INTEGER,
                    IsSynced INTEGER DEFAULT 0
                )",

                // جدول إعدادات المزامنة
                @"CREATE TABLE IF NOT EXISTS SyncSettings (
                    SettingKey TEXT PRIMARY KEY,
                    SettingValue TEXT,
                    LastUpdated TEXT DEFAULT CURRENT_TIMESTAMP
                )",

                // جدول حالة الاتصال
                @"CREATE TABLE IF NOT EXISTS ConnectionStatus (
                    StatusID INTEGER PRIMARY KEY,
                    IsOnline INTEGER DEFAULT 0,
                    LastOnlineCheck TEXT DEFAULT CURRENT_TIMESTAMP,
                    LastSyncDate TEXT,
                    PendingChanges INTEGER DEFAULT 0
                )"
            };

            using var connection = new SQLiteConnection(_connectionString);
            connection.Open();

            foreach (var query in createTableQueries)
            {
                using var command = new SQLiteCommand(query, connection);
                command.ExecuteNonQuery();
            }

            // إنشاء الفهارس
            CreateIndexes(connection);

            _logger?.LogInformation("تم إنشاء جداول قاعدة البيانات المحلية بنجاح");
        }

        /// <summary>
        /// إنشاء الفهارس لتحسين الأداء
        /// </summary>
        private void CreateIndexes(SQLiteConnection connection)
        {
            var indexQueries = new[]
            {
                "CREATE INDEX IF NOT EXISTS IX_Products_ProductCode ON Products(ProductCode)",
                "CREATE INDEX IF NOT EXISTS IX_Products_Barcode ON Products(Barcode)",
                "CREATE INDEX IF NOT EXISTS IX_Products_IsSynced ON Products(IsSynced)",
                "CREATE INDEX IF NOT EXISTS IX_Customers_CustomerCode ON Customers(CustomerCode)",
                "CREATE INDEX IF NOT EXISTS IX_Customers_IsSynced ON Customers(IsSynced)",
                "CREATE INDEX IF NOT EXISTS IX_Invoices_InvoiceNumber ON Invoices(InvoiceNumber)",
                "CREATE INDEX IF NOT EXISTS IX_Invoices_InvoiceDate ON Invoices(InvoiceDate)",
                "CREATE INDEX IF NOT EXISTS IX_Invoices_IsSynced ON Invoices(IsSynced)",
                "CREATE INDEX IF NOT EXISTS IX_InvoiceDetails_InvoiceID ON InvoiceDetails(InvoiceID)",
                "CREATE INDEX IF NOT EXISTS IX_InvoiceDetails_ProductID ON InvoiceDetails(ProductID)",
                "CREATE INDEX IF NOT EXISTS IX_ChangeLog_TableName ON ChangeLog(TableName)",
                "CREATE INDEX IF NOT EXISTS IX_ChangeLog_IsSynced ON ChangeLog(IsSynced)"
            };

            foreach (var query in indexQueries)
            {
                using var command = new SQLiteCommand(query, connection);
                command.ExecuteNonQuery();
            }
        }

        #endregion

        #region عمليات البيانات الأساسية

        /// <summary>
        /// تنفيذ استعلام وإرجاع النتائج
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>جدول النتائج</returns>
        public DataTable ExecuteQuery(string query, Dictionary<string, object> parameters = null)
        {
            try
            {
                lock (_lockObject)
                {
                    using var connection = new SQLiteConnection(_connectionString);
                    connection.Open();

                    using var command = new SQLiteCommand(query, connection);
                    
                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                        }
                    }

                    using var adapter = new SQLiteDataAdapter(command);
                    var dataTable = new DataTable();
                    adapter.Fill(dataTable);

                    return dataTable;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تنفيذ الاستعلام: {query}");
                throw;
            }
        }

        /// <summary>
        /// تنفيذ أمر بدون إرجاع نتائج
        /// </summary>
        /// <param name="query">الأمر</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public int ExecuteNonQuery(string query, Dictionary<string, object> parameters = null)
        {
            try
            {
                lock (_lockObject)
                {
                    using var connection = new SQLiteConnection(_connectionString);
                    connection.Open();

                    using var command = new SQLiteCommand(query, connection);
                    
                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                        }
                    }

                    var result = command.ExecuteNonQuery();
                    
                    // تسجيل التغيير في سجل التغييرات
                    LogChange(query, parameters);
                    
                    return result;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تنفيذ الأمر: {query}");
                throw;
            }
        }

        /// <summary>
        /// تنفيذ أمر وإرجاع قيمة واحدة
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>القيمة المرجعة</returns>
        public object ExecuteScalar(string query, Dictionary<string, object> parameters = null)
        {
            try
            {
                lock (_lockObject)
                {
                    using var connection = new SQLiteConnection(_connectionString);
                    connection.Open();

                    using var command = new SQLiteCommand(query, connection);
                    
                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                        }
                    }

                    return command.ExecuteScalar();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تنفيذ الاستعلام: {query}");
                throw;
            }
        }

        #endregion

        #region إدارة التغييرات والمزامنة

        /// <summary>
        /// تسجيل التغيير في سجل التغييرات
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        private void LogChange(string query, Dictionary<string, object> parameters)
        {
            try
            {
                // تحديد نوع العملية من الاستعلام
                var operationType = "UNKNOWN";
                var tableName = "UNKNOWN";

                if (query.TrimStart().StartsWith("INSERT", StringComparison.OrdinalIgnoreCase))
                {
                    operationType = "INSERT";
                    tableName = ExtractTableNameFromInsert(query);
                }
                else if (query.TrimStart().StartsWith("UPDATE", StringComparison.OrdinalIgnoreCase))
                {
                    operationType = "UPDATE";
                    tableName = ExtractTableNameFromUpdate(query);
                }
                else if (query.TrimStart().StartsWith("DELETE", StringComparison.OrdinalIgnoreCase))
                {
                    operationType = "DELETE";
                    tableName = ExtractTableNameFromDelete(query);
                }

                if (tableName != "UNKNOWN" && tableName != "ChangeLog")
                {
                    var changeLogQuery = @"
                        INSERT INTO ChangeLog (TableName, RecordID, OperationType, NewData, UserID)
                        VALUES (@TableName, @RecordID, @OperationType, @NewData, @UserID)";

                    var changeParams = new Dictionary<string, object>
                    {
                        { "@TableName", tableName },
                        { "@RecordID", GetRecordIdFromParameters(parameters) },
                        { "@OperationType", operationType },
                        { "@NewData", JsonConvert.SerializeObject(parameters) },
                        { "@UserID", GetCurrentUserId() }
                    };

                    using var connection = new SQLiteConnection(_connectionString);
                    connection.Open();
                    using var command = new SQLiteCommand(changeLogQuery, connection);
                    
                    foreach (var param in changeParams)
                    {
                        command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                    }
                    
                    command.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطأ في تسجيل التغيير في سجل التغييرات");
            }
        }

        /// <summary>
        /// استخراج اسم الجدول من استعلام INSERT
        /// </summary>
        private string ExtractTableNameFromInsert(string query)
        {
            try
            {
                var parts = query.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                var intoIndex = Array.FindIndex(parts, p => p.Equals("INTO", StringComparison.OrdinalIgnoreCase));
                if (intoIndex >= 0 && intoIndex + 1 < parts.Length)
                {
                    return parts[intoIndex + 1].Trim('(', ')', '[', ']');
                }
            }
            catch { }
            return "UNKNOWN";
        }

        /// <summary>
        /// استخراج اسم الجدول من استعلام UPDATE
        /// </summary>
        private string ExtractTableNameFromUpdate(string query)
        {
            try
            {
                var parts = query.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length > 1)
                {
                    return parts[1].Trim('(', ')', '[', ']');
                }
            }
            catch { }
            return "UNKNOWN";
        }

        /// <summary>
        /// استخراج اسم الجدول من استعلام DELETE
        /// </summary>
        private string ExtractTableNameFromDelete(string query)
        {
            try
            {
                var parts = query.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                var fromIndex = Array.FindIndex(parts, p => p.Equals("FROM", StringComparison.OrdinalIgnoreCase));
                if (fromIndex >= 0 && fromIndex + 1 < parts.Length)
                {
                    return parts[fromIndex + 1].Trim('(', ')', '[', ']');
                }
            }
            catch { }
            return "UNKNOWN";
        }

        /// <summary>
        /// الحصول على معرف السجل من المعاملات
        /// </summary>
        private string GetRecordIdFromParameters(Dictionary<string, object> parameters)
        {
            if (parameters == null) return "0";

            // البحث عن معرف السجل في المعاملات
            foreach (var param in parameters)
            {
                if (param.Key.EndsWith("ID", StringComparison.OrdinalIgnoreCase))
                {
                    return param.Value?.ToString() ?? "0";
                }
            }

            return "0";
        }

        /// <summary>
        /// الحصول على معرف المستخدم الحالي
        /// </summary>
        private int GetCurrentUserId()
        {
            // يمكن تحسين هذا للحصول على المستخدم الحالي من الجلسة
            return 1;
        }

        /// <summary>
        /// الحصول على التغييرات غير المتزامنة
        /// </summary>
        /// <returns>قائمة التغييرات</returns>
        public List<ChangeLogEntry> GetUnsyncedChanges()
        {
            try
            {
                var query = @"
                    SELECT ChangeID, TableName, RecordID, OperationType, OldData, NewData, ChangeDate, UserID
                    FROM ChangeLog 
                    WHERE IsSynced = 0 
                    ORDER BY ChangeDate";

                var dataTable = ExecuteQuery(query);
                var changes = new List<ChangeLogEntry>();

                foreach (DataRow row in dataTable.Rows)
                {
                    changes.Add(new ChangeLogEntry
                    {
                        ChangeID = Convert.ToInt64(row["ChangeID"]),
                        TableName = row["TableName"].ToString(),
                        RecordID = row["RecordID"].ToString(),
                        OperationType = row["OperationType"].ToString(),
                        OldData = row["OldData"].ToString(),
                        NewData = row["NewData"].ToString(),
                        ChangeDate = DateTime.Parse(row["ChangeDate"].ToString()),
                        UserID = Convert.ToInt32(row["UserID"])
                    });
                }

                return changes;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على التغييرات غير المتزامنة");
                return new List<ChangeLogEntry>();
            }
        }

        /// <summary>
        /// تحديد التغييرات كمتزامنة
        /// </summary>
        /// <param name="changeIds">معرفات التغييرات</param>
        public void MarkChangesAsSynced(IEnumerable<long> changeIds)
        {
            try
            {
                var query = $"UPDATE ChangeLog SET IsSynced = 1 WHERE ChangeID IN ({string.Join(",", changeIds)})";
                ExecuteNonQuery(query);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحديد التغييرات كمتزامنة");
            }
        }

        #endregion

        #region إدارة الاتصال والحالة

        /// <summary>
        /// فحص حالة الاتصال بالإنترنت
        /// </summary>
        /// <returns>true إذا كان متصل</returns>
        public bool CheckInternetConnection()
        {
            try
            {
                using var client = new System.Net.NetworkInformation.Ping();
                var reply = client.Send("*******", 3000);
                var isOnline = reply.Status == System.Net.NetworkInformation.IPStatus.Success;

                // تحديث حالة الاتصال في قاعدة البيانات
                UpdateConnectionStatus(isOnline);

                return isOnline;
            }
            catch
            {
                UpdateConnectionStatus(false);
                return false;
            }
        }

        /// <summary>
        /// تحديث حالة الاتصال
        /// </summary>
        /// <param name="isOnline">حالة الاتصال</param>
        private void UpdateConnectionStatus(bool isOnline)
        {
            try
            {
                var query = @"
                    INSERT OR REPLACE INTO ConnectionStatus (StatusID, IsOnline, LastOnlineCheck, PendingChanges)
                    VALUES (1, @IsOnline, @LastCheck, @PendingChanges)";

                var parameters = new Dictionary<string, object>
                {
                    { "@IsOnline", isOnline ? 1 : 0 },
                    { "@LastCheck", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") },
                    { "@PendingChanges", GetUnsyncedChanges().Count }
                };

                ExecuteNonQuery(query, parameters);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطأ في تحديث حالة الاتصال");
            }
        }

        /// <summary>
        /// الحصول على عدد التغييرات المعلقة
        /// </summary>
        /// <returns>عدد التغييرات</returns>
        public int GetPendingChangesCount()
        {
            try
            {
                var query = "SELECT COUNT(*) FROM ChangeLog WHERE IsSynced = 0";
                var result = ExecuteScalar(query);
                return Convert.ToInt32(result);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على عدد التغييرات المعلقة");
                return 0;
            }
        }

        #endregion

        #region تنظيف الموارد

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            try
            {
                // تنظيف سجل التغييرات القديم (أكثر من 30 يوم)
                var cleanupQuery = @"
                    DELETE FROM ChangeLog 
                    WHERE IsSynced = 1 
                    AND datetime(ChangeDate) < datetime('now', '-30 days')";

                ExecuteNonQuery(cleanupQuery);

                _logger?.LogInformation("تم تنظيف قاعدة البيانات المحلية");
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطأ في تنظيف قاعدة البيانات");
            }
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// نموذج إدخال سجل التغييرات
    /// </summary>
    public class ChangeLogEntry
    {
        public long ChangeID { get; set; }
        public string TableName { get; set; }
        public string RecordID { get; set; }
        public string OperationType { get; set; }
        public string OldData { get; set; }
        public string NewData { get; set; }
        public DateTime ChangeDate { get; set; }
        public int UserID { get; set; }
    }

    #endregion
}
