using System;
using System.Drawing;
using System.Globalization;
using System.Threading;
using System.Windows.Forms;

namespace AredooCashier
{
    /// <summary>
    /// اختبار بسيط للتطبيق المتجاوب
    /// </summary>
    public partial class SimpleResponsiveTest : Form
    {
        public SimpleResponsiveTest()
        {
            InitializeComponent();
            SetupArabicCulture();
        }

        private void InitializeComponent()
        {
            // إعدادات النموذج الأساسية
            Text = "اختبار أريدو الكاشير المتجاوب";
            Size = new Size(800, 600);
            StartPosition = FormStartPosition.CenterScreen;
            BackColor = Color.FromArgb(248, 249, 250);
            Font = new Font("Segoe UI", 12);
            RightToLeft = RightToLeft.Yes;

            // إنشاء عناصر الاختبار
            CreateTestControls();
        }

        private void CreateTestControls()
        {
            // عنوان التطبيق
            var titleLabel = new Label
            {
                Text = "🏪 أريدو الكاشير المتجاوب - اختبار",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 120, 215),
                Size = new Size(Width - 40, 50),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            // معلومات الشاشة
            var screenInfo = Screen.PrimaryScreen.Bounds;
            var infoLabel = new Label
            {
                Text = $"📊 معلومات الشاشة:\n" +
                       $"🖥️ الدقة: {screenInfo.Width}x{screenInfo.Height}\n" +
                       $"📏 DPI: {GetDpiInfo()}\n" +
                       $"🎯 نوع الشاشة: {GetScreenType(screenInfo.Width, screenInfo.Height)}",
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.FromArgb(96, 94, 92),
                Size = new Size(Width - 40, 120),
                Location = new Point(20, 80),
                RightToLeft = RightToLeft.Yes
            };

            // زر اختبار التطبيق الكامل
            var testButton = new Button
            {
                Text = "🚀 تشغيل التطبيق الكامل",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                Size = new Size(300, 50),
                Location = new Point((Width - 300) / 2, 220),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes
            };
            testButton.FlatAppearance.BorderSize = 0;
            testButton.Click += TestButton_Click;

            // زر اختبار المكونات
            var componentsButton = new Button
            {
                Text = "🧪 اختبار المكونات",
                Font = new Font("Segoe UI", 12),
                Size = new Size(200, 40),
                Location = new Point((Width - 200) / 2, 290),
                BackColor = Color.FromArgb(16, 124, 16),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes
            };
            componentsButton.FlatAppearance.BorderSize = 0;
            componentsButton.Click += ComponentsButton_Click;

            // رسالة الحالة
            var statusLabel = new Label
            {
                Text = "✅ النظام جاهز للاختبار",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(16, 124, 16),
                Size = new Size(Width - 40, 30),
                Location = new Point(20, Height - 80),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            // إضافة العناصر للنموذج
            Controls.AddRange(new Control[] { titleLabel, infoLabel, testButton, componentsButton, statusLabel });
        }

        private void SetupArabicCulture()
        {
            try
            {
                var arabicCulture = new CultureInfo("ar-SA");
                Thread.CurrentThread.CurrentCulture = arabicCulture;
                Thread.CurrentThread.CurrentUICulture = arabicCulture;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"تحذير: فشل في تعيين الثقافة العربية - {ex.Message}");
            }
        }

        private string GetDpiInfo()
        {
            try
            {
                using (var g = Graphics.FromHwnd(this.Handle))
                {
                    return $"{g.DpiX} DPI";
                }
            }
            catch
            {
                return "غير معروف";
            }
        }

        private string GetScreenType(int width, int height)
        {
            return (width, height) switch
            {
                var (w, h) when w >= 3840 && h >= 2160 => "4K Ultra HD",
                var (w, h) when w >= 2560 && h >= 1440 => "QHD/2K",
                var (w, h) when w >= 1920 && h >= 1080 => "Full HD",
                var (w, h) when w >= 1600 && h >= 900 => "HD+",
                var (w, h) when w >= 1366 && h >= 768 => "HD",
                _ => "قياسي"
            };
        }

        private void TestButton_Click(object sender, EventArgs e)
        {
            try
            {
                var fullApp = new ResponsiveAredooCashierApp();
                this.Hide();
                fullApp.FormClosed += (s, args) => this.Show();
                fullApp.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل التطبيق الكامل:\n\n{ex.Message}\n\nتفاصيل:\n{ex.StackTrace}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ComponentsButton_Click(object sender, EventArgs e)
        {
            var message = "🧪 اختبار المكونات:\n\n" +
                         "✅ ResponsiveLayoutSystem - نظام التخطيط المتجاوب\n" +
                         "✅ ResponsiveDesignSystem - نظام التصميم المتكيف\n" +
                         "✅ ResponsiveTopBar - الشريط العلوي المتجاوب\n" +
                         "✅ ResponsiveSidebar - الشريط الجانبي التفاعلي\n" +
                         "✅ ResponsiveDashboard - لوحة المعلومات الذكية\n" +
                         "✅ ResponsiveInvoiceForm - نموذج الفاتورة المتقدم\n" +
                         "✅ ResponsiveCustomersView - إدارة العملاء المتجاوبة\n\n" +
                         "جميع المكونات تم تطويرها وهي جاهزة للاستخدام!";

            MessageBox.Show(message, "اختبار المكونات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            
            // تحديث مواقع العناصر عند تغيير الحجم
            if (Controls.Count > 0)
            {
                foreach (Control control in Controls)
                {
                    if (control is Button button)
                    {
                        button.Location = new Point((Width - button.Width) / 2, button.Location.Y);
                    }
                    else if (control is Label label && label.TextAlign == ContentAlignment.MiddleCenter)
                    {
                        label.Size = new Size(Width - 40, label.Height);
                        label.Location = new Point(20, label.Location.Y);
                    }
                }
            }
        }
    }

    /// <summary>
    /// برنامج الاختبار البسيط
    /// </summary>
    internal static class SimpleTestProgram
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                // تعيين الثقافة العربية
                var arabicCulture = new CultureInfo("ar-SA");
                Thread.CurrentThread.CurrentCulture = arabicCulture;
                Thread.CurrentThread.CurrentUICulture = arabicCulture;

                Application.Run(new SimpleResponsiveTest());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في بدء التشغيل:\n\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
