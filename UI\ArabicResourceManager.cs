using System;
using System.Collections.Generic;
using System.Globalization;
using System.Resources;
using System.Windows.Forms;

namespace AredooPOS.UI
{
    /// <summary>
    /// مدير الموارد العربية
    /// يدير النصوص والرسائل والموارد باللغة العربية
    /// </summary>
    public static class ArabicResourceManager
    {
        #region الموارد النصية

        /// <summary>
        /// النصوص العامة للواجهة
        /// </summary>
        public static readonly Dictionary<string, string> GeneralTexts = new Dictionary<string, string>
        {
            // أزرار عامة
            { "OK", "موافق" },
            { "Cancel", "إلغاء" },
            { "Yes", "نعم" },
            { "No", "لا" },
            { "Save", "حفظ" },
            { "Delete", "حذف" },
            { "Edit", "تعديل" },
            { "Add", "إضافة" },
            { "New", "جديد" },
            { "Search", "بحث" },
            { "Print", "طباعة" },
            { "Export", "تصدير" },
            { "Import", "استيراد" },
            { "Refresh", "تحديث" },
            { "Close", "إغلاق" },
            { "Exit", "خروج" },
            { "Back", "رجوع" },
            { "Next", "التالي" },
            { "Previous", "السابق" },
            { "First", "الأول" },
            { "Last", "الأخير" },
            { "Clear", "مسح" },
            { "Reset", "إعادة تعيين" },
            { "Apply", "تطبيق" },
            { "Browse", "استعراض" },
            { "Select", "اختيار" },
            { "SelectAll", "تحديد الكل" },
            { "Copy", "نسخ" },
            { "Paste", "لصق" },
            { "Cut", "قص" },
            { "Undo", "تراجع" },
            { "Redo", "إعادة" },

            // حالات عامة
            { "Active", "نشط" },
            { "Inactive", "غير نشط" },
            { "Enabled", "مفعل" },
            { "Disabled", "معطل" },
            { "Available", "متاح" },
            { "Unavailable", "غير متاح" },
            { "Online", "متصل" },
            { "Offline", "غير متصل" },
            { "Connected", "متصل" },
            { "Disconnected", "منقطع" },
            { "Loading", "جاري التحميل..." },
            { "Processing", "جاري المعالجة..." },
            { "Saving", "جاري الحفظ..." },
            { "Deleting", "جاري الحذف..." },
            { "Updating", "جاري التحديث..." },
            { "Syncing", "جاري المزامنة..." },

            // أيام الأسبوع
            { "Sunday", "الأحد" },
            { "Monday", "الاثنين" },
            { "Tuesday", "الثلاثاء" },
            { "Wednesday", "الأربعاء" },
            { "Thursday", "الخميس" },
            { "Friday", "الجمعة" },
            { "Saturday", "السبت" },

            // الأشهر
            { "January", "يناير" },
            { "February", "فبراير" },
            { "March", "مارس" },
            { "April", "أبريل" },
            { "May", "مايو" },
            { "June", "يونيو" },
            { "July", "يوليو" },
            { "August", "أغسطس" },
            { "September", "سبتمبر" },
            { "October", "أكتوبر" },
            { "November", "نوفمبر" },
            { "December", "ديسمبر" }
        };

        /// <summary>
        /// نصوص نظام نقاط البيع
        /// </summary>
        public static readonly Dictionary<string, string> POSTexts = new Dictionary<string, string>
        {
            // القوائم الرئيسية
            { "File", "ملف" },
            { "Edit", "تحرير" },
            { "View", "عرض" },
            { "Tools", "أدوات" },
            { "Reports", "تقارير" },
            { "Settings", "إعدادات" },
            { "Help", "مساعدة" },

            // وحدات النظام
            { "Sales", "المبيعات" },
            { "Purchases", "المشتريات" },
            { "Inventory", "المخزون" },
            { "Customers", "العملاء" },
            { "Suppliers", "الموردين" },
            { "Products", "المنتجات" },
            { "Categories", "الفئات" },
            { "Users", "المستخدمين" },
            { "Roles", "الأدوار" },
            { "Permissions", "الصلاحيات" },
            { "Invoices", "الفواتير" },
            { "Receipts", "الإيصالات" },
            { "Payments", "المدفوعات" },
            { "Expenses", "المصروفات" },
            { "CashRegister", "الصندوق" },
            { "Backup", "النسخ الاحتياطي" },
            { "Restore", "الاستعادة" },

            // حقول البيانات
            { "ID", "الرقم" },
            { "Code", "الكود" },
            { "Name", "الاسم" },
            { "Description", "الوصف" },
            { "Price", "السعر" },
            { "Cost", "التكلفة" },
            { "Quantity", "الكمية" },
            { "Total", "الإجمالي" },
            { "Subtotal", "المجموع الفرعي" },
            { "Tax", "الضريبة" },
            { "Discount", "الخصم" },
            { "Date", "التاريخ" },
            { "Time", "الوقت" },
            { "Status", "الحالة" },
            { "Type", "النوع" },
            { "Category", "الفئة" },
            { "Unit", "الوحدة" },
            { "Barcode", "الباركود" },
            { "Phone", "الهاتف" },
            { "Email", "البريد الإلكتروني" },
            { "Address", "العنوان" },
            { "City", "المدينة" },
            { "Country", "البلد" },
            { "Notes", "ملاحظات" },

            // حالات الفواتير والمدفوعات
            { "Pending", "معلق" },
            { "Paid", "مدفوع" },
            { "Unpaid", "غير مدفوع" },
            { "PartiallyPaid", "مدفوع جزئياً" },
            { "Cancelled", "ملغي" },
            { "Completed", "مكتمل" },
            { "Draft", "مسودة" },
            { "Approved", "معتمد" },
            { "Rejected", "مرفوض" },

            // طرق الدفع
            { "Cash", "نقداً" },
            { "Card", "بطاقة" },
            { "Transfer", "تحويل" },
            { "Check", "شيك" },
            { "Credit", "آجل" },

            // أنواع العملاء
            { "Regular", "عادي" },
            { "Wholesale", "جملة" },
            { "VIP", "مميز" },
            { "Corporate", "شركة" }
        };

        /// <summary>
        /// رسائل النظام
        /// </summary>
        public static readonly Dictionary<string, string> SystemMessages = new Dictionary<string, string>
        {
            // رسائل النجاح
            { "SaveSuccess", "تم الحفظ بنجاح" },
            { "DeleteSuccess", "تم الحذف بنجاح" },
            { "UpdateSuccess", "تم التحديث بنجاح" },
            { "AddSuccess", "تم الإضافة بنجاح" },
            { "ImportSuccess", "تم الاستيراد بنجاح" },
            { "ExportSuccess", "تم التصدير بنجاح" },
            { "BackupSuccess", "تم إنشاء النسخة الاحتياطية بنجاح" },
            { "RestoreSuccess", "تم استعادة البيانات بنجاح" },
            { "SyncSuccess", "تم المزامنة بنجاح" },
            { "LoginSuccess", "تم تسجيل الدخول بنجاح" },
            { "LogoutSuccess", "تم تسجيل الخروج بنجاح" },

            // رسائل الخطأ
            { "SaveError", "خطأ في الحفظ" },
            { "DeleteError", "خطأ في الحذف" },
            { "UpdateError", "خطأ في التحديث" },
            { "AddError", "خطأ في الإضافة" },
            { "ImportError", "خطأ في الاستيراد" },
            { "ExportError", "خطأ في التصدير" },
            { "BackupError", "خطأ في إنشاء النسخة الاحتياطية" },
            { "RestoreError", "خطأ في استعادة البيانات" },
            { "SyncError", "خطأ في المزامنة" },
            { "LoginError", "خطأ في تسجيل الدخول" },
            { "ConnectionError", "خطأ في الاتصال" },
            { "DatabaseError", "خطأ في قاعدة البيانات" },
            { "ValidationError", "خطأ في التحقق من البيانات" },
            { "PermissionError", "ليس لديك صلاحية للقيام بهذا الإجراء" },
            { "NotFoundError", "العنصر المطلوب غير موجود" },
            { "DuplicateError", "هذا العنصر موجود بالفعل" },

            // رسائل التأكيد
            { "ConfirmDelete", "هل أنت متأكد من حذف هذا العنصر؟" },
            { "ConfirmSave", "هل تريد حفظ التغييرات؟" },
            { "ConfirmExit", "هل تريد الخروج من البرنامج؟" },
            { "ConfirmCancel", "هل تريد إلغاء العملية؟" },
            { "ConfirmRestore", "هل تريد استعادة البيانات؟ سيتم استبدال البيانات الحالية." },
            { "ConfirmReset", "هل تريد إعادة تعيين الإعدادات إلى القيم الافتراضية؟" },

            // رسائل التحذير
            { "UnsavedChanges", "يوجد تغييرات غير محفوظة" },
            { "LowStock", "تحذير: المخزون منخفض" },
            { "ExpiredProduct", "تحذير: منتج منتهي الصلاحية" },
            { "OverCreditLimit", "تحذير: تجاوز الحد الائتماني" },
            { "InvalidData", "البيانات المدخلة غير صحيحة" },
            { "RequiredField", "هذا الحقل مطلوب" },
            { "InvalidFormat", "تنسيق البيانات غير صحيح" },
            { "OutOfStock", "المنتج غير متوفر في المخزون" },

            // رسائل المعلومات
            { "NoDataFound", "لا توجد بيانات" },
            { "SearchCompleted", "اكتمل البحث" },
            { "ProcessingComplete", "اكتملت المعالجة" },
            { "SystemReady", "النظام جاهز" },
            { "WelcomeMessage", "مرحباً بك في نظام أريدو POS" },
            { "GoodMorning", "صباح الخير" },
            { "GoodAfternoon", "مساء الخير" },
            { "GoodEvening", "مساء الخير" }
        };

        /// <summary>
        /// تسميات التقارير
        /// </summary>
        public static readonly Dictionary<string, string> ReportLabels = new Dictionary<string, string>
        {
            { "SalesReport", "تقرير المبيعات" },
            { "PurchaseReport", "تقرير المشتريات" },
            { "InventoryReport", "تقرير المخزون" },
            { "CustomerReport", "تقرير العملاء" },
            { "SupplierReport", "تقرير الموردين" },
            { "ProductReport", "تقرير المنتجات" },
            { "PaymentReport", "تقرير المدفوعات" },
            { "ExpenseReport", "تقرير المصروفات" },
            { "ProfitLossReport", "تقرير الأرباح والخسائر" },
            { "CashFlowReport", "تقرير التدفق النقدي" },
            { "TaxReport", "تقرير الضرائب" },
            { "UserActivityReport", "تقرير نشاط المستخدمين" },

            // فترات التقارير
            { "Daily", "يومي" },
            { "Weekly", "أسبوعي" },
            { "Monthly", "شهري" },
            { "Quarterly", "ربع سنوي" },
            { "Yearly", "سنوي" },
            { "Custom", "مخصص" },
            { "FromDate", "من تاريخ" },
            { "ToDate", "إلى تاريخ" },
            { "Period", "الفترة" },
            { "Summary", "ملخص" },
            { "Details", "تفاصيل" }
        };

        #endregion

        #region دوال الحصول على النصوص

        /// <summary>
        /// الحصول على نص عام
        /// </summary>
        /// <param name="key">مفتاح النص</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>النص المترجم</returns>
        public static string GetText(string key, string defaultValue = null)
        {
            if (GeneralTexts.ContainsKey(key))
                return GeneralTexts[key];

            if (POSTexts.ContainsKey(key))
                return POSTexts[key];

            return defaultValue ?? key;
        }

        /// <summary>
        /// الحصول على رسالة نظام
        /// </summary>
        /// <param name="key">مفتاح الرسالة</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>الرسالة المترجمة</returns>
        public static string GetMessage(string key, string defaultValue = null)
        {
            if (SystemMessages.ContainsKey(key))
                return SystemMessages[key];

            return defaultValue ?? key;
        }

        /// <summary>
        /// الحصول على تسمية تقرير
        /// </summary>
        /// <param name="key">مفتاح التسمية</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>التسمية المترجمة</returns>
        public static string GetReportLabel(string key, string defaultValue = null)
        {
            if (ReportLabels.ContainsKey(key))
                return ReportLabels[key];

            return defaultValue ?? key;
        }

        #endregion

        #region تطبيق النصوص على العناصر

        /// <summary>
        /// تطبيق النصوص العربية على النموذج
        /// </summary>
        /// <param name="form">النموذج</param>
        public static void ApplyArabicTexts(Form form)
        {
            if (form == null) return;

            try
            {
                // تطبيق النصوص على النموذج نفسه
                ApplyTextsToControl(form);

                // تطبيق النصوص على جميع العناصر الفرعية
                ApplyTextsToChildren(form);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق النصوص العربية: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق النصوص على عنصر تحكم
        /// </summary>
        /// <param name="control">عنصر التحكم</param>
        private static void ApplyTextsToControl(Control control)
        {
            if (control == null) return;

            try
            {
                // تطبيق النصوص حسب نوع العنصر
                switch (control)
                {
                    case Button button:
                        ApplyButtonTexts(button);
                        break;

                    case Label label:
                        ApplyLabelTexts(label);
                        break;

                    case GroupBox groupBox:
                        ApplyGroupBoxTexts(groupBox);
                        break;

                    case TabPage tabPage:
                        ApplyTabPageTexts(tabPage);
                        break;

                    case ColumnHeader columnHeader:
                        ApplyColumnHeaderTexts(columnHeader);
                        break;

                    case DataGridViewColumn column:
                        ApplyDataGridViewColumnTexts(column);
                        break;

                    case ToolStripItem toolStripItem:
                        ApplyToolStripItemTexts(toolStripItem);
                        break;

                    case Form form:
                        ApplyFormTexts(form);
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق النصوص على العنصر: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق النصوص على الأزرار
        /// </summary>
        private static void ApplyButtonTexts(Button button)
        {
            var text = GetText(button.Name) ?? GetText(button.Text);
            if (!string.IsNullOrEmpty(text))
                button.Text = text;
        }

        /// <summary>
        /// تطبيق النصوص على التسميات
        /// </summary>
        private static void ApplyLabelTexts(Label label)
        {
            var text = GetText(label.Name) ?? GetText(label.Text);
            if (!string.IsNullOrEmpty(text))
                label.Text = text;
        }

        /// <summary>
        /// تطبيق النصوص على مجموعات العناصر
        /// </summary>
        private static void ApplyGroupBoxTexts(GroupBox groupBox)
        {
            var text = GetText(groupBox.Name) ?? GetText(groupBox.Text);
            if (!string.IsNullOrEmpty(text))
                groupBox.Text = text;
        }

        /// <summary>
        /// تطبيق النصوص على صفحات التبويب
        /// </summary>
        private static void ApplyTabPageTexts(TabPage tabPage)
        {
            var text = GetText(tabPage.Name) ?? GetText(tabPage.Text);
            if (!string.IsNullOrEmpty(text))
                tabPage.Text = text;
        }

        /// <summary>
        /// تطبيق النصوص على رؤوس الأعمدة
        /// </summary>
        private static void ApplyColumnHeaderTexts(ColumnHeader columnHeader)
        {
            var text = GetText(columnHeader.Name) ?? GetText(columnHeader.Text);
            if (!string.IsNullOrEmpty(text))
                columnHeader.Text = text;
        }

        /// <summary>
        /// تطبيق النصوص على أعمدة جدول البيانات
        /// </summary>
        private static void ApplyDataGridViewColumnTexts(DataGridViewColumn column)
        {
            var text = GetText(column.Name) ?? GetText(column.HeaderText);
            if (!string.IsNullOrEmpty(text))
                column.HeaderText = text;
        }

        /// <summary>
        /// تطبيق النصوص على عناصر شريط الأدوات
        /// </summary>
        private static void ApplyToolStripItemTexts(ToolStripItem toolStripItem)
        {
            var text = GetText(toolStripItem.Name) ?? GetText(toolStripItem.Text);
            if (!string.IsNullOrEmpty(text))
                toolStripItem.Text = text;
        }

        /// <summary>
        /// تطبيق النصوص على النماذج
        /// </summary>
        private static void ApplyFormTexts(Form form)
        {
            var text = GetText(form.Name) ?? GetText(form.Text);
            if (!string.IsNullOrEmpty(text))
                form.Text = text;
        }

        /// <summary>
        /// تطبيق النصوص على العناصر الفرعية
        /// </summary>
        private static void ApplyTextsToChildren(Control parent)
        {
            foreach (Control child in parent.Controls)
            {
                ApplyTextsToControl(child);
                ApplyTextsToChildren(child);
            }

            // تطبيق النصوص على عناصر القوائم
            if (parent is MenuStrip menuStrip)
            {
                foreach (ToolStripItem item in menuStrip.Items)
                {
                    ApplyTextsToToolStripItem(item);
                }
            }

            if (parent is ToolStrip toolStrip)
            {
                foreach (ToolStripItem item in toolStrip.Items)
                {
                    ApplyTextsToToolStripItem(item);
                }
            }

            if (parent is StatusStrip statusStrip)
            {
                foreach (ToolStripItem item in statusStrip.Items)
                {
                    ApplyTextsToToolStripItem(item);
                }
            }
        }

        /// <summary>
        /// تطبيق النصوص على عناصر شريط الأدوات بشكل متكرر
        /// </summary>
        private static void ApplyTextsToToolStripItem(ToolStripItem item)
        {
            ApplyToolStripItemTexts(item);

            if (item is ToolStripDropDownItem dropDownItem)
            {
                foreach (ToolStripItem subItem in dropDownItem.DropDownItems)
                {
                    ApplyTextsToToolStripItem(subItem);
                }
            }
        }

        #endregion

        #region دوال مساعدة

        /// <summary>
        /// تنسيق رسالة بمعاملات
        /// </summary>
        /// <param name="messageKey">مفتاح الرسالة</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>الرسالة المنسقة</returns>
        public static string FormatMessage(string messageKey, params object[] parameters)
        {
            var message = GetMessage(messageKey, messageKey);
            try
            {
                return string.Format(message, parameters);
            }
            catch
            {
                return message;
            }
        }

        /// <summary>
        /// الحصول على تحية مناسبة للوقت
        /// </summary>
        /// <returns>التحية</returns>
        public static string GetTimeBasedGreeting()
        {
            var hour = DateTime.Now.Hour;

            if (hour < 12)
                return GetMessage("GoodMorning");
            else if (hour < 18)
                return GetMessage("GoodAfternoon");
            else
                return GetMessage("GoodEvening");
        }

        /// <summary>
        /// تحويل التاريخ إلى نص عربي
        /// </summary>
        /// <param name="date">التاريخ</param>
        /// <param name="format">تنسيق التاريخ</param>
        /// <returns>التاريخ بالعربية</returns>
        public static string FormatArabicDate(DateTime date, string format = "dd/MM/yyyy")
        {
            try
            {
                var arabicCulture = new CultureInfo("ar-SA");
                return date.ToString(format, arabicCulture);
            }
            catch
            {
                return date.ToString(format);
            }
        }

        /// <summary>
        /// تحويل الرقم إلى نص عربي
        /// </summary>
        /// <param name="number">الرقم</param>
        /// <param name="format">تنسيق الرقم</param>
        /// <returns>الرقم بالعربية</returns>
        public static string FormatArabicNumber(decimal number, string format = "N2")
        {
            try
            {
                var arabicCulture = new CultureInfo("ar-SA");
                return number.ToString(format, arabicCulture);
            }
            catch
            {
                return number.ToString(format);
            }
        }

        #endregion
    }
}