using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using AridooPOS.Models;

namespace AridooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات للمنتجات
    /// </summary>
    public class ProductDAL
    {
        /// <summary>
        /// الحصول على منتج بالرقم
        /// </summary>
        /// <param name="productId">رقم المنتج</param>
        /// <returns>بيانات المنتج</returns>
        public Product GetProductById(int productId)
        {
            try
            {
                string query = @"
                    SELECT p.*, c.CategoryName 
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
                    WHERE p.ProductID = @ProductID";

                SqlParameter[] parameters = {
                    new SqlParameter("@ProductID", productId)
                };

                DataTable result = DatabaseConnection.ExecuteQuery(query, parameters);
                
                if (result.Rows.Count > 0)
                {
                    return MapDataRowToProduct(result.Rows[0]);
                }
                
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على المنتج: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على منتج بالكود
        /// </summary>
        /// <param name="productCode">كود المنتج</param>
        /// <returns>بيانات المنتج</returns>
        public Product GetProductByCode(string productCode)
        {
            try
            {
                string query = @"
                    SELECT p.*, c.CategoryName 
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
                    WHERE p.ProductCode = @ProductCode";

                SqlParameter[] parameters = {
                    new SqlParameter("@ProductCode", productCode)
                };

                DataTable result = DatabaseConnection.ExecuteQuery(query, parameters);
                
                if (result.Rows.Count > 0)
                {
                    return MapDataRowToProduct(result.Rows[0]);
                }
                
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على المنتج: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على منتج بالباركود
        /// </summary>
        /// <param name="barcode">الباركود</param>
        /// <returns>بيانات المنتج</returns>
        public Product GetProductByBarcode(string barcode)
        {
            try
            {
                string query = @"
                    SELECT p.*, c.CategoryName 
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
                    WHERE p.Barcode = @Barcode";

                SqlParameter[] parameters = {
                    new SqlParameter("@Barcode", barcode)
                };

                DataTable result = DatabaseConnection.ExecuteQuery(query, parameters);
                
                if (result.Rows.Count > 0)
                {
                    return MapDataRowToProduct(result.Rows[0]);
                }
                
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على المنتج: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// البحث عن المنتجات
        /// </summary>
        /// <param name="searchText">نص البحث</param>
        /// <returns>قائمة المنتجات</returns>
        public List<Product> SearchProducts(string searchText)
        {
            try
            {
                string query = @"
                    SELECT p.*, c.CategoryName 
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
                    WHERE p.IsActive = 1 AND (
                        p.ProductName LIKE @SearchText OR 
                        p.ProductCode LIKE @SearchText OR 
                        p.Barcode LIKE @SearchText
                    )
                    ORDER BY p.ProductName";

                SqlParameter[] parameters = {
                    new SqlParameter("@SearchText", $"%{searchText}%")
                };

                DataTable result = DatabaseConnection.ExecuteQuery(query, parameters);
                
                List<Product> products = new List<Product>();
                foreach (DataRow row in result.Rows)
                {
                    products.Add(MapDataRowToProduct(row));
                }
                
                return products;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث عن المنتجات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث منتج
        /// </summary>
        /// <param name="product">بيانات المنتج</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateProduct(Product product)
        {
            try
            {
                string query = @"
                    UPDATE Products SET
                        ProductName = @ProductName,
                        ProductNameEn = @ProductNameEn,
                        CategoryID = @CategoryID,
                        UnitPrice = @UnitPrice,
                        CostPrice = @CostPrice,
                        StockQuantity = @StockQuantity,
                        MinStockLevel = @MinStockLevel,
                        Barcode = @Barcode,
                        TaxRate = @TaxRate,
                        IsActive = @IsActive,
                        ModifiedDate = GETDATE()
                    WHERE ProductID = @ProductID";

                SqlParameter[] parameters = {
                    new SqlParameter("@ProductID", product.ProductID),
                    new SqlParameter("@ProductName", product.ProductName),
                    new SqlParameter("@ProductNameEn", product.ProductNameEn ?? ""),
                    new SqlParameter("@CategoryID", (object)product.CategoryID ?? DBNull.Value),
                    new SqlParameter("@UnitPrice", product.UnitPrice),
                    new SqlParameter("@CostPrice", product.CostPrice),
                    new SqlParameter("@StockQuantity", product.StockQuantity),
                    new SqlParameter("@MinStockLevel", product.MinStockLevel),
                    new SqlParameter("@Barcode", product.Barcode ?? ""),
                    new SqlParameter("@TaxRate", product.TaxRate),
                    new SqlParameter("@IsActive", product.IsActive)
                };

                int rowsAffected = DatabaseConnection.ExecuteNonQuery(query, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث المنتج: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على جميع المنتجات النشطة
        /// </summary>
        /// <returns>قائمة المنتجات</returns>
        public List<Product> GetAllActiveProducts()
        {
            try
            {
                string query = @"
                    SELECT p.*, c.CategoryName 
                    FROM Products p
                    LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
                    WHERE p.IsActive = 1
                    ORDER BY p.ProductName";

                DataTable result = DatabaseConnection.ExecuteQuery(query);
                
                List<Product> products = new List<Product>();
                foreach (DataRow row in result.Rows)
                {
                    products.Add(MapDataRowToProduct(row));
                }
                
                return products;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على المنتجات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحويل صف البيانات إلى كائن منتج
        /// </summary>
        /// <param name="row">صف البيانات</param>
        /// <returns>كائن المنتج</returns>
        private Product MapDataRowToProduct(DataRow row)
        {
            return new Product
            {
                ProductID = Convert.ToInt32(row["ProductID"]),
                ProductCode = row["ProductCode"].ToString(),
                ProductName = row["ProductName"].ToString(),
                ProductNameEn = row["ProductNameEn"].ToString(),
                CategoryID = row["CategoryID"] == DBNull.Value ? (int?)null : Convert.ToInt32(row["CategoryID"]),
                UnitPrice = Convert.ToDecimal(row["UnitPrice"]),
                CostPrice = Convert.ToDecimal(row["CostPrice"]),
                StockQuantity = Convert.ToInt32(row["StockQuantity"]),
                MinStockLevel = Convert.ToInt32(row["MinStockLevel"]),
                Barcode = row["Barcode"].ToString(),
                TaxRate = Convert.ToDecimal(row["TaxRate"]),
                IsActive = Convert.ToBoolean(row["IsActive"]),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                ModifiedDate = Convert.ToDateTime(row["ModifiedDate"]),
                CategoryName = row.Table.Columns.Contains("CategoryName") ? row["CategoryName"].ToString() : ""
            };
        }
    }
}
