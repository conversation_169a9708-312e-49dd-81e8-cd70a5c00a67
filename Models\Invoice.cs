using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AridooPOS.Models
{
    /// <summary>
    /// نموذج الفاتورة
    /// </summary>
    public class Invoice
    {
        public int InvoiceID { get; set; }
        
        [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
        [StringLength(20, ErrorMessage = "رقم الفاتورة لا يجب أن يتجاوز 20 حرف")]
        public string InvoiceNumber { get; set; }
        
        [Required(ErrorMessage = "تاريخ الفاتورة مطلوب")]
        public DateTime InvoiceDate { get; set; }
        
        public int? CustomerID { get; set; }
        
        [StringLength(100, ErrorMessage = "اسم العميل لا يجب أن يتجاوز 100 حرف")]
        public string CustomerName { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "المجموع الفرعي يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal SubTotal { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "مبلغ الخصم يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal DiscountAmount { get; set; }
        
        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public decimal DiscountPercent { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "مبلغ الضريبة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal TaxAmount { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "المبلغ الإجمالي يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal TotalAmount { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "المبلغ المدفوع يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal PaidAmount { get; set; }
        
        public decimal RemainingAmount { get; set; }
        
        [Required(ErrorMessage = "نوع الدفع مطلوب")]
        [StringLength(20, ErrorMessage = "نوع الدفع لا يجب أن يتجاوز 20 حرف")]
        public string PaymentType { get; set; }
        
        [StringLength(20, ErrorMessage = "حالة الفاتورة لا يجب أن تتجاوز 20 حرف")]
        public string InvoiceStatus { get; set; }
        
        [StringLength(500, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 500 حرف")]
        public string Notes { get; set; }
        
        [StringLength(50, ErrorMessage = "اسم الكاشير لا يجب أن يتجاوز 50 حرف")]
        public string CashierName { get; set; }
        
        public bool IsReturned { get; set; }
        
        public DateTime? ReturnedDate { get; set; }
        
        [StringLength(50, ErrorMessage = "اسم المنشئ لا يجب أن يتجاوز 50 حرف")]
        public string CreatedBy { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public DateTime ModifiedDate { get; set; }
        
        // قائمة تفاصيل الفاتورة
        public List<InvoiceDetail> InvoiceDetails { get; set; }
        
        // قائمة المدفوعات
        public List<Payment> Payments { get; set; }
        
        // قائمة الأقساط
        public List<Installment> Installments { get; set; }
        
        public Invoice()
        {
            InvoiceDetails = new List<InvoiceDetail>();
            Payments = new List<Payment>();
            Installments = new List<Installment>();
            InvoiceDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;
            InvoiceStatus = "مكتملة";
            PaymentType = "نقد";
            IsReturned = false;
        }
        
        /// <summary>
        /// حساب المجموع الفرعي
        /// </summary>
        public void CalculateSubTotal()
        {
            SubTotal = 0;
            foreach (var detail in InvoiceDetails)
            {
                SubTotal += detail.LineTotal;
            }
        }
        
        /// <summary>
        /// حساب المبلغ الإجمالي
        /// </summary>
        public void CalculateTotal()
        {
            CalculateSubTotal();
            TotalAmount = SubTotal - DiscountAmount + TaxAmount;
            RemainingAmount = TotalAmount - PaidAmount;
        }
        
        /// <summary>
        /// التحقق من صحة الفاتورة
        /// </summary>
        /// <returns>true إذا كانت الفاتورة صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(InvoiceNumber) &&
                   InvoiceDetails.Count > 0 &&
                   TotalAmount > 0;
        }
    }
    
    /// <summary>
    /// أنواع الدفع
    /// </summary>
    public static class PaymentTypes
    {
        public const string Cash = "نقد";
        public const string Card = "بطاقة";
        public const string Installment = "قسط";
        public const string Mixed = "مختلط";
    }
    
    /// <summary>
    /// حالات الفاتورة
    /// </summary>
    public static class InvoiceStatuses
    {
        public const string Completed = "مكتملة";
        public const string Cancelled = "ملغية";
        public const string Returned = "مرتجعة";
        public const string Pending = "معلقة";
    }
}
