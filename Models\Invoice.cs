using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace AridooPOS.Models
{
    /// <summary>
    /// نموذج الفاتورة الرئيسي
    /// يحتوي على جميع معلومات الفاتورة والعمليات الحسابية
    /// </summary>
    public class Invoice
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم الفاتورة في قاعدة البيانات (مفتاح أساسي)
        /// </summary>
        public int InvoiceID { get; set; }

        /// <summary>
        /// رقم الفاتورة المعروض للمستخدم
        /// </summary>
        [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
        [StringLength(20, ErrorMessage = "رقم الفاتورة لا يجب أن يتجاوز 20 حرف")]
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// نوع الفاتورة (بيع، إرجاع، عرض سعر)
        /// </summary>
        [Required(ErrorMessage = "نوع الفاتورة مطلوب")]
        public string InvoiceType { get; set; } = InvoiceTypes.Sale;

        /// <summary>
        /// تاريخ ووقت إنشاء الفاتورة
        /// </summary>
        [Required(ErrorMessage = "تاريخ الفاتورة مطلوب")]
        public DateTime InvoiceDate { get; set; }

        /// <summary>
        /// تاريخ استحقاق الدفع (للمبيعات الآجلة)
        /// </summary>
        public DateTime? DueDate { get; set; }

        #endregion

        #region معلومات العميل

        /// <summary>
        /// رقم العميل في قاعدة البيانات
        /// </summary>
        public int? CustomerID { get; set; }

        /// <summary>
        /// اسم العميل
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم العميل لا يجب أن يتجاوز 100 حرف")]
        public string CustomerName { get; set; }

        /// <summary>
        /// رقم هاتف العميل
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهاتف لا يجب أن يتجاوز 20 حرف")]
        public string CustomerPhone { get; set; }

        /// <summary>
        /// عنوان العميل
        /// </summary>
        [StringLength(200, ErrorMessage = "العنوان لا يجب أن يتجاوز 200 حرف")]
        public string CustomerAddress { get; set; }

        #endregion

        #region المبالغ المالية

        /// <summary>
        /// المجموع الفرعي (قبل الخصم والضريبة)
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "المجموع الفرعي يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal SubTotal { get; set; }

        /// <summary>
        /// مبلغ الخصم الإجمالي
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "مبلغ الخصم يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// نسبة الخصم الإجمالي
        /// </summary>
        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public decimal DiscountPercent { get; set; }

        /// <summary>
        /// مبلغ الضريبة الإجمالي
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "مبلغ الضريبة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// المبلغ الإجمالي النهائي
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "المبلغ الإجمالي يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// المبلغ المدفوع
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "المبلغ المدفوع يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal PaidAmount { get; set; }

        /// <summary>
        /// المبلغ المتبقي
        /// </summary>
        public decimal RemainingAmount { get; set; }

        #endregion

        #region معلومات الدفع والحالة

        /// <summary>
        /// نوع الدفع (نقد، بطاقة، آجل، مختلط)
        /// </summary>
        [Required(ErrorMessage = "نوع الدفع مطلوب")]
        [StringLength(20, ErrorMessage = "نوع الدفع لا يجب أن يتجاوز 20 حرف")]
        public string PaymentType { get; set; } = PaymentTypes.Cash;

        /// <summary>
        /// حالة الدفع (مدفوع، جزئي، غير مدفوع)
        /// </summary>
        [StringLength(20, ErrorMessage = "حالة الدفع لا يجب أن تتجاوز 20 حرف")]
        public string PaymentStatus { get; set; } = PaymentStatuses.Paid;

        /// <summary>
        /// حالة الفاتورة (مكتملة، معلقة، ملغية، مرتجعة)
        /// </summary>
        [StringLength(20, ErrorMessage = "حالة الفاتورة لا يجب أن تتجاوز 20 حرف")]
        public string InvoiceStatus { get; set; } = InvoiceStatuses.Completed;

        /// <summary>
        /// هل الفاتورة مرتجعة
        /// </summary>
        public bool IsReturned { get; set; }

        /// <summary>
        /// تاريخ الإرجاع
        /// </summary>
        public DateTime? ReturnedDate { get; set; }

        #endregion

        #region الملاحظات والمعلومات الإضافية

        /// <summary>
        /// ملاحظات عامة للفاتورة
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 500 حرف")]
        public string Notes { get; set; }

        /// <summary>
        /// ملاحظات داخلية (لا تظهر للعميل)
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات الداخلية لا يجب أن تتجاوز 500 حرف")]
        public string InternalNotes { get; set; }

        #endregion

        #region معلومات المستخدم والنظام

        /// <summary>
        /// رقم الكاشير
        /// </summary>
        public int? CashierID { get; set; }

        /// <summary>
        /// اسم الكاشير
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم الكاشير لا يجب أن يتجاوز 50 حرف")]
        public string CashierName { get; set; }

        /// <summary>
        /// منشئ الفاتورة
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المنشئ لا يجب أن يتجاوز 50 حرف")]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ إنشاء السجل
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime ModifiedDate { get; set; }

        #endregion

        #region القوائم المرتبطة

        /// <summary>
        /// قائمة تفاصيل الفاتورة (المنتجات)
        /// </summary>
        public List<InvoiceDetail> InvoiceDetails { get; set; }

        /// <summary>
        /// قائمة المدفوعات
        /// </summary>
        public List<Payment> Payments { get; set; }

        /// <summary>
        /// قائمة الأقساط (للمبيعات الآجلة)
        /// </summary>
        public List<Installment> Installments { get; set; }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ الفاتورة مع التهيئة الافتراضية
        /// </summary>
        public Invoice()
        {
            // تهيئة القوائم
            InvoiceDetails = new List<InvoiceDetail>();
            Payments = new List<Payment>();
            Installments = new List<Installment>();

            // تهيئة التواريخ
            InvoiceDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;

            // تهيئة القيم الافتراضية
            InvoiceType = InvoiceTypes.Sale;
            PaymentType = PaymentTypes.Cash;
            PaymentStatus = PaymentStatuses.Paid;
            InvoiceStatus = InvoiceStatuses.Completed;
            IsReturned = false;
        }

        #endregion

        #region العمليات الحسابية

        /// <summary>
        /// حساب المجموع الفرعي من تفاصيل الفاتورة
        /// </summary>
        public void CalculateSubTotal()
        {
            SubTotal = InvoiceDetails?.Sum(detail => detail.LineTotal) ?? 0;
        }

        /// <summary>
        /// حساب مبلغ الخصم الإجمالي
        /// </summary>
        public void CalculateDiscountAmount()
        {
            if (DiscountPercent > 0)
            {
                DiscountAmount = SubTotal * (DiscountPercent / 100);
            }
        }

        /// <summary>
        /// حساب مبلغ الضريبة الإجمالي
        /// </summary>
        public void CalculateTaxAmount()
        {
            TaxAmount = InvoiceDetails?.Sum(detail => detail.TaxAmount) ?? 0;
        }

        /// <summary>
        /// حساب المبلغ الإجمالي النهائي
        /// </summary>
        public void CalculateTotal()
        {
            // حساب المجاميع الفرعية
            CalculateSubTotal();
            CalculateDiscountAmount();
            CalculateTaxAmount();

            // حساب الإجمالي النهائي
            TotalAmount = SubTotal - DiscountAmount + TaxAmount;

            // حساب المبلغ المتبقي
            RemainingAmount = TotalAmount - PaidAmount;

            // تحديث تاريخ التعديل
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// حساب إجمالي التكلفة
        /// </summary>
        /// <returns>إجمالي تكلفة المنتجات</returns>
        public decimal GetTotalCost()
        {
            return InvoiceDetails?.Sum(detail => detail.LineCost) ?? 0;
        }

        /// <summary>
        /// حساب إجمالي الربح
        /// </summary>
        /// <returns>إجمالي الربح</returns>
        public decimal GetTotalProfit()
        {
            return InvoiceDetails?.Sum(detail => detail.LineProfit) ?? 0;
        }

        /// <summary>
        /// حساب نسبة الربح
        /// </summary>
        /// <returns>نسبة الربح كنسبة مئوية</returns>
        public decimal GetProfitMargin()
        {
            var totalCost = GetTotalCost();
            return totalCost > 0 ? (GetTotalProfit() / totalCost) * 100 : 0;
        }

        #endregion

        #region عمليات التحقق والتصديق

        /// <summary>
        /// التحقق من صحة بيانات الفاتورة
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(InvoiceNumber) &&
                   InvoiceDetails != null &&
                   InvoiceDetails.Count > 0 &&
                   InvoiceDetails.All(detail => detail.IsValid()) &&
                   TotalAmount >= 0;
        }

        /// <summary>
        /// التحقق من إمكانية تعديل الفاتورة
        /// </summary>
        /// <returns>true إذا كان بإمكان التعديل</returns>
        public bool CanEdit()
        {
            return !IsReturned &&
                   InvoiceStatus != InvoiceStatuses.Cancelled &&
                   InvoiceStatus != InvoiceStatuses.Returned;
        }

        /// <summary>
        /// التحقق من إمكانية إرجاع الفاتورة
        /// </summary>
        /// <returns>true إذا كان بإمكان الإرجاع</returns>
        public bool CanReturn()
        {
            return !IsReturned &&
                   InvoiceStatus == InvoiceStatuses.Completed &&
                   InvoiceType == InvoiceTypes.Sale;
        }

        /// <summary>
        /// التحقق من اكتمال الدفع
        /// </summary>
        /// <returns>true إذا كان الدفع مكتملاً</returns>
        public bool IsFullyPaid()
        {
            return RemainingAmount <= 0;
        }

        #endregion

        #region عمليات النسخ والتحويل

        /// <summary>
        /// إنشاء نسخة من الفاتورة
        /// </summary>
        /// <returns>نسخة من الفاتورة</returns>
        public Invoice Clone()
        {
            var clonedInvoice = new Invoice
            {
                // نسخ الخصائص الأساسية (بدون الرقم والتواريخ)
                InvoiceType = this.InvoiceType,
                CustomerID = this.CustomerID,
                CustomerName = this.CustomerName,
                CustomerPhone = this.CustomerPhone,
                CustomerAddress = this.CustomerAddress,
                PaymentType = this.PaymentType,
                Notes = this.Notes,
                CashierID = this.CashierID,
                CashierName = this.CashierName
            };

            // نسخ تفاصيل الفاتورة
            foreach (var detail in InvoiceDetails)
            {
                clonedInvoice.InvoiceDetails.Add(detail.Clone());
            }

            // إعادة حساب الإجماليات
            clonedInvoice.CalculateTotal();

            return clonedInvoice;
        }

        #endregion
    }

    #region الثوابت والتعدادات

    /// <summary>
    /// أنواع الفواتير
    /// </summary>
    public static class InvoiceTypes
    {
        public const string Sale = "بيع";
        public const string Return = "إرجاع";
        public const string Quote = "عرض سعر";
        public const string Proforma = "فاتورة أولية";
    }

    /// <summary>
    /// أنواع الدفع
    /// </summary>
    public static class PaymentTypes
    {
        public const string Cash = "نقد";
        public const string Card = "بطاقة";
        public const string Credit = "آجل";
        public const string Mixed = "مختلط";
        public const string Check = "شيك";
        public const string Transfer = "تحويل";
    }

    /// <summary>
    /// حالات الدفع
    /// </summary>
    public static class PaymentStatuses
    {
        public const string Paid = "مدفوع";
        public const string Partial = "جزئي";
        public const string Unpaid = "غير مدفوع";
        public const string Overdue = "متأخر";
    }

    /// <summary>
    /// حالات الفاتورة
    /// </summary>
    public static class InvoiceStatuses
    {
        public const string Completed = "مكتملة";
        public const string Pending = "معلقة";
        public const string Cancelled = "ملغية";
        public const string Returned = "مرتجعة";
        public const string Draft = "مسودة";
    }

    #endregion
