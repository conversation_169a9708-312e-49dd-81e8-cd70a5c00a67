using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Data.SqlClient;

namespace AredooPOS.Services
{
    /// <summary>
    /// خدمة النسخ الاحتياطي والاستعادة
    /// توفر إمكانيات شاملة للنسخ الاحتياطي والاستعادة مع التشفير والضغط
    /// </summary>
    public class BackupService
    {
        #region المتغيرات والخصائص الخاصة

        private readonly string _connectionString;
        private readonly ILogger<BackupService> _logger;
        private readonly BackupSettings _settings;

        #endregion

        #region الأحداث

        /// <summary>
        /// حدث عند بدء النسخ الاحتياطي
        /// </summary>
        public event EventHandler<BackupStartedEventArgs> BackupStarted;

        /// <summary>
        /// حدث عند تقدم النسخ الاحتياطي
        /// </summary>
        public event EventHandler<BackupProgressEventArgs> BackupProgress;

        /// <summary>
        /// حدث عند اكتمال النسخ الاحتياطي
        /// </summary>
        public event EventHandler<BackupCompletedEventArgs> BackupCompleted;

        /// <summary>
        /// حدث عند فشل النسخ الاحتياطي
        /// </summary>
        public event EventHandler<BackupFailedEventArgs> BackupFailed;

        /// <summary>
        /// حدث عند بدء الاستعادة
        /// </summary>
        public event EventHandler<RestoreStartedEventArgs> RestoreStarted;

        /// <summary>
        /// حدث عند تقدم الاستعادة
        /// </summary>
        public event EventHandler<RestoreProgressEventArgs> RestoreProgress;

        /// <summary>
        /// حدث عند اكتمال الاستعادة
        /// </summary>
        public event EventHandler<RestoreCompletedEventArgs> RestoreCompleted;

        /// <summary>
        /// حدث عند فشل الاستعادة
        /// </summary>
        public event EventHandler<RestoreFailedEventArgs> RestoreFailed;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ خدمة النسخ الاحتياطي
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="settings">إعدادات النسخ الاحتياطي</param>
        /// <param name="logger">مسجل الأحداث</param>
        public BackupService(string connectionString = null, BackupSettings settings = null, ILogger<BackupService> logger = null)
        {
            _connectionString = connectionString ?? DatabaseConfig.GetConnectionString();
            _settings = settings ?? new BackupSettings();
            _logger = logger;

            // إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
            if (!Directory.Exists(_settings.BackupDirectory))
            {
                Directory.CreateDirectory(_settings.BackupDirectory);
            }

            _logger?.LogInformation("تم تهيئة خدمة النسخ الاحتياطي");
        }

        #endregion

        #region النسخ الاحتياطي

        /// <summary>
        /// إنشاء نسخة احتياطية كاملة
        /// </summary>
        /// <param name="backupName">اسم النسخة الاحتياطية</param>
        /// <param name="includeSettings">تضمين الإعدادات</param>
        /// <param name="includeFiles">تضمين الملفات</param>
        /// <returns>معلومات النسخة الاحتياطية</returns>
        public async Task<BackupInfo> CreateFullBackupAsync(string backupName = null, bool includeSettings = true, bool includeFiles = true)
        {
            try
            {
                backupName = backupName ?? $"FullBackup_{DateTime.Now:yyyyMMdd_HHmmss}";
                var backupInfo = new BackupInfo
                {
                    BackupName = backupName,
                    BackupType = BackupType.Full,
                    StartTime = DateTime.Now,
                    IncludeSettings = includeSettings,
                    IncludeFiles = includeFiles
                };

                // إثارة حدث بدء النسخ الاحتياطي
                BackupStarted?.Invoke(this, new BackupStartedEventArgs { BackupInfo = backupInfo });

                _logger?.LogInformation($"بدء إنشاء نسخة احتياطية كاملة: {backupName}");

                // إنشاء مجلد مؤقت للنسخة الاحتياطية
                var tempBackupPath = Path.Combine(Path.GetTempPath(), $"AredooPOS_Backup_{Guid.NewGuid()}");
                Directory.CreateDirectory(tempBackupPath);

                try
                {
                    var totalSteps = 3; // قاعدة البيانات + الإعدادات + الملفات
                    var currentStep = 0;

                    // 1. نسخ قاعدة البيانات
                    ReportProgress(++currentStep, totalSteps, "نسخ قاعدة البيانات...");
                    var dbBackupPath = await BackupDatabaseAsync(tempBackupPath);
                    backupInfo.DatabaseBackupPath = dbBackupPath;

                    // 2. نسخ الإعدادات
                    if (includeSettings)
                    {
                        ReportProgress(++currentStep, totalSteps, "نسخ الإعدادات...");
                        var settingsBackupPath = await BackupSettingsAsync(tempBackupPath);
                        backupInfo.SettingsBackupPath = settingsBackupPath;
                    }

                    // 3. نسخ الملفات
                    if (includeFiles)
                    {
                        ReportProgress(++currentStep, totalSteps, "نسخ الملفات...");
                        var filesBackupPath = await BackupFilesAsync(tempBackupPath);
                        backupInfo.FilesBackupPath = filesBackupPath;
                    }

                    // ضغط النسخة الاحتياطية
                    ReportProgress(totalSteps, totalSteps, "ضغط النسخة الاحتياطية...");
                    var finalBackupPath = await CompressBackupAsync(tempBackupPath, backupName);
                    backupInfo.BackupFilePath = finalBackupPath;

                    // تشفير النسخة الاحتياطية إذا كان مطلوباً
                    if (_settings.EncryptBackups)
                    {
                        ReportProgress(totalSteps, totalSteps, "تشفير النسخة الاحتياطية...");
                        var encryptedPath = await EncryptBackupAsync(finalBackupPath);
                        File.Delete(finalBackupPath); // حذف النسخة غير المشفرة
                        backupInfo.BackupFilePath = encryptedPath;
                        backupInfo.IsEncrypted = true;
                    }

                    // حساب معلومات النسخة الاحتياطية
                    var fileInfo = new FileInfo(backupInfo.BackupFilePath);
                    backupInfo.BackupSize = fileInfo.Length;
                    backupInfo.EndTime = DateTime.Now;
                    backupInfo.Duration = backupInfo.EndTime - backupInfo.StartTime;
                    backupInfo.IsSuccessful = true;

                    // حفظ معلومات النسخة الاحتياطية
                    await SaveBackupInfoAsync(backupInfo);

                    // تنظيف النسخ الاحتياطية القديمة
                    await CleanupOldBackupsAsync();

                    // إثارة حدث اكتمال النسخ الاحتياطي
                    BackupCompleted?.Invoke(this, new BackupCompletedEventArgs { BackupInfo = backupInfo });

                    _logger?.LogInformation($"تم إنشاء النسخة الاحتياطية بنجاح: {backupInfo.BackupFilePath}");
                    return backupInfo;
                }
                finally
                {
                    // تنظيف المجلد المؤقت
                    if (Directory.Exists(tempBackupPath))
                    {
                        Directory.Delete(tempBackupPath, true);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إنشاء النسخة الاحتياطية: {backupName}");
                
                BackupFailed?.Invoke(this, new BackupFailedEventArgs 
                { 
                    BackupName = backupName, 
                    ErrorMessage = ex.Message,
                    FailureTime = DateTime.Now
                });

                throw;
            }
        }

        /// <summary>
        /// نسخ قاعدة البيانات
        /// </summary>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>مسار ملف قاعدة البيانات</returns>
        private async Task<string> BackupDatabaseAsync(string backupPath)
        {
            try
            {
                var dbBackupPath = Path.Combine(backupPath, "Database.bak");
                
                using var connection = new SqlConnection(_connectionString);
                var databaseName = connection.Database;
                
                var backupCommand = $@"
                    BACKUP DATABASE [{databaseName}] 
                    TO DISK = '{dbBackupPath}' 
                    WITH FORMAT, INIT, COMPRESSION";

                using var command = new SqlCommand(backupCommand, connection);
                command.CommandTimeout = _settings.BackupTimeoutMinutes * 60;

                await connection.OpenAsync();
                await command.ExecuteNonQueryAsync();

                _logger?.LogInformation($"تم نسخ قاعدة البيانات إلى: {dbBackupPath}");
                return dbBackupPath;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في نسخ قاعدة البيانات");
                throw;
            }
        }

        /// <summary>
        /// نسخ الإعدادات
        /// </summary>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>مسار ملف الإعدادات</returns>
        private async Task<string> BackupSettingsAsync(string backupPath)
        {
            try
            {
                var settingsService = new SettingsService(_connectionString, _logger);
                var settingsData = await settingsService.ExportAllSettingsAsync();
                
                var settingsBackupPath = Path.Combine(backupPath, "Settings.json");
                await File.WriteAllTextAsync(settingsBackupPath, settingsData, Encoding.UTF8);

                _logger?.LogInformation($"تم نسخ الإعدادات إلى: {settingsBackupPath}");
                return settingsBackupPath;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في نسخ الإعدادات");
                throw;
            }
        }

        /// <summary>
        /// نسخ الملفات
        /// </summary>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>مسار مجلد الملفات</returns>
        private async Task<string> BackupFilesAsync(string backupPath)
        {
            try
            {
                var filesBackupPath = Path.Combine(backupPath, "Files");
                Directory.CreateDirectory(filesBackupPath);

                // نسخ ملفات الشعارات والصور
                await CopyDirectoryAsync("Images", Path.Combine(filesBackupPath, "Images"));
                
                // نسخ ملفات التقارير
                await CopyDirectoryAsync("Reports", Path.Combine(filesBackupPath, "Reports"));
                
                // نسخ ملفات الإعدادات المحلية
                await CopyDirectoryAsync("Config", Path.Combine(filesBackupPath, "Config"));

                _logger?.LogInformation($"تم نسخ الملفات إلى: {filesBackupPath}");
                return filesBackupPath;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في نسخ الملفات");
                throw;
            }
        }

        /// <summary>
        /// نسخ مجلد بشكل متكرر
        /// </summary>
        /// <param name="sourceDir">المجلد المصدر</param>
        /// <param name="destDir">المجلد الهدف</param>
        private async Task CopyDirectoryAsync(string sourceDir, string destDir)
        {
            if (!Directory.Exists(sourceDir))
                return;

            Directory.CreateDirectory(destDir);

            // نسخ الملفات
            foreach (var file in Directory.GetFiles(sourceDir))
            {
                var destFile = Path.Combine(destDir, Path.GetFileName(file));
                await Task.Run(() => File.Copy(file, destFile, true));
            }

            // نسخ المجلدات الفرعية
            foreach (var subDir in Directory.GetDirectories(sourceDir))
            {
                var destSubDir = Path.Combine(destDir, Path.GetFileName(subDir));
                await CopyDirectoryAsync(subDir, destSubDir);
            }
        }

        /// <summary>
        /// ضغط النسخة الاحتياطية
        /// </summary>
        /// <param name="sourcePath">مسار المصدر</param>
        /// <param name="backupName">اسم النسخة الاحتياطية</param>
        /// <returns>مسار الملف المضغوط</returns>
        private async Task<string> CompressBackupAsync(string sourcePath, string backupName)
        {
            try
            {
                var compressedPath = Path.Combine(_settings.BackupDirectory, $"{backupName}.zip");
                
                await Task.Run(() => 
                {
                    if (File.Exists(compressedPath))
                        File.Delete(compressedPath);
                    
                    ZipFile.CreateFromDirectory(sourcePath, compressedPath, CompressionLevel.Optimal, false);
                });

                _logger?.LogInformation($"تم ضغط النسخة الاحتياطية إلى: {compressedPath}");
                return compressedPath;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في ضغط النسخة الاحتياطية");
                throw;
            }
        }

        /// <summary>
        /// تشفير النسخة الاحتياطية
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>مسار الملف المشفر</returns>
        private async Task<string> EncryptBackupAsync(string filePath)
        {
            try
            {
                var encryptedPath = filePath + ".encrypted";
                var password = _settings.EncryptionPassword ?? "AredooPOS2025";

                await Task.Run(() =>
                {
                    using var aes = Aes.Create();
                    var key = new Rfc2898DeriveBytes(password, Encoding.UTF8.GetBytes("AredooPOSSalt"), 10000);
                    aes.Key = key.GetBytes(32);
                    aes.IV = key.GetBytes(16);

                    using var sourceStream = File.OpenRead(filePath);
                    using var destStream = File.Create(encryptedPath);
                    using var cryptoStream = new CryptoStream(destStream, aes.CreateEncryptor(), CryptoStreamMode.Write);
                    
                    sourceStream.CopyTo(cryptoStream);
                });

                _logger?.LogInformation($"تم تشفير النسخة الاحتياطية إلى: {encryptedPath}");
                return encryptedPath;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تشفير النسخة الاحتياطية");
                throw;
            }
        }

        /// <summary>
        /// تقرير تقدم العملية
        /// </summary>
        /// <param name="currentStep">الخطوة الحالية</param>
        /// <param name="totalSteps">إجمالي الخطوات</param>
        /// <param name="message">الرسالة</param>
        private void ReportProgress(int currentStep, int totalSteps, string message)
        {
            var progressPercentage = (int)((double)currentStep / totalSteps * 100);
            
            BackupProgress?.Invoke(this, new BackupProgressEventArgs
            {
                ProgressPercentage = progressPercentage,
                CurrentStep = currentStep,
                TotalSteps = totalSteps,
                Message = message
            });
        }

        #endregion

        #region الاستعادة

        /// <summary>
        /// استعادة النسخة الاحتياطية
        /// </summary>
        /// <param name="backupFilePath">مسار ملف النسخة الاحتياطية</param>
        /// <param name="restoreDatabase">استعادة قاعدة البيانات</param>
        /// <param name="restoreSettings">استعادة الإعدادات</param>
        /// <param name="restoreFiles">استعادة الملفات</param>
        /// <returns>معلومات الاستعادة</returns>
        public async Task<RestoreInfo> RestoreBackupAsync(string backupFilePath, bool restoreDatabase = true, bool restoreSettings = true, bool restoreFiles = true)
        {
            try
            {
                var restoreInfo = new RestoreInfo
                {
                    BackupFilePath = backupFilePath,
                    StartTime = DateTime.Now,
                    RestoreDatabase = restoreDatabase,
                    RestoreSettings = restoreSettings,
                    RestoreFiles = restoreFiles
                };

                // إثارة حدث بدء الاستعادة
                RestoreStarted?.Invoke(this, new RestoreStartedEventArgs { RestoreInfo = restoreInfo });

                _logger?.LogInformation($"بدء استعادة النسخة الاحتياطية: {backupFilePath}");

                // إنشاء مجلد مؤقت للاستعادة
                var tempRestorePath = Path.Combine(Path.GetTempPath(), $"AredooPOS_Restore_{Guid.NewGuid()}");
                Directory.CreateDirectory(tempRestorePath);

                try
                {
                    var totalSteps = 4; // فك التشفير + فك الضغط + استعادة قاعدة البيانات + استعادة الإعدادات + استعادة الملفات
                    var currentStep = 0;

                    // 1. فك التشفير إذا كان مطلوباً
                    var workingFilePath = backupFilePath;
                    if (backupFilePath.EndsWith(".encrypted"))
                    {
                        ReportRestoreProgress(++currentStep, totalSteps, "فك تشفير النسخة الاحتياطية...");
                        workingFilePath = await DecryptBackupAsync(backupFilePath, tempRestorePath);
                        restoreInfo.IsEncrypted = true;
                    }

                    // 2. فك الضغط
                    ReportRestoreProgress(++currentStep, totalSteps, "فك ضغط النسخة الاحتياطية...");
                    await ExtractBackupAsync(workingFilePath, tempRestorePath);

                    // 3. استعادة قاعدة البيانات
                    if (restoreDatabase)
                    {
                        ReportRestoreProgress(++currentStep, totalSteps, "استعادة قاعدة البيانات...");
                        await RestoreDatabaseAsync(tempRestorePath);
                    }

                    // 4. استعادة الإعدادات
                    if (restoreSettings)
                    {
                        ReportRestoreProgress(++currentStep, totalSteps, "استعادة الإعدادات...");
                        await RestoreSettingsAsync(tempRestorePath);
                    }

                    // 5. استعادة الملفات
                    if (restoreFiles)
                    {
                        ReportRestoreProgress(totalSteps, totalSteps, "استعادة الملفات...");
                        await RestoreFilesAsync(tempRestorePath);
                    }

                    restoreInfo.EndTime = DateTime.Now;
                    restoreInfo.Duration = restoreInfo.EndTime - restoreInfo.StartTime;
                    restoreInfo.IsSuccessful = true;

                    // إثارة حدث اكتمال الاستعادة
                    RestoreCompleted?.Invoke(this, new RestoreCompletedEventArgs { RestoreInfo = restoreInfo });

                    _logger?.LogInformation($"تم استعادة النسخة الاحتياطية بنجاح");
                    return restoreInfo;
                }
                finally
                {
                    // تنظيف المجلد المؤقت
                    if (Directory.Exists(tempRestorePath))
                    {
                        Directory.Delete(tempRestorePath, true);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في استعادة النسخة الاحتياطية: {backupFilePath}");

                RestoreFailed?.Invoke(this, new RestoreFailedEventArgs
                {
                    BackupFilePath = backupFilePath,
                    ErrorMessage = ex.Message,
                    FailureTime = DateTime.Now
                });

                throw;
            }
        }

        /// <summary>
        /// فك تشفير النسخة الاحتياطية
        /// </summary>
        /// <param name="encryptedFilePath">مسار الملف المشفر</param>
        /// <param name="outputPath">مسار الإخراج</param>
        /// <returns>مسار الملف المفكوك التشفير</returns>
        private async Task<string> DecryptBackupAsync(string encryptedFilePath, string outputPath)
        {
            try
            {
                var decryptedPath = Path.Combine(outputPath, "decrypted_backup.zip");
                var password = _settings.EncryptionPassword ?? "AredooPOS2025";

                await Task.Run(() =>
                {
                    using var aes = Aes.Create();
                    var key = new Rfc2898DeriveBytes(password, Encoding.UTF8.GetBytes("AredooPOSSalt"), 10000);
                    aes.Key = key.GetBytes(32);
                    aes.IV = key.GetBytes(16);

                    using var sourceStream = File.OpenRead(encryptedFilePath);
                    using var destStream = File.Create(decryptedPath);
                    using var cryptoStream = new CryptoStream(sourceStream, aes.CreateDecryptor(), CryptoStreamMode.Read);

                    cryptoStream.CopyTo(destStream);
                });

                _logger?.LogInformation($"تم فك تشفير النسخة الاحتياطية إلى: {decryptedPath}");
                return decryptedPath;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فك تشفير النسخة الاحتياطية");
                throw;
            }
        }

        /// <summary>
        /// فك ضغط النسخة الاحتياطية
        /// </summary>
        /// <param name="zipFilePath">مسار الملف المضغوط</param>
        /// <param name="extractPath">مسار الاستخراج</param>
        private async Task ExtractBackupAsync(string zipFilePath, string extractPath)
        {
            try
            {
                var extractionPath = Path.Combine(extractPath, "extracted");

                await Task.Run(() =>
                {
                    ZipFile.ExtractToDirectory(zipFilePath, extractionPath);
                });

                _logger?.LogInformation($"تم فك ضغط النسخة الاحتياطية إلى: {extractionPath}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فك ضغط النسخة الاحتياطية");
                throw;
            }
        }

        /// <summary>
        /// استعادة قاعدة البيانات
        /// </summary>
        /// <param name="restorePath">مسار الاستعادة</param>
        private async Task RestoreDatabaseAsync(string restorePath)
        {
            try
            {
                var dbBackupPath = Path.Combine(restorePath, "extracted", "Database.bak");

                if (!File.Exists(dbBackupPath))
                {
                    _logger?.LogWarning("لم يتم العثور على ملف قاعدة البيانات في النسخة الاحتياطية");
                    return;
                }

                using var connection = new SqlConnection(_connectionString);
                var databaseName = connection.Database;

                // إغلاق جميع الاتصالات الحالية
                var killConnectionsCommand = $@"
                    ALTER DATABASE [{databaseName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;";

                var restoreCommand = $@"
                    RESTORE DATABASE [{databaseName}]
                    FROM DISK = '{dbBackupPath}'
                    WITH REPLACE;
                    ALTER DATABASE [{databaseName}] SET MULTI_USER;";

                using var command = new SqlCommand(killConnectionsCommand + restoreCommand, connection);
                command.CommandTimeout = _settings.RestoreTimeoutMinutes * 60;

                await connection.OpenAsync();
                await command.ExecuteNonQueryAsync();

                _logger?.LogInformation($"تم استعادة قاعدة البيانات من: {dbBackupPath}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في استعادة قاعدة البيانات");
                throw;
            }
        }

        /// <summary>
        /// استعادة الإعدادات
        /// </summary>
        /// <param name="restorePath">مسار الاستعادة</param>
        private async Task RestoreSettingsAsync(string restorePath)
        {
            try
            {
                var settingsBackupPath = Path.Combine(restorePath, "extracted", "Settings.json");

                if (!File.Exists(settingsBackupPath))
                {
                    _logger?.LogWarning("لم يتم العثور على ملف الإعدادات في النسخة الاحتياطية");
                    return;
                }

                var settingsData = await File.ReadAllTextAsync(settingsBackupPath, Encoding.UTF8);
                var settingsService = new SettingsService(_connectionString, _logger);

                await settingsService.ImportSettingsAsync(settingsData, "System Restore");

                _logger?.LogInformation($"تم استعادة الإعدادات من: {settingsBackupPath}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في استعادة الإعدادات");
                throw;
            }
        }

        /// <summary>
        /// استعادة الملفات
        /// </summary>
        /// <param name="restorePath">مسار الاستعادة</param>
        private async Task RestoreFilesAsync(string restorePath)
        {
            try
            {
                var filesBackupPath = Path.Combine(restorePath, "extracted", "Files");

                if (!Directory.Exists(filesBackupPath))
                {
                    _logger?.LogWarning("لم يتم العثور على مجلد الملفات في النسخة الاحتياطية");
                    return;
                }

                // استعادة الملفات
                await CopyDirectoryAsync(Path.Combine(filesBackupPath, "Images"), "Images");
                await CopyDirectoryAsync(Path.Combine(filesBackupPath, "Reports"), "Reports");
                await CopyDirectoryAsync(Path.Combine(filesBackupPath, "Config"), "Config");

                _logger?.LogInformation($"تم استعادة الملفات من: {filesBackupPath}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في استعادة الملفات");
                throw;
            }
        }

        /// <summary>
        /// تقرير تقدم الاستعادة
        /// </summary>
        /// <param name="currentStep">الخطوة الحالية</param>
        /// <param name="totalSteps">إجمالي الخطوات</param>
        /// <param name="message">الرسالة</param>
        private void ReportRestoreProgress(int currentStep, int totalSteps, string message)
        {
            var progressPercentage = (int)((double)currentStep / totalSteps * 100);

            RestoreProgress?.Invoke(this, new RestoreProgressEventArgs
            {
                ProgressPercentage = progressPercentage,
                CurrentStep = currentStep,
                TotalSteps = totalSteps,
                Message = message
            });
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// حفظ معلومات النسخة الاحتياطية
        /// </summary>
        /// <param name="backupInfo">معلومات النسخة الاحتياطية</param>
        private async Task SaveBackupInfoAsync(BackupInfo backupInfo)
        {
            try
            {
                var backupInfoPath = Path.Combine(_settings.BackupDirectory, "BackupHistory.json");
                var backupHistory = new List<BackupInfo>();

                // تحميل التاريخ الموجود
                if (File.Exists(backupInfoPath))
                {
                    var existingData = await File.ReadAllTextAsync(backupInfoPath);
                    backupHistory = JsonConvert.DeserializeObject<List<BackupInfo>>(existingData) ?? new List<BackupInfo>();
                }

                // إضافة النسخة الاحتياطية الجديدة
                backupHistory.Add(backupInfo);

                // حفظ التاريخ المحدث
                var updatedData = JsonConvert.SerializeObject(backupHistory, Formatting.Indented);
                await File.WriteAllTextAsync(backupInfoPath, updatedData);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ معلومات النسخة الاحتياطية");
            }
        }

        /// <summary>
        /// تنظيف النسخ الاحتياطية القديمة
        /// </summary>
        private async Task CleanupOldBackupsAsync()
        {
            try
            {
                if (_settings.MaxBackupCount <= 0)
                    return;

                var backupFiles = Directory.GetFiles(_settings.BackupDirectory, "*.zip")
                    .Concat(Directory.GetFiles(_settings.BackupDirectory, "*.encrypted"))
                    .Select(f => new FileInfo(f))
                    .OrderByDescending(f => f.CreationTime)
                    .ToList();

                if (backupFiles.Count > _settings.MaxBackupCount)
                {
                    var filesToDelete = backupFiles.Skip(_settings.MaxBackupCount);

                    foreach (var file in filesToDelete)
                    {
                        await Task.Run(() => file.Delete());
                        _logger?.LogInformation($"تم حذف النسخة الاحتياطية القديمة: {file.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تنظيف النسخ الاحتياطية القديمة");
            }
        }

        /// <summary>
        /// الحصول على قائمة النسخ الاحتياطية
        /// </summary>
        /// <returns>قائمة النسخ الاحتياطية</returns>
        public async Task<List<BackupInfo>> GetBackupHistoryAsync()
        {
            try
            {
                var backupInfoPath = Path.Combine(_settings.BackupDirectory, "BackupHistory.json");

                if (!File.Exists(backupInfoPath))
                    return new List<BackupInfo>();

                var data = await File.ReadAllTextAsync(backupInfoPath);
                return JsonConvert.DeserializeObject<List<BackupInfo>>(data) ?? new List<BackupInfo>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على تاريخ النسخ الاحتياطية");
                return new List<BackupInfo>();
            }
        }

        /// <summary>
        /// حذف نسخة احتياطية
        /// </summary>
        /// <param name="backupFilePath">مسار النسخة الاحتياطية</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public async Task<bool> DeleteBackupAsync(string backupFilePath)
        {
            try
            {
                if (File.Exists(backupFilePath))
                {
                    await Task.Run(() => File.Delete(backupFilePath));
                    _logger?.LogInformation($"تم حذف النسخة الاحتياطية: {backupFilePath}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حذف النسخة الاحتياطية: {backupFilePath}");
                return false;
            }
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// إعدادات النسخ الاحتياطي
    /// </summary>
    public class BackupSettings
    {
        /// <summary>
        /// مجلد النسخ الاحتياطي
        /// </summary>
        public string BackupDirectory { get; set; } = "Backups";

        /// <summary>
        /// تشفير النسخ الاحتياطية
        /// </summary>
        public bool EncryptBackups { get; set; } = true;

        /// <summary>
        /// كلمة مرور التشفير
        /// </summary>
        public string EncryptionPassword { get; set; }

        /// <summary>
        /// الحد الأقصى لعدد النسخ الاحتياطية
        /// </summary>
        public int MaxBackupCount { get; set; } = 10;

        /// <summary>
        /// مهلة النسخ الاحتياطي بالدقائق
        /// </summary>
        public int BackupTimeoutMinutes { get; set; } = 30;

        /// <summary>
        /// مهلة الاستعادة بالدقائق
        /// </summary>
        public int RestoreTimeoutMinutes { get; set; } = 60;

        /// <summary>
        /// ضغط النسخ الاحتياطية
        /// </summary>
        public bool CompressBackups { get; set; } = true;

        /// <summary>
        /// تضمين الملفات في النسخ الاحتياطي
        /// </summary>
        public bool IncludeFiles { get; set; } = true;

        /// <summary>
        /// تضمين الإعدادات في النسخ الاحتياطي
        /// </summary>
        public bool IncludeSettings { get; set; } = true;
    }

    /// <summary>
    /// معلومات النسخة الاحتياطية
    /// </summary>
    public class BackupInfo
    {
        /// <summary>
        /// اسم النسخة الاحتياطية
        /// </summary>
        public string BackupName { get; set; }

        /// <summary>
        /// نوع النسخة الاحتياطية
        /// </summary>
        public BackupType BackupType { get; set; }

        /// <summary>
        /// مسار ملف النسخة الاحتياطية
        /// </summary>
        public string BackupFilePath { get; set; }

        /// <summary>
        /// حجم النسخة الاحتياطية
        /// </summary>
        public long BackupSize { get; set; }

        /// <summary>
        /// وقت البدء
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// وقت الانتهاء
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// مدة النسخ الاحتياطي
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// هل النسخة الاحتياطية ناجحة
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// هل النسخة الاحتياطية مشفرة
        /// </summary>
        public bool IsEncrypted { get; set; }

        /// <summary>
        /// تضمين الإعدادات
        /// </summary>
        public bool IncludeSettings { get; set; }

        /// <summary>
        /// تضمين الملفات
        /// </summary>
        public bool IncludeFiles { get; set; }

        /// <summary>
        /// مسار نسخة قاعدة البيانات
        /// </summary>
        public string DatabaseBackupPath { get; set; }

        /// <summary>
        /// مسار نسخة الإعدادات
        /// </summary>
        public string SettingsBackupPath { get; set; }

        /// <summary>
        /// مسار نسخة الملفات
        /// </summary>
        public string FilesBackupPath { get; set; }

        /// <summary>
        /// رسالة الخطأ (إن وجدت)
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// معلومات الاستعادة
    /// </summary>
    public class RestoreInfo
    {
        /// <summary>
        /// مسار ملف النسخة الاحتياطية
        /// </summary>
        public string BackupFilePath { get; set; }

        /// <summary>
        /// وقت البدء
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// وقت الانتهاء
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// مدة الاستعادة
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// هل الاستعادة ناجحة
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// هل النسخة الاحتياطية مشفرة
        /// </summary>
        public bool IsEncrypted { get; set; }

        /// <summary>
        /// استعادة قاعدة البيانات
        /// </summary>
        public bool RestoreDatabase { get; set; }

        /// <summary>
        /// استعادة الإعدادات
        /// </summary>
        public bool RestoreSettings { get; set; }

        /// <summary>
        /// استعادة الملفات
        /// </summary>
        public bool RestoreFiles { get; set; }

        /// <summary>
        /// رسالة الخطأ (إن وجدت)
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// أنواع النسخ الاحتياطية
    /// </summary>
    public enum BackupType
    {
        /// <summary>
        /// نسخة احتياطية كاملة
        /// </summary>
        Full,

        /// <summary>
        /// نسخة احتياطية تزايدية
        /// </summary>
        Incremental,

        /// <summary>
        /// نسخة احتياطية تفاضلية
        /// </summary>
        Differential,

        /// <summary>
        /// نسخة احتياطية للإعدادات فقط
        /// </summary>
        SettingsOnly,

        /// <summary>
        /// نسخة احتياطية لقاعدة البيانات فقط
        /// </summary>
        DatabaseOnly
    }

    #endregion

    #region نماذج الأحداث

    /// <summary>
    /// بيانات حدث بدء النسخ الاحتياطي
    /// </summary>
    public class BackupStartedEventArgs : EventArgs
    {
        public BackupInfo BackupInfo { get; set; }
    }

    /// <summary>
    /// بيانات حدث تقدم النسخ الاحتياطي
    /// </summary>
    public class BackupProgressEventArgs : EventArgs
    {
        public int ProgressPercentage { get; set; }
        public int CurrentStep { get; set; }
        public int TotalSteps { get; set; }
        public string Message { get; set; }
    }

    /// <summary>
    /// بيانات حدث اكتمال النسخ الاحتياطي
    /// </summary>
    public class BackupCompletedEventArgs : EventArgs
    {
        public BackupInfo BackupInfo { get; set; }
    }

    /// <summary>
    /// بيانات حدث فشل النسخ الاحتياطي
    /// </summary>
    public class BackupFailedEventArgs : EventArgs
    {
        public string BackupName { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime FailureTime { get; set; }
    }

    /// <summary>
    /// بيانات حدث بدء الاستعادة
    /// </summary>
    public class RestoreStartedEventArgs : EventArgs
    {
        public RestoreInfo RestoreInfo { get; set; }
    }

    /// <summary>
    /// بيانات حدث تقدم الاستعادة
    /// </summary>
    public class RestoreProgressEventArgs : EventArgs
    {
        public int ProgressPercentage { get; set; }
        public int CurrentStep { get; set; }
        public int TotalSteps { get; set; }
        public string Message { get; set; }
    }

    /// <summary>
    /// بيانات حدث اكتمال الاستعادة
    /// </summary>
    public class RestoreCompletedEventArgs : EventArgs
    {
        public RestoreInfo RestoreInfo { get; set; }
    }

    /// <summary>
    /// بيانات حدث فشل الاستعادة
    /// </summary>
    public class RestoreFailedEventArgs : EventArgs
    {
        public string BackupFilePath { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime FailureTime { get; set; }
    }

    #endregion
}
