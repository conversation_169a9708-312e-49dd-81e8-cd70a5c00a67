using System;
using System.ComponentModel.DataAnnotations;

namespace AredooPOS.Models
{
    /// <summary>
    /// نموذج تذكيرات الديون - يمثل تذكير مرسل للعميل بخصوص دين مستحق
    /// يحتوي على جميع معلومات التذكير وطريقة الإرسال
    /// </summary>
    public class DebtReminder
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم التذكير في قاعدة البيانات (مفتاح أساسي)
        /// </summary>
        public int ReminderID { get; set; }

        /// <summary>
        /// رقم الدين المرتبط (مفتاح خارجي)
        /// </summary>
        [Required(ErrorMessage = "رقم الدين مطلوب")]
        public int DebtID { get; set; }

        /// <summary>
        /// رقم التذكير المعروض للمستخدم
        /// </summary>
        [Required(ErrorMessage = "رقم التذكير مطلوب")]
        [StringLength(20, ErrorMessage = "رقم التذكير لا يجب أن يتجاوز 20 حرف")]
        public string ReminderNumber { get; set; }

        /// <summary>
        /// تاريخ ووقت إرسال التذكير
        /// </summary>
        [Required(ErrorMessage = "تاريخ التذكير مطلوب")]
        public DateTime ReminderDate { get; set; }

        /// <summary>
        /// تاريخ الاستحقاق المذكور في التذكير
        /// </summary>
        public DateTime? DueDate { get; set; }

        #endregion

        #region معلومات التذكير

        /// <summary>
        /// نوع التذكير (تذكير ودي، إنذار أول، إنذار أخير، إشعار قانوني)
        /// </summary>
        [Required(ErrorMessage = "نوع التذكير مطلوب")]
        [StringLength(20, ErrorMessage = "نوع التذكير لا يجب أن يتجاوز 20 حرف")]
        public string ReminderType { get; set; }

        /// <summary>
        /// طريقة الإرسال (رسالة نصية، اتصال، بريد إلكتروني، زيارة شخصية)
        /// </summary>
        [Required(ErrorMessage = "طريقة الإرسال مطلوبة")]
        [StringLength(20, ErrorMessage = "طريقة الإرسال لا يجب أن تتجاوز 20 حرف")]
        public string DeliveryMethod { get; set; }

        /// <summary>
        /// عنوان الإرسال (رقم الهاتف، البريد الإلكتروني، العنوان)
        /// </summary>
        [StringLength(100, ErrorMessage = "عنوان الإرسال لا يجب أن يتجاوز 100 حرف")]
        public string DeliveryAddress { get; set; }

        /// <summary>
        /// موضوع التذكير
        /// </summary>
        [Required(ErrorMessage = "موضوع التذكير مطلوب")]
        [StringLength(100, ErrorMessage = "موضوع التذكير لا يجب أن يتجاوز 100 حرف")]
        public string Subject { get; set; }

        /// <summary>
        /// نص التذكير
        /// </summary>
        [Required(ErrorMessage = "نص التذكير مطلوب")]
        [StringLength(1000, ErrorMessage = "نص التذكير لا يجب أن يتجاوز 1000 حرف")]
        public string Message { get; set; }

        #endregion

        #region معلومات الدين المذكور

        /// <summary>
        /// مبلغ الدين المذكور في التذكير
        /// </summary>
        [Required(ErrorMessage = "مبلغ الدين مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "مبلغ الدين يجب أن يكون أكبر من صفر")]
        public decimal DebtAmount { get; set; }

        /// <summary>
        /// المبلغ المتبقي المذكور في التذكير
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "المبلغ المتبقي يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal RemainingAmount { get; set; }

        /// <summary>
        /// رسوم التأخير المذكورة في التذكير
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "رسوم التأخير يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal LateFees { get; set; }

        /// <summary>
        /// عدد أيام التأخير المذكورة في التذكير
        /// </summary>
        public int OverdueDays { get; set; }

        #endregion

        #region حالة التذكير

        /// <summary>
        /// حالة التذكير (مرسل، مستلم، مقروء، تم الرد، فشل الإرسال)
        /// </summary>
        [Required(ErrorMessage = "حالة التذكير مطلوبة")]
        [StringLength(20, ErrorMessage = "حالة التذكير لا يجب أن تتجاوز 20 حرف")]
        public string ReminderStatus { get; set; }

        /// <summary>
        /// تاريخ الاستلام أو القراءة
        /// </summary>
        public DateTime? DeliveredDate { get; set; }

        /// <summary>
        /// تاريخ الرد من العميل
        /// </summary>
        public DateTime? ResponseDate { get; set; }

        /// <summary>
        /// نص رد العميل
        /// </summary>
        [StringLength(500, ErrorMessage = "نص الرد لا يجب أن يتجاوز 500 حرف")]
        public string CustomerResponse { get; set; }

        /// <summary>
        /// تاريخ المتابعة المطلوب
        /// </summary>
        public DateTime? FollowUpDate { get; set; }

        #endregion

        #region معلومات الإرسال التقنية

        /// <summary>
        /// معرف الرسالة في النظام الخارجي (SMS Gateway, Email Service)
        /// </summary>
        [StringLength(100, ErrorMessage = "معرف الرسالة لا يجب أن يتجاوز 100 حرف")]
        public string ExternalMessageID { get; set; }

        /// <summary>
        /// تكلفة الإرسال
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "تكلفة الإرسال يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal DeliveryCost { get; set; }

        /// <summary>
        /// رمز حالة الإرسال من النظام الخارجي
        /// </summary>
        [StringLength(20, ErrorMessage = "رمز حالة الإرسال لا يجب أن يتجاوز 20 حرف")]
        public string DeliveryStatusCode { get; set; }

        /// <summary>
        /// رسالة حالة الإرسال من النظام الخارجي
        /// </summary>
        [StringLength(200, ErrorMessage = "رسالة حالة الإرسال لا يجب أن تتجاوز 200 حرف")]
        public string DeliveryStatusMessage { get; set; }

        #endregion

        #region الملاحظات والمعلومات الإضافية

        /// <summary>
        /// ملاحظات التذكير
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 500 حرف")]
        public string Notes { get; set; }

        /// <summary>
        /// ملاحظات داخلية (لا تظهر للعميل)
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات الداخلية لا يجب أن تتجاوز 500 حرف")]
        public string InternalNotes { get; set; }

        /// <summary>
        /// مرفقات (صور، ملفات PDF، إلخ)
        /// </summary>
        [StringLength(1000, ErrorMessage = "مسارات المرفقات لا يجب أن تتجاوز 1000 حرف")]
        public string Attachments { get; set; }

        #endregion

        #region معلومات المستخدم والنظام

        /// <summary>
        /// من أرسل التذكير
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المرسل لا يجب أن يتجاوز 50 حرف")]
        public string SentBy { get; set; }

        /// <summary>
        /// تاريخ إنشاء التذكير
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// آخر من عدل التذكير
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المعدل لا يجب أن يتجاوز 50 حرف")]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime ModifiedDate { get; set; }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ تذكير الدين مع التهيئة الافتراضية
        /// </summary>
        public DebtReminder()
        {
            // تهيئة التواريخ
            ReminderDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;

            // تهيئة القيم الافتراضية
            ReminderType = ReminderTypes.Friendly;
            DeliveryMethod = DeliveryMethods.SMS;
            ReminderStatus = ReminderStatuses.Pending;
            DeliveryCost = 0;
            OverdueDays = 0;
            LateFees = 0;
        }

        #endregion

        #region العمليات

        /// <summary>
        /// تحديد التذكير كمرسل
        /// </summary>
        /// <param name="externalMessageID">معرف الرسالة الخارجي</param>
        /// <param name="cost">تكلفة الإرسال</param>
        public void MarkAsSent(string externalMessageID = "", decimal cost = 0)
        {
            ReminderStatus = ReminderStatuses.Sent;
            ExternalMessageID = externalMessageID;
            DeliveryCost = cost;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// تحديد التذكير كمستلم
        /// </summary>
        public void MarkAsDelivered()
        {
            ReminderStatus = ReminderStatuses.Delivered;
            DeliveredDate = DateTime.Now;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// تسجيل رد العميل
        /// </summary>
        /// <param name="response">نص الرد</param>
        public void RecordCustomerResponse(string response)
        {
            ReminderStatus = ReminderStatuses.Responded;
            CustomerResponse = response;
            ResponseDate = DateTime.Now;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// تحديد التذكير كفاشل
        /// </summary>
        /// <param name="errorMessage">رسالة الخطأ</param>
        public void MarkAsFailed(string errorMessage)
        {
            ReminderStatus = ReminderStatuses.Failed;
            DeliveryStatusMessage = errorMessage;
            ModifiedDate = DateTime.Now;
        }

        #endregion

        #region عمليات التحقق والتصديق

        /// <summary>
        /// التحقق من صحة بيانات التذكير
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return DebtID > 0 &&
                   !string.IsNullOrWhiteSpace(ReminderNumber) &&
                   !string.IsNullOrWhiteSpace(ReminderType) &&
                   !string.IsNullOrWhiteSpace(DeliveryMethod) &&
                   !string.IsNullOrWhiteSpace(Subject) &&
                   !string.IsNullOrWhiteSpace(Message) &&
                   DebtAmount > 0;
        }

        /// <summary>
        /// التحقق من إمكانية تعديل التذكير
        /// </summary>
        /// <returns>true إذا كان بإمكان التعديل</returns>
        public bool CanEdit()
        {
            return ReminderStatus == ReminderStatuses.Pending ||
                   ReminderStatus == ReminderStatuses.Failed;
        }

        /// <summary>
        /// التحقق من كون التذكير مرسلاً
        /// </summary>
        /// <returns>true إذا كان التذكير مرسلاً</returns>
        public bool IsSent()
        {
            return ReminderStatus == ReminderStatuses.Sent ||
                   ReminderStatus == ReminderStatuses.Delivered ||
                   ReminderStatus == ReminderStatuses.Responded;
        }

        /// <summary>
        /// التحقق من حاجة التذكير للمتابعة
        /// </summary>
        /// <returns>true إذا كان يحتاج متابعة</returns>
        public bool NeedsFollowUp()
        {
            return FollowUpDate.HasValue && 
                   FollowUpDate.Value <= DateTime.Now &&
                   ReminderStatus != ReminderStatuses.Responded;
        }

        #endregion

        #region عمليات النسخ والتحويل

        /// <summary>
        /// تحويل التذكير إلى نص وصفي
        /// </summary>
        /// <returns>وصف نصي للتذكير</returns>
        public override string ToString()
        {
            return $"تذكير رقم {ReminderNumber} - {ReminderType} - {DeliveryMethod} - {ReminderDate:yyyy/MM/dd}";
        }

        #endregion
    }

    #region الثوابت والتعدادات

    /// <summary>
    /// أنواع التذكيرات
    /// </summary>
    public static class ReminderTypes
    {
        public const string Friendly = "تذكير ودي";
        public const string FirstWarning = "إنذار أول";
        public const string FinalWarning = "إنذار أخير";
        public const string LegalNotice = "إشعار قانوني";
        public const string CourtNotice = "إشعار محكمة";
    }

    /// <summary>
    /// طرق الإرسال
    /// </summary>
    public static class DeliveryMethods
    {
        public const string SMS = "رسالة نصية";
        public const string Call = "اتصال هاتفي";
        public const string Email = "بريد إلكتروني";
        public const string WhatsApp = "واتساب";
        public const string Letter = "خطاب رسمي";
        public const string PersonalVisit = "زيارة شخصية";
    }

    /// <summary>
    /// حالات التذكيرات
    /// </summary>
    public static class ReminderStatuses
    {
        public const string Pending = "في الانتظار";
        public const string Sent = "مرسل";
        public const string Delivered = "مستلم";
        public const string Read = "مقروء";
        public const string Responded = "تم الرد";
        public const string Failed = "فشل الإرسال";
    }

    #endregion
}
