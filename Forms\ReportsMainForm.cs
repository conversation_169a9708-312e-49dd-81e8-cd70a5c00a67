using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using AredooPOS.Models.Reports;
using AredooPOS.Services;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// النموذج الرئيسي للتقارير
    /// يوفر واجهة شاملة لعرض وإنشاء جميع أنواع التقارير
    /// </summary>
    public partial class ReportsMainForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly ReportsService _reportsService;
        private readonly ILogger<ReportsMainForm> _logger;
        private readonly string _currentUser;

        private Dictionary<string, UserControl> _reportPanels;
        private string _selectedReportType;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ النموذج الرئيسي للتقارير
        /// </summary>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <param name="logger">مسجل الأحداث</param>
        public ReportsMainForm(string currentUser, ILogger<ReportsMainForm> logger = null)
        {
            InitializeComponent();
            
            _currentUser = currentUser;
            _logger = logger;
            _reportsService = new ReportsService(null, logger);

            InitializeForm();
            LoadReportTypes();
            SetupEventHandlers();
        }

        /// <summary>
        /// تهيئة النموذج
        /// </summary>
        private void InitializeForm()
        {
            // تعيين النصوص العربية
            this.Text = "التقارير";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            // تهيئة قاموس لوحات التقارير
            _reportPanels = new Dictionary<string, UserControl>();

            // تعيين الألوان والتنسيق
            SetupStyling();
        }

        /// <summary>
        /// تعيين التنسيق والألوان
        /// </summary>
        private void SetupStyling()
        {
            this.BackColor = Color.FromArgb(240, 240, 240);
            
            // تنسيق الشريط الجانبي
            pnlSidebar.BackColor = Color.FromArgb(45, 45, 48);
            pnlSidebar.Width = 250;

            // تنسيق المنطقة الرئيسية
            pnlMain.BackColor = Color.White;
            pnlMain.Padding = new Padding(20);

            // تنسيق شريط الأدوات
            toolStrip.BackColor = Color.FromArgb(230, 230, 230);
            statusStrip.BackColor = Color.FromArgb(230, 230, 230);
        }

        /// <summary>
        /// تحميل أنواع التقارير
        /// </summary>
        private void LoadReportTypes()
        {
            try
            {
                // مسح القائمة الحالية
                lvReportTypes.Items.Clear();

                // إضافة أنواع التقارير
                var reportTypes = new[]
                {
                    new { Name = "تقرير المبيعات", Type = "Sales", Icon = "📊", Description = "تقارير المبيعات اليومية والشهرية وحسب المنتج" },
                    new { Name = "تقرير المصاريف", Type = "Expense", Icon = "💰", Description = "تقارير المصاريف والنفقات التشغيلية" },
                    new { Name = "تقرير الأرباح", Type = "Profit", Icon = "📈", Description = "تقارير الأرباح والخسائر والهوامش الربحية" },
                    new { Name = "تقرير الأقساط", Type = "Installment", Icon = "📅", Description = "تقارير الأقساط المستحقة والمدفوعة" },
                    new { Name = "تقرير الديون", Type = "Debt", Icon = "📋", Description = "تقارير الديون والذمم المدينة" },
                    new { Name = "تقرير حركة الصندوق", Type = "CashFlow", Icon = "🏦", Description = "تقارير حركة النقدية والتدفقات المالية" }
                };

                foreach (var reportType in reportTypes)
                {
                    var item = new ListViewItem(reportType.Name)
                    {
                        Tag = reportType.Type,
                        ToolTipText = reportType.Description
                    };
                    
                    item.SubItems.Add(reportType.Description);
                    lvReportTypes.Items.Add(item);
                }

                // تحديد النوع الأول افتراضياً
                if (lvReportTypes.Items.Count > 0)
                {
                    lvReportTypes.Items[0].Selected = true;
                    LoadReportPanel(reportTypes[0].Type);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل أنواع التقارير");
                MessageBox.Show($"خطأ في تحميل أنواع التقارير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تعيين معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // أحداث قائمة أنواع التقارير
            lvReportTypes.SelectedIndexChanged += LvReportTypes_SelectedIndexChanged;
            lvReportTypes.DoubleClick += LvReportTypes_DoubleClick;

            // أحداث شريط الأدوات
            tsbGenerate.Click += TsbGenerate_Click;
            tsbExport.Click += TsbExport_Click;
            tsbPrint.Click += TsbPrint_Click;
            tsbRefresh.Click += TsbRefresh_Click;
            tsbSettings.Click += TsbSettings_Click;

            // أحداث القوائم
            mnuFilePrint.Click += MnuFilePrint_Click;
            mnuFileExport.Click += MnuFileExport_Click;
            mnuFileExit.Click += MnuFileExit_Click;
            mnuViewRefresh.Click += MnuViewRefresh_Click;
            mnuToolsSettings.Click += MnuToolsSettings_Click;
            mnuHelpAbout.Click += MnuHelpAbout_Click;

            // أحداث خدمة التقارير
            _reportsService.ReportGenerationStarted += ReportsService_ReportGenerationStarted;
            _reportsService.ReportGenerationCompleted += ReportsService_ReportGenerationCompleted;
            _reportsService.ReportGenerationFailed += ReportsService_ReportGenerationFailed;

            // أحداث النموذج
            this.Load += ReportsMainForm_Load;
            this.FormClosing += ReportsMainForm_FormClosing;
        }

        #endregion

        #region تحميل لوحات التقارير

        /// <summary>
        /// تحميل لوحة التقرير المحددة
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        private void LoadReportPanel(string reportType)
        {
            try
            {
                _selectedReportType = reportType;

                // إخفاء اللوحة الحالية
                foreach (Control control in pnlMain.Controls)
                {
                    if (control is UserControl)
                    {
                        control.Visible = false;
                    }
                }

                // الحصول على اللوحة أو إنشاؤها
                if (!_reportPanels.TryGetValue(reportType, out var panel))
                {
                    panel = CreateReportPanel(reportType);
                    if (panel != null)
                    {
                        _reportPanels[reportType] = panel;
                        pnlMain.Controls.Add(panel);
                        panel.Dock = DockStyle.Fill;
                    }
                }

                // إظهار اللوحة
                if (panel != null)
                {
                    panel.Visible = true;
                    panel.BringToFront();
                }

                // تحديث شريط الحالة
                UpdateStatusBar(reportType);

                _logger?.LogInformation($"تم تحميل لوحة تقرير {reportType}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحميل لوحة التقرير {reportType}");
                MessageBox.Show($"خطأ في تحميل لوحة التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إنشاء لوحة التقرير حسب النوع
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        /// <returns>لوحة التقرير</returns>
        private UserControl CreateReportPanel(string reportType)
        {
            return reportType switch
            {
                "Sales" => new SalesReportPanel(_reportsService, _currentUser, _logger),
                "Expense" => new ExpenseReportPanel(_reportsService, _currentUser, _logger),
                "Profit" => new ProfitReportPanel(_reportsService, _currentUser, _logger),
                "Installment" => new InstallmentReportPanel(_reportsService, _currentUser, _logger),
                "Debt" => new DebtReportPanel(_reportsService, _currentUser, _logger),
                "CashFlow" => new CashFlowReportPanel(_reportsService, _currentUser, _logger),
                _ => null
            };
        }

        /// <summary>
        /// تحديث شريط الحالة
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        private void UpdateStatusBar(string reportType)
        {
            var reportNames = new Dictionary<string, string>
            {
                { "Sales", "تقرير المبيعات" },
                { "Expense", "تقرير المصاريف" },
                { "Profit", "تقرير الأرباح" },
                { "Installment", "تقرير الأقساط" },
                { "Debt", "تقرير الديون" },
                { "CashFlow", "تقرير حركة الصندوق" }
            };

            tslStatus.Text = $"التقرير الحالي: {reportNames.GetValueOrDefault(reportType, "غير محدد")}";
            tslUser.Text = $"المستخدم: {_currentUser}";
            tslDateTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
        }

        #endregion

        #region أحداث واجهة المستخدم

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void ReportsMainForm_Load(object sender, EventArgs e)
        {
            try
            {
                // تحديث شريط الحالة
                tslUser.Text = $"المستخدم: {_currentUser}";
                tslDateTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
                tslStatus.Text = "جاهز";

                _logger?.LogInformation("تم تحميل النموذج الرئيسي للتقارير");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل النموذج");
            }
        }

        /// <summary>
        /// حدث تغيير التحديد في قائمة أنواع التقارير
        /// </summary>
        private void LvReportTypes_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (lvReportTypes.SelectedItems.Count > 0)
            {
                var selectedItem = lvReportTypes.SelectedItems[0];
                var reportType = selectedItem.Tag.ToString();
                LoadReportPanel(reportType);
            }
        }

        /// <summary>
        /// حدث النقر المزدوج على قائمة أنواع التقارير
        /// </summary>
        private void LvReportTypes_DoubleClick(object sender, EventArgs e)
        {
            if (lvReportTypes.SelectedItems.Count > 0)
            {
                TsbGenerate_Click(sender, e);
            }
        }

        /// <summary>
        /// حدث زر إنشاء التقرير
        /// </summary>
        private async void TsbGenerate_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(_selectedReportType))
                {
                    MessageBox.Show("يرجى اختيار نوع التقرير أولاً", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // الحصول على اللوحة الحالية وتنفيذ إنشاء التقرير
                if (_reportPanels.TryGetValue(_selectedReportType, out var panel))
                {
                    if (panel is IReportPanel reportPanel)
                    {
                        await reportPanel.GenerateReportAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنشاء التقرير");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث زر تصدير التقرير
        /// </summary>
        private void TsbExport_Click(object sender, EventArgs e)
        {
            try
            {
                if (_reportPanels.TryGetValue(_selectedReportType, out var panel))
                {
                    if (panel is IReportPanel reportPanel)
                    {
                        reportPanel.ExportReport();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تصدير التقرير");
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث زر طباعة التقرير
        /// </summary>
        private void TsbPrint_Click(object sender, EventArgs e)
        {
            try
            {
                if (_reportPanels.TryGetValue(_selectedReportType, out var panel))
                {
                    if (panel is IReportPanel reportPanel)
                    {
                        reportPanel.PrintReport();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في طباعة التقرير");
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث زر تحديث
        /// </summary>
        private void TsbRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                if (_reportPanels.TryGetValue(_selectedReportType, out var panel))
                {
                    if (panel is IReportPanel reportPanel)
                    {
                        reportPanel.RefreshReport();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحديث التقرير");
                MessageBox.Show($"خطأ في تحديث التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث زر الإعدادات
        /// </summary>
        private void TsbSettings_Click(object sender, EventArgs e)
        {
            try
            {
                var settingsForm = new ReportsSettingsForm();
                settingsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فتح إعدادات التقارير");
                MessageBox.Show($"خطأ في فتح الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region أحداث القوائم

        /// <summary>
        /// حدث قائمة ملف - طباعة
        /// </summary>
        private void MnuFilePrint_Click(object sender, EventArgs e)
        {
            TsbPrint_Click(sender, e);
        }

        /// <summary>
        /// حدث قائمة ملف - تصدير
        /// </summary>
        private void MnuFileExport_Click(object sender, EventArgs e)
        {
            TsbExport_Click(sender, e);
        }

        /// <summary>
        /// حدث قائمة ملف - خروج
        /// </summary>
        private void MnuFileExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// حدث قائمة عرض - تحديث
        /// </summary>
        private void MnuViewRefresh_Click(object sender, EventArgs e)
        {
            TsbRefresh_Click(sender, e);
        }

        /// <summary>
        /// حدث قائمة أدوات - إعدادات
        /// </summary>
        private void MnuToolsSettings_Click(object sender, EventArgs e)
        {
            TsbSettings_Click(sender, e);
        }

        /// <summary>
        /// حدث قائمة مساعدة - حول
        /// </summary>
        private void MnuHelpAbout_Click(object sender, EventArgs e)
        {
            var aboutForm = new AboutForm();
            aboutForm.ShowDialog();
        }

        #endregion

        #region أحداث خدمة التقارير

        /// <summary>
        /// حدث بدء إنشاء التقرير
        /// </summary>
        private void ReportsService_ReportGenerationStarted(object sender, ReportGenerationStartedEventArgs e)
        {
            try
            {
                // تحديث واجهة المستخدم
                this.Invoke(new Action(() =>
                {
                    tslStatus.Text = $"جاري إنشاء {e.ReportType}...";
                    progressBar.Visible = true;
                    progressBar.Style = ProgressBarStyle.Marquee;

                    // تعطيل الأزرار أثناء الإنشاء
                    tsbGenerate.Enabled = false;
                    tsbExport.Enabled = false;
                    tsbPrint.Enabled = false;
                }));

                _logger?.LogInformation($"بدء إنشاء تقرير {e.ReportType} بواسطة {e.RequestedBy}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة حدث بدء إنشاء التقرير");
            }
        }

        /// <summary>
        /// حدث اكتمال إنشاء التقرير
        /// </summary>
        private void ReportsService_ReportGenerationCompleted(object sender, ReportGenerationCompletedEventArgs e)
        {
            try
            {
                // تحديث واجهة المستخدم
                this.Invoke(new Action(() =>
                {
                    tslStatus.Text = $"تم إنشاء {e.ReportType} بنجاح";
                    progressBar.Visible = false;

                    // إعادة تفعيل الأزرار
                    tsbGenerate.Enabled = true;
                    tsbExport.Enabled = true;
                    tsbPrint.Enabled = true;

                    // إظهار رسالة نجاح
                    if (e.Result.IsSuccessful)
                    {
                        var message = $"تم إنشاء التقرير بنجاح\n";
                        if (!string.IsNullOrEmpty(e.Result.FilePath))
                        {
                            message += $"الملف: {e.Result.FilePath}\n";
                            message += $"الحجم: {e.Result.FileSize / 1024:N0} كيلوبايت";
                        }

                        MessageBox.Show(message, "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }));

                _logger?.LogInformation($"تم إنشاء تقرير {e.ReportType} بنجاح");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة حدث اكتمال إنشاء التقرير");
            }
        }

        /// <summary>
        /// حدث فشل إنشاء التقرير
        /// </summary>
        private void ReportsService_ReportGenerationFailed(object sender, ReportGenerationFailedEventArgs e)
        {
            try
            {
                // تحديث واجهة المستخدم
                this.Invoke(new Action(() =>
                {
                    tslStatus.Text = $"فشل في إنشاء {e.ReportType}";
                    progressBar.Visible = false;

                    // إعادة تفعيل الأزرار
                    tsbGenerate.Enabled = true;
                    tsbExport.Enabled = true;
                    tsbPrint.Enabled = true;

                    // إظهار رسالة خطأ
                    MessageBox.Show($"فشل في إنشاء التقرير:\n{e.ErrorMessage}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }));

                _logger?.LogError($"فشل في إنشاء تقرير {e.ReportType}: {e.ErrorMessage}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة حدث فشل إنشاء التقرير");
            }
        }

        #endregion

        #region أحداث أخرى

        /// <summary>
        /// حدث إغلاق النموذج
        /// </summary>
        private void ReportsMainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // التحقق من وجود تقارير قيد الإنشاء
                if (progressBar.Visible)
                {
                    var result = MessageBox.Show(
                        "يوجد تقرير قيد الإنشاء. هل تريد إغلاق النموذج؟",
                        "تأكيد الإغلاق",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.No)
                    {
                        e.Cancel = true;
                        return;
                    }
                }

                // تنظيف الموارد
                foreach (var panel in _reportPanels.Values)
                {
                    panel?.Dispose();
                }
                _reportPanels.Clear();

                _logger?.LogInformation("تم إغلاق النموذج الرئيسي للتقارير");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إغلاق النموذج");
            }
        }

        /// <summary>
        /// تحديث مؤقت لشريط الحالة
        /// </summary>
        private void Timer_Tick(object sender, EventArgs e)
        {
            tslDateTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// تحديث حالة الأزرار
        /// </summary>
        /// <param name="enabled">تفعيل أم لا</param>
        private void UpdateButtonsState(bool enabled)
        {
            tsbGenerate.Enabled = enabled;
            tsbExport.Enabled = enabled;
            tsbPrint.Enabled = enabled;
            tsbRefresh.Enabled = enabled;
        }

        /// <summary>
        /// إظهار رسالة في شريط الحالة
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="isError">هل هي رسالة خطأ</param>
        private void ShowStatusMessage(string message, bool isError = false)
        {
            tslStatus.Text = message;
            tslStatus.ForeColor = isError ? Color.Red : Color.Black;
        }

        /// <summary>
        /// الحصول على اللوحة الحالية
        /// </summary>
        /// <returns>اللوحة الحالية</returns>
        private IReportPanel GetCurrentReportPanel()
        {
            if (string.IsNullOrEmpty(_selectedReportType))
                return null;

            if (_reportPanels.TryGetValue(_selectedReportType, out var panel))
            {
                return panel as IReportPanel;
            }

            return null;
        }

        /// <summary>
        /// تصدير جميع التقارير
        /// </summary>
        private async void ExportAllReports()
        {
            try
            {
                var exportDialog = new BulkExportDialog();
                if (exportDialog.ShowDialog() == DialogResult.OK)
                {
                    ShowStatusMessage("جاري تصدير جميع التقارير...");
                    progressBar.Visible = true;

                    // TODO: تنفيذ تصدير جميع التقارير
                    await Task.Delay(3000); // محاكاة وقت المعالجة

                    ShowStatusMessage("تم تصدير جميع التقارير بنجاح");
                    progressBar.Visible = false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تصدير جميع التقارير");
                ShowStatusMessage("فشل في تصدير التقارير", true);
                progressBar.Visible = false;
            }
        }

        #endregion
    }

    #region واجهة لوحة التقرير

    /// <summary>
    /// واجهة لوحة التقرير
    /// </summary>
    public interface IReportPanel
    {
        Task GenerateReportAsync();
        void ExportReport();
        void PrintReport();
        void RefreshReport();
    }

    #endregion

    #region النماذج المساعدة

    /// <summary>
    /// نموذج حول البرنامج
    /// </summary>
    public partial class AboutForm : Form
    {
        public AboutForm()
        {
            InitializeComponent();
            this.Text = "حول البرنامج";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
        }
    }

    /// <summary>
    /// نموذج إعدادات التقارير
    /// </summary>
    public partial class ReportsSettingsForm : Form
    {
        public ReportsSettingsForm()
        {
            InitializeComponent();
            this.Text = "إعدادات التقارير";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
        }
    }

    /// <summary>
    /// نموذج التصدير المجمع
    /// </summary>
    public partial class BulkExportDialog : Form
    {
        public BulkExportDialog()
        {
            InitializeComponent();
            this.Text = "تصدير مجمع للتقارير";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
        }
    }

    #endregion
}
