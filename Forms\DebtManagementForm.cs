using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AredooPOS.Models;
using AredooPOS.BLL;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// واجهة إدارة الديون والتحصيل
    /// توفر واجهة شاملة لإدارة ديون العملاء وعمليات التحصيل
    /// </summary>
    public partial class DebtManagementForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly DebtBLL _debtBLL;
        private readonly CustomerBLL _customerBLL;
        private readonly ILogger<DebtManagementForm> _logger;

        // ألوان النظام
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SuccessColor = Color.FromArgb(46, 204, 113);
        private readonly Color WarningColor = Color.FromArgb(241, 196, 15);
        private readonly Color DangerColor = Color.FromArgb(231, 76, 60);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);

        // البيانات الحالية
        private Debt _selectedDebt;
        private Customer _selectedCustomer;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ واجهة إدارة الديون
        /// </summary>
        /// <param name="debtBLL">طبقة منطق الأعمال للديون</param>
        /// <param name="customerBLL">طبقة منطق الأعمال للعملاء</param>
        /// <param name="logger">مسجل الأحداث</param>
        public DebtManagementForm(DebtBLL debtBLL, CustomerBLL customerBLL, ILogger<DebtManagementForm> logger = null)
        {
            _debtBLL = debtBLL ?? throw new ArgumentNullException(nameof(debtBLL));
            _customerBLL = customerBLL ?? throw new ArgumentNullException(nameof(customerBLL));
            _logger = logger;

            InitializeComponent();
            InitializeArabicUI();
            LoadInitialData();
            SetupEventHandlers();
        }

        /// <summary>
        /// تهيئة الواجهة العربية
        /// </summary>
        private void InitializeArabicUI()
        {
            // إعدادات النموذج الأساسية
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "إدارة الديون والتحصيل";
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = LightGray;

            // تطبيق الألوان والأنماط
            ApplyThemeColors();
            UpdateUITexts();
        }

        /// <summary>
        /// تطبيق ألوان النظام
        /// </summary>
        private void ApplyThemeColors()
        {
            // شريط العنوان
            pnlHeader.BackColor = PrimaryColor;
            lblTitle.ForeColor = Color.White;

            // أزرار العمليات
            btnAddDebt.BackColor = SuccessColor;
            btnAddDebt.ForeColor = Color.White;
            btnAddDebt.FlatStyle = FlatStyle.Flat;

            btnRecordPayment.BackColor = PrimaryColor;
            btnRecordPayment.ForeColor = Color.White;
            btnRecordPayment.FlatStyle = FlatStyle.Flat;

            btnSendReminder.BackColor = WarningColor;
            btnSendReminder.ForeColor = Color.White;
            btnSendReminder.FlatStyle = FlatStyle.Flat;

            btnCancelDebt.BackColor = DangerColor;
            btnCancelDebt.ForeColor = Color.White;
            btnCancelDebt.FlatStyle = FlatStyle.Flat;

            // شبكة البيانات
            dgvDebts.BackgroundColor = Color.White;
            dgvDebts.GridColor = LightGray;
            dgvDebts.DefaultCellStyle.Font = new Font("Tahoma", 9F);
            dgvDebts.ColumnHeadersDefaultCellStyle.BackColor = PrimaryColor;
            dgvDebts.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvDebts.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 9F, FontStyle.Bold);

            // لوحة الإحصائيات
            pnlStats.BackColor = Color.White;
            pnlStats.BorderStyle = BorderStyle.FixedSingle;
        }

        /// <summary>
        /// تحديث النصوص في الواجهة
        /// </summary>
        private void UpdateUITexts()
        {
            lblTitle.Text = "إدارة الديون والتحصيل";
            
            // أزرار العمليات
            btnAddDebt.Text = "إضافة دين جديد";
            btnRecordPayment.Text = "تسجيل دفعة";
            btnSendReminder.Text = "إرسال تذكير";
            btnCancelDebt.Text = "إلغاء دين";
            btnRefresh.Text = "تحديث";
            btnSearch.Text = "بحث";

            // مجموعات البحث
            grpSearch.Text = "البحث والتصفية";
            lblCustomerName.Text = "اسم العميل:";
            lblDebtStatus.Text = "حالة الدين:";
            lblDateRange.Text = "الفترة الزمنية:";
            chkOverdueOnly.Text = "الديون المتأخرة فقط";
            chkOutstandingOnly.Text = "الديون المستحقة فقط";

            // لوحة الإحصائيات
            grpStats.Text = "ملخص الديون";
            lblTotalDebts.Text = "إجمالي الديون: 0";
            lblTotalAmount.Text = "إجمالي المبلغ: 0.00 ر.س";
            lblOverdueDebts.Text = "الديون المتأخرة: 0";
            lblOverdueAmount.Text = "مبلغ الديون المتأخرة: 0.00 ر.س";

            // أعمدة الشبكة
            SetupDataGridColumns();
        }

        /// <summary>
        /// إعداد أعمدة شبكة البيانات
        /// </summary>
        private void SetupDataGridColumns()
        {
            dgvDebts.Columns.Clear();
            dgvDebts.AutoGenerateColumns = false;

            // رقم الدين
            dgvDebts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DebtNumber",
                HeaderText = "رقم الدين",
                DataPropertyName = "DebtNumber",
                Width = 120,
                ReadOnly = true
            });

            // اسم العميل
            dgvDebts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CustomerName",
                HeaderText = "اسم العميل",
                DataPropertyName = "CustomerName",
                Width = 200,
                ReadOnly = true
            });

            // تاريخ الدين
            dgvDebts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DebtDate",
                HeaderText = "تاريخ الدين",
                DataPropertyName = "DebtDate",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = { Format = "yyyy/MM/dd" }
            });

            // تاريخ الاستحقاق
            dgvDebts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DueDate",
                HeaderText = "تاريخ الاستحقاق",
                DataPropertyName = "DueDate",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = { Format = "yyyy/MM/dd" }
            });

            // المبلغ الأصلي
            dgvDebts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "OriginalAmount",
                HeaderText = "المبلغ الأصلي",
                DataPropertyName = "OriginalAmount",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = { Format = "C", Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            // المبلغ المدفوع
            dgvDebts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PaidAmount",
                HeaderText = "المبلغ المدفوع",
                DataPropertyName = "PaidAmount",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = { Format = "C", Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            // المبلغ المتبقي
            dgvDebts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "RemainingAmount",
                HeaderText = "المبلغ المتبقي",
                DataPropertyName = "RemainingAmount",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = { Format = "C", Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            // أيام التأخير
            dgvDebts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "OverdueDays",
                HeaderText = "أيام التأخير",
                DataPropertyName = "OverdueDays",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // حالة الدين
            dgvDebts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DebtStatus",
                HeaderText = "الحالة",
                DataPropertyName = "DebtStatus",
                Width = 100,
                ReadOnly = true
            });

            // الأولوية
            dgvDebts.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Priority",
                HeaderText = "الأولوية",
                DataPropertyName = "Priority",
                Width = 80,
                ReadOnly = true
            });
        }

        /// <summary>
        /// تحميل البيانات الأولية
        /// </summary>
        private void LoadInitialData()
        {
            try
            {
                // تحميل حالات الديون
                cmbDebtStatus.Items.Clear();
                cmbDebtStatus.Items.Add("الكل");
                cmbDebtStatus.Items.Add(DebtStatuses.Outstanding);
                cmbDebtStatus.Items.Add(DebtStatuses.PartiallyPaid);
                cmbDebtStatus.Items.Add(DebtStatuses.FullyPaid);
                cmbDebtStatus.Items.Add(DebtStatuses.Overdue);
                cmbDebtStatus.Items.Add(DebtStatuses.Cancelled);
                cmbDebtStatus.SelectedIndex = 0;

                // تعيين التواريخ الافتراضية
                dtpFromDate.Value = DateTime.Now.AddMonths(-1);
                dtpToDate.Value = DateTime.Now;

                // تحميل الديون
                LoadDebts();

                // تحديث الإحصائيات
                UpdateStatistics();

                _logger?.LogInformation("تم تحميل البيانات الأولية لواجهة إدارة الديون");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل البيانات الأولية");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // أحداث الأزرار
            btnAddDebt.Click += BtnAddDebt_Click;
            btnRecordPayment.Click += BtnRecordPayment_Click;
            btnSendReminder.Click += BtnSendReminder_Click;
            btnCancelDebt.Click += BtnCancelDebt_Click;
            btnRefresh.Click += BtnRefresh_Click;
            btnSearch.Click += BtnSearch_Click;

            // أحداث الشبكة
            dgvDebts.SelectionChanged += DgvDebts_SelectionChanged;
            dgvDebts.CellDoubleClick += DgvDebts_CellDoubleClick;
            dgvDebts.CellFormatting += DgvDebts_CellFormatting;

            // أحداث البحث
            txtCustomerName.TextChanged += SearchCriteria_Changed;
            cmbDebtStatus.SelectedIndexChanged += SearchCriteria_Changed;
            chkOverdueOnly.CheckedChanged += SearchCriteria_Changed;
            chkOutstandingOnly.CheckedChanged += SearchCriteria_Changed;

            // أحداث النموذج
            this.Load += DebtManagementForm_Load;
            this.KeyDown += DebtManagementForm_KeyDown;
            this.KeyPreview = true;
        }

        #endregion

        #region تحميل وعرض البيانات

        /// <summary>
        /// تحميل الديون حسب معايير البحث
        /// </summary>
        private void LoadDebts()
        {
            try
            {
                var criteria = BuildSearchCriteria();
                var debts = _debtBLL.SearchDebts(criteria);

                dgvDebts.DataSource = debts;
                
                // تحديث عدد النتائج
                lblResultsCount.Text = $"عدد النتائج: {debts.Count}";

                _logger?.LogInformation("تم تحميل {Count} دين", debts.Count);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل الديون");
                MessageBox.Show($"خطأ في تحميل الديون: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// بناء معايير البحث من الواجهة
        /// </summary>
        /// <returns>معايير البحث</returns>
        private DebtSearchCriteria BuildSearchCriteria()
        {
            var criteria = new DebtSearchCriteria();

            // اسم العميل
            if (!string.IsNullOrWhiteSpace(txtCustomerName.Text))
            {
                criteria.CustomerName = txtCustomerName.Text.Trim();
            }

            // حالة الدين
            if (cmbDebtStatus.SelectedIndex > 0)
            {
                criteria.DebtStatus = cmbDebtStatus.SelectedItem.ToString();
            }

            // الفترة الزمنية
            criteria.FromDate = dtpFromDate.Value.Date;
            criteria.ToDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

            // الديون المتأخرة فقط
            criteria.OnlyOverdue = chkOverdueOnly.Checked;

            // الديون المستحقة فقط
            criteria.OnlyOutstanding = chkOutstandingOnly.Checked;

            // حد النتائج
            criteria.Limit = 1000;

            return criteria;
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private void UpdateStatistics()
        {
            try
            {
                var summary = _debtBLL.GetDebtSummary();

                lblTotalDebts.Text = $"إجمالي الديون: {summary.TotalOutstandingDebts:N0}";
                lblTotalAmount.Text = $"إجمالي المبلغ: {summary.TotalOutstandingAmount:C}";
                lblOverdueDebts.Text = $"الديون المتأخرة: {summary.TotalOverdueDebts:N0}";
                lblOverdueAmount.Text = $"مبلغ الديون المتأخرة: {summary.TotalOverdueAmount:C}";

                // تلوين الإحصائيات حسب الحالة
                if (summary.TotalOverdueDebts > 0)
                {
                    lblOverdueDebts.ForeColor = DangerColor;
                    lblOverdueAmount.ForeColor = DangerColor;
                }
                else
                {
                    lblOverdueDebts.ForeColor = SuccessColor;
                    lblOverdueAmount.ForeColor = SuccessColor;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحديث الإحصائيات");
            }
        }

        #endregion

        #region معالجات الأحداث

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void DebtManagementForm_Load(object sender, EventArgs e)
        {
            _logger?.LogInformation("تم تحميل واجهة إدارة الديون");
        }

        /// <summary>
        /// حدث الضغط على مفاتيح الاختصار
        /// </summary>
        private void DebtManagementForm_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.F1:
                    BtnAddDebt_Click(sender, e);
                    break;
                case Keys.F2:
                    BtnRecordPayment_Click(sender, e);
                    break;
                case Keys.F3:
                    BtnSendReminder_Click(sender, e);
                    break;
                case Keys.F5:
                    BtnRefresh_Click(sender, e);
                    break;
                case Keys.Escape:
                    this.Close();
                    break;
            }
        }

        /// <summary>
        /// حدث تغيير الاختيار في الشبكة
        /// </summary>
        private void DgvDebts_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvDebts.CurrentRow?.DataBoundItem is Debt selectedDebt)
            {
                _selectedDebt = selectedDebt;
                UpdateButtonStates();
            }
        }

        /// <summary>
        /// حدث النقر المزدوج على الشبكة
        /// </summary>
        private void DgvDebts_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && _selectedDebt != null)
            {
                ShowDebtDetails();
            }
        }

        /// <summary>
        /// حدث تنسيق خلايا الشبكة
        /// </summary>
        private void DgvDebts_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvDebts.Rows[e.RowIndex].DataBoundItem is Debt debt)
            {
                // تلوين الصفوف حسب حالة الدين
                if (debt.IsOverdue())
                {
                    e.CellStyle.BackColor = Color.FromArgb(255, 235, 235); // أحمر فاتح
                    e.CellStyle.ForeColor = DangerColor;
                }
                else if (debt.DebtStatus == DebtStatuses.PartiallyPaid)
                {
                    e.CellStyle.BackColor = Color.FromArgb(255, 248, 220); // أصفر فاتح
                    e.CellStyle.ForeColor = Color.FromArgb(138, 109, 59);
                }
                else if (debt.DebtStatus == DebtStatuses.FullyPaid)
                {
                    e.CellStyle.BackColor = Color.FromArgb(235, 255, 235); // أخضر فاتح
                    e.CellStyle.ForeColor = SuccessColor;
                }
            }
        }

        /// <summary>
        /// حدث تغيير معايير البحث
        /// </summary>
        private void SearchCriteria_Changed(object sender, EventArgs e)
        {
            // تأخير البحث لتجنب البحث المتكرر
            searchTimer.Stop();
            searchTimer.Start();
        }

        #endregion

        #region عمليات الأزرار

        /// <summary>
        /// إضافة دين جديد
        /// </summary>
        private void BtnAddDebt_Click(object sender, EventArgs e)
        {
            try
            {
                var addDebtForm = new AddDebtForm(_debtBLL, _customerBLL);
                if (addDebtForm.ShowDialog() == DialogResult.OK)
                {
                    LoadDebts();
                    UpdateStatistics();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فتح نافذة إضافة دين");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تسجيل دفعة
        /// </summary>
        private void BtnRecordPayment_Click(object sender, EventArgs e)
        {
            if (_selectedDebt == null)
            {
                MessageBox.Show("يرجى اختيار دين أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var paymentForm = new RecordPaymentForm(_debtBLL, _selectedDebt);
                if (paymentForm.ShowDialog() == DialogResult.OK)
                {
                    LoadDebts();
                    UpdateStatistics();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فتح نافذة تسجيل الدفعة");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إرسال تذكير
        /// </summary>
        private void BtnSendReminder_Click(object sender, EventArgs e)
        {
            if (_selectedDebt == null)
            {
                MessageBox.Show("يرجى اختيار دين أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var reminderForm = new SendReminderForm(_selectedDebt);
                if (reminderForm.ShowDialog() == DialogResult.OK)
                {
                    LoadDebts();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فتح نافذة إرسال التذكير");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء دين
        /// </summary>
        private void BtnCancelDebt_Click(object sender, EventArgs e)
        {
            if (_selectedDebt == null)
            {
                MessageBox.Show("يرجى اختيار دين أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!_selectedDebt.CanCancel())
            {
                MessageBox.Show("لا يمكن إلغاء هذا الدين", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show(
                $"هل تريد إلغاء الدين رقم {_selectedDebt.DebtNumber}؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الإلغاء",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    var reason = Microsoft.VisualBasic.Interaction.InputBox(
                        "يرجى إدخال سبب الإلغاء:", "سبب الإلغاء", "");

                    if (!string.IsNullOrWhiteSpace(reason))
                    {
                        var success = _debtBLL.CancelDebt(_selectedDebt.DebtID, reason);
                        if (success)
                        {
                            MessageBox.Show("تم إلغاء الدين بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadDebts();
                            UpdateStatistics();
                        }
                        else
                        {
                            MessageBox.Show("فشل في إلغاء الدين", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "خطأ في إلغاء الدين");
                    MessageBox.Show($"خطأ في إلغاء الدين: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadDebts();
            UpdateStatistics();
        }

        /// <summary>
        /// البحث
        /// </summary>
        private void BtnSearch_Click(object sender, EventArgs e)
        {
            LoadDebts();
        }

        #endregion

        #region عمليات مساعدة

        /// <summary>
        /// تحديث حالة الأزرار
        /// </summary>
        private void UpdateButtonStates()
        {
            var hasSelection = _selectedDebt != null;
            
            btnRecordPayment.Enabled = hasSelection && _selectedDebt?.CanEdit() == true;
            btnSendReminder.Enabled = hasSelection && _selectedDebt?.RemainingAmount > 0;
            btnCancelDebt.Enabled = hasSelection && _selectedDebt?.CanCancel() == true;
        }

        /// <summary>
        /// عرض تفاصيل الدين
        /// </summary>
        private void ShowDebtDetails()
        {
            try
            {
                var detailsForm = new DebtDetailsForm(_selectedDebt);
                detailsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في عرض تفاصيل الدين");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
