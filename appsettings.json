{"ApplicationSettings": {"ApplicationName": "أريدوو - نظام نقاط البيع", "Version": "1.0.0", "CompanyName": "أريدوو", "SupportEmail": "<EMAIL>", "SupportPhone": "+966 XX XXX XXXX"}, "DatabaseSettings": {"DatabaseType": "SQLite", "ConnectionString": "Data Source=Database/AredooPOS.db;Version=3;", "BackupEnabled": true, "BackupInterval": 24, "BackupPath": "Backup/", "MaxBackupFiles": 30}, "CompanyInfo": {"CompanyName": "أريدوو", "CompanyNameEn": "Aredoo", "Address": "المملكة العربية السعودية", "AddressEn": "Saudi Arabia", "Phone": "+966 XX XXX XXXX", "Email": "<EMAIL>", "Website": "www.aredoo.com", "TaxNumber": "*********", "CommercialRegister": "*********0", "Logo": "Resources/logo.png"}, "TaxSettings": {"DefaultTaxRate": 15.0, "TaxIncluded": false, "TaxNumber": "*********", "TaxName": "ضريبة القيمة المضافة", "TaxNameEn": "VAT"}, "CurrencySettings": {"CurrencyCode": "SAR", "CurrencyName": "ريال سعودي", "CurrencyNameEn": "Saudi Riyal", "CurrencySymbol": "ر.س", "CurrencySymbolEn": "SAR", "DecimalPlaces": 2, "ThousandsSeparator": ",", "DecimalSeparator": "."}, "InvoiceSettings": {"InvoicePrefix": "INV", "InvoiceNumberLength": 8, "InvoiceStartNumber": 1, "AutoGenerateNumber": true, "AllowEdit": true, "AllowDelete": false, "RequireCustomer": false, "DefaultPaymentType": "نقد", "PrintAfterSave": true, "ShowPreview": true}, "PrintSettings": {"DefaultPrinter": "", "PrinterType": "Thermal", "PaperSize": "80mm", "PrintCopies": 1, "PrintHeader": true, "PrintFooter": true, "PrintLogo": true, "PrintBarcode": false, "FontName": "<PERSON><PERSON><PERSON>", "FontSize": 10, "HeaderFontSize": 12, "FooterText": "شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى"}, "SecuritySettings": {"RequireLogin": true, "SessionTimeout": 480, "PasswordMinLength": 6, "PasswordRequireNumbers": false, "PasswordRequireSymbols": false, "MaxLoginAttempts": 3, "LockoutDuration": 15, "EnableAuditLog": true}, "UISettings": {"Language": "ar-SA", "Theme": "<PERSON><PERSON><PERSON>", "RightToLeft": true, "FontName": "<PERSON><PERSON><PERSON>", "FontSize": 9, "ShowToolTips": true, "ShowStatusBar": true, "ShowToolbar": true, "AutoRefresh": true, "RefreshInterval": 60}, "InventorySettings": {"TrackInventory": true, "AllowNegativeStock": false, "LowStockWarning": true, "LowStockThreshold": 10, "AutoUpdateCost": true, "CostMethod": "FIFO", "RequireSerialNumbers": false, "TrackExpiryDates": false}, "CustomerSettings": {"RequireCustomerCode": true, "AutoGenerateCode": true, "CodePrefix": "CUST", "DefaultCreditLimit": 0, "DefaultPaymentTerms": "نقدي", "RequirePhone": false, "RequireAddress": false, "AllowDuplicateNames": false}, "ReportSettings": {"DefaultDateRange": "Today", "ShowZeroValues": false, "GroupByCategory": true, "IncludeInactive": false, "ExportFormat": "PDF", "AutoSave": false, "SavePath": "Reports/"}, "BackupSettings": {"AutoBackup": true, "BackupTime": "23:00", "BackupPath": "Backup/", "CompressBackup": true, "EmailBackup": false, "BackupEmail": "", "RetentionDays": 30}, "NotificationSettings": {"ShowNotifications": true, "LowStockNotifications": true, "PaymentDueNotifications": true, "SystemNotifications": true, "SoundEnabled": true, "NotificationDuration": 5000}, "IntegrationSettings": {"EnableAPI": false, "APIPort": 8080, "APIKey": "", "EnableWebhooks": false, "WebhookURL": "", "SyncEnabled": false, "SyncInterval": 60}, "LoggingSettings": {"LogLevel": "Information", "LogToFile": true, "LogPath": "Logs/", "MaxLogFileSize": "10MB", "MaxLogFiles": 10, "LogFormat": "json", "EnableConsoleLogging": false}, "PerformanceSettings": {"CacheEnabled": true, "CacheTimeout": 300, "MaxCacheSize": "100MB", "DatabaseTimeout": 30, "QueryTimeout": 30, "EnableProfiling": false}, "FeatureFlags": {"EnableInstallments": true, "EnableReturns": true, "EnableDiscounts": true, "EnableMultiCurrency": false, "EnableBarcode": true, "EnableTouchScreen": false, "EnableOfflineMode": false, "EnableAdvancedReports": true}}