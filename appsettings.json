{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\MSSQLLocalDB;Database=AredooPOS;Trusted_Connection=true;MultipleActiveResultSets=true;", "BackupConnection": "Server=.\\SQLEXPRESS;Database=AredooPOS;Integrated Security=true;MultipleActiveResultSets=true;"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:HH:mm:ss} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "Logs/aredoo-pos-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 10485760, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "ApplicationSettings": {"ApplicationName": "أريدو POS", "Version": "1.0.0", "CompanyName": "أريدو", "SupportEmail": "<EMAIL>", "SupportPhone": "+966 XX XXX XXXX", "Culture": "ar-SA", "Theme": "<PERSON><PERSON><PERSON>", "AutoBackup": true, "BackupInterval": 24, "MaxBackupFiles": 30, "EnableSync": true, "SyncInterval": 60, "EnableAuditLog": true}, "CompanyInfo": {"CompanyName": "أريدوو", "CompanyNameEn": "Aredoo", "Address": "المملكة العربية السعودية", "AddressEn": "Saudi Arabia", "Phone": "+966 XX XXX XXXX", "Email": "<EMAIL>", "Website": "www.aredoo.com", "TaxNumber": "*********", "CommercialRegister": "*********0", "Logo": "Resources/logo.png"}, "TaxSettings": {"DefaultTaxRate": 15.0, "TaxIncluded": false, "TaxNumber": "*********", "TaxName": "ضريبة القيمة المضافة", "TaxNameEn": "VAT"}, "CurrencySettings": {"CurrencyCode": "SAR", "CurrencyName": "ريال سعودي", "CurrencyNameEn": "Saudi Riyal", "CurrencySymbol": "ر.س", "CurrencySymbolEn": "SAR", "DecimalPlaces": 2, "ThousandsSeparator": ",", "DecimalSeparator": "."}, "InvoiceSettings": {"InvoicePrefix": "INV", "InvoiceNumberLength": 8, "InvoiceStartNumber": 1, "AutoGenerateNumber": true, "AllowEdit": true, "AllowDelete": false, "RequireCustomer": false, "DefaultPaymentType": "نقد", "PrintAfterSave": true, "ShowPreview": true}, "PrinterSettings": {"DefaultPrinter": "", "ThermalPrinter": {"Enabled": true, "ConnectionType": "USB", "PrinterName": "", "IPAddress": "*************", "Port": 9100, "SerialPort": "COM1", "BaudRate": 9600, "PaperWidth": 80, "CharactersPerLine": 48, "PrintStoreLogo": true, "PrintStoreInfo": true, "PrintCustomerInfo": true, "PrintBarcode": true, "PrintThankYouMessage": true, "PrintPaymentMethod": true, "AutoCutPaper": true, "OpenCashDrawer": true, "CustomThankYouMessage": "شكراً لزيارتكم - أريدو"}, "A4Printer": {"Enabled": false, "PrinterName": "", "Orientation": "Portrait", "PaperSize": "A4"}}, "BarcodeSettings": {"DefaultType": "Code128", "DefaultWidth": 300, "DefaultHeight": 100, "IncludeTextByDefault": true, "ProductPrefix": "PRD", "CustomerPrefix": "CUS", "InvoicePrefix": "INV", "SaveDirectory": "Barcodes", "DefaultSaveFormat": "PNG", "AutoGenerateProductCodes": true, "CodeLength": 10}, "POSSettings": {"AllowNegativeStock": false, "RequireCustomerForCredit": true, "AllowPartialPayments": true, "AutoCalculateChange": true, "RoundToNearestCent": true, "DefaultPaymentMethod": "Cash", "RequireReasonForDiscount": true, "MaxDiscountPercent": 50.0, "AllowZeroPriceItems": false, "ShowProductImages": true, "EnableQuickSale": true, "EnableLayaway": true, "EnableReturns": true, "ReturnPeriodDays": 30, "RequireReceiptForReturn": true}, "SecuritySettings": {"RequireLogin": true, "SessionTimeout": 480, "PasswordMinLength": 6, "PasswordRequireNumbers": false, "PasswordRequireSymbols": false, "MaxLoginAttempts": 3, "LockoutDuration": 15, "EnableAuditLog": true}, "UISettings": {"Language": "ar-SA", "Theme": "<PERSON><PERSON><PERSON>", "RightToLeft": true, "FontName": "<PERSON><PERSON><PERSON>", "FontSize": 9, "ShowToolTips": true, "ShowStatusBar": true, "ShowToolbar": true, "AutoRefresh": true, "RefreshInterval": 60}, "InventorySettings": {"TrackInventory": true, "AllowNegativeStock": false, "LowStockWarning": true, "LowStockThreshold": 10, "AutoUpdateCost": true, "CostMethod": "FIFO", "RequireSerialNumbers": false, "TrackExpiryDates": false}, "CustomerSettings": {"RequireCustomerCode": true, "AutoGenerateCode": true, "CodePrefix": "CUST", "DefaultCreditLimit": 0, "DefaultPaymentTerms": "نقدي", "RequirePhone": false, "RequireAddress": false, "AllowDuplicateNames": false}, "ReportSettings": {"DefaultDateRange": "Today", "ShowZeroValues": false, "GroupByCategory": true, "IncludeInactive": false, "ExportFormat": "PDF", "AutoSave": false, "SavePath": "Reports/"}, "BackupSettings": {"AutoBackup": true, "BackupTime": "23:00", "BackupPath": "Backup/", "CompressBackup": true, "EmailBackup": false, "BackupEmail": "", "RetentionDays": 30}, "NotificationSettings": {"ShowNotifications": true, "LowStockNotifications": true, "PaymentDueNotifications": true, "SystemNotifications": true, "SoundEnabled": true, "NotificationDuration": 5000}, "IntegrationSettings": {"EnableAPI": false, "APIPort": 8080, "APIKey": "", "EnableWebhooks": false, "WebhookURL": "", "SyncEnabled": false, "SyncInterval": 60}, "LoggingSettings": {"LogLevel": "Information", "LogToFile": true, "LogPath": "Logs/", "MaxLogFileSize": "10MB", "MaxLogFiles": 10, "LogFormat": "json", "EnableConsoleLogging": false}, "PerformanceSettings": {"CacheEnabled": true, "CacheTimeout": 300, "MaxCacheSize": "100MB", "DatabaseTimeout": 30, "QueryTimeout": 30, "EnableProfiling": false}, "FeatureFlags": {"EnableInstallments": true, "EnableReturns": true, "EnableDiscounts": true, "EnableMultiCurrency": false, "EnableBarcode": true, "EnableTouchScreen": false, "EnableOfflineMode": false, "EnableAdvancedReports": true}}