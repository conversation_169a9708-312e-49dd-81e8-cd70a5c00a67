using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;
using AredooPOS.Models;
using AredooPOS.BLL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Services
{
    /// <summary>
    /// خدمة التنبيهات للمخزون
    /// تقوم بمراقبة المخزون وإرسال التنبيهات عند انخفاض الكميات
    /// </summary>
    public class AlertService : IDisposable
    {
        #region المتغيرات والخصائص الخاصة

        private readonly ProductBLL _productBLL;
        private readonly StockBLL _stockBLL;
        private readonly ILogger<AlertService> _logger;
        private readonly Timer _alertTimer;
        private readonly AlertSettings _settings;

        // قائمة المراقبين للتنبيهات
        private readonly List<IAlertObserver> _observers;

        // حالة الخدمة
        private bool _isRunning;
        private bool _disposed;

        #endregion

        #region الأحداث

        /// <summary>
        /// حدث عند اكتشاف منتجات منخفضة المخزون
        /// </summary>
        public event EventHandler<LowStockAlertEventArgs> LowStockDetected;

        /// <summary>
        /// حدث عند اكتشاف منتجات نافدة المخزون
        /// </summary>
        public event EventHandler<OutOfStockAlertEventArgs> OutOfStockDetected;

        /// <summary>
        /// حدث عند اكتشاف منتجات منتهية الصلاحية
        /// </summary>
        public event EventHandler<ExpiryAlertEventArgs> ExpiryDetected;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ خدمة التنبيهات
        /// </summary>
        /// <param name="productBLL">طبقة منطق الأعمال للمنتجات</param>
        /// <param name="stockBLL">طبقة منطق الأعمال للمخزون</param>
        /// <param name="settings">إعدادات التنبيهات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public AlertService(ProductBLL productBLL, StockBLL stockBLL, AlertSettings settings = null, ILogger<AlertService> logger = null)
        {
            _productBLL = productBLL ?? throw new ArgumentNullException(nameof(productBLL));
            _stockBLL = stockBLL ?? throw new ArgumentNullException(nameof(stockBLL));
            _settings = settings ?? new AlertSettings();
            _logger = logger;
            _observers = new List<IAlertObserver>();

            // إعداد مؤقت التنبيهات
            _alertTimer = new Timer(_settings.CheckIntervalMinutes * 60 * 1000); // تحويل إلى ميلي ثانية
            _alertTimer.Elapsed += AlertTimer_Elapsed;
            _alertTimer.AutoReset = true;

            _logger?.LogInformation("تم تهيئة خدمة التنبيهات");
        }

        #endregion

        #region إدارة الخدمة

        /// <summary>
        /// بدء خدمة التنبيهات
        /// </summary>
        public void Start()
        {
            if (_isRunning)
                return;

            try
            {
                _alertTimer.Start();
                _isRunning = true;
                
                _logger?.LogInformation("تم بدء خدمة التنبيهات");
                
                // تشغيل فحص فوري
                Task.Run(() => CheckAlerts());
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في بدء خدمة التنبيهات");
                throw;
            }
        }

        /// <summary>
        /// إيقاف خدمة التنبيهات
        /// </summary>
        public void Stop()
        {
            if (!_isRunning)
                return;

            try
            {
                _alertTimer.Stop();
                _isRunning = false;
                
                _logger?.LogInformation("تم إيقاف خدمة التنبيهات");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إيقاف خدمة التنبيهات");
                throw;
            }
        }

        /// <summary>
        /// إعادة تشغيل خدمة التنبيهات
        /// </summary>
        public void Restart()
        {
            Stop();
            Start();
        }

        /// <summary>
        /// التحقق من حالة الخدمة
        /// </summary>
        public bool IsRunning => _isRunning;

        #endregion

        #region إدارة المراقبين

        /// <summary>
        /// إضافة مراقب للتنبيهات
        /// </summary>
        /// <param name="observer">المراقب</param>
        public void AddObserver(IAlertObserver observer)
        {
            if (observer != null && !_observers.Contains(observer))
            {
                _observers.Add(observer);
                _logger?.LogInformation($"تم إضافة مراقب تنبيهات: {observer.GetType().Name}");
            }
        }

        /// <summary>
        /// إزالة مراقب للتنبيهات
        /// </summary>
        /// <param name="observer">المراقب</param>
        public void RemoveObserver(IAlertObserver observer)
        {
            if (observer != null && _observers.Contains(observer))
            {
                _observers.Remove(observer);
                _logger?.LogInformation($"تم إزالة مراقب تنبيهات: {observer.GetType().Name}");
            }
        }

        #endregion

        #region فحص التنبيهات

        /// <summary>
        /// حدث مؤقت التنبيهات
        /// </summary>
        private async void AlertTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            await CheckAlerts();
        }

        /// <summary>
        /// فحص جميع أنواع التنبيهات
        /// </summary>
        public async Task CheckAlerts()
        {
            if (!_settings.EnableAlerts)
                return;

            try
            {
                _logger?.LogDebug("بدء فحص التنبيهات");

                // فحص المنتجات منخفضة المخزون
                if (_settings.EnableLowStockAlerts)
                {
                    await CheckLowStockAlerts();
                }

                // فحص المنتجات نافدة المخزون
                if (_settings.EnableOutOfStockAlerts)
                {
                    await CheckOutOfStockAlerts();
                }

                // فحص المنتجات منتهية الصلاحية
                if (_settings.EnableExpiryAlerts)
                {
                    await CheckExpiryAlerts();
                }

                _logger?.LogDebug("انتهاء فحص التنبيهات");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فحص التنبيهات");
            }
        }

        /// <summary>
        /// فحص المنتجات منخفضة المخزون
        /// </summary>
        private async Task CheckLowStockAlerts()
        {
            try
            {
                var lowStockProducts = _productBLL.GetLowStockProducts();
                
                if (lowStockProducts.Any())
                {
                    var alertData = new LowStockAlertEventArgs
                    {
                        Products = lowStockProducts,
                        AlertTime = DateTime.Now,
                        TotalCount = lowStockProducts.Count
                    };

                    // إثارة الحدث
                    LowStockDetected?.Invoke(this, alertData);

                    // إشعار المراقبين
                    await NotifyObservers(AlertType.LowStock, alertData);

                    _logger?.LogWarning($"تم اكتشاف {lowStockProducts.Count} منتج منخفض المخزون");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فحص المنتجات منخفضة المخزون");
            }
        }

        /// <summary>
        /// فحص المنتجات نافدة المخزون
        /// </summary>
        private async Task CheckOutOfStockAlerts()
        {
            try
            {
                var outOfStockProducts = _productBLL.GetOutOfStockProducts();
                
                if (outOfStockProducts.Any())
                {
                    var alertData = new OutOfStockAlertEventArgs
                    {
                        Products = outOfStockProducts,
                        AlertTime = DateTime.Now,
                        TotalCount = outOfStockProducts.Count
                    };

                    // إثارة الحدث
                    OutOfStockDetected?.Invoke(this, alertData);

                    // إشعار المراقبين
                    await NotifyObservers(AlertType.OutOfStock, alertData);

                    _logger?.LogError($"تم اكتشاف {outOfStockProducts.Count} منتج نافد المخزون");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فحص المنتجات نافدة المخزون");
            }
        }

        /// <summary>
        /// فحص المنتجات منتهية الصلاحية
        /// </summary>
        private async Task CheckExpiryAlerts()
        {
            try
            {
                var allProducts = _productBLL.GetAllProducts(false);
                var expiringProducts = allProducts.Where(p => 
                    p.ExpiryDate.HasValue && 
                    p.ExpiryDate.Value <= DateTime.Now.AddDays(_settings.ExpiryWarningDays))
                    .ToList();
                
                if (expiringProducts.Any())
                {
                    var alertData = new ExpiryAlertEventArgs
                    {
                        Products = expiringProducts,
                        AlertTime = DateTime.Now,
                        TotalCount = expiringProducts.Count,
                        WarningDays = _settings.ExpiryWarningDays
                    };

                    // إثارة الحدث
                    ExpiryDetected?.Invoke(this, alertData);

                    // إشعار المراقبين
                    await NotifyObservers(AlertType.Expiry, alertData);

                    _logger?.LogWarning($"تم اكتشاف {expiringProducts.Count} منتج قريب من انتهاء الصلاحية");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فحص المنتجات منتهية الصلاحية");
            }
        }

        #endregion

        #region إشعار المراقبين

        /// <summary>
        /// إشعار جميع المراقبين
        /// </summary>
        /// <param name="alertType">نوع التنبيه</param>
        /// <param name="alertData">بيانات التنبيه</param>
        private async Task NotifyObservers(AlertType alertType, object alertData)
        {
            var tasks = _observers.Select(observer => 
                Task.Run(() => observer.OnAlert(alertType, alertData)));
            
            await Task.WhenAll(tasks);
        }

        #endregion

        #region فحص يدوي

        /// <summary>
        /// فحص يدوي للمنتجات منخفضة المخزون
        /// </summary>
        /// <returns>قائمة المنتجات منخفضة المخزون</returns>
        public List<Product> GetLowStockProducts()
        {
            return _productBLL.GetLowStockProducts();
        }

        /// <summary>
        /// فحص يدوي للمنتجات نافدة المخزون
        /// </summary>
        /// <returns>قائمة المنتجات نافدة المخزون</returns>
        public List<Product> GetOutOfStockProducts()
        {
            return _productBLL.GetOutOfStockProducts();
        }

        /// <summary>
        /// فحص يدوي للمنتجات منتهية الصلاحية
        /// </summary>
        /// <param name="warningDays">عدد أيام التحذير</param>
        /// <returns>قائمة المنتجات منتهية الصلاحية</returns>
        public List<Product> GetExpiringProducts(int warningDays = 30)
        {
            var allProducts = _productBLL.GetAllProducts(false);
            return allProducts.Where(p => 
                p.ExpiryDate.HasValue && 
                p.ExpiryDate.Value <= DateTime.Now.AddDays(warningDays))
                .ToList();
        }

        /// <summary>
        /// الحصول على ملخص التنبيهات
        /// </summary>
        /// <returns>ملخص التنبيهات</returns>
        public AlertSummary GetAlertSummary()
        {
            try
            {
                var lowStockCount = GetLowStockProducts().Count;
                var outOfStockCount = GetOutOfStockProducts().Count;
                var expiringCount = GetExpiringProducts(_settings.ExpiryWarningDays).Count;

                return new AlertSummary
                {
                    LowStockCount = lowStockCount,
                    OutOfStockCount = outOfStockCount,
                    ExpiringProductsCount = expiringCount,
                    TotalAlerts = lowStockCount + outOfStockCount + expiringCount,
                    LastCheckTime = DateTime.Now,
                    IsServiceRunning = _isRunning
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على ملخص التنبيهات");
                return new AlertSummary();
            }
        }

        #endregion

        #region تحديث الإعدادات

        /// <summary>
        /// تحديث إعدادات التنبيهات
        /// </summary>
        /// <param name="newSettings">الإعدادات الجديدة</param>
        public void UpdateSettings(AlertSettings newSettings)
        {
            if (newSettings == null)
                throw new ArgumentNullException(nameof(newSettings));

            // تحديث الإعدادات
            _settings.EnableAlerts = newSettings.EnableAlerts;
            _settings.EnableLowStockAlerts = newSettings.EnableLowStockAlerts;
            _settings.EnableOutOfStockAlerts = newSettings.EnableOutOfStockAlerts;
            _settings.EnableExpiryAlerts = newSettings.EnableExpiryAlerts;
            _settings.CheckIntervalMinutes = newSettings.CheckIntervalMinutes;
            _settings.ExpiryWarningDays = newSettings.ExpiryWarningDays;

            // تحديث فترة المؤقت
            _alertTimer.Interval = _settings.CheckIntervalMinutes * 60 * 1000;

            _logger?.LogInformation("تم تحديث إعدادات التنبيهات");
        }

        /// <summary>
        /// الحصول على الإعدادات الحالية
        /// </summary>
        /// <returns>الإعدادات الحالية</returns>
        public AlertSettings GetCurrentSettings()
        {
            return new AlertSettings
            {
                EnableAlerts = _settings.EnableAlerts,
                EnableLowStockAlerts = _settings.EnableLowStockAlerts,
                EnableOutOfStockAlerts = _settings.EnableOutOfStockAlerts,
                EnableExpiryAlerts = _settings.EnableExpiryAlerts,
                CheckIntervalMinutes = _settings.CheckIntervalMinutes,
                ExpiryWarningDays = _settings.ExpiryWarningDays
            };
        }

        #endregion

        #region التنظيف والإغلاق

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        /// <param name="disposing">هل يتم التنظيف</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                Stop();
                _alertTimer?.Dispose();
                _observers.Clear();
                _disposed = true;
                
                _logger?.LogInformation("تم تنظيف خدمة التنبيهات");
            }
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// إعدادات التنبيهات
    /// </summary>
    public class AlertSettings
    {
        public bool EnableAlerts { get; set; } = true;
        public bool EnableLowStockAlerts { get; set; } = true;
        public bool EnableOutOfStockAlerts { get; set; } = true;
        public bool EnableExpiryAlerts { get; set; } = true;
        public int CheckIntervalMinutes { get; set; } = 30;
        public int ExpiryWarningDays { get; set; } = 30;
    }

    /// <summary>
    /// ملخص التنبيهات
    /// </summary>
    public class AlertSummary
    {
        public int LowStockCount { get; set; }
        public int OutOfStockCount { get; set; }
        public int ExpiringProductsCount { get; set; }
        public int TotalAlerts { get; set; }
        public DateTime LastCheckTime { get; set; }
        public bool IsServiceRunning { get; set; }
    }

    /// <summary>
    /// أنواع التنبيهات
    /// </summary>
    public enum AlertType
    {
        LowStock,
        OutOfStock,
        Expiry
    }

    /// <summary>
    /// واجهة مراقب التنبيهات
    /// </summary>
    public interface IAlertObserver
    {
        Task OnAlert(AlertType alertType, object alertData);
    }

    /// <summary>
    /// بيانات تنبيه المخزون المنخفض
    /// </summary>
    public class LowStockAlertEventArgs : EventArgs
    {
        public List<Product> Products { get; set; }
        public DateTime AlertTime { get; set; }
        public int TotalCount { get; set; }
    }

    /// <summary>
    /// بيانات تنبيه المخزون النافد
    /// </summary>
    public class OutOfStockAlertEventArgs : EventArgs
    {
        public List<Product> Products { get; set; }
        public DateTime AlertTime { get; set; }
        public int TotalCount { get; set; }
    }

    /// <summary>
    /// بيانات تنبيه انتهاء الصلاحية
    /// </summary>
    public class ExpiryAlertEventArgs : EventArgs
    {
        public List<Product> Products { get; set; }
        public DateTime AlertTime { get; set; }
        public int TotalCount { get; set; }
        public int WarningDays { get; set; }
    }

    #endregion
}
