using System;
using System.Collections.Generic;
using System.Linq;
using AredooPOS.Models;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.BLL
{
    /// <summary>
    /// طبقة منطق الأعمال لإدارة المخزون
    /// تحتوي على جميع العمليات التجارية المتعلقة بالمخزون وحركاته
    /// </summary>
    public class StockBLL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly ProductDAL _productDAL;
        private readonly StockMovementDAL _stockMovementDAL;
        private readonly ILogger<StockBLL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة منطق الأعمال للمخزون
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public StockBLL(string connectionString = null, ILogger<StockBLL> logger = null)
        {
            _productDAL = new ProductDAL(connectionString, null);
            _stockMovementDAL = new StockMovementDAL(connectionString, null);
            _logger = logger;
        }

        #endregion

        #region إدارة حركات المخزون

        /// <summary>
        /// إضافة حركة مخزون جديدة
        /// </summary>
        /// <param name="movement">بيانات حركة المخزون</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <param name="updateProductStock">تحديث مخزون المنتج تلقائياً</param>
        /// <returns>رقم حركة المخزون الجديدة</returns>
        public int AddStockMovement(StockMovement movement, string currentUser, bool updateProductStock = true)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateStockMovement(movement);

                // الحصول على بيانات المنتج
                var product = _productDAL.GetProductById(movement.ProductID);
                if (product == null)
                {
                    throw new InvalidOperationException("المنتج غير موجود");
                }

                // تعيين بيانات المنتج في الحركة
                movement.ProductCode = product.ProductCode;
                movement.ProductName = product.ProductName;
                movement.StockBefore = product.StockQuantity;

                // حساب المخزون بعد الحركة
                var stockImpact = movement.GetStockImpact();
                movement.StockAfter = movement.StockBefore + stockImpact;

                // التحقق من عدم وجود مخزون سالب
                if (movement.StockAfter < 0 && product.TrackStock)
                {
                    throw new InvalidOperationException($"الحركة ستؤدي إلى مخزون سالب. المخزون الحالي: {movement.StockBefore}, التأثير: {stockImpact}");
                }

                // تعيين معلومات النظام
                movement.CreatedBy = currentUser;
                movement.ModifiedBy = currentUser;
                movement.CreatedDate = DateTime.Now;
                movement.ModifiedDate = DateTime.Now;

                // إنشاء رقم مرجعي إذا لم يكن موجود
                if (string.IsNullOrWhiteSpace(movement.ReferenceNumber))
                {
                    movement.GenerateReferenceNumber();
                }

                // إضافة حركة المخزون
                var movementId = _stockMovementDAL.AddStockMovement(movement);

                // تحديث مخزون المنتج إذا كان مطلوباً
                if (updateProductStock && product.TrackStock)
                {
                    _productDAL.UpdateStockQuantity(movement.ProductID, movement.StockAfter, currentUser);
                }

                _logger?.LogInformation($"تم إضافة حركة مخزون جديدة: {movement.ReferenceNumber} للمنتج {product.ProductCode}");
                return movementId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إضافة حركة المخزون للمنتج {movement?.ProductID}");
                throw;
            }
        }

        /// <summary>
        /// تحديث حركة مخزون
        /// </summary>
        /// <param name="movement">بيانات حركة المخزون المحدثة</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateStockMovement(StockMovement movement, string currentUser)
        {
            try
            {
                // التحقق من وجود الحركة
                var existingMovement = _stockMovementDAL.GetStockMovementById(movement.StockMovementID);
                if (existingMovement == null)
                {
                    throw new InvalidOperationException("حركة المخزون غير موجودة");
                }

                // التحقق من إمكانية التعديل
                if (existingMovement.IsCancelled)
                {
                    throw new InvalidOperationException("لا يمكن تعديل حركة مخزون ملغاة");
                }

                // التحقق من صحة البيانات
                ValidateStockMovement(movement);

                // تعيين معلومات التعديل
                movement.ModifiedBy = currentUser;
                movement.ModifiedDate = DateTime.Now;
                movement.CreatedBy = existingMovement.CreatedBy;
                movement.CreatedDate = existingMovement.CreatedDate;

                // تحديث حركة المخزون
                var success = _stockMovementDAL.UpdateStockMovement(movement);

                if (success)
                {
                    _logger?.LogInformation($"تم تحديث حركة المخزون: {movement.ReferenceNumber}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث حركة المخزون {movement?.StockMovementID}");
                throw;
            }
        }

        /// <summary>
        /// إلغاء حركة مخزون
        /// </summary>
        /// <param name="movementId">رقم حركة المخزون</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <param name="reverseStockImpact">عكس تأثير الحركة على المخزون</param>
        /// <returns>true إذا تم الإلغاء بنجاح</returns>
        public bool CancelStockMovement(int movementId, string currentUser, bool reverseStockImpact = true)
        {
            try
            {
                var movement = _stockMovementDAL.GetStockMovementById(movementId);
                if (movement == null)
                {
                    throw new InvalidOperationException("حركة المخزون غير موجودة");
                }

                if (movement.IsCancelled)
                {
                    throw new InvalidOperationException("حركة المخزون ملغاة مسبقاً");
                }

                // إلغاء الحركة
                movement.Cancel(currentUser);
                var success = _stockMovementDAL.UpdateStockMovement(movement);

                // عكس تأثير الحركة على المخزون إذا كان مطلوباً
                if (success && reverseStockImpact && movement.IsConfirmed)
                {
                    var product = _productDAL.GetProductById(movement.ProductID);
                    if (product != null && product.TrackStock)
                    {
                        var reversedStock = product.StockQuantity - movement.GetStockImpact();
                        _productDAL.UpdateStockQuantity(movement.ProductID, reversedStock, currentUser);

                        // إضافة حركة عكسية
                        var reverseMovement = new StockMovement
                        {
                            ProductID = movement.ProductID,
                            ProductCode = movement.ProductCode,
                            ProductName = movement.ProductName,
                            MovementDate = DateTime.Now,
                            MovementType = StockMovementTypes.Adjustment,
                            Quantity = -movement.GetStockImpact(),
                            StockBefore = product.StockQuantity,
                            StockAfter = reversedStock,
                            UnitCost = movement.UnitCost,
                            Reason = $"عكس حركة {movement.ReferenceNumber}",
                            CreatedBy = currentUser,
                            CreatedDate = DateTime.Now,
                            ModifiedDate = DateTime.Now
                        };

                        reverseMovement.GenerateReferenceNumber();
                        _stockMovementDAL.AddStockMovement(reverseMovement);
                    }
                }

                if (success)
                {
                    _logger?.LogInformation($"تم إلغاء حركة المخزون: {movement.ReferenceNumber}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إلغاء حركة المخزون {movementId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على حركات المخزون لمنتج معين
        /// </summary>
        /// <param name="productId">رقم المنتج</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>قائمة حركات المخزون</returns>
        public List<StockMovement> GetStockMovementsByProduct(int productId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                return _stockMovementDAL.GetStockMovementsByProduct(productId, fromDate, toDate);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على حركات المخزون للمنتج {productId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على حركات المخزون حسب النوع
        /// </summary>
        /// <param name="movementType">نوع الحركة</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>قائمة حركات المخزون</returns>
        public List<StockMovement> GetStockMovementsByType(string movementType, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                return _stockMovementDAL.GetStockMovementsByType(movementType, fromDate, toDate);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على حركات المخزون من النوع {movementType}");
                throw;
            }
        }

        #endregion

        #region عمليات المخزون المتقدمة

        /// <summary>
        /// تسوية المخزون
        /// </summary>
        /// <param name="productId">رقم المنتج</param>
        /// <param name="actualQuantity">الكمية الفعلية</param>
        /// <param name="reason">سبب التسوية</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تمت التسوية بنجاح</returns>
        public bool AdjustStock(int productId, decimal actualQuantity, string reason, string currentUser)
        {
            try
            {
                var product = _productDAL.GetProductById(productId);
                if (product == null)
                {
                    throw new InvalidOperationException("المنتج غير موجود");
                }

                if (!product.TrackStock)
                {
                    throw new InvalidOperationException("هذا المنتج لا يتم تتبع مخزونه");
                }

                var difference = actualQuantity - product.StockQuantity;
                if (difference == 0)
                {
                    return true; // لا توجد حاجة للتسوية
                }

                // إنشاء حركة تسوية
                var adjustmentMovement = new StockMovement
                {
                    ProductID = productId,
                    ProductCode = product.ProductCode,
                    ProductName = product.ProductName,
                    MovementDate = DateTime.Now,
                    MovementType = StockMovementTypes.Adjustment,
                    Quantity = difference,
                    StockBefore = product.StockQuantity,
                    StockAfter = actualQuantity,
                    UnitCost = product.CostPrice,
                    Reason = reason,
                    CreatedBy = currentUser,
                    CreatedDate = DateTime.Now,
                    ModifiedDate = DateTime.Now
                };

                adjustmentMovement.GenerateReferenceNumber();

                // إضافة حركة التسوية
                var movementId = _stockMovementDAL.AddStockMovement(adjustmentMovement);

                // تحديث مخزون المنتج
                var success = _productDAL.UpdateStockQuantity(productId, actualQuantity, currentUser);

                if (success)
                {
                    _logger?.LogInformation($"تم تسوية مخزون المنتج {product.ProductCode} من {product.StockQuantity} إلى {actualQuantity}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تسوية مخزون المنتج {productId}");
                throw;
            }
        }

        /// <summary>
        /// تحويل مخزون بين المنتجات
        /// </summary>
        /// <param name="fromProductId">رقم المنتج المصدر</param>
        /// <param name="toProductId">رقم المنتج الهدف</param>
        /// <param name="quantity">الكمية</param>
        /// <param name="reason">سبب التحويل</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم التحويل بنجاح</returns>
        public bool TransferStock(int fromProductId, int toProductId, decimal quantity, string reason, string currentUser)
        {
            try
            {
                var fromProduct = _productDAL.GetProductById(fromProductId);
                var toProduct = _productDAL.GetProductById(toProductId);

                if (fromProduct == null || toProduct == null)
                {
                    throw new InvalidOperationException("أحد المنتجات غير موجود");
                }

                if (!fromProduct.TrackStock || !toProduct.TrackStock)
                {
                    throw new InvalidOperationException("كلا المنتجين يجب أن يكونا قابلين لتتبع المخزون");
                }

                if (fromProduct.AvailableStock < quantity)
                {
                    throw new InvalidOperationException($"الكمية المتاحة في المنتج المصدر ({fromProduct.AvailableStock}) أقل من الكمية المطلوبة ({quantity})");
                }

                // حركة خصم من المنتج المصدر
                var outMovement = new StockMovement
                {
                    ProductID = fromProductId,
                    ProductCode = fromProduct.ProductCode,
                    ProductName = fromProduct.ProductName,
                    MovementDate = DateTime.Now,
                    MovementType = StockMovementTypes.Transfer,
                    Quantity = -quantity,
                    StockBefore = fromProduct.StockQuantity,
                    StockAfter = fromProduct.StockQuantity - quantity,
                    UnitCost = fromProduct.CostPrice,
                    Reason = $"{reason} - تحويل إلى {toProduct.ProductCode}",
                    CreatedBy = currentUser,
                    CreatedDate = DateTime.Now,
                    ModifiedDate = DateTime.Now
                };

                // حركة إضافة للمنتج الهدف
                var inMovement = new StockMovement
                {
                    ProductID = toProductId,
                    ProductCode = toProduct.ProductCode,
                    ProductName = toProduct.ProductName,
                    MovementDate = DateTime.Now,
                    MovementType = StockMovementTypes.Transfer,
                    Quantity = quantity,
                    StockBefore = toProduct.StockQuantity,
                    StockAfter = toProduct.StockQuantity + quantity,
                    UnitCost = toProduct.CostPrice,
                    Reason = $"{reason} - تحويل من {fromProduct.ProductCode}",
                    CreatedBy = currentUser,
                    CreatedDate = DateTime.Now,
                    ModifiedDate = DateTime.Now
                };

                outMovement.GenerateReferenceNumber();
                inMovement.GenerateReferenceNumber();

                // إضافة الحركات
                _stockMovementDAL.AddStockMovement(outMovement);
                _stockMovementDAL.AddStockMovement(inMovement);

                // تحديث المخزون
                var success1 = _productDAL.UpdateStockQuantity(fromProductId, outMovement.StockAfter, currentUser);
                var success2 = _productDAL.UpdateStockQuantity(toProductId, inMovement.StockAfter, currentUser);

                var success = success1 && success2;

                if (success)
                {
                    _logger?.LogInformation($"تم تحويل {quantity} وحدة من {fromProduct.ProductCode} إلى {toProduct.ProductCode}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحويل المخزون من {fromProductId} إلى {toProductId}");
                throw;
            }
        }

        #endregion

        #region التقارير والإحصائيات

        /// <summary>
        /// حساب قيمة المخزون الإجمالية
        /// </summary>
        /// <param name="categoryId">رقم الفئة (اختياري)</param>
        /// <returns>قيمة المخزون الإجمالية</returns>
        public decimal CalculateTotalStockValue(int? categoryId = null)
        {
            try
            {
                var products = categoryId.HasValue 
                    ? _productDAL.SearchProducts("", categoryId, false)
                    : _productDAL.GetAllProducts(false);

                return products.Where(p => p.TrackStock).Sum(p => p.GetStockValue());
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حساب قيمة المخزون الإجمالية");
                throw;
            }
        }

        /// <summary>
        /// الحصول على ملخص حالة المخزون
        /// </summary>
        /// <returns>ملخص حالة المخزون</returns>
        public StockSummary GetStockSummary()
        {
            try
            {
                var allProducts = _productDAL.GetAllProducts(false);
                var trackedProducts = allProducts.Where(p => p.TrackStock).ToList();

                return new StockSummary
                {
                    TotalProducts = trackedProducts.Count,
                    TotalStockValue = trackedProducts.Sum(p => p.GetStockValue()),
                    LowStockCount = trackedProducts.Count(p => p.IsLowStock()),
                    OutOfStockCount = trackedProducts.Count(p => p.IsOutOfStock()),
                    OverStockCount = trackedProducts.Count(p => p.MaxStockLevel > 0 && p.StockQuantity > p.MaxStockLevel),
                    AverageStockValue = trackedProducts.Count > 0 ? trackedProducts.Average(p => p.GetStockValue()) : 0
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على ملخص حالة المخزون");
                throw;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// التحقق من صحة بيانات حركة المخزون
        /// </summary>
        /// <param name="movement">بيانات حركة المخزون</param>
        private void ValidateStockMovement(StockMovement movement)
        {
            if (movement == null)
                throw new ArgumentNullException(nameof(movement));

            if (movement.ProductID <= 0)
                throw new ArgumentException("رقم المنتج مطلوب");

            if (string.IsNullOrWhiteSpace(movement.MovementType))
                throw new ArgumentException("نوع الحركة مطلوب");

            if (movement.Quantity == 0)
                throw new ArgumentException("الكمية لا يمكن أن تكون صفر");

            if (string.IsNullOrWhiteSpace(movement.Reason))
                throw new ArgumentException("سبب الحركة مطلوب");

            if (movement.UnitCost < 0)
                throw new ArgumentException("تكلفة الوحدة لا يمكن أن تكون سالبة");
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// ملخص حالة المخزون
    /// </summary>
    public class StockSummary
    {
        public int TotalProducts { get; set; }
        public decimal TotalStockValue { get; set; }
        public int LowStockCount { get; set; }
        public int OutOfStockCount { get; set; }
        public int OverStockCount { get; set; }
        public decimal AverageStockValue { get; set; }
    }

    #endregion
}
