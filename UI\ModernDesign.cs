using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// نظام التصميم الحديث والبسيط
    /// </summary>
    public static class ModernDesign
    {
        #region الألوان الحديثة

        public static class Colors
        {
            // الألوان الأساسية
            public static readonly Color Primary = Color.FromArgb(33, 150, 243);      // أزرق جميل
            public static readonly Color PrimaryDark = Color.FromArgb(25, 118, 210);  // أزرق داكن
            public static readonly Color Secondary = Color.FromArgb(76, 175, 80);     // أخضر جميل
            public static readonly Color Accent = Color.FromArgb(255, 193, 7);        // أصفر ذهبي

            // ألوان الخلفية
            public static readonly Color Background = Color.FromArgb(250, 250, 250);  // رمادي فاتح جداً
            public static readonly Color Surface = Color.White;                       // أبيض نقي
            public static readonly Color Card = Color.FromArgb(255, 255, 255);        // أبيض للبطاقات

            // ألوان النص
            public static readonly Color TextPrimary = Color.FromArgb(33, 33, 33);    // أسود ناعم
            public static readonly Color TextSecondary = Color.FromArgb(117, 117, 117); // رمادي متوسط
            public static readonly Color TextHint = Color.FromArgb(158, 158, 158);     // رمادي فاتح

            // ألوان الحالة
            public static readonly Color Success = Color.FromArgb(76, 175, 80);       // أخضر
            public static readonly Color Warning = Color.FromArgb(255, 152, 0);       // برتقالي
            public static readonly Color Error = Color.FromArgb(244, 67, 54);         // أحمر
            public static readonly Color Info = Color.FromArgb(33, 150, 243);         // أزرق

            // ألوان التفاعل
            public static readonly Color Hover = Color.FromArgb(245, 245, 245);       // رمادي للتمرير
            public static readonly Color Pressed = Color.FromArgb(238, 238, 238);     // رمادي للضغط
            public static readonly Color Selected = Color.FromArgb(227, 242, 253);    // أزرق فاتح للتحديد

            // ألوان الحدود
            public static readonly Color Border = Color.FromArgb(224, 224, 224);      // رمادي للحدود
            public static readonly Color Divider = Color.FromArgb(238, 238, 238);     // رمادي للفواصل
        }

        #endregion

        #region الخطوط الحديثة

        public static class Fonts
        {
            public static Font GetFont(float size, FontStyle style = FontStyle.Regular)
            {
                return new Font("Segoe UI", size, style);
            }

            // خطوط العناوين
            public static Font Title => GetFont(24, FontStyle.Bold);
            public static Font Heading => GetFont(20, FontStyle.Bold);
            public static Font Subheading => GetFont(16, FontStyle.Bold);

            // خطوط النص
            public static Font Body => GetFont(14);
            public static Font BodyBold => GetFont(14, FontStyle.Bold);
            public static Font Caption => GetFont(12);
            public static Font Small => GetFont(10);

            // خطوط الأزرار
            public static Font Button => GetFont(14, FontStyle.Bold);
            public static Font ButtonLarge => GetFont(16, FontStyle.Bold);
        }

        #endregion

        #region الأبعاد والمسافات

        public static class Spacing
        {
            public const int XSmall = 4;
            public const int Small = 8;
            public const int Medium = 16;
            public const int Large = 24;
            public const int XLarge = 32;
            public const int XXLarge = 48;
        }

        public static class Sizes
        {
            public const int ButtonHeight = 40;
            public const int InputHeight = 36;
            public const int IconSize = 24;
            public const int LargeIconSize = 32;
            public const int CornerRadius = 8;
        }

        #endregion

        #region مساعدات الرسم

        /// <summary>
        /// رسم بطاقة حديثة
        /// </summary>
        public static void DrawCard(Graphics g, Rectangle bounds, Color backgroundColor, int cornerRadius = 8)
        {
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // رسم الظل
            var shadowBounds = new Rectangle(bounds.X + 2, bounds.Y + 2, bounds.Width, bounds.Height);
            using (var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
            using (var shadowPath = CreateRoundedRectangle(shadowBounds, cornerRadius))
            {
                g.FillPath(shadowBrush, shadowPath);
            }

            // رسم البطاقة
            using (var cardBrush = new SolidBrush(backgroundColor))
            using (var cardPath = CreateRoundedRectangle(bounds, cornerRadius))
            {
                g.FillPath(cardBrush, cardPath);
            }

            // رسم الحدود
            using (var borderPen = new Pen(Colors.Border, 1))
            using (var borderPath = CreateRoundedRectangle(bounds, cornerRadius))
            {
                g.DrawPath(borderPen, borderPath);
            }
        }

        /// <summary>
        /// رسم زر حديث
        /// </summary>
        public static void DrawButton(Graphics g, Rectangle bounds, Color backgroundColor, string text, Font font, Color textColor, bool isHovered = false, bool isPressed = false)
        {
            g.SmoothingMode = SmoothingMode.AntiAlias;

            var buttonColor = backgroundColor;
            if (isPressed)
                buttonColor = DarkenColor(backgroundColor, 0.2f);
            else if (isHovered)
                buttonColor = LightenColor(backgroundColor, 0.1f);

            // رسم الزر
            using (var buttonBrush = new SolidBrush(buttonColor))
            using (var buttonPath = CreateRoundedRectangle(bounds, Sizes.CornerRadius))
            {
                g.FillPath(buttonBrush, buttonPath);
            }

            // رسم النص
            using (var textBrush = new SolidBrush(textColor))
            {
                var textFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center,
                    FormatFlags = StringFormatFlags.DirectionRightToLeft
                };
                g.DrawString(text, font, textBrush, bounds, textFormat);
            }
        }

        /// <summary>
        /// إنشاء مستطيل بحواف مدورة
        /// </summary>
        public static GraphicsPath CreateRoundedRectangle(Rectangle bounds, int cornerRadius)
        {
            var path = new GraphicsPath();
            var diameter = cornerRadius * 2;

            if (cornerRadius == 0)
            {
                path.AddRectangle(bounds);
                return path;
            }

            path.AddArc(bounds.X, bounds.Y, diameter, diameter, 180, 90);
            path.AddArc(bounds.Right - diameter, bounds.Y, diameter, diameter, 270, 90);
            path.AddArc(bounds.Right - diameter, bounds.Bottom - diameter, diameter, diameter, 0, 90);
            path.AddArc(bounds.X, bounds.Bottom - diameter, diameter, diameter, 90, 90);
            path.CloseFigure();

            return path;
        }

        /// <summary>
        /// تفتيح لون
        /// </summary>
        public static Color LightenColor(Color color, float factor)
        {
            var r = (int)(color.R + (255 - color.R) * factor);
            var g = (int)(color.G + (255 - color.G) * factor);
            var b = (int)(color.B + (255 - color.B) * factor);
            return Color.FromArgb(color.A, Math.Min(255, r), Math.Min(255, g), Math.Min(255, b));
        }

        /// <summary>
        /// تغميق لون
        /// </summary>
        public static Color DarkenColor(Color color, float factor)
        {
            var r = (int)(color.R * (1 - factor));
            var g = (int)(color.G * (1 - factor));
            var b = (int)(color.B * (1 - factor));
            return Color.FromArgb(color.A, Math.Max(0, r), Math.Max(0, g), Math.Max(0, b));
        }

        #endregion

        #region إنشاء عناصر تحكم حديثة

        /// <summary>
        /// إنشاء زر حديث
        /// </summary>
        public static Button CreateModernButton(string text, Color backgroundColor, Color textColor, EventHandler clickHandler = null)
        {
            var button = new Button
            {
                Text = text,
                Font = Fonts.Button,
                BackColor = backgroundColor,
                ForeColor = textColor,
                FlatStyle = FlatStyle.Flat,
                Height = Sizes.ButtonHeight,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = LightenColor(backgroundColor, 0.1f);
            button.FlatAppearance.MouseDownBackColor = DarkenColor(backgroundColor, 0.1f);

            if (clickHandler != null)
                button.Click += clickHandler;

            return button;
        }

        /// <summary>
        /// إنشاء تسمية حديثة
        /// </summary>
        public static Label CreateModernLabel(string text, Font font, Color textColor, ContentAlignment alignment = ContentAlignment.MiddleRight)
        {
            return new Label
            {
                Text = text,
                Font = font,
                ForeColor = textColor,
                TextAlign = alignment,
                RightToLeft = RightToLeft.Yes,
                AutoSize = false
            };
        }

        /// <summary>
        /// إنشاء لوحة حديثة
        /// </summary>
        public static Panel CreateModernPanel(Color backgroundColor, int padding = 16)
        {
            var panel = new Panel
            {
                BackColor = backgroundColor,
                Padding = new Padding(padding)
            };

            panel.Paint += (s, e) =>
            {
                DrawCard(e.Graphics, panel.ClientRectangle, backgroundColor);
            };

            return panel;
        }

        /// <summary>
        /// إنشاء حقل إدخال حديث
        /// </summary>
        public static TextBox CreateModernTextBox(string placeholder = "")
        {
            var textBox = new TextBox
            {
                Font = Fonts.Body,
                Height = Sizes.InputHeight,
                BorderStyle = BorderStyle.FixedSingle,
                RightToLeft = RightToLeft.Yes
            };

            if (!string.IsNullOrEmpty(placeholder))
            {
                textBox.Text = placeholder;
                textBox.ForeColor = Colors.TextHint;

                textBox.GotFocus += (s, e) =>
                {
                    if (textBox.Text == placeholder)
                    {
                        textBox.Text = "";
                        textBox.ForeColor = Colors.TextPrimary;
                    }
                };

                textBox.LostFocus += (s, e) =>
                {
                    if (string.IsNullOrWhiteSpace(textBox.Text))
                    {
                        textBox.Text = placeholder;
                        textBox.ForeColor = Colors.TextHint;
                    }
                };
            }

            return textBox;
        }

        #endregion
    }
}
