# دليل البناء والتطوير - نظام أريدوو للكاشير
# Build and Development Guide - Aredoo POS System

---

## 📋 متطلبات التطوير

### البرمجيات المطلوبة
- **Visual Studio 2019** أو أحدث (Community Edition مجاني)
- **.NET Framework 4.7.2** أو أحدث
- **SQL Server 2017** أو أحدث (Express Edition مجاني)
- **Git** لإدارة الكود المصدري

### الأدوات الاختيارية
- **SQL Server Management Studio (SSMS)** لإدارة قاعدة البيانات
- **Visual Studio Code** للتحرير السريع
- **Postman** لاختبار APIs (مستقبلاً)

---

## 🚀 إعداد بيئة التطوير

### 1. استنساخ المشروع

```bash
# استنساخ المستودع
git clone https://github.com/aredoo/aredoo-pos.git

# الانتقال لمجلد المشروع
cd aredoo-pos

# إنشاء فرع للتطوير
git checkout -b development
```

### 2. إعداد قاعدة البيانات

#### أ) إنشاء قاعدة البيانات
```sql
-- فتح SQL Server Management Studio
-- تشغيل الاستعلام التالي:

CREATE DATABASE AredooPOS;
GO

USE AredooPOS;
GO
```

#### ب) تشغيل سكريبتات الإنشاء
```bash
# تشغيل سكريبت إنشاء الجداول
sqlcmd -S .\SQLEXPRESS -d AredooPOS -i "Database\AredooPOS_Complete.sql"

# تشغيل سكريبت البيانات الأولية
sqlcmd -S .\SQLEXPRESS -d AredooPOS -i "Database\InitialData_And_Indexes.sql"

# تشغيل سكريبت الإجراءات المخزنة
sqlcmd -S .\SQLEXPRESS -d AredooPOS -i "Database\StoredProcedures.sql"
```

### 3. تكوين سلسلة الاتصال

تحديث ملف `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.\\SQLEXPRESS;Database=AredooPOS;Integrated Security=true;TrustServerCertificate=true;"
  }
}
```

### 4. استعادة الحزم

```bash
# استعادة حزم NuGet
dotnet restore

# أو من Visual Studio
# Tools > NuGet Package Manager > Restore NuGet Packages
```

---

## 🔨 بناء المشروع

### البناء من سطر الأوامر

```bash
# بناء المشروع في وضع Debug
dotnet build --configuration Debug

# بناء المشروع في وضع Release
dotnet build --configuration Release

# بناء مع تفاصيل إضافية
dotnet build --verbosity detailed
```

### البناء من Visual Studio

1. فتح ملف `AredooPOS.sln`
2. اختيار Configuration (Debug/Release)
3. Build > Build Solution (Ctrl+Shift+B)

### إعدادات البناء

#### Debug Configuration
- تحسين: معطل
- رموز التشخيص: مفعل
- تعريفات: DEBUG;TRACE
- مخرجات مفصلة للأخطاء

#### Release Configuration
- تحسين: مفعل
- رموز التشخيص: PDB فقط
- تعريفات: TRACE
- تحسين الحجم والأداء

---

## 🧪 الاختبار

### اختبارات الوحدة

```bash
# تشغيل جميع الاختبارات
dotnet test

# تشغيل اختبارات محددة
dotnet test --filter "TestCategory=Unit"

# تشغيل مع تقرير التغطية
dotnet test --collect:"XPlat Code Coverage"
```

### اختبارات التكامل

```bash
# اختبارات قاعدة البيانات
dotnet test --filter "TestCategory=Integration"

# اختبارات واجهة المستخدم
dotnet test --filter "TestCategory=UI"
```

### اختبارات الأداء

```bash
# اختبارات الأداء
dotnet test --filter "TestCategory=Performance"

# قياس استهلاك الذاكرة
dotnet test --collect:"Code Coverage" --logger:trx
```

---

## 📦 التعبئة والنشر

### إنشاء حزمة التوزيع

```bash
# نشر للنظام المحلي
dotnet publish -c Release -r win-x64 --self-contained false

# نشر مع تضمين .NET Framework
dotnet publish -c Release -r win-x64 --self-contained true

# إنشاء حزمة مضغوطة
dotnet publish -c Release -o ./publish
```

### إنشاء مثبت Windows

```bash
# استخدام WiX Toolset
candle AredooPOS.wxs
light AredooPOS.wixobj -o AredooPOS.msi

# أو استخدام Inno Setup
iscc AredooPOS.iss
```

### النشر التلقائي

```yaml
# ملف GitHub Actions (.github/workflows/build.yml)
name: Build and Deploy

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v1
      with:
        dotnet-version: 4.7.2
        
    - name: Restore dependencies
      run: dotnet restore
      
    - name: Build
      run: dotnet build --no-restore
      
    - name: Test
      run: dotnet test --no-build --verbosity normal
      
    - name: Publish
      run: dotnet publish -c Release -o ./publish
```

---

## 🐛 التشخيص واستكشاف الأخطاء

### تشخيص مشاكل البناء

```bash
# بناء مع تفاصيل كاملة
dotnet build --verbosity diagnostic

# تنظيف المشروع
dotnet clean

# إعادة بناء كاملة
dotnet clean && dotnet restore && dotnet build
```

### تشخيص مشاكل قاعدة البيانات

```sql
-- التحقق من الاتصال
SELECT @@VERSION;

-- التحقق من وجود قاعدة البيانات
SELECT name FROM sys.databases WHERE name = 'AredooPOS';

-- التحقق من الجداول
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES;
```

### تشخيص مشاكل الأداء

```bash
# تشغيل مع profiler
dotnet run --configuration Release --verbosity detailed

# مراقبة استهلاك الذاكرة
dotnet-counters monitor --process-id [PID]

# تحليل الأداء
dotnet-trace collect --process-id [PID]
```

---

## 📁 هيكل المشروع

```
AredooPOS/
├── 📁 Models/              # نماذج البيانات
├── 📁 DAL/                 # طبقة الوصول للبيانات
├── 📁 BLL/                 # طبقة منطق الأعمال
├── 📁 Forms/               # واجهات المستخدم
├── 📁 Helpers/             # المساعدات والأدوات
├── 📁 Database/            # سكريبتات قاعدة البيانات
├── 📁 Resources/           # الموارد والصور
├── 📁 Tests/               # الاختبارات
├── 📄 Program.cs           # نقطة البداية
├── 📄 AredooPOS.csproj     # ملف المشروع
├── 📄 appsettings.json     # الإعدادات
└── 📄 README.md            # التوثيق
```

---

## 🔧 أدوات التطوير

### إضافات Visual Studio المفيدة

- **ReSharper**: تحسين الكود وإعادة التنظيم
- **CodeMaid**: تنظيف وتنسيق الكود
- **Git Extensions**: إدارة Git المتقدمة
- **SQL Server Data Tools**: أدوات قاعدة البيانات

### أدوات سطر الأوامر

```bash
# تثبيت أدوات .NET العامة
dotnet tool install -g dotnet-ef
dotnet tool install -g dotnet-counters
dotnet tool install -g dotnet-trace

# أدوات التحليل
dotnet tool install -g dotnet-sonarscanner
dotnet tool install -g security-scan
```

---

## 📊 مراقبة الجودة

### تحليل الكود

```bash
# تشغيل SonarQube
dotnet sonarscanner begin /k:"aredoo-pos"
dotnet build
dotnet sonarscanner end

# تحليل الأمان
security-scan AredooPOS.dll
```

### معايير الترميز

- استخدام أسماء واضحة ومعبرة
- توثيق جميع الطرق العامة
- اتباع معايير C# الرسمية
- استخدام async/await للعمليات الطويلة

### مراجعة الكود

- مراجعة جميع Pull Requests
- اختبار الميزات الجديدة
- التحقق من الأداء
- مراجعة الأمان

---

## 🚀 النشر للإنتاج

### قائمة التحقق قبل النشر

- [ ] تشغيل جميع الاختبارات
- [ ] مراجعة الكود
- [ ] تحديث رقم الإصدار
- [ ] إنشاء النسخة الاحتياطية
- [ ] اختبار النشر في بيئة التجريب
- [ ] توثيق التغييرات

### خطوات النشر

1. **إنشاء Release Branch**
   ```bash
   git checkout -b release/v1.0.0
   git push origin release/v1.0.0
   ```

2. **تحديث رقم الإصدار**
   ```xml
   <AssemblyVersion>1.0.0.0</AssemblyVersion>
   <FileVersion>1.0.0.0</FileVersion>
   ```

3. **بناء النسخة النهائية**
   ```bash
   dotnet publish -c Release -r win-x64
   ```

4. **إنشاء المثبت**
   ```bash
   iscc installer.iss
   ```

5. **اختبار المثبت**
   - تثبيت على نظام نظيف
   - اختبار جميع الميزات
   - التحقق من الأداء

6. **النشر**
   ```bash
   git tag v1.0.0
   git push origin v1.0.0
   ```

---

## 📞 الدعم والمساعدة

### للمطورين
- **البريد الإلكتروني**: <EMAIL>
- **Slack**: #aredoo-dev
- **Wiki**: wiki.aredoo.com

### الموارد المفيدة
- [دليل C# الرسمي](https://docs.microsoft.com/en-us/dotnet/csharp/)
- [دليل SQL Server](https://docs.microsoft.com/en-us/sql/sql-server/)
- [أفضل ممارسات .NET](https://docs.microsoft.com/en-us/dotnet/standard/design-guidelines/)

---

**تم إعداد هذا الدليل لمساعدة المطورين في بناء وتطوير نظام أريدوو بكفاءة وجودة عالية.**
