using System;
using System.Threading.Tasks;
using AredooPOS.BLL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Services
{
    /// <summary>
    /// أنواع طرق الدفع
    /// </summary>
    public static class PaymentMethodTypes
    {
        public const string Cash = "CASH";
        public const string Card = "CARD";
        public const string Transfer = "TRANSFER";
        public const string Check = "CHECK";
        public const string GiftCard = "GIFTCARD";
        public const string Credit = "CREDIT";
        public const string Installment = "INSTALLMENT";
    }
}

namespace AredooPOS.Services
{
    /// <summary>
    /// واجهة معالج الدفع
    /// </summary>
    public interface IPaymentProcessor
    {
        /// <summary>
        /// معالجة الدفع
        /// </summary>
        /// <param name="paymentRequest">طلب الدفع</param>
        /// <returns>نتيجة معالجة الدفع</returns>
        Task<PaymentResult> ProcessPaymentAsync(PaymentRequest paymentRequest);

        /// <summary>
        /// التراجع عن الدفع
        /// </summary>
        /// <param name="paymentRequest">طلب الدفع</param>
        /// <returns>نتيجة التراجع</returns>
        Task<bool> RollbackPaymentAsync(PaymentRequest paymentRequest);

        /// <summary>
        /// التحقق من صحة الدفع
        /// </summary>
        /// <param name="paymentRequest">طلب الدفع</param>
        /// <returns>نتيجة التحقق</returns>
        Task<ValidationResult> ValidatePaymentAsync(PaymentRequest paymentRequest);
    }

    /// <summary>
    /// معالج الدفع النقدي
    /// </summary>
    public class CashPaymentProcessor : IPaymentProcessor
    {
        public async Task<PaymentResult> ProcessPaymentAsync(PaymentRequest paymentRequest)
        {
            // الدفع النقدي لا يحتاج معالجة خاصة
            return new PaymentResult
            {
                IsSuccessful = true,
                ProcessedAmount = paymentRequest.Amount,
                ProcessingFee = 0,
                TotalAmount = paymentRequest.Amount,
                ProcessedDate = DateTime.Now
            };
        }

        public async Task<bool> RollbackPaymentAsync(PaymentRequest paymentRequest)
        {
            // الدفع النقدي لا يحتاج تراجع خاص
            return true;
        }

        public async Task<ValidationResult> ValidatePaymentAsync(PaymentRequest paymentRequest)
        {
            if (paymentRequest.Amount <= 0)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "مبلغ الدفع النقدي يجب أن يكون أكبر من صفر"
                };
            }

            return new ValidationResult { IsValid = true };
        }
    }

    /// <summary>
    /// معالج دفع البطاقات
    /// </summary>
    public class CardPaymentProcessor : IPaymentProcessor
    {
        private readonly PaymentServiceSettings _settings;
        private readonly ILogger<CardPaymentProcessor> _logger;

        public CardPaymentProcessor(PaymentServiceSettings settings, ILogger<CardPaymentProcessor> logger = null)
        {
            _settings = settings;
            _logger = logger;
        }

        public async Task<PaymentResult> ProcessPaymentAsync(PaymentRequest paymentRequest)
        {
            try
            {
                _logger?.LogInformation($"بدء معالجة دفع بطاقة بمبلغ {paymentRequest.Amount:C}");

                // محاكاة معالجة البطاقة
                await SimulateCardProcessing(paymentRequest);

                // حساب رسوم المعالجة (2.5% للبطاقات)
                var processingFee = Math.Round(paymentRequest.Amount * 0.025m, 2);

                _logger?.LogInformation($"تم معالجة دفع البطاقة بنجاح");

                return new PaymentResult
                {
                    IsSuccessful = true,
                    ProcessedAmount = paymentRequest.Amount,
                    ProcessingFee = processingFee,
                    TotalAmount = paymentRequest.Amount + processingFee,
                    ProcessedDate = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة دفع البطاقة");
                return new PaymentResult
                {
                    IsSuccessful = false,
                    ErrorMessage = "فشل في معالجة دفع البطاقة"
                };
            }
        }

        public async Task<bool> RollbackPaymentAsync(PaymentRequest paymentRequest)
        {
            try
            {
                _logger?.LogInformation($"بدء التراجع عن دفع البطاقة");

                // محاكاة التراجع عن معالجة البطاقة
                await SimulateCardRollback(paymentRequest);

                _logger?.LogInformation($"تم التراجع عن دفع البطاقة بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في التراجع عن دفع البطاقة");
                return false;
            }
        }

        public async Task<ValidationResult> ValidatePaymentAsync(PaymentRequest paymentRequest)
        {
            if (paymentRequest.Amount <= 0)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "مبلغ دفع البطاقة يجب أن يكون أكبر من صفر"
                };
            }

            if (paymentRequest.Amount < 10)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "الحد الأدنى لدفع البطاقة هو 10 ريال"
                };
            }

            if (string.IsNullOrWhiteSpace(paymentRequest.ReferenceNumber))
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "رقم مرجعي مطلوب لدفع البطاقة"
                };
            }

            return new ValidationResult { IsValid = true };
        }

        /// <summary>
        /// محاكاة معالجة البطاقة
        /// </summary>
        private async Task SimulateCardProcessing(PaymentRequest paymentRequest)
        {
            // محاكاة وقت المعالجة
            await Task.Delay(2000);

            // محاكاة احتمالية فشل المعالجة (5%)
            var random = new Random();
            if (random.Next(1, 101) <= 5)
            {
                throw new InvalidOperationException("فشل في الاتصال بشبكة البطاقات");
            }
        }

        /// <summary>
        /// محاكاة التراجع عن معالجة البطاقة
        /// </summary>
        private async Task SimulateCardRollback(PaymentRequest paymentRequest)
        {
            // محاكاة وقت التراجع
            await Task.Delay(1000);
        }
    }

    /// <summary>
    /// معالج التحويل البنكي
    /// </summary>
    public class BankTransferProcessor : IPaymentProcessor
    {
        private readonly PaymentServiceSettings _settings;
        private readonly ILogger<BankTransferProcessor> _logger;

        public BankTransferProcessor(PaymentServiceSettings settings, ILogger<BankTransferProcessor> logger = null)
        {
            _settings = settings;
            _logger = logger;
        }

        public async Task<PaymentResult> ProcessPaymentAsync(PaymentRequest paymentRequest)
        {
            try
            {
                _logger?.LogInformation($"بدء معالجة تحويل بنكي بمبلغ {paymentRequest.Amount:C}");

                // محاكاة معالجة التحويل البنكي
                await SimulateBankTransferProcessing(paymentRequest);

                // حساب رسوم المعالجة (رسم ثابت 5 ريال)
                var processingFee = 5m;

                _logger?.LogInformation($"تم معالجة التحويل البنكي بنجاح");

                return new PaymentResult
                {
                    IsSuccessful = true,
                    ProcessedAmount = paymentRequest.Amount,
                    ProcessingFee = processingFee,
                    TotalAmount = paymentRequest.Amount + processingFee,
                    ProcessedDate = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة التحويل البنكي");
                return new PaymentResult
                {
                    IsSuccessful = false,
                    ErrorMessage = "فشل في معالجة التحويل البنكي"
                };
            }
        }

        public async Task<bool> RollbackPaymentAsync(PaymentRequest paymentRequest)
        {
            try
            {
                _logger?.LogInformation($"بدء التراجع عن التحويل البنكي");

                // محاكاة التراجع عن التحويل البنكي
                await SimulateBankTransferRollback(paymentRequest);

                _logger?.LogInformation($"تم التراجع عن التحويل البنكي بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في التراجع عن التحويل البنكي");
                return false;
            }
        }

        public async Task<ValidationResult> ValidatePaymentAsync(PaymentRequest paymentRequest)
        {
            if (paymentRequest.Amount <= 0)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "مبلغ التحويل البنكي يجب أن يكون أكبر من صفر"
                };
            }

            if (paymentRequest.Amount < 50)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "الحد الأدنى للتحويل البنكي هو 50 ريال"
                };
            }

            if (string.IsNullOrWhiteSpace(paymentRequest.ReferenceNumber))
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "رقم مرجعي مطلوب للتحويل البنكي"
                };
            }

            // التحقق من صيغة الرقم المرجعي
            if (paymentRequest.ReferenceNumber.Length < 10)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "الرقم المرجعي للتحويل البنكي يجب أن يكون 10 أرقام على الأقل"
                };
            }

            return new ValidationResult { IsValid = true };
        }

        /// <summary>
        /// محاكاة معالجة التحويل البنكي
        /// </summary>
        private async Task SimulateBankTransferProcessing(PaymentRequest paymentRequest)
        {
            // محاكاة وقت المعالجة
            await Task.Delay(3000);

            // محاكاة احتمالية فشل المعالجة (3%)
            var random = new Random();
            if (random.Next(1, 101) <= 3)
            {
                throw new InvalidOperationException("فشل في الاتصال بالنظام البنكي");
            }
        }

        /// <summary>
        /// محاكاة التراجع عن التحويل البنكي
        /// </summary>
        private async Task SimulateBankTransferRollback(PaymentRequest paymentRequest)
        {
            // محاكاة وقت التراجع
            await Task.Delay(2000);
        }
    }

    /// <summary>
    /// معالج الشيكات
    /// </summary>
    public class CheckPaymentProcessor : IPaymentProcessor
    {
        private readonly ILogger<CheckPaymentProcessor> _logger;

        public CheckPaymentProcessor(ILogger<CheckPaymentProcessor> logger = null)
        {
            _logger = logger;
        }

        public async Task<PaymentResult> ProcessPaymentAsync(PaymentRequest paymentRequest)
        {
            try
            {
                _logger?.LogInformation($"بدء معالجة دفع بشيك بمبلغ {paymentRequest.Amount:C}");

                // الشيكات تحتاج تأكيد يدوي
                await Task.Delay(500);

                _logger?.LogInformation($"تم قبول الشيك - يحتاج تأكيد لاحق");

                return new PaymentResult
                {
                    IsSuccessful = true,
                    ProcessedAmount = paymentRequest.Amount,
                    ProcessingFee = 0,
                    TotalAmount = paymentRequest.Amount,
                    ProcessedDate = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة دفع الشيك");
                return new PaymentResult
                {
                    IsSuccessful = false,
                    ErrorMessage = "فشل في معالجة دفع الشيك"
                };
            }
        }

        public async Task<bool> RollbackPaymentAsync(PaymentRequest paymentRequest)
        {
            // الشيكات لا تحتاج تراجع فوري
            return true;
        }

        public async Task<ValidationResult> ValidatePaymentAsync(PaymentRequest paymentRequest)
        {
            if (paymentRequest.Amount <= 0)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "مبلغ الشيك يجب أن يكون أكبر من صفر"
                };
            }

            if (paymentRequest.Amount < 100)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "الحد الأدنى للدفع بالشيك هو 100 ريال"
                };
            }

            if (string.IsNullOrWhiteSpace(paymentRequest.ReferenceNumber))
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "رقم الشيك مطلوب"
                };
            }

            return new ValidationResult { IsValid = true };
        }
    }

    /// <summary>
    /// معالج بطاقات الهدايا
    /// </summary>
    public class GiftCardProcessor : IPaymentProcessor
    {
        private readonly ILogger<GiftCardProcessor> _logger;

        public GiftCardProcessor(ILogger<GiftCardProcessor> logger = null)
        {
            _logger = logger;
        }

        public async Task<PaymentResult> ProcessPaymentAsync(PaymentRequest paymentRequest)
        {
            try
            {
                _logger?.LogInformation($"بدء معالجة دفع ببطاقة هدايا بمبلغ {paymentRequest.Amount:C}");

                // محاكاة التحقق من رصيد بطاقة الهدايا
                await SimulateGiftCardValidation(paymentRequest);

                _logger?.LogInformation($"تم معالجة دفع بطاقة الهدايا بنجاح");

                return new PaymentResult
                {
                    IsSuccessful = true,
                    ProcessedAmount = paymentRequest.Amount,
                    ProcessingFee = 0,
                    TotalAmount = paymentRequest.Amount,
                    ProcessedDate = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة دفع بطاقة الهدايا");
                return new PaymentResult
                {
                    IsSuccessful = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<bool> RollbackPaymentAsync(PaymentRequest paymentRequest)
        {
            try
            {
                _logger?.LogInformation($"بدء التراجع عن دفع بطاقة الهدايا");

                // محاكاة إعادة الرصيد لبطاقة الهدايا
                await Task.Delay(1000);

                _logger?.LogInformation($"تم التراجع عن دفع بطاقة الهدايا بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في التراجع عن دفع بطاقة الهدايا");
                return false;
            }
        }

        public async Task<ValidationResult> ValidatePaymentAsync(PaymentRequest paymentRequest)
        {
            if (paymentRequest.Amount <= 0)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "مبلغ بطاقة الهدايا يجب أن يكون أكبر من صفر"
                };
            }

            if (string.IsNullOrWhiteSpace(paymentRequest.ReferenceNumber))
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "رقم بطاقة الهدايا مطلوب"
                };
            }

            return new ValidationResult { IsValid = true };
        }

        /// <summary>
        /// محاكاة التحقق من بطاقة الهدايا
        /// </summary>
        private async Task SimulateGiftCardValidation(PaymentRequest paymentRequest)
        {
            await Task.Delay(1500);

            // محاكاة التحقق من الرصيد
            var random = new Random();
            var availableBalance = random.Next(0, 1000);

            if (availableBalance < paymentRequest.Amount)
            {
                throw new InvalidOperationException($"رصيد بطاقة الهدايا غير كافي. الرصيد المتاح: {availableBalance:C}");
            }

            // محاكاة احتمالية انتهاء صلاحية البطاقة (2%)
            if (random.Next(1, 101) <= 2)
            {
                throw new InvalidOperationException("بطاقة الهدايا منتهية الصلاحية");
            }
        }
    }
}
