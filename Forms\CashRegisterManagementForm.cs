using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using AredooPOS.Models;
using AredooPOS.BLL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// نموذج إدارة الصناديق
    /// يوفر واجهة شاملة لإدارة الصناديق النقدية
    /// </summary>
    public partial class CashRegisterManagementForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly CashRegisterBLL _cashRegisterBLL;
        private readonly ILogger<CashRegisterManagementForm> _logger;
        private readonly string _currentUser;

        private List<CashRegister> _cashRegisters;
        private CashRegister _selectedCashRegister;
        private bool _isEditing = false;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ نموذج إدارة الصناديق
        /// </summary>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <param name="logger">مسجل الأحداث</param>
        public CashRegisterManagementForm(string currentUser, ILogger<CashRegisterManagementForm> logger = null)
        {
            InitializeComponent();
            
            _currentUser = currentUser;
            _logger = logger;
            _cashRegisterBLL = new CashRegisterBLL(null, logger);

            InitializeForm();
            LoadCashRegisters();
        }

        /// <summary>
        /// تهيئة النموذج
        /// </summary>
        private void InitializeForm()
        {
            // تعيين النصوص العربية
            this.Text = "إدارة الصناديق النقدية";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // تهيئة الأعمدة
            InitializeDataGridView();

            // تهيئة الأحداث
            InitializeEvents();

            // تعطيل أزرار التحرير في البداية
            EnableEditingControls(false);
        }

        /// <summary>
        /// تهيئة جدول البيانات
        /// </summary>
        private void InitializeDataGridView()
        {
            dgvCashRegisters.AutoGenerateColumns = false;
            dgvCashRegisters.AllowUserToAddRows = false;
            dgvCashRegisters.AllowUserToDeleteRows = false;
            dgvCashRegisters.ReadOnly = true;
            dgvCashRegisters.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvCashRegisters.MultiSelect = false;

            // إضافة الأعمدة
            dgvCashRegisters.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "RegisterName",
                HeaderText = "اسم الصندوق",
                DataPropertyName = "RegisterName",
                Width = 150
            });

            dgvCashRegisters.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "RegisterCode",
                HeaderText = "رمز الصندوق",
                DataPropertyName = "RegisterCode",
                Width = 100
            });

            dgvCashRegisters.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Location",
                HeaderText = "الموقع",
                DataPropertyName = "Location",
                Width = 120
            });

            dgvCashRegisters.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CurrentBalance",
                HeaderText = "الرصيد الحالي",
                DataPropertyName = "CurrentBalance",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            });

            dgvCashRegisters.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                HeaderText = "نشط",
                DataPropertyName = "IsActive",
                Width = 60
            });

            dgvCashRegisters.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LastTransactionDate",
                HeaderText = "آخر معاملة",
                DataPropertyName = "LastTransactionDate",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy HH:mm" }
            });
        }

        /// <summary>
        /// تهيئة الأحداث
        /// </summary>
        private void InitializeEvents()
        {
            // أحداث الأزرار
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnRefresh.Click += BtnRefresh_Click;
            btnSearch.Click += BtnSearch_Click;

            // أحداث جدول البيانات
            dgvCashRegisters.SelectionChanged += DgvCashRegisters_SelectionChanged;
            dgvCashRegisters.CellDoubleClick += DgvCashRegisters_CellDoubleClick;

            // أحداث مربعات النص
            txtSearch.KeyPress += TxtSearch_KeyPress;
            txtRegisterCode.TextChanged += TxtRegisterCode_TextChanged;

            // أحداث مربعات الأرقام
            numOpeningBalance.ValueChanged += NumOpeningBalance_ValueChanged;
            numMinCashLimit.ValueChanged += NumMinCashLimit_ValueChanged;
            numMaxCashLimit.ValueChanged += NumMaxCashLimit_ValueChanged;

            // حدث إغلاق النموذج
            this.FormClosing += CashRegisterManagementForm_FormClosing;
        }

        #endregion

        #region تحميل البيانات

        /// <summary>
        /// تحميل قائمة الصناديق
        /// </summary>
        private async void LoadCashRegisters()
        {
            try
            {
                lblStatus.Text = "جاري تحميل البيانات...";
                dgvCashRegisters.DataSource = null;

                await Task.Run(() =>
                {
                    _cashRegisters = _cashRegisterBLL.GetAllCashRegisters(chkIncludeInactive.Checked);
                });

                dgvCashRegisters.DataSource = _cashRegisters;
                
                lblStatus.Text = $"تم تحميل {_cashRegisters.Count} صندوق";
                
                // تحديث ملخص الحالة
                UpdateStatusSummary();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل الصناديق");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في تحميل البيانات";
            }
        }

        /// <summary>
        /// تحديث ملخص الحالة
        /// </summary>
        private void UpdateStatusSummary()
        {
            if (_cashRegisters == null || !_cashRegisters.Any())
            {
                lblSummary.Text = "لا توجد صناديق";
                return;
            }

            var summary = _cashRegisterBLL.GetCashRegisterSummary();
            lblSummary.Text = $"المجموع: {summary.TotalRegisters} | " +
                             $"نشط: {summary.ActiveRegisters} | " +
                             $"إجمالي الرصيد: {summary.TotalBalance:C}";

            // تحديث التنبيهات
            if (summary.RegistersNeedingAlert > 0)
            {
                lblAlerts.Text = $"تنبيه: {summary.RegistersNeedingAlert} صندوق يحتاج انتباه";
                lblAlerts.ForeColor = Color.Red;
                lblAlerts.Visible = true;
            }
            else
            {
                lblAlerts.Visible = false;
            }
        }

        #endregion

        #region أحداث الأزرار

        /// <summary>
        /// حدث زر إضافة صندوق جديد
        /// </summary>
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                _isEditing = false;
                _selectedCashRegister = null;
                
                ClearForm();
                EnableEditingControls(true);
                
                txtRegisterName.Focus();
                lblFormMode.Text = "إضافة صندوق جديد";
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في بدء إضافة صندوق جديد");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث زر تحرير صندوق
        /// </summary>
        private void BtnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedCashRegister == null)
                {
                    MessageBox.Show("يرجى اختيار صندوق للتحرير", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                _isEditing = true;
                
                LoadCashRegisterToForm(_selectedCashRegister);
                EnableEditingControls(true);
                
                txtRegisterName.Focus();
                lblFormMode.Text = $"تحرير الصندوق: {_selectedCashRegister.RegisterName}";
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في بدء تحرير الصندوق");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث زر حذف صندوق
        /// </summary>
        private void BtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (_selectedCashRegister == null)
                {
                    MessageBox.Show("يرجى اختيار صندوق للحذف", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الصندوق '{_selectedCashRegister.RegisterName}'؟\n" +
                    "هذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var success = _cashRegisterBLL.DeleteCashRegister(_selectedCashRegister.CashRegisterID, _currentUser);
                    
                    if (success)
                    {
                        MessageBox.Show("تم حذف الصندوق بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                        LoadCashRegisters();
                        ClearForm();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف الصندوق", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حذف الصندوق");
                MessageBox.Show($"خطأ في حذف الصندوق: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث زر حفظ
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm())
                    return;

                var cashRegister = CreateCashRegisterFromForm();

                bool success;
                if (_isEditing)
                {
                    cashRegister.CashRegisterID = _selectedCashRegister.CashRegisterID;
                    success = _cashRegisterBLL.UpdateCashRegister(cashRegister, _currentUser);
                }
                else
                {
                    var newId = _cashRegisterBLL.AddCashRegister(cashRegister, _currentUser);
                    success = newId > 0;
                }

                if (success)
                {
                    MessageBox.Show(_isEditing ? "تم تحديث الصندوق بنجاح" : "تم إضافة الصندوق بنجاح", 
                        "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    LoadCashRegisters();
                    ClearForm();
                    EnableEditingControls(false);
                    lblFormMode.Text = "";
                }
                else
                {
                    MessageBox.Show(_isEditing ? "فشل في تحديث الصندوق" : "فشل في إضافة الصندوق", 
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ الصندوق");
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث زر إلغاء
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                ClearForm();
                EnableEditingControls(false);
                lblFormMode.Text = "";
                _isEditing = false;
                _selectedCashRegister = null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إلغاء العملية");
            }
        }

        /// <summary>
        /// حدث زر تحديث
        /// </summary>
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadCashRegisters();
        }

        /// <summary>
        /// حدث زر البحث
        /// </summary>
        private void BtnSearch_Click(object sender, EventArgs e)
        {
            PerformSearch();
        }

        #endregion

        #region أحداث أخرى

        /// <summary>
        /// حدث تغيير التحديد في جدول البيانات
        /// </summary>
        private void DgvCashRegisters_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                if (dgvCashRegisters.SelectedRows.Count > 0)
                {
                    var selectedRow = dgvCashRegisters.SelectedRows[0];
                    _selectedCashRegister = selectedRow.DataBoundItem as CashRegister;
                    
                    if (_selectedCashRegister != null && !_isEditing)
                    {
                        LoadCashRegisterToForm(_selectedCashRegister);
                    }
                    
                    // تفعيل أزرار التحرير والحذف
                    btnEdit.Enabled = true;
                    btnDelete.Enabled = true;
                }
                else
                {
                    _selectedCashRegister = null;
                    btnEdit.Enabled = false;
                    btnDelete.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تغيير التحديد");
            }
        }

        /// <summary>
        /// حدث النقر المزدوج على جدول البيانات
        /// </summary>
        private void DgvCashRegisters_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && !_isEditing)
            {
                BtnEdit_Click(sender, e);
            }
        }

        /// <summary>
        /// حدث الضغط على مفتاح في مربع البحث
        /// </summary>
        private void TxtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                PerformSearch();
                e.Handled = true;
            }
        }

        /// <summary>
        /// حدث تغيير نص رمز الصندوق
        /// </summary>
        private void TxtRegisterCode_TextChanged(object sender, EventArgs e)
        {
            // تحويل النص إلى أحرف كبيرة
            if (txtRegisterCode.Text != txtRegisterCode.Text.ToUpper())
            {
                var selectionStart = txtRegisterCode.SelectionStart;
                txtRegisterCode.Text = txtRegisterCode.Text.ToUpper();
                txtRegisterCode.SelectionStart = selectionStart;
            }
        }

        /// <summary>
        /// حدث تغيير الرصيد الافتتاحي
        /// </summary>
        private void NumOpeningBalance_ValueChanged(object sender, EventArgs e)
        {
            // تحديث الرصيد الحالي إذا كان صندوق جديد
            if (!_isEditing)
            {
                lblCurrentBalance.Text = $"الرصيد الحالي: {numOpeningBalance.Value:C}";
            }
        }

        /// <summary>
        /// حدث تغيير الحد الأدنى
        /// </summary>
        private void NumMinCashLimit_ValueChanged(object sender, EventArgs e)
        {
            // التأكد من أن الحد الأقصى أكبر من الحد الأدنى
            if (chkHasMaxLimit.Checked && numMaxCashLimit.Value <= numMinCashLimit.Value)
            {
                numMaxCashLimit.Value = numMinCashLimit.Value + 1000;
            }
        }

        /// <summary>
        /// حدث تغيير الحد الأقصى
        /// </summary>
        private void NumMaxCashLimit_ValueChanged(object sender, EventArgs e)
        {
            // التأكد من أن الحد الأقصى أكبر من الحد الأدنى
            if (numMaxCashLimit.Value <= numMinCashLimit.Value)
            {
                numMaxCashLimit.Value = numMinCashLimit.Value + 1000;
            }
        }

        /// <summary>
        /// حدث إغلاق النموذج
        /// </summary>
        private void CashRegisterManagementForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_isEditing)
            {
                var result = MessageBox.Show(
                    "يوجد تعديلات غير محفوظة. هل تريد إغلاق النموذج؟",
                    "تأكيد الإغلاق",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                }
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// تفعيل/تعطيل عناصر التحكم للتحرير
        /// </summary>
        /// <param name="enabled">تفعيل أم لا</param>
        private void EnableEditingControls(bool enabled)
        {
            // مربعات النص
            txtRegisterName.Enabled = enabled;
            txtRegisterCode.Enabled = enabled;
            txtLocation.Enabled = enabled;
            txtDescription.Enabled = enabled;

            // مربعات الأرقام
            numOpeningBalance.Enabled = enabled && !_isEditing; // الرصيد الافتتاحي فقط للجديد
            numMinCashLimit.Enabled = enabled;
            numMaxCashLimit.Enabled = enabled;

            // مربعات الاختيار
            chkIsActive.Enabled = enabled;
            chkAllowNegativeBalance.Enabled = enabled;
            chkRequireManagerApproval.Enabled = enabled;
            chkHasMaxLimit.Enabled = enabled;

            // منتقي الوقت
            dtpAutoCloseTime.Enabled = enabled;
            chkHasAutoClose.Enabled = enabled;

            // الأزرار
            btnSave.Enabled = enabled;
            btnCancel.Enabled = enabled;
            btnAdd.Enabled = !enabled;
            btnEdit.Enabled = !enabled && _selectedCashRegister != null;
            btnDelete.Enabled = !enabled && _selectedCashRegister != null;

            // تفعيل/تعطيل الحد الأقصى حسب الاختيار
            numMaxCashLimit.Enabled = enabled && chkHasMaxLimit.Checked;
            dtpAutoCloseTime.Enabled = enabled && chkHasAutoClose.Checked;
        }

        /// <summary>
        /// مسح النموذج
        /// </summary>
        private void ClearForm()
        {
            txtRegisterName.Clear();
            txtRegisterCode.Clear();
            txtLocation.Clear();
            txtDescription.Clear();

            numOpeningBalance.Value = 0;
            numMinCashLimit.Value = 0;
            numMaxCashLimit.Value = 10000;

            chkIsActive.Checked = true;
            chkAllowNegativeBalance.Checked = false;
            chkRequireManagerApproval.Checked = false;
            chkHasMaxLimit.Checked = false;
            chkHasAutoClose.Checked = false;

            dtpAutoCloseTime.Value = DateTime.Today.AddHours(23);

            lblCurrentBalance.Text = "الرصيد الحالي: 0.00 ر.س";
        }

        /// <summary>
        /// تحميل بيانات الصندوق إلى النموذج
        /// </summary>
        /// <param name="cashRegister">بيانات الصندوق</param>
        private void LoadCashRegisterToForm(CashRegister cashRegister)
        {
            if (cashRegister == null) return;

            txtRegisterName.Text = cashRegister.RegisterName;
            txtRegisterCode.Text = cashRegister.RegisterCode;
            txtLocation.Text = cashRegister.Location ?? "";
            txtDescription.Text = cashRegister.Description ?? "";

            numOpeningBalance.Value = cashRegister.OpeningBalance;
            numMinCashLimit.Value = cashRegister.MinCashLimit;
            numMaxCashLimit.Value = cashRegister.MaxCashLimit ?? 10000;

            chkIsActive.Checked = cashRegister.IsActive;
            chkAllowNegativeBalance.Checked = cashRegister.AllowNegativeBalance;
            chkRequireManagerApproval.Checked = cashRegister.RequireManagerApproval;
            chkHasMaxLimit.Checked = cashRegister.HasMaxLimit;
            chkHasAutoClose.Checked = cashRegister.HasAutoClose;

            if (cashRegister.HasAutoClose)
            {
                dtpAutoCloseTime.Value = DateTime.Today.Add(cashRegister.AutoCloseTime.Value);
            }

            lblCurrentBalance.Text = $"الرصيد الحالي: {cashRegister.CurrentBalance:C}";
        }

        /// <summary>
        /// إنشاء كائن صندوق من النموذج
        /// </summary>
        /// <returns>كائن الصندوق</returns>
        private CashRegister CreateCashRegisterFromForm()
        {
            return new CashRegister
            {
                RegisterName = txtRegisterName.Text.Trim(),
                RegisterCode = txtRegisterCode.Text.Trim().ToUpper(),
                Location = string.IsNullOrWhiteSpace(txtLocation.Text) ? null : txtLocation.Text.Trim(),
                Description = string.IsNullOrWhiteSpace(txtDescription.Text) ? null : txtDescription.Text.Trim(),
                IsActive = chkIsActive.Checked,
                OpeningBalance = numOpeningBalance.Value,
                MinCashLimit = numMinCashLimit.Value,
                MaxCashLimit = chkHasMaxLimit.Checked ? numMaxCashLimit.Value : null,
                RequireManagerApproval = chkRequireManagerApproval.Checked,
                AllowNegativeBalance = chkAllowNegativeBalance.Checked,
                AutoCloseTime = chkHasAutoClose.Checked ? dtpAutoCloseTime.Value.TimeOfDay : null
            };
        }

        /// <summary>
        /// التحقق من صحة النموذج
        /// </summary>
        /// <returns>true إذا كان النموذج صحيح</returns>
        private bool ValidateForm()
        {
            // التحقق من اسم الصندوق
            if (string.IsNullOrWhiteSpace(txtRegisterName.Text))
            {
                MessageBox.Show("اسم الصندوق مطلوب", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRegisterName.Focus();
                return false;
            }

            if (txtRegisterName.Text.Trim().Length < 2)
            {
                MessageBox.Show("اسم الصندوق يجب أن يكون حرفين على الأقل", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRegisterName.Focus();
                return false;
            }

            // التحقق من رمز الصندوق
            if (string.IsNullOrWhiteSpace(txtRegisterCode.Text))
            {
                MessageBox.Show("رمز الصندوق مطلوب", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRegisterCode.Focus();
                return false;
            }

            if (txtRegisterCode.Text.Trim().Length < 3)
            {
                MessageBox.Show("رمز الصندوق يجب أن يكون 3 أحرف على الأقل", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRegisterCode.Focus();
                return false;
            }

            // التحقق من الرصيد الافتتاحي
            if (numOpeningBalance.Value < 0)
            {
                MessageBox.Show("الرصيد الافتتاحي لا يمكن أن يكون سالب", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numOpeningBalance.Focus();
                return false;
            }

            // التحقق من الحد الأدنى
            if (numMinCashLimit.Value < 0)
            {
                MessageBox.Show("الحد الأدنى للنقدية لا يمكن أن يكون سالب", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numMinCashLimit.Focus();
                return false;
            }

            // التحقق من الحد الأقصى
            if (chkHasMaxLimit.Checked && numMaxCashLimit.Value <= numMinCashLimit.Value)
            {
                MessageBox.Show("الحد الأقصى للنقدية يجب أن يكون أكبر من الحد الأدنى", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numMaxCashLimit.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// تنفيذ البحث
        /// </summary>
        private async void PerformSearch()
        {
            try
            {
                var searchTerm = txtSearch.Text.Trim();

                lblStatus.Text = "جاري البحث...";
                dgvCashRegisters.DataSource = null;

                List<CashRegister> searchResults;

                await Task.Run(() =>
                {
                    if (string.IsNullOrWhiteSpace(searchTerm))
                    {
                        searchResults = _cashRegisterBLL.GetAllCashRegisters(chkIncludeInactive.Checked);
                    }
                    else
                    {
                        searchResults = _cashRegisterBLL.SearchCashRegisters(searchTerm, chkIncludeInactive.Checked);
                    }

                    _cashRegisters = searchResults;
                });

                dgvCashRegisters.DataSource = _cashRegisters;

                lblStatus.Text = $"تم العثور على {_cashRegisters.Count} صندوق";
                UpdateStatusSummary();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في البحث");
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في البحث";
            }
        }

        #endregion

        #region أحداث إضافية

        /// <summary>
        /// حدث تغيير حالة تضمين الصناديق غير النشطة
        /// </summary>
        private void ChkIncludeInactive_CheckedChanged(object sender, EventArgs e)
        {
            LoadCashRegisters();
        }

        /// <summary>
        /// حدث تغيير حالة وجود حد أقصى
        /// </summary>
        private void ChkHasMaxLimit_CheckedChanged(object sender, EventArgs e)
        {
            numMaxCashLimit.Enabled = chkHasMaxLimit.Checked && (btnSave.Enabled || btnCancel.Enabled);
            lblMaxCashLimit.Enabled = chkHasMaxLimit.Checked;
        }

        /// <summary>
        /// حدث تغيير حالة وجود إغلاق تلقائي
        /// </summary>
        private void ChkHasAutoClose_CheckedChanged(object sender, EventArgs e)
        {
            dtpAutoCloseTime.Enabled = chkHasAutoClose.Checked && (btnSave.Enabled || btnCancel.Enabled);
            lblAutoCloseTime.Enabled = chkHasAutoClose.Checked;
        }

        /// <summary>
        /// حدث زر عرض التفاصيل
        /// </summary>
        private void BtnViewDetails_Click(object sender, EventArgs e)
        {
            if (_selectedCashRegister == null)
            {
                MessageBox.Show("يرجى اختيار صندوق لعرض التفاصيل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // فتح نموذج تفاصيل الصندوق
            var detailsForm = new CashRegisterDetailsForm(_selectedCashRegister);
            detailsForm.ShowDialog();
        }

        /// <summary>
        /// حدث زر عرض التنبيهات
        /// </summary>
        private void BtnViewAlerts_Click(object sender, EventArgs e)
        {
            try
            {
                var alertRegisters = _cashRegisterBLL.GetCashRegistersNeedingAlert();

                if (!alertRegisters.Any())
                {
                    MessageBox.Show("لا توجد صناديق تحتاج تنبيه", "معلومات",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // فتح نموذج التنبيهات
                var alertsForm = new CashRegisterAlertsForm(alertRegisters);
                alertsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في عرض التنبيهات");
                MessageBox.Show($"خطأ في عرض التنبيهات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث زر تصدير البيانات
        /// </summary>
        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                if (_cashRegisters == null || !_cashRegisters.Any())
                {
                    MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx|CSV Files|*.csv",
                    Title = "تصدير بيانات الصناديق",
                    FileName = $"CashRegisters_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    // تنفيذ التصدير
                    ExportData(saveDialog.FileName, saveDialog.FilterIndex);

                    MessageBox.Show("تم تصدير البيانات بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تصدير البيانات");
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        /// <param name="fileName">اسم الملف</param>
        /// <param name="filterIndex">نوع الملف</param>
        private void ExportData(string fileName, int filterIndex)
        {
            // TODO: تنفيذ تصدير البيانات إلى Excel أو CSV
            // يمكن استخدام مكتبة مثل EPPlus للـ Excel
        }

        #endregion
    }
}
