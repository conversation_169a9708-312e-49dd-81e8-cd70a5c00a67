using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AredooPOS.Models;
using AredooPOS.BLL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// واجهة إدارة المستخدمين
    /// توفر إدارة شاملة للمستخدمين والأدوار والصلاحيات
    /// </summary>
    public partial class UserManagementForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly UserBLL _userBLL;
        private readonly AuthorizationBLL _authBLL;
        private readonly User _currentUser;
        private readonly ILogger<UserManagementForm> _logger;

        // ألوان النظام
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SuccessColor = Color.FromArgb(46, 204, 113);
        private readonly Color WarningColor = Color.FromArgb(241, 196, 15);
        private readonly Color DangerColor = Color.FromArgb(231, 76, 60);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);

        // بيانات المستخدمين
        private List<User> _users;
        private List<Role> _roles;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ واجهة إدارة المستخدمين
        /// </summary>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <param name="logger">مسجل الأحداث</param>
        public UserManagementForm(User currentUser, ILogger<UserManagementForm> logger = null)
        {
            _currentUser = currentUser ?? throw new ArgumentNullException(nameof(currentUser));
            _userBLL = new UserBLL();
            _authBLL = new AuthorizationBLL();
            _logger = logger;

            InitializeComponent();
            InitializeArabicUI();
            SetupEventHandlers();
            LoadData();
        }

        /// <summary>
        /// تهيئة الواجهة العربية
        /// </summary>
        private void InitializeArabicUI()
        {
            // إعدادات النموذج الأساسية
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "إدارة المستخدمين";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = LightGray;
            this.WindowState = FormWindowState.Maximized;

            // تطبيق الألوان والأنماط
            ApplyThemeColors();
            UpdateUITexts();
        }

        /// <summary>
        /// تطبيق ألوان النظام
        /// </summary>
        private void ApplyThemeColors()
        {
            // شريط العنوان
            pnlHeader.BackColor = PrimaryColor;
            lblTitle.ForeColor = Color.White;

            // شريط الأدوات
            toolStrip.BackColor = Color.White;
            
            // أزرار العمليات
            btnAddUser.BackColor = SuccessColor;
            btnAddUser.ForeColor = Color.White;
            btnAddUser.FlatStyle = FlatStyle.Flat;

            btnEditUser.BackColor = PrimaryColor;
            btnEditUser.ForeColor = Color.White;
            btnEditUser.FlatStyle = FlatStyle.Flat;

            btnDeleteUser.BackColor = DangerColor;
            btnDeleteUser.ForeColor = Color.White;
            btnDeleteUser.FlatStyle = FlatStyle.Flat;

            btnLockUser.BackColor = WarningColor;
            btnLockUser.ForeColor = Color.White;
            btnLockUser.FlatStyle = FlatStyle.Flat;

            btnUnlockUser.BackColor = SuccessColor;
            btnUnlockUser.ForeColor = Color.White;
            btnUnlockUser.FlatStyle = FlatStyle.Flat;

            btnResetPassword.BackColor = Color.Orange;
            btnResetPassword.ForeColor = Color.White;
            btnResetPassword.FlatStyle = FlatStyle.Flat;

            btnRefresh.BackColor = Color.Gray;
            btnRefresh.ForeColor = Color.White;
            btnRefresh.FlatStyle = FlatStyle.Flat;

            // شبكة البيانات
            dgvUsers.BackgroundColor = Color.White;
            dgvUsers.GridColor = LightGray;
            dgvUsers.DefaultCellStyle.BackColor = Color.White;
            dgvUsers.DefaultCellStyle.ForeColor = Color.Black;
            dgvUsers.DefaultCellStyle.SelectionBackColor = PrimaryColor;
            dgvUsers.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvUsers.ColumnHeadersDefaultCellStyle.BackColor = PrimaryColor;
            dgvUsers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvUsers.EnableHeadersVisualStyles = false;

            // لوحة البحث
            pnlSearch.BackColor = Color.White;
            txtSearch.BorderStyle = BorderStyle.FixedSingle;
            cmbRoleFilter.FlatStyle = FlatStyle.Flat;
        }

        /// <summary>
        /// تحديث النصوص في الواجهة
        /// </summary>
        private void UpdateUITexts()
        {
            lblTitle.Text = "إدارة المستخدمين";
            
            // أزرار العمليات
            btnAddUser.Text = "إضافة مستخدم";
            btnEditUser.Text = "تعديل";
            btnDeleteUser.Text = "حذف";
            btnLockUser.Text = "قفل";
            btnUnlockUser.Text = "إلغاء القفل";
            btnResetPassword.Text = "إعادة تعيين كلمة المرور";
            btnRefresh.Text = "تحديث";

            // البحث والفلترة
            lblSearch.Text = "البحث:";
            lblRoleFilter.Text = "الدور:";
            chkShowInactive.Text = "إظهار المستخدمين غير النشطين";

            // إعداد أعمدة الشبكة
            SetupDataGridColumns();
        }

        /// <summary>
        /// إعداد أعمدة شبكة البيانات
        /// </summary>
        private void SetupDataGridColumns()
        {
            dgvUsers.Columns.Clear();
            dgvUsers.AutoGenerateColumns = false;

            // عمود الحالة
            var colStatus = new DataGridViewImageColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                Width = 50,
                ReadOnly = true
            };
            dgvUsers.Columns.Add(colStatus);

            // عمود اسم المستخدم
            var colUsername = new DataGridViewTextBoxColumn
            {
                Name = "Username",
                HeaderText = "اسم المستخدم",
                DataPropertyName = "Username",
                Width = 120,
                ReadOnly = true
            };
            dgvUsers.Columns.Add(colUsername);

            // عمود الاسم الكامل
            var colFullName = new DataGridViewTextBoxColumn
            {
                Name = "FullName",
                HeaderText = "الاسم الكامل",
                DataPropertyName = "FullName",
                Width = 200,
                ReadOnly = true
            };
            dgvUsers.Columns.Add(colFullName);

            // عمود البريد الإلكتروني
            var colEmail = new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "البريد الإلكتروني",
                DataPropertyName = "Email",
                Width = 180,
                ReadOnly = true
            };
            dgvUsers.Columns.Add(colEmail);

            // عمود الدور
            var colRole = new DataGridViewTextBoxColumn
            {
                Name = "RoleName",
                HeaderText = "الدور",
                DataPropertyName = "RoleName",
                Width = 120,
                ReadOnly = true
            };
            dgvUsers.Columns.Add(colRole);

            // عمود رقم الهاتف
            var colPhone = new DataGridViewTextBoxColumn
            {
                Name = "PhoneNumber",
                HeaderText = "رقم الهاتف",
                DataPropertyName = "PhoneNumber",
                Width = 120,
                ReadOnly = true
            };
            dgvUsers.Columns.Add(colPhone);

            // عمود آخر تسجيل دخول
            var colLastLogin = new DataGridViewTextBoxColumn
            {
                Name = "LastLoginDate",
                HeaderText = "آخر تسجيل دخول",
                DataPropertyName = "LastLoginDate",
                Width = 140,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy-MM-dd HH:mm" }
            };
            dgvUsers.Columns.Add(colLastLogin);

            // عمود تاريخ الإنشاء
            var colCreatedDate = new DataGridViewTextBoxColumn
            {
                Name = "CreatedDate",
                HeaderText = "تاريخ الإنشاء",
                DataPropertyName = "CreatedDate",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy-MM-dd" }
            };
            dgvUsers.Columns.Add(colCreatedDate);
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // أحداث الأزرار
            btnAddUser.Click += BtnAddUser_Click;
            btnEditUser.Click += BtnEditUser_Click;
            btnDeleteUser.Click += BtnDeleteUser_Click;
            btnLockUser.Click += BtnLockUser_Click;
            btnUnlockUser.Click += BtnUnlockUser_Click;
            btnResetPassword.Click += BtnResetPassword_Click;
            btnRefresh.Click += BtnRefresh_Click;

            // أحداث البحث والفلترة
            txtSearch.TextChanged += TxtSearch_TextChanged;
            cmbRoleFilter.SelectedIndexChanged += CmbRoleFilter_SelectedIndexChanged;
            chkShowInactive.CheckedChanged += ChkShowInactive_CheckedChanged;

            // أحداث الشبكة
            dgvUsers.SelectionChanged += DgvUsers_SelectionChanged;
            dgvUsers.CellDoubleClick += DgvUsers_CellDoubleClick;
            dgvUsers.CellFormatting += DgvUsers_CellFormatting;

            // أحداث النموذج
            this.Load += UserManagementForm_Load;
        }

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private void LoadData()
        {
            LoadUsers();
            LoadRoles();
        }

        /// <summary>
        /// تحميل المستخدمين
        /// </summary>
        private void LoadUsers()
        {
            try
            {
                _users = _userBLL.GetAllUsers(chkShowInactive.Checked);
                ApplyFilters();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل المستخدمين");
                MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل الأدوار
        /// </summary>
        private void LoadRoles()
        {
            try
            {
                _roles = _authBLL.GetAllRoles(false);
                
                // إضافة خيار "جميع الأدوار"
                var allRolesItem = new Role { RoleID = 0, RoleName = "جميع الأدوار" };
                _roles.Insert(0, allRolesItem);
                
                cmbRoleFilter.DataSource = _roles;
                cmbRoleFilter.DisplayMember = "RoleName";
                cmbRoleFilter.ValueMember = "RoleID";
                cmbRoleFilter.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل الأدوار");
                MessageBox.Show($"خطأ في تحميل الأدوار: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region معالجات الأحداث

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void UserManagementForm_Load(object sender, EventArgs e)
        {
            _logger?.LogInformation("تم تحميل واجهة إدارة المستخدمين");
        }

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        private void BtnAddUser_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من الصلاحية
                if (!_authBLL.HasPermission(_currentUser.UserID, "manage_users"))
                {
                    MessageBox.Show("ليس لديك صلاحية لإضافة المستخدمين", "غير مخول", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // فتح واجهة إضافة مستخدم
                using var addUserForm = new AddEditUserForm(_userBLL, null, _currentUser.Username);
                if (addUserForm.ShowDialog() == DialogResult.OK)
                {
                    LoadUsers();
                    MessageBox.Show("تم إضافة المستخدم بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إضافة مستخدم");
                MessageBox.Show($"خطأ في إضافة المستخدم: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تعديل مستخدم
        /// </summary>
        private void BtnEditUser_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedUser = GetSelectedUser();
                if (selectedUser == null)
                {
                    MessageBox.Show("يرجى اختيار مستخدم للتعديل", "لم يتم الاختيار", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // التحقق من الصلاحية
                if (!_authBLL.HasPermission(_currentUser.UserID, "manage_users"))
                {
                    MessageBox.Show("ليس لديك صلاحية لتعديل المستخدمين", "غير مخول", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // فتح واجهة تعديل المستخدم
                using var editUserForm = new AddEditUserForm(_userBLL, selectedUser, _currentUser.Username);
                if (editUserForm.ShowDialog() == DialogResult.OK)
                {
                    LoadUsers();
                    MessageBox.Show("تم تحديث المستخدم بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تعديل مستخدم");
                MessageBox.Show($"خطأ في تعديل المستخدم: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        private void BtnDeleteUser_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedUser = GetSelectedUser();
                if (selectedUser == null)
                {
                    MessageBox.Show("يرجى اختيار مستخدم للحذف", "لم يتم الاختيار", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // التحقق من الصلاحية
                if (!_authBLL.HasPermission(_currentUser.UserID, "manage_users"))
                {
                    MessageBox.Show("ليس لديك صلاحية لحذف المستخدمين", "غير مخول", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // التحقق من عدم حذف المستخدم الحالي
                if (selectedUser.UserID == _currentUser.UserID)
                {
                    MessageBox.Show("لا يمكن حذف المستخدم الحالي", "غير مسموح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // تأكيد الحذف
                var result = MessageBox.Show($"هل أنت متأكد من حذف المستخدم '{selectedUser.FullName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.", 
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _userBLL.DeleteUser(selectedUser.UserID, _currentUser.Username);
                    LoadUsers();
                    MessageBox.Show("تم حذف المستخدم بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حذف مستخدم");
                MessageBox.Show($"خطأ في حذف المستخدم: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// قفل مستخدم
        /// </summary>
        private void BtnLockUser_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedUser = GetSelectedUser();
                if (selectedUser == null)
                {
                    MessageBox.Show("يرجى اختيار مستخدم للقفل", "لم يتم الاختيار", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (selectedUser.IsLocked)
                {
                    MessageBox.Show("المستخدم مقفل مسبقاً", "معلومات", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // التحقق من عدم قفل المستخدم الحالي
                if (selectedUser.UserID == _currentUser.UserID)
                {
                    MessageBox.Show("لا يمكن قفل المستخدم الحالي", "غير مسموح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // طلب سبب القفل
                var reason = Microsoft.VisualBasic.Interaction.InputBox(
                    "يرجى إدخال سبب قفل المستخدم:", "سبب القفل", "قفل إداري");

                if (!string.IsNullOrWhiteSpace(reason))
                {
                    _userBLL.LockUser(selectedUser.UserID, reason, _currentUser.Username);
                    LoadUsers();
                    MessageBox.Show("تم قفل المستخدم بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في قفل مستخدم");
                MessageBox.Show($"خطأ في قفل المستخدم: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء قفل مستخدم
        /// </summary>
        private void BtnUnlockUser_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedUser = GetSelectedUser();
                if (selectedUser == null)
                {
                    MessageBox.Show("يرجى اختيار مستخدم لإلغاء القفل", "لم يتم الاختيار", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!selectedUser.IsLocked)
                {
                    MessageBox.Show("المستخدم غير مقفل", "معلومات", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                _userBLL.UnlockUser(selectedUser.UserID, _currentUser.Username);
                LoadUsers();
                MessageBox.Show("تم إلغاء قفل المستخدم بنجاح", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إلغاء قفل مستخدم");
                MessageBox.Show($"خطأ في إلغاء قفل المستخدم: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعادة تعيين كلمة المرور
        /// </summary>
        private void BtnResetPassword_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedUser = GetSelectedUser();
                if (selectedUser == null)
                {
                    MessageBox.Show("يرجى اختيار مستخدم لإعادة تعيين كلمة المرور", "لم يتم الاختيار", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // تأكيد العملية
                var result = MessageBox.Show($"هل أنت متأكد من إعادة تعيين كلمة مرور المستخدم '{selectedUser.FullName}'؟", 
                    "تأكيد إعادة التعيين", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // توليد كلمة مرور مؤقتة
                    var tempPassword = BLL.UserBLL.PasswordHelper.GenerateRandomPassword(8);
                    
                    _userBLL.ResetPassword(selectedUser.UserID, tempPassword, _currentUser.Username);
                    
                    MessageBox.Show($"تم إعادة تعيين كلمة المرور بنجاح\n\nكلمة المرور المؤقتة: {tempPassword}\n\nيرجى إبلاغ المستخدم بكلمة المرور الجديدة", 
                        "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إعادة تعيين كلمة المرور");
                MessageBox.Show($"خطأ في إعادة تعيين كلمة المرور: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        /// <summary>
        /// البحث في المستخدمين
        /// </summary>
        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// فلترة حسب الدور
        /// </summary>
        private void CmbRoleFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        /// <summary>
        /// إظهار/إخفاء المستخدمين غير النشطين
        /// </summary>
        private void ChkShowInactive_CheckedChanged(object sender, EventArgs e)
        {
            LoadUsers();
        }

        /// <summary>
        /// تغيير الاختيار في الشبكة
        /// </summary>
        private void DgvUsers_SelectionChanged(object sender, EventArgs e)
        {
            UpdateButtonStates();
        }

        /// <summary>
        /// النقر المزدوج على الشبكة
        /// </summary>
        private void DgvUsers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEditUser_Click(sender, e);
            }
        }

        /// <summary>
        /// تنسيق خلايا الشبكة
        /// </summary>
        private void DgvUsers_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvUsers.Columns[e.ColumnIndex].Name == "Status")
            {
                var user = dgvUsers.Rows[e.RowIndex].DataBoundItem as User;
                if (user != null)
                {
                    if (user.IsLocked)
                        e.Value = Properties.Resources.IconLocked; // أيقونة القفل
                    else if (!user.IsActive)
                        e.Value = Properties.Resources.IconInactive; // أيقونة غير نشط
                    else
                        e.Value = Properties.Resources.IconActive; // أيقونة نشط
                }
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// تطبيق الفلاتر على البيانات
        /// </summary>
        private void ApplyFilters()
        {
            if (_users == null)
                return;

            var filteredUsers = _users.AsEnumerable();

            // فلتر البحث
            var searchTerm = txtSearch.Text.Trim().ToLower();
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                filteredUsers = filteredUsers.Where(u => 
                    u.Username.ToLower().Contains(searchTerm) ||
                    u.FullName.ToLower().Contains(searchTerm) ||
                    (u.Email?.ToLower().Contains(searchTerm) == true) ||
                    (u.PhoneNumber?.Contains(searchTerm) == true));
            }

            // فلتر الدور
            if (cmbRoleFilter.SelectedValue != null && Convert.ToInt32(cmbRoleFilter.SelectedValue) > 0)
            {
                var selectedRoleId = Convert.ToInt32(cmbRoleFilter.SelectedValue);
                filteredUsers = filteredUsers.Where(u => u.RoleID == selectedRoleId);
            }

            dgvUsers.DataSource = filteredUsers.ToList();
        }

        /// <summary>
        /// الحصول على المستخدم المختار
        /// </summary>
        /// <returns>المستخدم المختار</returns>
        private User GetSelectedUser()
        {
            if (dgvUsers.SelectedRows.Count > 0)
            {
                return dgvUsers.SelectedRows[0].DataBoundItem as User;
            }
            return null;
        }

        /// <summary>
        /// تحديث حالة الأزرار
        /// </summary>
        private void UpdateButtonStates()
        {
            var selectedUser = GetSelectedUser();
            var hasSelection = selectedUser != null;
            var canManageUsers = _authBLL.HasPermission(_currentUser.UserID, "manage_users", false);

            btnEditUser.Enabled = hasSelection && canManageUsers;
            btnDeleteUser.Enabled = hasSelection && canManageUsers && selectedUser?.UserID != _currentUser.UserID;
            btnLockUser.Enabled = hasSelection && canManageUsers && selectedUser?.UserID != _currentUser.UserID && !selectedUser?.IsLocked == true;
            btnUnlockUser.Enabled = hasSelection && canManageUsers && selectedUser?.IsLocked == true;
            btnResetPassword.Enabled = hasSelection && canManageUsers;
        }

        #endregion
    }
}
