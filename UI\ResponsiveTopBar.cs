using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// الشريط العلوي المتجاوب مع الإشعارات الديناميكية
    /// </summary>
    public class ResponsiveTopBar : UserControl
    {
        #region المتغيرات

        private string _userName = "أحمد محمد الكاشير";
        private string _userRole = "كاشير رئيسي";
        private string _storeName = "متجر الأمل التجاري";
        private DateTime _loginTime = DateTime.Now;
        private bool _isOnline = true;

        // عناصر الواجهة المتجاوبة
        private Panel _logoPanel;
        private Panel _notificationsPanel;
        private Panel _userPanel;
        private Panel _statusPanel;
        private Label _appNameLabel;
        private Label _storeNameLabel;
        private Label _userNameLabel;
        private Label _userRoleLabel;
        private Label _timeLabel;
        private Label _statusLabel;
        private Button _settingsButton;
        private Button _logoutButton;
        private Timer _clockTimer;

        // نظام الإشعارات
        private List<NotificationItem> _notifications;
        private Panel _notificationBadgesPanel;

        #endregion

        #region الخصائص

        public string UserName
        {
            get => _userName;
            set { _userName = value; UpdateUserInfo(); }
        }

        public string UserRole
        {
            get => _userRole;
            set { _userRole = value; UpdateUserInfo(); }
        }

        public string StoreName
        {
            get => _storeName;
            set { _storeName = value; UpdateStoreInfo(); }
        }

        public bool IsOnline
        {
            get => _isOnline;
            set { _isOnline = value; UpdateStatus(); }
        }

        #endregion

        #region الأحداث

        public event EventHandler SettingsClicked;
        public event EventHandler LogoutClicked;
        public event EventHandler<NotificationClickedEventArgs> NotificationClicked;

        #endregion

        #region البناء والتهيئة

        public ResponsiveTopBar()
        {
            _notifications = new List<NotificationItem>();
            InitializeComponent();
            SetupResponsiveDesign();
            StartClock();
            LoadSampleNotifications();
        }

        private void InitializeComponent()
        {
            // إعدادات الشريط العلوي المتجاوبة
            Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetTopBarHeight();
            BackColor = ResponsiveDesignSystem.Colors.Surface;
            Dock = DockStyle.Top;
            Font = ResponsiveDesignSystem.Fonts.GetBody();

            CreateResponsivePanels();
            CreateResponsiveControls();
            ArrangeResponsiveLayout();
        }

        private void CreateResponsivePanels()
        {
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();
            var panelHeight = Height - (spacing * 2);

            // لوحة الشعار والاسم
            _logoPanel = new Panel
            {
                Size = new Size(ResponsiveLayoutSystem.Grid.GetColumnWidth(Width, 3), panelHeight),
                Location = new Point(spacing, spacing),
                BackColor = Color.Transparent
            };

            // لوحة الإشعارات
            _notificationsPanel = new Panel
            {
                Size = new Size(ResponsiveLayoutSystem.Grid.GetColumnWidth(Width, 3), panelHeight),
                BackColor = Color.Transparent
            };

            // لوحة الحالة
            _statusPanel = new Panel
            {
                Size = new Size(ResponsiveLayoutSystem.Grid.GetColumnWidth(Width, 3), panelHeight),
                BackColor = Color.Transparent
            };

            // لوحة المستخدم
            _userPanel = new Panel
            {
                Size = new Size(ResponsiveLayoutSystem.Grid.GetColumnWidth(Width, 3), panelHeight),
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
        }

        private void CreateResponsiveControls()
        {
            // تسمية اسم التطبيق
            _appNameLabel = new Label
            {
                Text = "أريدو الكاشير",
                Font = ResponsiveDesignSystem.Fonts.GetHeading4(),
                ForeColor = ResponsiveDesignSystem.Colors.Primary,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // تسمية اسم المتجر
            _storeNameLabel = new Label
            {
                Text = _storeName,
                Font = ResponsiveDesignSystem.Fonts.GetCaption(),
                ForeColor = ResponsiveDesignSystem.Colors.TextSecondary,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // تسمية اسم المستخدم
            _userNameLabel = new Label
            {
                Text = _userName,
                Font = ResponsiveDesignSystem.Fonts.GetBody(),
                ForeColor = ResponsiveDesignSystem.Colors.TextPrimary,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // تسمية دور المستخدم
            _userRoleLabel = new Label
            {
                Text = _userRole,
                Font = ResponsiveDesignSystem.Fonts.GetCaption(),
                ForeColor = ResponsiveDesignSystem.Colors.TextSecondary,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // تسمية الوقت
            _timeLabel = new Label
            {
                Text = DateTime.Now.ToString("HH:mm:ss"),
                Font = ResponsiveDesignSystem.Fonts.GetNumbers(),
                ForeColor = ResponsiveDesignSystem.Colors.TextPrimary,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // تسمية الحالة
            _statusLabel = new Label
            {
                Text = "متصل",
                Font = ResponsiveDesignSystem.Fonts.GetCaption(),
                ForeColor = ResponsiveDesignSystem.Colors.Success,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            // زر الإعدادات
            var buttonSize = ResponsiveLayoutSystem.ResponsiveDimensions.GetLargeButtonHeight();
            _settingsButton = CreateResponsiveButton("⚙️", buttonSize, ResponsiveDesignSystem.Colors.TextSecondary);
            _settingsButton.Click += (s, e) => SettingsClicked?.Invoke(this, e);

            // زر تسجيل الخروج
            _logoutButton = CreateResponsiveButton("🚪", buttonSize, ResponsiveDesignSystem.Colors.Error);
            _logoutButton.Click += (s, e) => LogoutClicked?.Invoke(this, e);

            // لوحة شارات الإشعارات
            _notificationBadgesPanel = new Panel
            {
                Size = new Size(_notificationsPanel.Width, _notificationsPanel.Height),
                BackColor = Color.Transparent,
                AutoScroll = false
            };
        }

        private Button CreateResponsiveButton(string icon, int size, Color color)
        {
            var button = new Button
            {
                Text = icon,
                Font = ResponsiveDesignSystem.Fonts.GetHighDpiFont(16),
                Size = new Size(size, size),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent,
                ForeColor = color,
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ResponsiveDesignSystem.Colors.SurfaceHover;
            button.FlatAppearance.MouseDownBackColor = ResponsiveDesignSystem.Colors.SurfacePressed;

            return button;
        }

        private void ArrangeResponsiveLayout()
        {
            // ترتيب العناصر في لوحة الشعار
            var logoY = (_logoPanel.Height - _appNameLabel.Height - _storeNameLabel.Height) / 2;
            _appNameLabel.Location = new Point(_logoPanel.Width - _appNameLabel.Width - 60, logoY);
            _storeNameLabel.Location = new Point(_logoPanel.Width - _storeNameLabel.Width - 60, logoY + _appNameLabel.Height + 2);

            // ترتيب العناصر في لوحة المستخدم
            var userY = (_userPanel.Height - _userNameLabel.Height - _userRoleLabel.Height) / 2;
            _userNameLabel.Location = new Point(_userPanel.Width - _userNameLabel.Width - 60, userY);
            _userRoleLabel.Location = new Point(_userPanel.Width - _userRoleLabel.Width - 60, userY + _userNameLabel.Height + 2);

            // ترتيب العناصر في لوحة الحالة
            var statusY = (_statusPanel.Height - _timeLabel.Height - _statusLabel.Height) / 2;
            _timeLabel.Location = new Point((_statusPanel.Width - _timeLabel.Width) / 2, statusY);
            _statusLabel.Location = new Point((_statusPanel.Width - _statusLabel.Width) / 2, statusY + _timeLabel.Height + 2);

            // ترتيب الأزرار
            var buttonSpacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetSmallSpacing();
            _settingsButton.Location = new Point(buttonSpacing, (_userPanel.Height - _settingsButton.Height) / 2);
            _logoutButton.Location = new Point(_userPanel.Width - _logoutButton.Width - buttonSpacing, (_userPanel.Height - _logoutButton.Height) / 2);

            // إضافة العناصر للوحات
            _logoPanel.Controls.AddRange(new Control[] { _appNameLabel, _storeNameLabel });
            _userPanel.Controls.AddRange(new Control[] { _userNameLabel, _userRoleLabel, _settingsButton, _logoutButton });
            _statusPanel.Controls.AddRange(new Control[] { _timeLabel, _statusLabel });
            _notificationsPanel.Controls.Add(_notificationBadgesPanel);

            // إضافة اللوحات للشريط العلوي
            Controls.AddRange(new Control[] { _logoPanel, _notificationsPanel, _statusPanel, _userPanel });
        }

        #endregion

        #region نظام الإشعارات

        private void LoadSampleNotifications()
        {
            _notifications.AddRange(new[]
            {
                new NotificationItem
                {
                    Type = NotificationType.LowStock,
                    Title = "مخزون منخفض",
                    Message = "23 منتج وصل للحد الأدنى",
                    Count = 23,
                    Icon = "📦",
                    Color = ResponsiveDesignSystem.Colors.Warning
                },
                new NotificationItem
                {
                    Type = NotificationType.PendingInstallments,
                    Title = "أقساط معلقة",
                    Message = "15 قسط مستحق اليوم",
                    Count = 15,
                    Icon = "📅",
                    Color = ResponsiveDesignSystem.Colors.Error
                },
                new NotificationItem
                {
                    Type = NotificationType.OverdueDebts,
                    Title = "ديون متأخرة",
                    Message = "8 عملاء لديهم ديون متأخرة",
                    Count = 8,
                    Icon = "💳",
                    Color = ResponsiveDesignSystem.Colors.Error
                }
            });

            UpdateNotificationBadges();
        }

        private void UpdateNotificationBadges()
        {
            _notificationBadgesPanel.Controls.Clear();

            var badgeSize = ResponsiveLayoutSystem.ResponsiveDimensions.GetLargeIconSize();
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetSmallSpacing();
            var x = spacing;

            foreach (var notification in _notifications.Where(n => n.Count > 0))
            {
                var badge = CreateNotificationBadge(notification, badgeSize);
                badge.Location = new Point(x, (_notificationBadgesPanel.Height - badgeSize) / 2);
                _notificationBadgesPanel.Controls.Add(badge);
                x += badgeSize + spacing;
            }
        }

        private Control CreateNotificationBadge(NotificationItem notification, int size)
        {
            var badge = new Panel
            {
                Size = new Size(size, size),
                BackColor = Color.Transparent,
                Cursor = Cursors.Hand,
                Tag = notification
            };

            badge.Click += (s, e) => NotificationClicked?.Invoke(this, new NotificationClickedEventArgs(notification));
            badge.Paint += (s, e) => DrawNotificationBadge(e.Graphics, badge.ClientRectangle, notification);

            return badge;
        }

        private void DrawNotificationBadge(Graphics g, Rectangle bounds, NotificationItem notification)
        {
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // رسم دائرة الخلفية
            using (var backgroundBrush = new SolidBrush(Color.FromArgb(50, notification.Color)))
            {
                g.FillEllipse(backgroundBrush, bounds);
            }

            // رسم الحدود
            using (var borderPen = new Pen(notification.Color, 2))
            {
                g.DrawEllipse(borderPen, bounds);
            }

            // رسم الأيقونة
            var iconFont = ResponsiveDesignSystem.Fonts.GetHighDpiFont(bounds.Width * 0.4f);
            using (var iconBrush = new SolidBrush(notification.Color))
            {
                var iconFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };
                g.DrawString(notification.Icon, iconFont, iconBrush, bounds, iconFormat);
            }

            // رسم شارة العدد
            if (notification.Count > 0)
            {
                var badgeSize = bounds.Width / 3;
                var badgeBounds = new Rectangle(
                    bounds.Right - badgeSize,
                    bounds.Top,
                    badgeSize,
                    badgeSize
                );

                using (var badgeBrush = new SolidBrush(ResponsiveDesignSystem.Colors.Error))
                {
                    g.FillEllipse(badgeBrush, badgeBounds);
                }

                var countText = notification.Count > 99 ? "99+" : notification.Count.ToString();
                var countFont = ResponsiveDesignSystem.Fonts.GetHighDpiFont(badgeSize * 0.4f, FontStyle.Bold);
                using (var countBrush = new SolidBrush(ResponsiveDesignSystem.Colors.TextOnPrimary))
                {
                    var countFormat = new StringFormat
                    {
                        Alignment = StringAlignment.Center,
                        LineAlignment = StringAlignment.Center
                    };
                    g.DrawString(countText, countFont, countBrush, badgeBounds, countFormat);
                }
            }
        }

        #endregion

        #region التحديثات والأحداث

        private void SetupResponsiveDesign()
        {
            RightToLeft = RightToLeft.Yes;
            Paint += OnPaint;
            Resize += OnResize;
        }

        private void StartClock()
        {
            _clockTimer = new Timer { Interval = 1000 };
            _clockTimer.Tick += (s, e) => UpdateTime();
            _clockTimer.Start();
        }

        private void UpdateUserInfo()
        {
            if (_userNameLabel != null) _userNameLabel.Text = _userName;
            if (_userRoleLabel != null) _userRoleLabel.Text = _userRole;
        }

        private void UpdateStoreInfo()
        {
            if (_storeNameLabel != null) _storeNameLabel.Text = _storeName;
        }

        private void UpdateTime()
        {
            if (_timeLabel != null) _timeLabel.Text = DateTime.Now.ToString("HH:mm:ss");
        }

        private void UpdateStatus()
        {
            if (_statusLabel != null)
            {
                _statusLabel.Text = _isOnline ? "متصل" : "غير متصل";
                _statusLabel.ForeColor = _isOnline ?
                    ResponsiveDesignSystem.Colors.Success :
                    ResponsiveDesignSystem.Colors.Error;
            }
        }

        private void OnResize(object sender, EventArgs e)
        {
            ResponsiveLayoutSystem.HandleResize(this.FindForm());
            ArrangeResponsiveLayout();
        }

        private void OnPaint(object sender, PaintEventArgs e)
        {
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // رسم الخلفية
            using (var backgroundBrush = new SolidBrush(ResponsiveDesignSystem.Colors.Surface))
            {
                g.FillRectangle(backgroundBrush, ClientRectangle);
            }

            // رسم الحد السفلي
            using (var borderPen = new Pen(ResponsiveDesignSystem.Colors.Border, 1))
            {
                g.DrawLine(borderPen, 0, Height - 1, Width, Height - 1);
            }

            // رسم أيقونة التطبيق
            var logoSize = ResponsiveLayoutSystem.ResponsiveDimensions.GetLargeIconSize();
            var logoRect = new Rectangle(10, (Height - logoSize) / 2, logoSize, logoSize);
            
            ResponsiveDesignSystem.DrawResponsiveCard(g, logoRect, ResponsiveDesignSystem.Colors.Primary, 8);

            using (var iconBrush = new SolidBrush(ResponsiveDesignSystem.Colors.TextOnPrimary))
            {
                var iconFont = ResponsiveDesignSystem.Fonts.GetHighDpiFont(logoSize * 0.6f, FontStyle.Bold);
                var iconFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };
                g.DrawString("🏪", iconFont, iconBrush, logoRect, iconFormat);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _clockTimer?.Stop();
                _clockTimer?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }

    #region الكلاسات المساعدة

    public class NotificationItem
    {
        public NotificationType Type { get; set; }
        public string Title { get; set; }
        public string Message { get; set; }
        public int Count { get; set; }
        public string Icon { get; set; }
        public Color Color { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    public enum NotificationType
    {
        LowStock,
        PendingInstallments,
        OverdueDebts,
        SystemAlert,
        UserAction
    }

    public class NotificationClickedEventArgs : EventArgs
    {
        public NotificationItem Notification { get; }

        public NotificationClickedEventArgs(NotificationItem notification)
        {
            Notification = notification;
        }
    }

    #endregion
}
