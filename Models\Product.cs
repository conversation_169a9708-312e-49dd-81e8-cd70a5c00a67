using System;
using System.ComponentModel.DataAnnotations;

namespace AridooPOS.Models
{
    /// <summary>
    /// نموذج المنتج
    /// </summary>
    public class Product
    {
        public int ProductID { get; set; }
        
        [Required(ErrorMessage = "كود المنتج مطلوب")]
        [StringLength(20, ErrorMessage = "كود المنتج لا يجب أن يتجاوز 20 حرف")]
        public string ProductCode { get; set; }
        
        [Required(ErrorMessage = "اسم المنتج مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المنتج لا يجب أن يتجاوز 100 حرف")]
        public string ProductName { get; set; }
        
        [StringLength(100, ErrorMessage = "الاسم الإنجليزي لا يجب أن يتجاوز 100 حرف")]
        public string ProductNameEn { get; set; }
        
        public int? CategoryID { get; set; }
        
        [Required(ErrorMessage = "سعر البيع مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر البيع يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal UnitPrice { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "سعر التكلفة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal CostPrice { get; set; }
        
        [Range(0, int.MaxValue, ErrorMessage = "كمية المخزون يجب أن تكون أكبر من أو تساوي صفر")]
        public int StockQuantity { get; set; }
        
        [Range(0, int.MaxValue, ErrorMessage = "الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public int MinStockLevel { get; set; }
        
        [StringLength(50, ErrorMessage = "الباركود لا يجب أن يتجاوز 50 حرف")]
        public string Barcode { get; set; }
        
        [Range(0, 100, ErrorMessage = "معدل الضريبة يجب أن يكون بين 0 و 100")]
        public decimal TaxRate { get; set; }
        
        public bool IsActive { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public DateTime ModifiedDate { get; set; }
        
        // خصائص إضافية للعرض
        public string CategoryName { get; set; }
        
        public Product()
        {
            IsActive = true;
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;
            StockQuantity = 0;
            MinStockLevel = 0;
            TaxRate = 0;
            CostPrice = 0;
        }
        
        /// <summary>
        /// التحقق من توفر المنتج في المخزون
        /// </summary>
        /// <param name="requestedQuantity">الكمية المطلوبة</param>
        /// <returns>true إذا كان المنتج متوفر</returns>
        public bool IsAvailable(decimal requestedQuantity)
        {
            return IsActive && StockQuantity >= requestedQuantity;
        }
        
        /// <summary>
        /// التحقق من أن المخزون أقل من الحد الأدنى
        /// </summary>
        /// <returns>true إذا كان المخزون أقل من الحد الأدنى</returns>
        public bool IsLowStock()
        {
            return StockQuantity <= MinStockLevel;
        }
        
        /// <summary>
        /// حساب هامش الربح
        /// </summary>
        /// <returns>هامش الربح كنسبة مئوية</returns>
        public decimal GetProfitMargin()
        {
            if (CostPrice == 0) return 0;
            return ((UnitPrice - CostPrice) / CostPrice) * 100;
        }
        
        /// <summary>
        /// حساب مبلغ الربح للوحدة الواحدة
        /// </summary>
        /// <returns>مبلغ الربح</returns>
        public decimal GetProfitAmount()
        {
            return UnitPrice - CostPrice;
        }
        
        /// <summary>
        /// التحقق من صحة بيانات المنتج
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(ProductCode) &&
                   !string.IsNullOrEmpty(ProductName) &&
                   UnitPrice >= 0 &&
                   CostPrice >= 0 &&
                   StockQuantity >= 0;
        }
        
        /// <summary>
        /// تحديث كمية المخزون
        /// </summary>
        /// <param name="quantity">الكمية (موجبة للإضافة، سالبة للخصم)</param>
        public void UpdateStock(decimal quantity)
        {
            StockQuantity += (int)quantity;
            ModifiedDate = DateTime.Now;
            
            // التأكد من عدم وجود مخزون سالب
            if (StockQuantity < 0)
                StockQuantity = 0;
        }
        
        /// <summary>
        /// نسخ المنتج
        /// </summary>
        /// <returns>نسخة من المنتج</returns>
        public Product Clone()
        {
            return new Product
            {
                ProductID = this.ProductID,
                ProductCode = this.ProductCode,
                ProductName = this.ProductName,
                ProductNameEn = this.ProductNameEn,
                CategoryID = this.CategoryID,
                UnitPrice = this.UnitPrice,
                CostPrice = this.CostPrice,
                StockQuantity = this.StockQuantity,
                MinStockLevel = this.MinStockLevel,
                Barcode = this.Barcode,
                TaxRate = this.TaxRate,
                IsActive = this.IsActive,
                CreatedDate = this.CreatedDate,
                ModifiedDate = this.ModifiedDate,
                CategoryName = this.CategoryName
            };
        }
    }
}
