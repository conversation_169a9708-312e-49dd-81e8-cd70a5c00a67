using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace AredooPOS.Models
{
    /// <summary>
    /// نموذج المنتج - يمثل منتج في النظام
    /// يحتوي على جميع معلومات المنتج والأسعار والمخزون
    /// </summary>
    public class Product
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم المنتج في قاعدة البيانات (مفتاح أساسي)
        /// </summary>
        public int ProductID { get; set; }

        /// <summary>
        /// كود المنتج المعروض للمستخدم
        /// </summary>
        [Required(ErrorMessage = "كود المنتج مطلوب")]
        [StringLength(50, ErrorMessage = "كود المنتج لا يجب أن يتجاوز 50 حرف")]
        public string ProductCode { get; set; }

        /// <summary>
        /// اسم المنتج
        /// </summary>
        [Required(ErrorMessage = "اسم المنتج مطلوب")]
        [StringLength(200, ErrorMessage = "اسم المنتج لا يجب أن يتجاوز 200 حرف")]
        public string ProductName { get; set; }

        /// <summary>
        /// اسم المنتج باللغة الإنجليزية
        /// </summary>
        [StringLength(200, ErrorMessage = "اسم المنتج بالإنجليزية لا يجب أن يتجاوز 200 حرف")]
        public string ProductNameEn { get; set; }

        /// <summary>
        /// وصف المنتج
        /// </summary>
        [StringLength(1000, ErrorMessage = "وصف المنتج لا يجب أن يتجاوز 1000 حرف")]
        public string Description { get; set; }

        /// <summary>
        /// رقم الفئة (مفتاح خارجي)
        /// </summary>
        [Required(ErrorMessage = "فئة المنتج مطلوبة")]
        public int CategoryID { get; set; }

        /// <summary>
        /// اسم الفئة (للعرض)
        /// </summary>
        public string CategoryName { get; set; }

        #endregion

        #region معلومات الباركود

        /// <summary>
        /// الباركود الأساسي للمنتج
        /// </summary>
        [StringLength(50, ErrorMessage = "الباركود لا يجب أن يتجاوز 50 حرف")]
        public string Barcode { get; set; }

        /// <summary>
        /// نوع الباركود (EAN13, Code128, QR, إلخ)
        /// </summary>
        [StringLength(20, ErrorMessage = "نوع الباركود لا يجب أن يتجاوز 20 حرف")]
        public string BarcodeType { get; set; } = BarcodeTypes.EAN13;

        #endregion

        #region الأسعار والتكلفة

        /// <summary>
        /// سعر التكلفة (سعر الشراء)
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "سعر التكلفة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal CostPrice { get; set; }

        /// <summary>
        /// سعر البيع الأساسي
        /// </summary>
        [Required(ErrorMessage = "سعر البيع مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "سعر البيع يجب أن يكون أكبر من صفر")]
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// سعر الجملة
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "سعر الجملة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal WholesalePrice { get; set; }

        /// <summary>
        /// الحد الأدنى لسعر البيع
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الحد الأدنى للسعر يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal MinSalePrice { get; set; }

        /// <summary>
        /// نسبة الربح المستهدفة
        /// </summary>
        [Range(0, 1000, ErrorMessage = "نسبة الربح يجب أن تكون بين 0 و 1000")]
        public decimal ProfitMargin { get; set; }

        /// <summary>
        /// نسبة الضريبة
        /// </summary>
        [Range(0, 100, ErrorMessage = "نسبة الضريبة يجب أن تكون بين 0 و 100")]
        public decimal TaxRate { get; set; } = 15; // 15% ضريبة القيمة المضافة

        /// <summary>
        /// هل المنتج خاضع للضريبة
        /// </summary>
        public bool IsTaxable { get; set; } = true;

        #endregion

        #region معلومات المخزون

        /// <summary>
        /// الكمية الحالية في المخزون
        /// </summary>
        public decimal StockQuantity { get; set; }

        /// <summary>
        /// الحد الأدنى للمخزون (نقطة إعادة الطلب)
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal MinStockLevel { get; set; }

        /// <summary>
        /// الحد الأقصى للمخزون
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الحد الأقصى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal MaxStockLevel { get; set; }

        /// <summary>
        /// الكمية المحجوزة (في الطلبات المعلقة)
        /// </summary>
        public decimal ReservedStock { get; set; }

        /// <summary>
        /// الكمية المتاحة للبيع
        /// </summary>
        public decimal AvailableStock => StockQuantity - ReservedStock;

        /// <summary>
        /// وحدة القياس الأساسية
        /// </summary>
        [Required(ErrorMessage = "وحدة القياس مطلوبة")]
        [StringLength(20, ErrorMessage = "وحدة القياس لا يجب أن تتجاوز 20 حرف")]
        public string Unit { get; set; } = "قطعة";

        /// <summary>
        /// هل يتم تتبع المخزون لهذا المنتج
        /// </summary>
        public bool TrackStock { get; set; } = true;

        #endregion

        #region الصور والمرفقات

        /// <summary>
        /// مسار الصورة الأساسية
        /// </summary>
        [StringLength(500, ErrorMessage = "مسار الصورة لا يجب أن يتجاوز 500 حرف")]
        public string ImagePath { get; set; }

        #endregion

        #region الحالة والإعدادات

        /// <summary>
        /// هل المنتج نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل المنتج متاح للبيع
        /// </summary>
        public bool IsAvailableForSale { get; set; } = true;

        /// <summary>
        /// هل المنتج قابل للإرجاع
        /// </summary>
        public bool IsReturnable { get; set; } = true;

        /// <summary>
        /// تاريخ انتهاء الصلاحية
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        #endregion

        #region الملاحظات والمعلومات الإضافية

        /// <summary>
        /// ملاحظات عامة
        /// </summary>
        [StringLength(1000, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 1000 حرف")]
        public string Notes { get; set; }

        #endregion

        #region معلومات النظام

        /// <summary>
        /// منشئ السجل
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المنشئ لا يجب أن يتجاوز 50 حرف")]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ إنشاء السجل
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// آخر من عدل السجل
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المعدل لا يجب أن يتجاوز 50 حرف")]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime ModifiedDate { get; set; }

        #endregion

        #region القوائم المرتبطة

        /// <summary>
        /// فئة المنتج
        /// </summary>
        public Category Category { get; set; }

        /// <summary>
        /// حركات المخزون
        /// </summary>
        public List<StockMovement> StockMovements { get; set; }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ المنتج مع التهيئة الافتراضية
        /// </summary>
        public Product()
        {
            // تهيئة القوائم
            StockMovements = new List<StockMovement>();

            // تهيئة التواريخ
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;

            // تهيئة القيم الافتراضية
            BarcodeType = BarcodeTypes.EAN13;
            Unit = "قطعة";
            TaxRate = 15;
            IsTaxable = true;
            TrackStock = true;
            IsActive = true;
            IsAvailableForSale = true;
            IsReturnable = true;
            ProfitMargin = 0;
            StockQuantity = 0;
            ReservedStock = 0;
        }

        #endregion

        #region العمليات الحسابية

        /// <summary>
        /// حساب سعر البيع بناءً على التكلفة ونسبة الربح
        /// </summary>
        public void CalculateSalePriceFromMargin()
        {
            if (CostPrice > 0 && ProfitMargin > 0)
            {
                UnitPrice = CostPrice * (1 + ProfitMargin / 100);
                ModifiedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// حساب نسبة الربح بناءً على التكلفة وسعر البيع
        /// </summary>
        public void CalculateProfitMargin()
        {
            if (CostPrice > 0 && UnitPrice > CostPrice)
            {
                ProfitMargin = ((UnitPrice - CostPrice) / CostPrice) * 100;
                ModifiedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// حساب مبلغ الربح لوحدة واحدة
        /// </summary>
        /// <returns>مبلغ الربح</returns>
        public decimal GetProfitAmount()
        {
            return UnitPrice - CostPrice;
        }

        /// <summary>
        /// حساب السعر شامل الضريبة
        /// </summary>
        /// <returns>السعر شامل الضريبة</returns>
        public decimal GetPriceIncludingTax()
        {
            if (IsTaxable && TaxRate > 0)
            {
                return UnitPrice * (1 + TaxRate / 100);
            }
            return UnitPrice;
        }

        /// <summary>
        /// حساب مبلغ الضريبة
        /// </summary>
        /// <returns>مبلغ الضريبة</returns>
        public decimal GetTaxAmount()
        {
            if (IsTaxable && TaxRate > 0)
            {
                return UnitPrice * (TaxRate / 100);
            }
            return 0;
        }

        /// <summary>
        /// حساب قيمة المخزون الحالي
        /// </summary>
        /// <returns>قيمة المخزون</returns>
        public decimal GetStockValue()
        {
            return StockQuantity * CostPrice;
        }

        #endregion

        #region عمليات المخزون

        /// <summary>
        /// إضافة كمية للمخزون
        /// </summary>
        /// <param name="quantity">الكمية</param>
        /// <param name="reason">السبب</param>
        public void AddStock(decimal quantity, string reason = "إضافة مخزون")
        {
            if (quantity > 0)
            {
                StockQuantity += quantity;
                ModifiedDate = DateTime.Now;

                // إضافة حركة مخزون
                AddStockMovement(quantity, StockMovementTypes.StockIn, reason);
            }
        }

        /// <summary>
        /// خصم كمية من المخزون
        /// </summary>
        /// <param name="quantity">الكمية</param>
        /// <param name="reason">السبب</param>
        /// <returns>true إذا تم الخصم بنجاح</returns>
        public bool DeductStock(decimal quantity, string reason = "بيع")
        {
            if (quantity > 0 && AvailableStock >= quantity)
            {
                StockQuantity -= quantity;
                ModifiedDate = DateTime.Now;

                // إضافة حركة مخزون
                AddStockMovement(-quantity, StockMovementTypes.StockOut, reason);

                return true;
            }
            return false;
        }

        /// <summary>
        /// حجز كمية من المخزون
        /// </summary>
        /// <param name="quantity">الكمية</param>
        /// <returns>true إذا تم الحجز بنجاح</returns>
        public bool ReserveStock(decimal quantity)
        {
            if (quantity > 0 && AvailableStock >= quantity)
            {
                ReservedStock += quantity;
                ModifiedDate = DateTime.Now;
                return true;
            }
            return false;
        }

        /// <summary>
        /// إلغاء حجز كمية من المخزون
        /// </summary>
        /// <param name="quantity">الكمية</param>
        public void UnreserveStock(decimal quantity)
        {
            if (quantity > 0 && ReservedStock >= quantity)
            {
                ReservedStock -= quantity;
                ModifiedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// إضافة حركة مخزون
        /// </summary>
        /// <param name="quantity">الكمية</param>
        /// <param name="movementType">نوع الحركة</param>
        /// <param name="reason">السبب</param>
        private void AddStockMovement(decimal quantity, string movementType, string reason)
        {
            var movement = new StockMovement
            {
                ProductID = this.ProductID,
                MovementDate = DateTime.Now,
                MovementType = movementType,
                Quantity = quantity,
                StockAfter = this.StockQuantity,
                Reason = reason,
                CreatedDate = DateTime.Now
            };

            StockMovements.Add(movement);
        }

        #endregion

        #region عمليات التحقق والتصديق

        /// <summary>
        /// التحقق من صحة بيانات المنتج
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(ProductCode) &&
                   !string.IsNullOrWhiteSpace(ProductName) &&
                   CategoryID > 0 &&
                   UnitPrice > 0 &&
                   !string.IsNullOrWhiteSpace(Unit);
        }

        /// <summary>
        /// التحقق من انخفاض المخزون
        /// </summary>
        /// <returns>true إذا كان المخزون منخفض</returns>
        public bool IsLowStock()
        {
            return TrackStock && StockQuantity <= MinStockLevel;
        }

        /// <summary>
        /// التحقق من نفاد المخزون
        /// </summary>
        /// <returns>true إذا نفد المخزون</returns>
        public bool IsOutOfStock()
        {
            return TrackStock && StockQuantity <= 0;
        }

        /// <summary>
        /// التحقق من توفر كمية للبيع
        /// </summary>
        /// <param name="quantity">الكمية المطلوبة</param>
        /// <returns>true إذا كانت الكمية متوفرة</returns>
        public bool IsQuantityAvailable(decimal quantity)
        {
            return !TrackStock || AvailableStock >= quantity;
        }

        /// <summary>
        /// التحقق من انتهاء الصلاحية
        /// </summary>
        /// <returns>true إذا انتهت الصلاحية</returns>
        public bool IsExpired()
        {
            return ExpiryDate.HasValue && ExpiryDate.Value < DateTime.Now;
        }

        #endregion

        #region عمليات النسخ والتحويل

        /// <summary>
        /// تحويل المنتج إلى نص وصفي
        /// </summary>
        /// <returns>وصف نصي للمنتج</returns>
        public override string ToString()
        {
            return $"{ProductCode} - {ProductName} - {UnitPrice:C}";
        }

        #endregion
    }

    #region الثوابت والتعدادات

    /// <summary>
    /// أنواع الباركود
    /// </summary>
    public static class BarcodeTypes
    {
        public const string EAN13 = "EAN13";
        public const string EAN8 = "EAN8";
        public const string Code128 = "Code128";
        public const string Code39 = "Code39";
        public const string QRCode = "QRCode";
        public const string DataMatrix = "DataMatrix";
    }

    /// <summary>
    /// أنواع حركات المخزون
    /// </summary>
    public static class StockMovementTypes
    {
        public const string StockIn = "وارد";
        public const string StockOut = "صادر";
        public const string Adjustment = "تسوية";
        public const string Transfer = "تحويل";
        public const string Return = "مرتجع";
        public const string Damage = "تالف";
        public const string Loss = "فقدان";
    }

    #endregion
}
