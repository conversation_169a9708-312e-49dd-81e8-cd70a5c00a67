using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using AridooPOS.Models;

namespace AridooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات للعملاء
    /// </summary>
    public class CustomerDAL
    {
        /// <summary>
        /// الحصول على عميل بالرقم
        /// </summary>
        /// <param name="customerId">رقم العميل</param>
        /// <returns>بيانات العميل</returns>
        public Customer GetCustomerById(int customerId)
        {
            try
            {
                string query = "SELECT * FROM Customers WHERE CustomerID = @CustomerID";

                SqlParameter[] parameters = {
                    new SqlParameter("@CustomerID", customerId)
                };

                DataTable result = DatabaseConnection.ExecuteQuery(query, parameters);
                
                if (result.Rows.Count > 0)
                {
                    return MapDataRowToCustomer(result.Rows[0]);
                }
                
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على العميل: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على عميل بالكود
        /// </summary>
        /// <param name="customerCode">كود العميل</param>
        /// <returns>بيانات العميل</returns>
        public Customer GetCustomerByCode(string customerCode)
        {
            try
            {
                string query = "SELECT * FROM Customers WHERE CustomerCode = @CustomerCode";

                SqlParameter[] parameters = {
                    new SqlParameter("@CustomerCode", customerCode)
                };

                DataTable result = DatabaseConnection.ExecuteQuery(query, parameters);
                
                if (result.Rows.Count > 0)
                {
                    return MapDataRowToCustomer(result.Rows[0]);
                }
                
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على العميل: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// البحث عن العملاء
        /// </summary>
        /// <param name="searchText">نص البحث</param>
        /// <returns>قائمة العملاء</returns>
        public List<Customer> SearchCustomers(string searchText)
        {
            try
            {
                string query = @"
                    SELECT * FROM Customers 
                    WHERE IsActive = 1 AND (
                        CustomerName LIKE @SearchText OR 
                        CustomerCode LIKE @SearchText OR 
                        Phone LIKE @SearchText
                    )
                    ORDER BY CustomerName";

                SqlParameter[] parameters = {
                    new SqlParameter("@SearchText", $"%{searchText}%")
                };

                DataTable result = DatabaseConnection.ExecuteQuery(query, parameters);
                
                List<Customer> customers = new List<Customer>();
                foreach (DataRow row in result.Rows)
                {
                    customers.Add(MapDataRowToCustomer(row));
                }
                
                return customers;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث عن العملاء: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث عميل
        /// </summary>
        /// <param name="customer">بيانات العميل</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateCustomer(Customer customer)
        {
            try
            {
                string query = @"
                    UPDATE Customers SET
                        CustomerName = @CustomerName,
                        Phone = @Phone,
                        Address = @Address,
                        Email = @Email,
                        CreditLimit = @CreditLimit,
                        CurrentBalance = @CurrentBalance,
                        IsActive = @IsActive,
                        ModifiedDate = GETDATE()
                    WHERE CustomerID = @CustomerID";

                SqlParameter[] parameters = {
                    new SqlParameter("@CustomerID", customer.CustomerID),
                    new SqlParameter("@CustomerName", customer.CustomerName),
                    new SqlParameter("@Phone", customer.Phone ?? ""),
                    new SqlParameter("@Address", customer.Address ?? ""),
                    new SqlParameter("@Email", customer.Email ?? ""),
                    new SqlParameter("@CreditLimit", customer.CreditLimit),
                    new SqlParameter("@CurrentBalance", customer.CurrentBalance),
                    new SqlParameter("@IsActive", customer.IsActive)
                };

                int rowsAffected = DatabaseConnection.ExecuteNonQuery(query, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث العميل: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على جميع العملاء النشطين
        /// </summary>
        /// <returns>قائمة العملاء</returns>
        public List<Customer> GetAllActiveCustomers()
        {
            try
            {
                string query = @"
                    SELECT * FROM Customers 
                    WHERE IsActive = 1
                    ORDER BY CustomerName";

                DataTable result = DatabaseConnection.ExecuteQuery(query);
                
                List<Customer> customers = new List<Customer>();
                foreach (DataRow row in result.Rows)
                {
                    customers.Add(MapDataRowToCustomer(row));
                }
                
                return customers;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على العملاء: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحويل صف البيانات إلى كائن عميل
        /// </summary>
        /// <param name="row">صف البيانات</param>
        /// <returns>كائن العميل</returns>
        private Customer MapDataRowToCustomer(DataRow row)
        {
            return new Customer
            {
                CustomerID = Convert.ToInt32(row["CustomerID"]),
                CustomerCode = row["CustomerCode"].ToString(),
                CustomerName = row["CustomerName"].ToString(),
                Phone = row["Phone"].ToString(),
                Address = row["Address"].ToString(),
                Email = row["Email"].ToString(),
                CreditLimit = Convert.ToDecimal(row["CreditLimit"]),
                CurrentBalance = Convert.ToDecimal(row["CurrentBalance"]),
                IsActive = Convert.ToBoolean(row["IsActive"]),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                ModifiedDate = Convert.ToDateTime(row["ModifiedDate"])
            };
        }
    }
}
