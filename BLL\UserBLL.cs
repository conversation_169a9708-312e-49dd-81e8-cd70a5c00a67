using System;
using System.Collections.Generic;
using System.Linq;
using AredooPOS.Models;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.BLL
{
    /// <summary>
    /// طبقة منطق الأعمال للمستخدمين
    /// تحتوي على جميع العمليات التجارية المتعلقة بإدارة المستخدمين
    /// </summary>
    public class UserBLL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly UserDAL _userDAL;
        private readonly RoleDAL _roleDAL;
        private readonly UserActivityDAL _activityDAL;
        private readonly ILogger<UserBLL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة منطق الأعمال للمستخدمين
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public UserBLL(string connectionString = null, ILogger<UserBLL> logger = null)
        {
            _userDAL = new UserDAL(connectionString, null);
            _roleDAL = new RoleDAL(connectionString, null);
            _activityDAL = new UserActivityDAL(connectionString, null);
            _logger = logger;
        }

        #endregion

        #region إدارة المستخدمين

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>رقم المستخدم الجديد</returns>
        public int AddUser(User user, string currentUser)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateUser(user, true);

                // التحقق من عدم تكرار اسم المستخدم
                if (_userDAL.UsernameExists(user.Username))
                {
                    throw new InvalidOperationException("اسم المستخدم موجود مسبقاً");
                }

                // التحقق من عدم تكرار البريد الإلكتروني
                if (!string.IsNullOrWhiteSpace(user.Email) && _userDAL.EmailExists(user.Email))
                {
                    throw new InvalidOperationException("البريد الإلكتروني موجود مسبقاً");
                }

                // التحقق من وجود الدور
                var role = _roleDAL.GetRoleById(user.RoleID);
                if (role == null)
                {
                    throw new InvalidOperationException("الدور المحدد غير موجود");
                }

                // تعيين معلومات النظام
                user.CreatedBy = currentUser;
                user.ModifiedBy = currentUser;
                user.CreatedDate = DateTime.Now;
                user.ModifiedDate = DateTime.Now;

                // نسخ الصلاحيات من الدور
                CopyPermissionsFromRole(user, role);

                // إضافة المستخدم
                var userId = _userDAL.AddUser(user);

                // تسجيل النشاط
                LogActivity(userId, ActivityTypes.Create, $"تم إضافة مستخدم جديد: {user.Username}", 
                    SystemModules.UserManagement, SystemActions.Add, currentUser);

                _logger?.LogInformation($"تم إضافة مستخدم جديد: {user.Username} برقم {userId}");
                return userId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إضافة المستخدم {user?.Username}");
                throw;
            }
        }

        /// <summary>
        /// تحديث بيانات المستخدم
        /// </summary>
        /// <param name="user">بيانات المستخدم المحدثة</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateUser(User user, string currentUser)
        {
            try
            {
                // التحقق من وجود المستخدم
                var existingUser = _userDAL.GetUserById(user.UserID);
                if (existingUser == null)
                {
                    throw new InvalidOperationException("المستخدم غير موجود");
                }

                // التحقق من صحة البيانات
                ValidateUser(user, false);

                // التحقق من عدم تكرار اسم المستخدم
                if (_userDAL.UsernameExists(user.Username, user.UserID))
                {
                    throw new InvalidOperationException("اسم المستخدم موجود مسبقاً");
                }

                // التحقق من عدم تكرار البريد الإلكتروني
                if (!string.IsNullOrWhiteSpace(user.Email) && _userDAL.EmailExists(user.Email, user.UserID))
                {
                    throw new InvalidOperationException("البريد الإلكتروني موجود مسبقاً");
                }

                // التحقق من وجود الدور
                var role = _roleDAL.GetRoleById(user.RoleID);
                if (role == null)
                {
                    throw new InvalidOperationException("الدور المحدد غير موجود");
                }

                // تعيين معلومات التعديل
                user.ModifiedBy = currentUser;
                user.ModifiedDate = DateTime.Now;
                user.CreatedBy = existingUser.CreatedBy;
                user.CreatedDate = existingUser.CreatedDate;

                // نسخ الصلاحيات من الدور إذا تغير
                if (user.RoleID != existingUser.RoleID)
                {
                    CopyPermissionsFromRole(user, role);
                }

                // تحديث المستخدم
                var success = _userDAL.UpdateUser(user);

                if (success)
                {
                    // تسجيل النشاط
                    LogActivity(user.UserID, ActivityTypes.Update, $"تم تحديث بيانات المستخدم: {user.Username}", 
                        SystemModules.UserManagement, SystemActions.Edit, currentUser);

                    _logger?.LogInformation($"تم تحديث المستخدم: {user.Username}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث المستخدم {user?.UserID}");
                throw;
            }
        }

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public bool DeleteUser(int userId, string currentUser)
        {
            try
            {
                var user = _userDAL.GetUserById(userId);
                if (user == null)
                {
                    throw new InvalidOperationException("المستخدم غير موجود");
                }

                // التحقق من عدم حذف مدير النظام الوحيد
                if (user.IsSystemAdmin)
                {
                    var adminCount = _userDAL.GetAllUsers(true).Count(u => u.IsSystemAdmin && u.IsActive);
                    if (adminCount <= 1)
                    {
                        throw new InvalidOperationException("لا يمكن حذف مدير النظام الوحيد");
                    }
                }

                var success = _userDAL.DeleteUser(userId, currentUser);

                if (success)
                {
                    // تسجيل النشاط
                    LogActivity(userId, ActivityTypes.Delete, $"تم حذف المستخدم: {user.Username}", 
                        SystemModules.UserManagement, SystemActions.Delete, currentUser);

                    _logger?.LogInformation($"تم حذف المستخدم: {user.Username}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حذف المستخدم {userId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على مستخدم بالرقم
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <returns>بيانات المستخدم</returns>
        public User GetUserById(int userId)
        {
            try
            {
                return _userDAL.GetUserById(userId);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على المستخدم {userId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على مستخدم باسم المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>بيانات المستخدم</returns>
        public User GetUserByUsername(string username)
        {
            try
            {
                return _userDAL.GetUserByUsername(username);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على المستخدم {username}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع المستخدمين
        /// </summary>
        /// <param name="includeInactive">تضمين المستخدمين غير النشطين</param>
        /// <returns>قائمة المستخدمين</returns>
        public List<User> GetAllUsers(bool includeInactive = false)
        {
            try
            {
                return _userDAL.GetAllUsers(includeInactive);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على جميع المستخدمين");
                throw;
            }
        }

        /// <summary>
        /// البحث في المستخدمين
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="roleId">رقم الدور (اختياري)</param>
        /// <param name="includeInactive">تضمين المستخدمين غير النشطين</param>
        /// <returns>قائمة المستخدمين المطابقة</returns>
        public List<User> SearchUsers(string searchTerm, int? roleId = null, bool includeInactive = false)
        {
            try
            {
                return _userDAL.SearchUsers(searchTerm, roleId, includeInactive);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في البحث في المستخدمين");
                throw;
            }
        }

        #endregion

        #region الإحصائيات والتقارير

        /// <summary>
        /// الحصول على إحصائيات المستخدمين
        /// </summary>
        /// <returns>إحصائيات المستخدمين</returns>
        public UserStatistics GetUserStatistics()
        {
            try
            {
                return _userDAL.GetUserStatistics();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إحصائيات المستخدمين");
                throw;
            }
        }

        /// <summary>
        /// الحصول على المستخدمين حسب الدور
        /// </summary>
        /// <param name="roleId">رقم الدور</param>
        /// <param name="includeInactive">تضمين المستخدمين غير النشطين</param>
        /// <returns>قائمة المستخدمين</returns>
        public List<User> GetUsersByRole(int roleId, bool includeInactive = false)
        {
            try
            {
                return _userDAL.GetUsersByRole(roleId, includeInactive);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على المستخدمين للدور {roleId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على المستخدمين الذين تنتهي صلاحية كلمات مرورهم قريباً
        /// </summary>
        /// <param name="daysBeforeExpiry">عدد الأيام قبل انتهاء الصلاحية</param>
        /// <returns>قائمة المستخدمين</returns>
        public List<User> GetUsersWithExpiringPasswords(int daysBeforeExpiry = 7)
        {
            try
            {
                var allUsers = _userDAL.GetAllUsers(false);
                var expiringDate = DateTime.Now.AddDays(daysBeforeExpiry);

                return allUsers.Where(u => u.PasswordExpiryDate.HasValue &&
                                          u.PasswordExpiryDate.Value <= expiringDate &&
                                          u.PasswordExpiryDate.Value > DateTime.Now)
                              .ToList();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على المستخدمين ذوي كلمات المرور المنتهية الصلاحية");
                throw;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// التحقق من صحة بيانات المستخدم
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        /// <param name="isNew">هل المستخدم جديد</param>
        private void ValidateUser(User user, bool isNew)
        {
            if (user == null)
                throw new ArgumentNullException(nameof(user));

            if (string.IsNullOrWhiteSpace(user.Username))
                throw new ArgumentException("اسم المستخدم مطلوب");

            if (user.Username.Length < 3)
                throw new ArgumentException("اسم المستخدم يجب أن يكون 3 أحرف على الأقل");

            if (string.IsNullOrWhiteSpace(user.FirstName))
                throw new ArgumentException("الاسم الأول مطلوب");

            if (string.IsNullOrWhiteSpace(user.LastName))
                throw new ArgumentException("الاسم الأخير مطلوب");

            if (user.RoleID <= 0)
                throw new ArgumentException("دور المستخدم مطلوب");

            if (!string.IsNullOrWhiteSpace(user.Email) && !IsValidEmail(user.Email))
                throw new ArgumentException("البريد الإلكتروني غير صحيح");

            if (isNew && string.IsNullOrWhiteSpace(user.PasswordHash))
                throw new ArgumentException("كلمة المرور مطلوبة للمستخدم الجديد");
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        /// <param name="email">البريد الإلكتروني</param>
        /// <returns>true إذا كان البريد الإلكتروني صحيح</returns>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من قوة كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        private void ValidatePasswordStrength(string password)
        {
            if (string.IsNullOrWhiteSpace(password))
                throw new ArgumentException("كلمة المرور مطلوبة");

            if (password.Length < 8)
                throw new ArgumentException("كلمة المرور يجب أن تكون 8 أحرف على الأقل");

            bool hasUpper = password.Any(char.IsUpper);
            bool hasLower = password.Any(char.IsLower);
            bool hasDigit = password.Any(char.IsDigit);
            bool hasSpecial = password.Any(c => !char.IsLetterOrDigit(c));

            if (!hasUpper)
                throw new ArgumentException("كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل");

            if (!hasLower)
                throw new ArgumentException("كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل");

            if (!hasDigit)
                throw new ArgumentException("كلمة المرور يجب أن تحتوي على رقم واحد على الأقل");

            if (!hasSpecial)
                throw new ArgumentException("كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل");
        }

        /// <summary>
        /// نسخ الصلاحيات من الدور إلى المستخدم
        /// </summary>
        /// <param name="user">المستخدم</param>
        /// <param name="role">الدور</param>
        private void CopyPermissionsFromRole(User user, Role role)
        {
            user.RoleName = role.RoleName;
            user.PermissionLevel = role.PermissionLevel;
            user.IsSystemAdmin = role.IsAdminRole;
            user.CanAccessSystem = role.CanAccessSystem;
            user.CanManageUsers = role.CanManageUsers;
            user.CanViewReports = role.CanViewReports;
            user.CanManageProducts = role.CanManageProducts;
            user.CanProcessSales = role.CanProcessSales;
        }

        /// <summary>
        /// تسجيل نشاط المستخدم
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="activityType">نوع النشاط</param>
        /// <param name="description">وصف النشاط</param>
        /// <param name="module">الوحدة</param>
        /// <param name="action">الإجراء</param>
        /// <param name="performedBy">من قام بالعملية</param>
        /// <param name="isSecuritySensitive">هل النشاط حساس أمنياً</param>
        private void LogActivity(int userId, string activityType, string description, string module,
            string action, string performedBy, bool isSecuritySensitive = false)
        {
            try
            {
                var activity = new UserActivity(userId, activityType, description, module, action)
                {
                    IsSecuritySensitive = isSecuritySensitive,
                    RequiresReview = isSecuritySensitive,
                    Severity = isSecuritySensitive ? ActivitySeverity.High : ActivitySeverity.Medium
                };

                _activityDAL.AddUserActivity(activity);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تسجيل نشاط المستخدم");
                // لا نرمي الاستثناء هنا لأن تسجيل النشاط لا يجب أن يؤثر على العملية الأساسية
            }
        }

        #endregion
    }

    #region المساعدات

    /// <summary>
    /// مساعد تشفير كلمات المرور
    /// </summary>
    public static class PasswordHelper
    {
        /// <summary>
        /// توليد ملح عشوائي
        /// </summary>
        /// <returns>الملح</returns>
        public static string GenerateSalt()
        {
            var saltBytes = new byte[32];
            using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
            {
                rng.GetBytes(saltBytes);
            }
            return Convert.ToBase64String(saltBytes);
        }

        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <param name="salt">الملح</param>
        /// <returns>كلمة المرور المشفرة</returns>
        public static string HashPassword(string password, string salt)
        {
            using var pbkdf2 = new System.Security.Cryptography.Rfc2898DeriveBytes(password, Convert.FromBase64String(salt), 10000);
            var hash = pbkdf2.GetBytes(32);
            return Convert.ToBase64String(hash);
        }

        /// <summary>
        /// التحقق من كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <param name="hash">الهاش المحفوظ</param>
        /// <param name="salt">الملح</param>
        /// <returns>true إذا كانت كلمة المرور صحيحة</returns>
        public static bool VerifyPassword(string password, string hash, string salt)
        {
            var computedHash = HashPassword(password, salt);
            return computedHash == hash;
        }

        /// <summary>
        /// توليد كلمة مرور عشوائية
        /// </summary>
        /// <param name="length">طول كلمة المرور</param>
        /// <returns>كلمة المرور العشوائية</returns>
        public static string GenerateRandomPassword(int length = 12)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
    }

    #endregion
}

        #region إدارة كلمات المرور

        /// <summary>
        /// تغيير كلمة المرور
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="oldPassword">كلمة المرور القديمة</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم التغيير بنجاح</returns>
        public bool ChangePassword(int userId, string oldPassword, string newPassword, string currentUser)
        {
            try
            {
                var user = _userDAL.GetUserById(userId);
                if (user == null)
                {
                    throw new InvalidOperationException("المستخدم غير موجود");
                }

                // التحقق من كلمة المرور القديمة
                if (!PasswordHelper.VerifyPassword(oldPassword, user.PasswordHash, user.PasswordSalt))
                {
                    throw new InvalidOperationException("كلمة المرور القديمة غير صحيحة");
                }

                // التحقق من قوة كلمة المرور الجديدة
                ValidatePasswordStrength(newPassword);

                // تشفير كلمة المرور الجديدة
                var salt = PasswordHelper.GenerateSalt();
                var hash = PasswordHelper.HashPassword(newPassword, salt);

                // تحديث كلمة المرور
                var success = _userDAL.UpdatePassword(userId, hash, salt, DateTime.Now.AddDays(90), currentUser);

                if (success)
                {
                    // تسجيل النشاط
                    LogActivity(userId, ActivityTypes.Update, "تم تغيير كلمة المرور", 
                        SystemModules.UserManagement, "تغيير كلمة المرور", currentUser, true);

                    _logger?.LogInformation($"تم تغيير كلمة مرور المستخدم {user.Username}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تغيير كلمة مرور المستخدم {userId}");
                throw;
            }
        }

        /// <summary>
        /// إعادة تعيين كلمة المرور
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="tempPassword">كلمة المرور المؤقتة</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم إعادة التعيين بنجاح</returns>
        public bool ResetPassword(int userId, string tempPassword, string currentUser)
        {
            try
            {
                var user = _userDAL.GetUserById(userId);
                if (user == null)
                {
                    throw new InvalidOperationException("المستخدم غير موجود");
                }

                // تشفير كلمة المرور المؤقتة
                var salt = PasswordHelper.GenerateSalt();
                var hash = PasswordHelper.HashPassword(tempPassword, salt);

                // إعادة تعيين كلمة المرور
                var success = _userDAL.ResetPassword(userId, hash, salt, currentUser);

                if (success)
                {
                    // تسجيل النشاط
                    LogActivity(userId, ActivityTypes.Update, $"تم إعادة تعيين كلمة مرور المستخدم: {user.Username}", 
                        SystemModules.UserManagement, "إعادة تعيين كلمة المرور", currentUser, true);

                    _logger?.LogInformation($"تم إعادة تعيين كلمة مرور المستخدم {user.Username}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إعادة تعيين كلمة مرور المستخدم {userId}");
                throw;
            }
        }

        #endregion

        #region إدارة الأمان

        /// <summary>
        /// قفل حساب المستخدم
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="reason">سبب القفل</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم القفل بنجاح</returns>
        public bool LockUser(int userId, string reason, string currentUser)
        {
            try
            {
                var user = _userDAL.GetUserById(userId);
                if (user == null)
                {
                    throw new InvalidOperationException("المستخدم غير موجود");
                }

                if (user.IsLocked)
                {
                    throw new InvalidOperationException("المستخدم مقفل مسبقاً");
                }

                var success = _userDAL.LockUser(userId, reason, currentUser);

                if (success)
                {
                    // تسجيل النشاط
                    LogActivity(userId, ActivityTypes.Security, $"تم قفل المستخدم: {user.Username} - السبب: {reason}", 
                        SystemModules.Security, SystemActions.Lock, currentUser, true);

                    _logger?.LogInformation($"تم قفل المستخدم {user.Username}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في قفل المستخدم {userId}");
                throw;
            }
        }

        /// <summary>
        /// إلغاء قفل حساب المستخدم
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم إلغاء القفل بنجاح</returns>
        public bool UnlockUser(int userId, string currentUser)
        {
            try
            {
                var user = _userDAL.GetUserById(userId);
                if (user == null)
                {
                    throw new InvalidOperationException("المستخدم غير موجود");
                }

                if (!user.IsLocked)
                {
                    throw new InvalidOperationException("المستخدم غير مقفل");
                }

                var success = _userDAL.UnlockUser(userId, currentUser);

                if (success)
                {
                    // تسجيل النشاط
                    LogActivity(userId, ActivityTypes.Security, $"تم إلغاء قفل المستخدم: {user.Username}", 
                        SystemModules.Security, SystemActions.Unlock, currentUser, true);

                    _logger?.LogInformation($"تم إلغاء قفل المستخدم {user.Username}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إلغاء قفل المستخدم {userId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على المستخدمين المقفلين
        /// </summary>
        /// <returns>قائمة المستخدمين المقفلين</returns>
        public List<User> GetLockedUsers()
        {
            try
            {
                return _userDAL.GetLockedUsers();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على المستخدمين المقفلين");
                throw;
            }
        }

        #endregion
