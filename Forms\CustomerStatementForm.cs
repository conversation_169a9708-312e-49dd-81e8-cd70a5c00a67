using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AredooPOS.Models;
using AredooPOS.BLL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// واجهة كشف حساب العميل
    /// توفر عرض تفصيلي لجميع معاملات العميل وحالة حسابه
    /// </summary>
    public partial class CustomerStatementForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly CustomerBLL _customerBLL;
        private readonly DebtBLL _debtBLL;
        private readonly ILogger<CustomerStatementForm> _logger;

        // ألوان النظام
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SuccessColor = Color.FromArgb(46, 204, 113);
        private readonly Color WarningColor = Color.FromArgb(241, 196, 15);
        private readonly Color DangerColor = Color.FromArgb(231, 76, 60);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);

        // البيانات الحالية
        private Customer _customer;
        private DateTime _fromDate;
        private DateTime _toDate;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ واجهة كشف حساب العميل
        /// </summary>
        /// <param name="customerBLL">طبقة منطق الأعمال للعملاء</param>
        /// <param name="debtBLL">طبقة منطق الأعمال للديون</param>
        /// <param name="customer">العميل المحدد</param>
        /// <param name="logger">مسجل الأحداث</param>
        public CustomerStatementForm(CustomerBLL customerBLL, DebtBLL debtBLL, Customer customer, ILogger<CustomerStatementForm> logger = null)
        {
            _customerBLL = customerBLL ?? throw new ArgumentNullException(nameof(customerBLL));
            _debtBLL = debtBLL ?? throw new ArgumentNullException(nameof(debtBLL));
            _customer = customer ?? throw new ArgumentNullException(nameof(customer));
            _logger = logger;

            InitializeComponent();
            InitializeArabicUI();
            LoadCustomerStatement();
            SetupEventHandlers();
        }

        /// <summary>
        /// تهيئة الواجهة العربية
        /// </summary>
        private void InitializeArabicUI()
        {
            // إعدادات النموذج الأساسية
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = $"كشف حساب العميل - {_customer.CustomerName}";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = LightGray;

            // تطبيق الألوان والأنماط
            ApplyThemeColors();
            UpdateUITexts();
        }

        /// <summary>
        /// تطبيق ألوان النظام
        /// </summary>
        private void ApplyThemeColors()
        {
            // شريط العنوان
            pnlHeader.BackColor = PrimaryColor;
            lblTitle.ForeColor = Color.White;

            // أزرار العمليات
            btnPrint.BackColor = PrimaryColor;
            btnPrint.ForeColor = Color.White;
            btnPrint.FlatStyle = FlatStyle.Flat;

            btnExport.BackColor = SuccessColor;
            btnExport.ForeColor = Color.White;
            btnExport.FlatStyle = FlatStyle.Flat;

            btnRefresh.BackColor = Color.FromArgb(52, 152, 219);
            btnRefresh.ForeColor = Color.White;
            btnRefresh.FlatStyle = FlatStyle.Flat;

            btnClose.BackColor = Color.Gray;
            btnClose.ForeColor = Color.White;
            btnClose.FlatStyle = FlatStyle.Flat;

            // شبكة البيانات
            dgvStatement.BackgroundColor = Color.White;
            dgvStatement.GridColor = LightGray;
            dgvStatement.DefaultCellStyle.Font = new Font("Tahoma", 9F);
            dgvStatement.ColumnHeadersDefaultCellStyle.BackColor = PrimaryColor;
            dgvStatement.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvStatement.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 9F, FontStyle.Bold);

            // لوحات المعلومات
            pnlCustomerInfo.BackColor = Color.White;
            pnlCustomerInfo.BorderStyle = BorderStyle.FixedSingle;

            pnlSummary.BackColor = Color.White;
            pnlSummary.BorderStyle = BorderStyle.FixedSingle;

            pnlPeriod.BackColor = Color.White;
            pnlPeriod.BorderStyle = BorderStyle.FixedSingle;
        }

        /// <summary>
        /// تحديث النصوص في الواجهة
        /// </summary>
        private void UpdateUITexts()
        {
            lblTitle.Text = $"كشف حساب العميل - {_customer.CustomerName}";
            
            // أزرار العمليات
            btnPrint.Text = "طباعة";
            btnExport.Text = "تصدير";
            btnRefresh.Text = "تحديث";
            btnClose.Text = "إغلاق";

            // معلومات العميل
            grpCustomerInfo.Text = "معلومات العميل";
            lblCustomerCode.Text = $"كود العميل: {_customer.CustomerCode}";
            lblCustomerName.Text = $"اسم العميل: {_customer.CustomerName}";
            lblCustomerType.Text = $"نوع العميل: {_customer.CustomerType}";
            lblPhone.Text = $"الهاتف: {_customer.Phone ?? "-"}";
            lblEmail.Text = $"البريد الإلكتروني: {_customer.Email ?? "-"}";
            lblCreditLimit.Text = $"حد الائتمان: {_customer.CreditLimit:C}";

            // فترة الكشف
            grpPeriod.Text = "فترة الكشف";
            lblFromDate.Text = "من تاريخ:";
            lblToDate.Text = "إلى تاريخ:";

            // ملخص الحساب
            grpSummary.Text = "ملخص الحساب";

            // إعداد أعمدة الشبكة
            SetupStatementGridColumns();
        }

        /// <summary>
        /// إعداد أعمدة شبكة كشف الحساب
        /// </summary>
        private void SetupStatementGridColumns()
        {
            dgvStatement.Columns.Clear();
            dgvStatement.AutoGenerateColumns = false;

            // التاريخ
            dgvStatement.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TransactionDate",
                HeaderText = "التاريخ",
                DataPropertyName = "TransactionDate",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = { Format = "yyyy/MM/dd" }
            });

            // الوقت
            dgvStatement.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TransactionTime",
                HeaderText = "الوقت",
                DataPropertyName = "TransactionDate",
                Width = 80,
                ReadOnly = true,
                DefaultCellStyle = { Format = "HH:mm" }
            });

            // نوع المعاملة
            dgvStatement.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TransactionType",
                HeaderText = "نوع المعاملة",
                DataPropertyName = "TransactionType",
                Width = 120,
                ReadOnly = true
            });

            // الوصف
            dgvStatement.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "الوصف",
                DataPropertyName = "Description",
                Width = 250,
                ReadOnly = true
            });

            // المرجع
            dgvStatement.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Reference",
                HeaderText = "المرجع",
                DataPropertyName = "Reference",
                Width = 120,
                ReadOnly = true
            });

            // المدين (الديون)
            dgvStatement.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DebitAmount",
                HeaderText = "مدين",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = { Format = "C", Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            // الدائن (المدفوعات)
            dgvStatement.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreditAmount",
                HeaderText = "دائن",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = { Format = "C", Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            // الرصيد
            dgvStatement.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Balance",
                HeaderText = "الرصيد",
                DataPropertyName = "BalanceAfter",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = { Format = "C", Alignment = DataGridViewContentAlignment.MiddleRight }
            });
        }

        /// <summary>
        /// تحميل كشف حساب العميل
        /// </summary>
        private void LoadCustomerStatement()
        {
            try
            {
                // تعيين الفترة الافتراضية (آخر 3 أشهر)
                _fromDate = DateTime.Now.AddMonths(-3);
                _toDate = DateTime.Now;

                dtpFromDate.Value = _fromDate;
                dtpToDate.Value = _toDate;

                // تحميل البيانات
                RefreshStatement();

                _logger?.LogInformation("تم تحميل كشف حساب العميل {CustomerName}", _customer.CustomerName);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل كشف حساب العميل");
                MessageBox.Show($"خطأ في تحميل كشف الحساب: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // أحداث الأزرار
            btnPrint.Click += BtnPrint_Click;
            btnExport.Click += BtnExport_Click;
            btnRefresh.Click += BtnRefresh_Click;
            btnClose.Click += BtnClose_Click;

            // أحداث تغيير التواريخ
            dtpFromDate.ValueChanged += DateRange_Changed;
            dtpToDate.ValueChanged += DateRange_Changed;

            // أحداث الشبكة
            dgvStatement.CellFormatting += DgvStatement_CellFormatting;

            // أحداث النموذج
            this.Load += CustomerStatementForm_Load;
            this.KeyDown += CustomerStatementForm_KeyDown;
            this.KeyPreview = true;
        }

        #endregion

        #region تحميل وعرض البيانات

        /// <summary>
        /// تحديث كشف الحساب
        /// </summary>
        private void RefreshStatement()
        {
            try
            {
                _fromDate = dtpFromDate.Value.Date;
                _toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                // تحميل معاملات العميل في الفترة المحددة
                var transactions = _customerBLL.GetCustomerTransactions(_customer.CustomerID, _fromDate, _toDate);

                // تحويل المعاملات إلى صفوف كشف الحساب
                var statementRows = ConvertTransactionsToStatementRows(transactions);

                // عرض البيانات
                dgvStatement.DataSource = statementRows;

                // تحديث الملخص
                UpdateSummary(statementRows);

                // تحديث عدد المعاملات
                lblTransactionsCount.Text = $"عدد المعاملات: {statementRows.Count}";

                _logger?.LogInformation("تم تحديث كشف الحساب للعميل {CustomerName} للفترة من {FromDate} إلى {ToDate}", 
                    _customer.CustomerName, _fromDate, _toDate);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحديث كشف الحساب");
                MessageBox.Show($"خطأ في تحديث كشف الحساب: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحويل المعاملات إلى صفوف كشف الحساب
        /// </summary>
        /// <param name="transactions">قائمة المعاملات</param>
        /// <returns>قائمة صفوف كشف الحساب</returns>
        private List<StatementRow> ConvertTransactionsToStatementRows(List<CustomerTransaction> transactions)
        {
            var statementRows = new List<StatementRow>();

            // إضافة رصيد البداية
            var openingBalance = CalculateOpeningBalance();
            if (openingBalance != 0)
            {
                statementRows.Add(new StatementRow
                {
                    TransactionDate = _fromDate,
                    TransactionType = "رصيد البداية",
                    Description = "الرصيد المرحل من الفترة السابقة",
                    Reference = "-",
                    DebitAmount = openingBalance > 0 ? openingBalance : 0,
                    CreditAmount = openingBalance < 0 ? Math.Abs(openingBalance) : 0,
                    BalanceAfter = openingBalance
                });
            }

            // إضافة المعاملات
            decimal runningBalance = openingBalance;
            foreach (var transaction in transactions.OrderBy(t => t.TransactionDate))
            {
                runningBalance += transaction.Amount;

                statementRows.Add(new StatementRow
                {
                    TransactionDate = transaction.TransactionDate,
                    TransactionType = transaction.TransactionType,
                    Description = transaction.Description,
                    Reference = transaction.Reference,
                    DebitAmount = transaction.Amount > 0 ? transaction.Amount : 0,
                    CreditAmount = transaction.Amount < 0 ? Math.Abs(transaction.Amount) : 0,
                    BalanceAfter = runningBalance
                });
            }

            return statementRows;
        }

        /// <summary>
        /// حساب رصيد البداية
        /// </summary>
        /// <returns>رصيد البداية</returns>
        private decimal CalculateOpeningBalance()
        {
            try
            {
                // حساب الرصيد من جميع المعاملات قبل تاريخ البداية
                var previousTransactions = _customerBLL.GetCustomerTransactions(_customer.CustomerID, null, _fromDate.AddDays(-1));
                return previousTransactions.Sum(t => t.Amount);
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// تحديث ملخص الحساب
        /// </summary>
        /// <param name="statementRows">صفوف كشف الحساب</param>
        private void UpdateSummary(List<StatementRow> statementRows)
        {
            var totalDebit = statementRows.Sum(r => r.DebitAmount);
            var totalCredit = statementRows.Sum(r => r.CreditAmount);
            var closingBalance = statementRows.LastOrDefault()?.BalanceAfter ?? 0;

            lblTotalDebit.Text = $"إجمالي المدين: {totalDebit:C}";
            lblTotalCredit.Text = $"إجمالي الدائن: {totalCredit:C}";
            lblClosingBalance.Text = $"الرصيد الختامي: {closingBalance:C}";

            // تلوين الرصيد الختامي
            if (closingBalance > 0)
            {
                lblClosingBalance.ForeColor = DangerColor; // دين على العميل
            }
            else if (closingBalance < 0)
            {
                lblClosingBalance.ForeColor = SuccessColor; // رصيد للعميل
            }
            else
            {
                lblClosingBalance.ForeColor = Color.Black; // رصيد صفر
            }

            // معلومات إضافية
            var currentBalance = _customer.CurrentBalance;
            lblCurrentBalance.Text = $"الرصيد الحالي: {currentBalance:C}";
            lblAvailableCredit.Text = $"الرصيد المتاح: {(_customer.CreditLimit - currentBalance):C}";

            // حالة الحساب
            if (_customer.IsOverCreditLimit())
            {
                lblAccountStatus.Text = "حالة الحساب: تجاوز حد الائتمان";
                lblAccountStatus.ForeColor = DangerColor;
            }
            else if (_customer.HasOverdueDebts())
            {
                lblAccountStatus.Text = "حالة الحساب: يوجد ديون متأخرة";
                lblAccountStatus.ForeColor = WarningColor;
            }
            else
            {
                lblAccountStatus.Text = "حالة الحساب: جيد";
                lblAccountStatus.ForeColor = SuccessColor;
            }
        }

        #endregion

        #region معالجات الأحداث

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void CustomerStatementForm_Load(object sender, EventArgs e)
        {
            _logger?.LogInformation("تم تحميل واجهة كشف حساب العميل");
        }

        /// <summary>
        /// حدث الضغط على مفاتيح الاختصار
        /// </summary>
        private void CustomerStatementForm_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.F5:
                    BtnRefresh_Click(sender, e);
                    break;
                case Keys.F6:
                    BtnPrint_Click(sender, e);
                    break;
                case Keys.F7:
                    BtnExport_Click(sender, e);
                    break;
                case Keys.Escape:
                    this.Close();
                    break;
            }
        }

        /// <summary>
        /// حدث تغيير نطاق التواريخ
        /// </summary>
        private void DateRange_Changed(object sender, EventArgs e)
        {
            if (dtpFromDate.Value <= dtpToDate.Value)
            {
                RefreshStatement();
            }
            else
            {
                MessageBox.Show("تاريخ البداية يجب أن يكون أقل من أو يساوي تاريخ النهاية", "خطأ في التاريخ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// حدث تنسيق خلايا الشبكة
        /// </summary>
        private void DgvStatement_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvStatement.Rows[e.RowIndex].DataBoundItem is StatementRow row)
            {
                // تلوين الصفوف حسب نوع المعاملة
                if (row.TransactionType == "رصيد البداية")
                {
                    e.CellStyle.BackColor = Color.FromArgb(240, 248, 255); // أزرق فاتح
                    e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold);
                }
                else if (row.DebitAmount > 0) // معاملة مدينة
                {
                    e.CellStyle.BackColor = Color.FromArgb(255, 245, 245); // أحمر فاتح جداً
                }
                else if (row.CreditAmount > 0) // معاملة دائنة
                {
                    e.CellStyle.BackColor = Color.FromArgb(245, 255, 245); // أخضر فاتح جداً
                }

                // تلوين الرصيد
                if (e.ColumnIndex == dgvStatement.Columns["Balance"].Index)
                {
                    if (row.BalanceAfter > 0)
                    {
                        e.CellStyle.ForeColor = DangerColor; // دين
                    }
                    else if (row.BalanceAfter < 0)
                    {
                        e.CellStyle.ForeColor = SuccessColor; // رصيد
                    }
                }
            }
        }

        #endregion

        #region عمليات الأزرار

        /// <summary>
        /// طباعة كشف الحساب
        /// </summary>
        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                // إنشاء تقرير كشف الحساب
                var reportForm = new CustomerStatementReportForm(_customer, dgvStatement.DataSource as List<StatementRow>, _fromDate, _toDate);
                reportForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في طباعة كشف الحساب");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصدير كشف الحساب
        /// </summary>
        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx|CSV Files|*.csv|PDF Files|*.pdf",
                    FileName = $"كشف_حساب_{_customer.CustomerCode}_{DateTime.Now:yyyyMMdd}.xlsx"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    var data = dgvStatement.DataSource as List<StatementRow>;
                    var exporter = new DataExporter();

                    switch (System.IO.Path.GetExtension(saveDialog.FileName).ToLower())
                    {
                        case ".xlsx":
                            exporter.ExportToExcel(data, saveDialog.FileName, $"كشف حساب {_customer.CustomerName}");
                            break;
                        case ".csv":
                            exporter.ExportToCSV(data, saveDialog.FileName);
                            break;
                        case ".pdf":
                            exporter.ExportToPDF(data, saveDialog.FileName, $"كشف حساب {_customer.CustomerName}");
                            break;
                    }

                    MessageBox.Show("تم تصدير كشف الحساب بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تصدير كشف الحساب");
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            RefreshStatement();
        }

        /// <summary>
        /// إغلاق النموذج
        /// </summary>
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion
    }

    #region نموذج صف كشف الحساب

    /// <summary>
    /// نموذج صف في كشف الحساب
    /// </summary>
    public class StatementRow
    {
        public DateTime TransactionDate { get; set; }
        public string TransactionType { get; set; }
        public string Description { get; set; }
        public string Reference { get; set; }
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
        public decimal BalanceAfter { get; set; }
    }

    #endregion
}
