using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using AredooPOS.Models;
using Microsoft.Extensions.Logging;

namespace AredooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات للصناديق
    /// تحتوي على جميع العمليات المتعلقة بقاعدة البيانات للصناديق
    /// </summary>
    public class CashRegisterDAL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly string _connectionString;
        private readonly ILogger<CashRegisterDAL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة الوصول للبيانات للصناديق
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public CashRegisterDAL(string connectionString = null, ILogger<CashRegisterDAL> logger = null)
        {
            _connectionString = connectionString ?? DatabaseConfig.GetConnectionString();
            _logger = logger;
        }

        #endregion

        #region العمليات الأساسية

        /// <summary>
        /// إضافة صندوق جديد
        /// </summary>
        /// <param name="cashRegister">بيانات الصندوق</param>
        /// <returns>رقم الصندوق الجديد</returns>
        public int AddCashRegister(CashRegister cashRegister)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("INSERT INTO CashRegisters (RegisterName, RegisterCode, Location, Description, IsActive, OpeningBalance, CurrentBalance, MaxCashLimit, MinCashLimit, RequireManagerApproval, AllowNegativeBalance, AutoCloseTime, CreatedBy) OUTPUT INSERTED.CashRegisterID VALUES (@RegisterName, @RegisterCode, @Location, @Description, @IsActive, @OpeningBalance, @CurrentBalance, @MaxCashLimit, @MinCashLimit, @RequireManagerApproval, @AllowNegativeBalance, @AutoCloseTime, @CreatedBy)", connection);

                command.Parameters.AddWithValue("@RegisterName", cashRegister.RegisterName);
                command.Parameters.AddWithValue("@RegisterCode", cashRegister.RegisterCode);
                command.Parameters.AddWithValue("@Location", cashRegister.Location ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Description", cashRegister.Description ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@IsActive", cashRegister.IsActive);
                command.Parameters.AddWithValue("@OpeningBalance", cashRegister.OpeningBalance);
                command.Parameters.AddWithValue("@CurrentBalance", cashRegister.CurrentBalance);
                command.Parameters.AddWithValue("@MaxCashLimit", cashRegister.MaxCashLimit ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@MinCashLimit", cashRegister.MinCashLimit);
                command.Parameters.AddWithValue("@RequireManagerApproval", cashRegister.RequireManagerApproval);
                command.Parameters.AddWithValue("@AllowNegativeBalance", cashRegister.AllowNegativeBalance);
                command.Parameters.AddWithValue("@AutoCloseTime", cashRegister.AutoCloseTime ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@CreatedBy", cashRegister.CreatedBy);

                connection.Open();
                var registerId = (int)command.ExecuteScalar();

                _logger?.LogInformation($"تم إضافة صندوق جديد: {cashRegister.RegisterName} برقم {registerId}");
                return registerId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إضافة الصندوق {cashRegister?.RegisterName}");
                throw;
            }
        }

        /// <summary>
        /// تحديث بيانات الصندوق
        /// </summary>
        /// <param name="cashRegister">بيانات الصندوق المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateCashRegister(CashRegister cashRegister)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand(@"
                    UPDATE CashRegisters 
                    SET RegisterName = @RegisterName,
                        RegisterCode = @RegisterCode,
                        Location = @Location,
                        Description = @Description,
                        IsActive = @IsActive,
                        OpeningBalance = @OpeningBalance,
                        MaxCashLimit = @MaxCashLimit,
                        MinCashLimit = @MinCashLimit,
                        RequireManagerApproval = @RequireManagerApproval,
                        AllowNegativeBalance = @AllowNegativeBalance,
                        AutoCloseTime = @AutoCloseTime,
                        ModifiedBy = @ModifiedBy,
                        ModifiedDate = GETDATE()
                    WHERE CashRegisterID = @CashRegisterID", connection);

                command.Parameters.AddWithValue("@CashRegisterID", cashRegister.CashRegisterID);
                command.Parameters.AddWithValue("@RegisterName", cashRegister.RegisterName);
                command.Parameters.AddWithValue("@RegisterCode", cashRegister.RegisterCode);
                command.Parameters.AddWithValue("@Location", cashRegister.Location ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Description", cashRegister.Description ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@IsActive", cashRegister.IsActive);
                command.Parameters.AddWithValue("@OpeningBalance", cashRegister.OpeningBalance);
                command.Parameters.AddWithValue("@MaxCashLimit", cashRegister.MaxCashLimit ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@MinCashLimit", cashRegister.MinCashLimit);
                command.Parameters.AddWithValue("@RequireManagerApproval", cashRegister.RequireManagerApproval);
                command.Parameters.AddWithValue("@AllowNegativeBalance", cashRegister.AllowNegativeBalance);
                command.Parameters.AddWithValue("@AutoCloseTime", cashRegister.AutoCloseTime ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ModifiedBy", cashRegister.ModifiedBy);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم تحديث الصندوق {cashRegister.RegisterName}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث الصندوق {cashRegister?.CashRegisterID}");
                throw;
            }
        }

        /// <summary>
        /// حذف صندوق
        /// </summary>
        /// <param name="cashRegisterID">رقم الصندوق</param>
        /// <param name="deletedBy">من حذف الصندوق</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public bool DeleteCashRegister(int cashRegisterID, string deletedBy)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand(@"
                    UPDATE CashRegisters 
                    SET IsDeleted = 1,
                        DeletedBy = @DeletedBy,
                        DeletedDate = GETDATE(),
                        ModifiedBy = @DeletedBy,
                        ModifiedDate = GETDATE()
                    WHERE CashRegisterID = @CashRegisterID", connection);

                command.Parameters.AddWithValue("@CashRegisterID", cashRegisterID);
                command.Parameters.AddWithValue("@DeletedBy", deletedBy);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم حذف الصندوق {cashRegisterID}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حذف الصندوق {cashRegisterID}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على صندوق بالرقم
        /// </summary>
        /// <param name="cashRegisterID">رقم الصندوق</param>
        /// <returns>بيانات الصندوق</returns>
        public CashRegister GetCashRegisterById(int cashRegisterID)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("SELECT * FROM CashRegisters WHERE CashRegisterID = @CashRegisterID AND IsDeleted = 0", connection);

                command.Parameters.AddWithValue("@CashRegisterID", cashRegisterID);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapCashRegisterFromReader(reader);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الصندوق {cashRegisterID}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على صندوق بالرمز
        /// </summary>
        /// <param name="registerCode">رمز الصندوق</param>
        /// <returns>بيانات الصندوق</returns>
        public CashRegister GetCashRegisterByCode(string registerCode)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("SELECT * FROM CashRegisters WHERE RegisterCode = @RegisterCode AND IsDeleted = 0", connection);

                command.Parameters.AddWithValue("@RegisterCode", registerCode);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapCashRegisterFromReader(reader);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الصندوق {registerCode}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع الصناديق
        /// </summary>
        /// <param name="includeInactive">تضمين الصناديق غير النشطة</param>
        /// <returns>قائمة الصناديق</returns>
        public List<CashRegister> GetAllCashRegisters(bool includeInactive = false)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var sql = "SELECT * FROM CashRegisters WHERE IsDeleted = 0";
                if (!includeInactive)
                    sql += " AND IsActive = 1";
                sql += " ORDER BY RegisterName";

                using var command = new SqlCommand(sql, connection);

                connection.Open();
                using var reader = command.ExecuteReader();

                var cashRegisters = new List<CashRegister>();
                while (reader.Read())
                {
                    cashRegisters.Add(MapCashRegisterFromReader(reader));
                }

                return cashRegisters;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على جميع الصناديق");
                throw;
            }
        }

        /// <summary>
        /// البحث في الصناديق
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="includeInactive">تضمين الصناديق غير النشطة</param>
        /// <returns>قائمة الصناديق المطابقة</returns>
        public List<CashRegister> SearchCashRegisters(string searchTerm, bool includeInactive = false)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var sql = @"
                    SELECT * FROM CashRegisters 
                    WHERE IsDeleted = 0 
                    AND (RegisterName LIKE @SearchTerm 
                         OR RegisterCode LIKE @SearchTerm 
                         OR Location LIKE @SearchTerm)";
                
                if (!includeInactive)
                    sql += " AND IsActive = 1";
                
                sql += " ORDER BY RegisterName";

                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");

                connection.Open();
                using var reader = command.ExecuteReader();

                var cashRegisters = new List<CashRegister>();
                while (reader.Read())
                {
                    cashRegisters.Add(MapCashRegisterFromReader(reader));
                }

                return cashRegisters;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في البحث في الصناديق");
                throw;
            }
        }

        /// <summary>
        /// تحديث رصيد الصندوق
        /// </summary>
        /// <param name="cashRegisterID">رقم الصندوق</param>
        /// <param name="newBalance">الرصيد الجديد</param>
        /// <param name="modifiedBy">من عدل الرصيد</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateCashRegisterBalance(int cashRegisterID, decimal newBalance, string modifiedBy)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand(@"
                    UPDATE CashRegisters 
                    SET CurrentBalance = @NewBalance,
                        LastTransactionDate = GETDATE(),
                        ModifiedBy = @ModifiedBy,
                        ModifiedDate = GETDATE()
                    WHERE CashRegisterID = @CashRegisterID", connection);

                command.Parameters.AddWithValue("@CashRegisterID", cashRegisterID);
                command.Parameters.AddWithValue("@NewBalance", newBalance);
                command.Parameters.AddWithValue("@ModifiedBy", modifiedBy);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث رصيد الصندوق {cashRegisterID}");
                throw;
            }
        }

        /// <summary>
        /// التحقق من وجود رمز الصندوق
        /// </summary>
        /// <param name="registerCode">رمز الصندوق</param>
        /// <param name="excludeID">استثناء رقم معين</param>
        /// <returns>true إذا كان الرمز موجود</returns>
        public bool RegisterCodeExists(string registerCode, int? excludeID = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var sql = "SELECT COUNT(*) FROM CashRegisters WHERE RegisterCode = @RegisterCode AND IsDeleted = 0";
                if (excludeID.HasValue)
                    sql += " AND CashRegisterID <> @ExcludeID";

                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@RegisterCode", registerCode);
                if (excludeID.HasValue)
                    command.Parameters.AddWithValue("@ExcludeID", excludeID.Value);

                connection.Open();
                var count = (int)command.ExecuteScalar();

                return count > 0;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في التحقق من رمز الصندوق {registerCode}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على الصناديق التي تحتاج تنبيه
        /// </summary>
        /// <returns>قائمة الصناديق التي تحتاج تنبيه</returns>
        public List<CashRegister> GetCashRegistersNeedingAlert()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand(@"
                    SELECT * FROM CashRegisters 
                    WHERE IsDeleted = 0 AND IsActive = 1
                    AND (
                        (CurrentBalance < MinCashLimit) OR
                        (MaxCashLimit IS NOT NULL AND CurrentBalance > MaxCashLimit) OR
                        (AllowNegativeBalance = 0 AND CurrentBalance < 0)
                    )
                    ORDER BY RegisterName", connection);

                connection.Open();
                using var reader = command.ExecuteReader();

                var cashRegisters = new List<CashRegister>();
                while (reader.Read())
                {
                    cashRegisters.Add(MapCashRegisterFromReader(reader));
                }

                return cashRegisters;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على الصناديق التي تحتاج تنبيه");
                throw;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// تحويل بيانات القارئ إلى كائن صندوق
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن الصندوق</returns>
        private CashRegister MapCashRegisterFromReader(SqlDataReader reader)
        {
            return new CashRegister
            {
                CashRegisterID = reader.GetInt32("CashRegisterID"),
                RegisterName = reader.GetString("RegisterName"),
                RegisterCode = reader.GetString("RegisterCode"),
                Location = reader.IsDBNull("Location") ? null : reader.GetString("Location"),
                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                IsActive = reader.GetBoolean("IsActive"),
                OpeningBalance = reader.GetDecimal("OpeningBalance"),
                CurrentBalance = reader.GetDecimal("CurrentBalance"),
                MaxCashLimit = reader.IsDBNull("MaxCashLimit") ? null : reader.GetDecimal("MaxCashLimit"),
                MinCashLimit = reader.GetDecimal("MinCashLimit"),
                RequireManagerApproval = reader.GetBoolean("RequireManagerApproval"),
                AllowNegativeBalance = reader.GetBoolean("AllowNegativeBalance"),
                AutoCloseTime = reader.IsDBNull("AutoCloseTime") ? null : reader.GetTimeSpan("AutoCloseTime"),
                LastSessionID = reader.IsDBNull("LastSessionID") ? null : reader.GetInt32("LastSessionID"),
                LastTransactionDate = reader.IsDBNull("LastTransactionDate") ? null : reader.GetDateTime("LastTransactionDate"),
                CreatedBy = reader.GetString("CreatedBy"),
                CreatedDate = reader.GetDateTime("CreatedDate"),
                ModifiedBy = reader.IsDBNull("ModifiedBy") ? null : reader.GetString("ModifiedBy"),
                ModifiedDate = reader.IsDBNull("ModifiedDate") ? null : reader.GetDateTime("ModifiedDate"),
                IsDeleted = reader.GetBoolean("IsDeleted"),
                DeletedBy = reader.IsDBNull("DeletedBy") ? null : reader.GetString("DeletedBy"),
                DeletedDate = reader.IsDBNull("DeletedDate") ? null : reader.GetDateTime("DeletedDate")
            };
        }

        #endregion
    }
}
