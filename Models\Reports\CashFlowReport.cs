using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace AredooPOS.Models.Reports
{
    /// <summary>
    /// نموذج تقرير حركة الصندوق
    /// يحتوي على جميع البيانات المتعلقة بتقارير حركة النقدية والتدفقات المالية
    /// </summary>
    public class CashFlowReport
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم التقرير
        /// </summary>
        public int ReportID { get; set; }

        /// <summary>
        /// نوع التقرير
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ReportType { get; set; }

        /// <summary>
        /// تاريخ بداية التقرير
        /// </summary>
        [Required]
        public DateTime FromDate { get; set; }

        /// <summary>
        /// تاريخ نهاية التقرير
        /// </summary>
        [Required]
        public DateTime ToDate { get; set; }

        /// <summary>
        /// تاريخ إنشاء التقرير
        /// </summary>
        public DateTime GeneratedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// من أنشأ التقرير
        /// </summary>
        [Required]
        [StringLength(50)]
        public string GeneratedBy { get; set; }

        /// <summary>
        /// رقم الصندوق (اختياري)
        /// </summary>
        public int? CashRegisterID { get; set; }

        /// <summary>
        /// اسم الصندوق
        /// </summary>
        public string CashRegisterName { get; set; }

        #endregion

        #region الأرصدة

        /// <summary>
        /// الرصيد الافتتاحي
        /// </summary>
        public decimal OpeningBalance { get; set; }

        /// <summary>
        /// الرصيد الختامي
        /// </summary>
        public decimal ClosingBalance { get; set; }

        /// <summary>
        /// صافي التدفق النقدي
        /// </summary>
        public decimal NetCashFlow => ClosingBalance - OpeningBalance;

        #endregion

        #region التدفقات الداخلة

        /// <summary>
        /// إجمالي المبيعات النقدية
        /// </summary>
        public decimal CashSales { get; set; }

        /// <summary>
        /// تحصيلات الديون
        /// </summary>
        public decimal DebtCollections { get; set; }

        /// <summary>
        /// تحصيلات الأقساط
        /// </summary>
        public decimal InstallmentCollections { get; set; }

        /// <summary>
        /// الإيداعات النقدية
        /// </summary>
        public decimal CashDeposits { get; set; }

        /// <summary>
        /// إيرادات أخرى
        /// </summary>
        public decimal OtherIncome { get; set; }

        /// <summary>
        /// إجمالي التدفقات الداخلة
        /// </summary>
        public decimal TotalCashInflows => CashSales + DebtCollections + InstallmentCollections + CashDeposits + OtherIncome;

        #endregion

        #region التدفقات الخارجة

        /// <summary>
        /// المصاريف النقدية
        /// </summary>
        public decimal CashExpenses { get; set; }

        /// <summary>
        /// السحوبات النقدية
        /// </summary>
        public decimal CashWithdrawals { get; set; }

        /// <summary>
        /// مدفوعات الموردين
        /// </summary>
        public decimal SupplierPayments { get; set; }

        /// <summary>
        /// المرتجعات النقدية
        /// </summary>
        public decimal CashReturns { get; set; }

        /// <summary>
        /// مصاريف أخرى
        /// </summary>
        public decimal OtherExpenses { get; set; }

        /// <summary>
        /// إجمالي التدفقات الخارجة
        /// </summary>
        public decimal TotalCashOutflows => CashExpenses + CashWithdrawals + SupplierPayments + CashReturns + OtherExpenses;

        #endregion

        #region تفاصيل طرق الدفع

        /// <summary>
        /// المعاملات النقدية
        /// </summary>
        public decimal CashTransactions { get; set; }

        /// <summary>
        /// معاملات البطاقات
        /// </summary>
        public decimal CardTransactions { get; set; }

        /// <summary>
        /// معاملات التحويل البنكي
        /// </summary>
        public decimal TransferTransactions { get; set; }

        /// <summary>
        /// معاملات الشيكات
        /// </summary>
        public decimal CheckTransactions { get; set; }

        #endregion

        #region قوائم التفاصيل

        /// <summary>
        /// تفاصيل المعاملات النقدية
        /// </summary>
        public List<CashTransactionDetail> TransactionDetails { get; set; } = new List<CashTransactionDetail>();

        /// <summary>
        /// تفاصيل الحركة اليومية
        /// </summary>
        public List<DailyCashFlowDetail> DailyDetails { get; set; } = new List<DailyCashFlowDetail>();

        /// <summary>
        /// تفاصيل الحركة حسب الفئة
        /// </summary>
        public List<CategoryCashFlowDetail> CategoryDetails { get; set; } = new List<CategoryCashFlowDetail>();

        /// <summary>
        /// تفاصيل الجلسات
        /// </summary>
        public List<SessionCashFlowDetail> SessionDetails { get; set; } = new List<SessionCashFlowDetail>();

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// فترة التقرير بالأيام
        /// </summary>
        public int ReportPeriodDays => (ToDate - FromDate).Days + 1;

        /// <summary>
        /// متوسط التدفق النقدي اليومي
        /// </summary>
        public decimal DailyAverageCashFlow => ReportPeriodDays > 0 ? NetCashFlow / ReportPeriodDays : 0;

        /// <summary>
        /// متوسط المبيعات النقدية اليومية
        /// </summary>
        public decimal DailyAverageCashSales => ReportPeriodDays > 0 ? CashSales / ReportPeriodDays : 0;

        /// <summary>
        /// نسبة المبيعات النقدية
        /// </summary>
        public decimal CashSalesPercentage => TotalCashInflows > 0 ? (CashSales / TotalCashInflows) * 100 : 0;

        /// <summary>
        /// نسبة المصاريف النقدية
        /// </summary>
        public decimal CashExpensePercentage => TotalCashOutflows > 0 ? (CashExpenses / TotalCashOutflows) * 100 : 0;

        /// <summary>
        /// معدل دوران النقدية
        /// </summary>
        public decimal CashTurnoverRate
        {
            get
            {
                var averageBalance = (OpeningBalance + ClosingBalance) / 2;
                return averageBalance > 0 ? TotalCashInflows / averageBalance : 0;
            }
        }

        /// <summary>
        /// أفضل يوم من ناحية التدفق النقدي
        /// </summary>
        public DailyCashFlowDetail BestCashFlowDay => DailyDetails?.OrderByDescending(d => d.NetCashFlow).FirstOrDefault();

        /// <summary>
        /// أسوأ يوم من ناحية التدفق النقدي
        /// </summary>
        public DailyCashFlowDetail WorstCashFlowDay => DailyDetails?.OrderBy(d => d.NetCashFlow).FirstOrDefault();

        #endregion

        #region الدوال المساعدة

        /// <summary>
        /// حساب الإجماليات من التفاصيل
        /// </summary>
        public void CalculateTotals()
        {
            if (TransactionDetails?.Any() == true)
            {
                CashSales = TransactionDetails.Where(t => t.TransactionType == "Sale").Sum(t => t.Amount);
                CashExpenses = TransactionDetails.Where(t => t.TransactionType == "Expense").Sum(t => t.Amount);
                CashWithdrawals = TransactionDetails.Where(t => t.TransactionType == "Withdrawal").Sum(t => t.Amount);
                CashDeposits = TransactionDetails.Where(t => t.TransactionType == "Deposit").Sum(t => t.Amount);
                CashReturns = TransactionDetails.Where(t => t.TransactionType == "Return").Sum(t => t.Amount);
            }

            if (DailyDetails?.Any() == true)
            {
                OpeningBalance = DailyDetails.OrderBy(d => d.FlowDate).First().OpeningBalance;
                ClosingBalance = DailyDetails.OrderByDescending(d => d.FlowDate).First().ClosingBalance;
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            if (FromDate > ToDate)
                return false;

            if (string.IsNullOrWhiteSpace(ReportType))
                return false;

            if (string.IsNullOrWhiteSpace(GeneratedBy))
                return false;

            return true;
        }

        /// <summary>
        /// الحصول على ملخص التقرير
        /// </summary>
        /// <returns>ملخص التقرير</returns>
        public string GetSummary()
        {
            return $"تقرير حركة الصندوق من {FromDate:dd/MM/yyyy} إلى {ToDate:dd/MM/yyyy}\n" +
                   $"الرصيد الافتتاحي: {OpeningBalance:C}\n" +
                   $"إجمالي الداخل: {TotalCashInflows:C}\n" +
                   $"إجمالي الخارج: {TotalCashOutflows:C}\n" +
                   $"الرصيد الختامي: {ClosingBalance:C}\n" +
                   $"صافي التدفق: {NetCashFlow:C}";
        }

        /// <summary>
        /// تحليل الاتجاه
        /// </summary>
        /// <returns>تحليل الاتجاه</returns>
        public string GetTrendAnalysis()
        {
            if (DailyDetails?.Count >= 7)
            {
                var recentWeek = DailyDetails.OrderByDescending(d => d.FlowDate).Take(7);
                var previousWeek = DailyDetails.OrderByDescending(d => d.FlowDate).Skip(7).Take(7);
                
                var recentAverage = recentWeek.Average(d => d.NetCashFlow);
                var previousAverage = previousWeek.Average(d => d.NetCashFlow);
                
                if (recentAverage > previousAverage)
                    return "اتجاه إيجابي في التدفق النقدي";
                else if (recentAverage < previousAverage)
                    return "اتجاه سلبي في التدفق النقدي";
                else
                    return "استقرار في التدفق النقدي";
            }
            
            return "لا توجد بيانات كافية للتحليل";
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// تفاصيل المعاملة النقدية
    /// </summary>
    public class CashTransactionDetail
    {
        public int TransactionID { get; set; }
        public string TransactionNumber { get; set; }
        public DateTime TransactionDate { get; set; }
        public string TransactionType { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public string UserName { get; set; }
        public string ReferenceNumber { get; set; }
        public bool IsVoided { get; set; }
    }

    /// <summary>
    /// تفاصيل التدفق النقدي اليومي
    /// </summary>
    public class DailyCashFlowDetail
    {
        public DateTime FlowDate { get; set; }
        public decimal OpeningBalance { get; set; }
        public decimal CashInflows { get; set; }
        public decimal CashOutflows { get; set; }
        public decimal NetCashFlow { get; set; }
        public decimal ClosingBalance { get; set; }
        public int TransactionCount { get; set; }
        public int SessionCount { get; set; }
        public string DayName => FlowDate.ToString("dddd");
    }

    /// <summary>
    /// تفاصيل التدفق النقدي حسب الفئة
    /// </summary>
    public class CategoryCashFlowDetail
    {
        public string CategoryName { get; set; }
        public string CategoryType { get; set; } // Inflow or Outflow
        public decimal TotalAmount { get; set; }
        public int TransactionCount { get; set; }
        public decimal AverageAmount { get; set; }
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// تفاصيل التدفق النقدي للجلسة
    /// </summary>
    public class SessionCashFlowDetail
    {
        public int SessionID { get; set; }
        public DateTime SessionDate { get; set; }
        public DateTime OpenTime { get; set; }
        public DateTime? CloseTime { get; set; }
        public decimal OpeningBalance { get; set; }
        public decimal ClosingBalance { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetFlow { get; set; }
        public int TransactionCount { get; set; }
        public string UserName { get; set; }
        public string Status { get; set; }
    }

    #endregion

    #region التعدادات

    /// <summary>
    /// أنواع تقارير حركة الصندوق
    /// </summary>
    public static class CashFlowReportTypes
    {
        public const string Daily = "Daily";
        public const string Weekly = "Weekly";
        public const string Monthly = "Monthly";
        public const string ByRegister = "ByRegister";
        public const string BySession = "BySession";
        public const string ByCategory = "ByCategory";
        public const string Summary = "Summary";
        public const string Detailed = "Detailed";
    }

    /// <summary>
    /// أنواع التدفقات النقدية
    /// </summary>
    public static class CashFlowTypes
    {
        public const string Inflow = "Inflow";
        public const string Outflow = "Outflow";
    }

    #endregion
}
