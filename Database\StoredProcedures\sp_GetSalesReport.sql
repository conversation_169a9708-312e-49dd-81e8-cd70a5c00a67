-- =============================================
-- إجراء مخزن: sp_GetSalesReport
-- الوصف: الحصول على تقرير المبيعات الشامل
-- المؤلف: نظام أريدو POS
-- تاريخ الإنشاء: 2025-01-11
-- =============================================

CREATE PROCEDURE [dbo].[sp_GetSalesReport]
    @FromDate DATE,
    @ToDate DATE,
    @ReportType NVARCHAR(50) = 'Daily'
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(4000);
    
    BEGIN TRY
        -- التحقق من صحة المدخلات
        IF @FromDate > @ToDate
        BEGIN
            RAISERROR(N'تاريخ البداية يجب أن يكون أقل من أو يساوي تاريخ النهاية', 16, 1);
            RETURN;
        END
        
        IF @FromDate > GETDATE()
        BEGIN
            RAISERROR(N'تاريخ البداية لا يمكن أن يكون في المستقبل', 16, 1);
            RETURN;
        END
        
        -- الإجماليات العامة
        SELECT 
            ISNULL(SUM(i.TotalAmount), 0) AS TotalSales,
            ISNULL(SUM(id.Quantity), 0) AS TotalQuantitySold,
            COUNT(DISTINCT i.InvoiceID) AS TotalInvoices,
            ISNULL(SUM(i.DiscountAmount), 0) AS TotalDiscounts,
            ISNULL(SUM(i.TaxAmount), 0) AS TotalTaxes,
            ISNULL(SUM(CASE WHEN i.PaymentMethod = 'Cash' THEN i.TotalAmount ELSE 0 END), 0) AS CashSales,
            ISNULL(SUM(CASE WHEN i.PaymentMethod = 'Card' THEN i.TotalAmount ELSE 0 END), 0) AS CardSales,
            ISNULL(SUM(CASE WHEN i.PaymentMethod = 'Transfer' THEN i.TotalAmount ELSE 0 END), 0) AS TransferSales,
            ISNULL(SUM(CASE WHEN i.PaymentMethod = 'Credit' THEN i.TotalAmount ELSE 0 END), 0) AS CreditSales,
            ISNULL(SUM(CASE WHEN i.InvoiceType = 'Return' THEN i.TotalAmount ELSE 0 END), 0) AS TotalReturns,
            COUNT(CASE WHEN i.InvoiceType = 'Return' THEN 1 END) AS ReturnInvoicesCount
        FROM Invoices i
        LEFT JOIN InvoiceDetails id ON i.InvoiceID = id.InvoiceID
        WHERE i.InvoiceDate BETWEEN @FromDate AND @ToDate
            AND i.IsVoided = 0;
        
        -- تفاصيل المنتجات
        SELECT 
            p.ProductID,
            p.ProductName,
            p.ProductCode,
            c.CategoryName,
            ISNULL(SUM(id.Quantity), 0) AS QuantitySold,
            ISNULL(AVG(id.UnitPrice), 0) AS UnitPrice,
            ISNULL(SUM(id.TotalPrice), 0) AS TotalSales,
            ISNULL(SUM(id.Quantity * p.CostPrice), 0) AS TotalCost,
            ISNULL(SUM(id.TotalPrice - (id.Quantity * p.CostPrice)), 0) AS TotalProfit,
            ISNULL(SUM(id.DiscountAmount), 0) AS TotalDiscount,
            ISNULL(SUM(id.TaxAmount), 0) AS TotalTax,
            COUNT(DISTINCT i.InvoiceID) AS InvoiceCount
        FROM Products p
        LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
        LEFT JOIN InvoiceDetails id ON p.ProductID = id.ProductID
        LEFT JOIN Invoices i ON id.InvoiceID = i.InvoiceID 
            AND i.InvoiceDate BETWEEN @FromDate AND @ToDate
            AND i.IsVoided = 0
            AND i.InvoiceType = 'Sale'
        GROUP BY p.ProductID, p.ProductName, p.ProductCode, c.CategoryName, p.CostPrice
        HAVING SUM(id.Quantity) > 0
        ORDER BY TotalSales DESC;
        
        -- التفاصيل اليومية
        IF @ReportType = 'Daily'
        BEGIN
            WITH DateRange AS (
                SELECT @FromDate AS ReportDate
                UNION ALL
                SELECT DATEADD(DAY, 1, ReportDate)
                FROM DateRange
                WHERE ReportDate < @ToDate
            )
            SELECT 
                dr.ReportDate AS SalesDate,
                ISNULL(SUM(i.TotalAmount), 0) AS TotalSales,
                COUNT(i.InvoiceID) AS InvoiceCount,
                ISNULL(SUM(id.Quantity), 0) AS TotalQuantity,
                ISNULL(SUM(i.DiscountAmount), 0) AS TotalDiscount,
                ISNULL(SUM(i.TaxAmount), 0) AS TotalTax,
                ISNULL(SUM(CASE WHEN i.InvoiceType = 'Return' THEN i.TotalAmount ELSE 0 END), 0) AS TotalReturns,
                COUNT(CASE WHEN i.InvoiceType = 'Return' THEN 1 END) AS ReturnCount
            FROM DateRange dr
            LEFT JOIN Invoices i ON CAST(i.InvoiceDate AS DATE) = dr.ReportDate
                AND i.IsVoided = 0
            LEFT JOIN InvoiceDetails id ON i.InvoiceID = id.InvoiceID
            GROUP BY dr.ReportDate
            ORDER BY dr.ReportDate
            OPTION (MAXRECURSION 366);
        END
        
        -- التفاصيل الأسبوعية
        ELSE IF @ReportType = 'Weekly'
        BEGIN
            SELECT 
                DATEPART(YEAR, i.InvoiceDate) AS Year,
                DATEPART(WEEK, i.InvoiceDate) AS WeekNumber,
                MIN(i.InvoiceDate) AS WeekStart,
                MAX(i.InvoiceDate) AS WeekEnd,
                ISNULL(SUM(i.TotalAmount), 0) AS TotalSales,
                COUNT(i.InvoiceID) AS InvoiceCount,
                ISNULL(SUM(id.Quantity), 0) AS TotalQuantity,
                ISNULL(SUM(i.DiscountAmount), 0) AS TotalDiscount,
                ISNULL(SUM(i.TaxAmount), 0) AS TotalTax
            FROM Invoices i
            LEFT JOIN InvoiceDetails id ON i.InvoiceID = id.InvoiceID
            WHERE i.InvoiceDate BETWEEN @FromDate AND @ToDate
                AND i.IsVoided = 0
                AND i.InvoiceType = 'Sale'
            GROUP BY DATEPART(YEAR, i.InvoiceDate), DATEPART(WEEK, i.InvoiceDate)
            ORDER BY Year, WeekNumber;
        END
        
        -- التفاصيل الشهرية
        ELSE IF @ReportType = 'Monthly'
        BEGIN
            SELECT 
                DATEPART(YEAR, i.InvoiceDate) AS Year,
                DATEPART(MONTH, i.InvoiceDate) AS Month,
                DATENAME(MONTH, i.InvoiceDate) AS MonthName,
                ISNULL(SUM(i.TotalAmount), 0) AS TotalSales,
                COUNT(i.InvoiceID) AS InvoiceCount,
                ISNULL(SUM(id.Quantity), 0) AS TotalQuantity,
                ISNULL(SUM(i.DiscountAmount), 0) AS TotalDiscount,
                ISNULL(SUM(i.TaxAmount), 0) AS TotalTax
            FROM Invoices i
            LEFT JOIN InvoiceDetails id ON i.InvoiceID = id.InvoiceID
            WHERE i.InvoiceDate BETWEEN @FromDate AND @ToDate
                AND i.IsVoided = 0
                AND i.InvoiceType = 'Sale'
            GROUP BY DATEPART(YEAR, i.InvoiceDate), DATEPART(MONTH, i.InvoiceDate), DATENAME(MONTH, i.InvoiceDate)
            ORDER BY Year, Month;
        END
        
        -- التفاصيل حسب الفئة
        ELSE IF @ReportType = 'ByCategory'
        BEGIN
            SELECT 
                c.CategoryID,
                c.CategoryName,
                c.CategoryCode,
                ISNULL(SUM(id.Quantity), 0) AS QuantitySold,
                ISNULL(SUM(id.TotalPrice), 0) AS TotalSales,
                ISNULL(SUM(id.Quantity * p.CostPrice), 0) AS TotalCost,
                ISNULL(SUM(id.TotalPrice - (id.Quantity * p.CostPrice)), 0) AS TotalProfit,
                COUNT(DISTINCT i.InvoiceID) AS InvoiceCount,
                COUNT(DISTINCT p.ProductID) AS ProductCount
            FROM Categories c
            LEFT JOIN Products p ON c.CategoryID = p.CategoryID
            LEFT JOIN InvoiceDetails id ON p.ProductID = id.ProductID
            LEFT JOIN Invoices i ON id.InvoiceID = i.InvoiceID 
                AND i.InvoiceDate BETWEEN @FromDate AND @ToDate
                AND i.IsVoided = 0
                AND i.InvoiceType = 'Sale'
            GROUP BY c.CategoryID, c.CategoryName, c.CategoryCode
            HAVING SUM(id.Quantity) > 0
            ORDER BY TotalSales DESC;
        END
        
        -- التفاصيل حسب العميل
        ELSE IF @ReportType = 'ByCustomer'
        BEGIN
            SELECT 
                cust.CustomerID,
                cust.CustomerName,
                cust.CustomerPhone,
                ISNULL(SUM(i.TotalAmount), 0) AS TotalSales,
                COUNT(i.InvoiceID) AS InvoiceCount,
                ISNULL(AVG(i.TotalAmount), 0) AS AverageInvoiceValue,
                ISNULL(SUM(i.DiscountAmount), 0) AS TotalDiscount,
                MIN(i.InvoiceDate) AS FirstPurchase,
                MAX(i.InvoiceDate) AS LastPurchase
            FROM Customers cust
            LEFT JOIN Invoices i ON cust.CustomerID = i.CustomerID
                AND i.InvoiceDate BETWEEN @FromDate AND @ToDate
                AND i.IsVoided = 0
                AND i.InvoiceType = 'Sale'
            GROUP BY cust.CustomerID, cust.CustomerName, cust.CustomerPhone
            HAVING SUM(i.TotalAmount) > 0
            ORDER BY TotalSales DESC;
        END
        
        -- التفاصيل حسب المستخدم
        ELSE IF @ReportType = 'ByUser'
        BEGIN
            SELECT 
                u.UserID,
                u.UserName,
                u.FullName,
                ISNULL(SUM(i.TotalAmount), 0) AS TotalSales,
                COUNT(i.InvoiceID) AS InvoiceCount,
                ISNULL(AVG(i.TotalAmount), 0) AS AverageInvoiceValue,
                ISNULL(SUM(i.DiscountAmount), 0) AS TotalDiscount,
                MIN(i.InvoiceDate) AS FirstSale,
                MAX(i.InvoiceDate) AS LastSale
            FROM Users u
            LEFT JOIN Invoices i ON u.UserID = i.CreatedBy
                AND i.InvoiceDate BETWEEN @FromDate AND @ToDate
                AND i.IsVoided = 0
                AND i.InvoiceType = 'Sale'
            GROUP BY u.UserID, u.UserName, u.FullName
            HAVING SUM(i.TotalAmount) > 0
            ORDER BY TotalSales DESC;
        END
        
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END

GO

-- =============================================
-- إجراء مخزن: sp_GetProductSalesDetails
-- الوصف: الحصول على تفاصيل مبيعات المنتجات
-- =============================================

CREATE PROCEDURE [dbo].[sp_GetProductSalesDetails]
    @FromDate DATE,
    @ToDate DATE,
    @ProductID INT = NULL,
    @CategoryID INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        SELECT 
            p.ProductID,
            p.ProductName,
            p.ProductCode,
            p.Barcode,
            c.CategoryName,
            p.UnitPrice,
            p.CostPrice,
            ISNULL(SUM(id.Quantity), 0) AS QuantitySold,
            ISNULL(SUM(id.TotalPrice), 0) AS TotalSales,
            ISNULL(SUM(id.Quantity * p.CostPrice), 0) AS TotalCost,
            ISNULL(SUM(id.TotalPrice - (id.Quantity * p.CostPrice)), 0) AS TotalProfit,
            ISNULL(SUM(id.DiscountAmount), 0) AS TotalDiscount,
            ISNULL(SUM(id.TaxAmount), 0) AS TotalTax,
            COUNT(DISTINCT i.InvoiceID) AS InvoiceCount,
            COUNT(DISTINCT i.CustomerID) AS CustomerCount,
            ISNULL(AVG(id.UnitPrice), 0) AS AverageSellingPrice,
            CASE 
                WHEN SUM(id.TotalPrice) > 0 
                THEN (SUM(id.TotalPrice - (id.Quantity * p.CostPrice)) / SUM(id.TotalPrice)) * 100 
                ELSE 0 
            END AS ProfitMargin
        FROM Products p
        LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
        LEFT JOIN InvoiceDetails id ON p.ProductID = id.ProductID
        LEFT JOIN Invoices i ON id.InvoiceID = i.InvoiceID 
            AND i.InvoiceDate BETWEEN @FromDate AND @ToDate
            AND i.IsVoided = 0
            AND i.InvoiceType = 'Sale'
        WHERE (@ProductID IS NULL OR p.ProductID = @ProductID)
            AND (@CategoryID IS NULL OR p.CategoryID = @CategoryID)
        GROUP BY p.ProductID, p.ProductName, p.ProductCode, p.Barcode, c.CategoryName, p.UnitPrice, p.CostPrice
        HAVING SUM(id.Quantity) > 0
        ORDER BY TotalSales DESC;
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END

GO

-- =============================================
-- إجراء مخزن: sp_GetSalesAnalytics
-- الوصف: الحصول على تحليلات المبيعات المتقدمة
-- =============================================

CREATE PROCEDURE [dbo].[sp_GetSalesAnalytics]
    @FromDate DATE,
    @ToDate DATE
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- تحليل الاتجاهات
        SELECT 
            'Trends' AS AnalysisType,
            CAST(i.InvoiceDate AS DATE) AS AnalysisDate,
            SUM(i.TotalAmount) AS DailySales,
            COUNT(i.InvoiceID) AS DailyInvoices,
            AVG(i.TotalAmount) AS AverageInvoiceValue,
            LAG(SUM(i.TotalAmount)) OVER (ORDER BY CAST(i.InvoiceDate AS DATE)) AS PreviousDaySales,
            CASE 
                WHEN LAG(SUM(i.TotalAmount)) OVER (ORDER BY CAST(i.InvoiceDate AS DATE)) > 0
                THEN ((SUM(i.TotalAmount) - LAG(SUM(i.TotalAmount)) OVER (ORDER BY CAST(i.InvoiceDate AS DATE))) 
                      / LAG(SUM(i.TotalAmount)) OVER (ORDER BY CAST(i.InvoiceDate AS DATE))) * 100
                ELSE 0
            END AS GrowthRate
        FROM Invoices i
        WHERE i.InvoiceDate BETWEEN @FromDate AND @ToDate
            AND i.IsVoided = 0
            AND i.InvoiceType = 'Sale'
        GROUP BY CAST(i.InvoiceDate AS DATE)
        ORDER BY AnalysisDate;
        
        -- تحليل أفضل المنتجات
        SELECT TOP 10
            'TopProducts' AS AnalysisType,
            p.ProductName,
            SUM(id.Quantity) AS QuantitySold,
            SUM(id.TotalPrice) AS TotalSales,
            SUM(id.TotalPrice - (id.Quantity * p.CostPrice)) AS TotalProfit,
            RANK() OVER (ORDER BY SUM(id.TotalPrice) DESC) AS SalesRank,
            RANK() OVER (ORDER BY SUM(id.TotalPrice - (id.Quantity * p.CostPrice)) DESC) AS ProfitRank
        FROM Products p
        JOIN InvoiceDetails id ON p.ProductID = id.ProductID
        JOIN Invoices i ON id.InvoiceID = i.InvoiceID
        WHERE i.InvoiceDate BETWEEN @FromDate AND @ToDate
            AND i.IsVoided = 0
            AND i.InvoiceType = 'Sale'
        GROUP BY p.ProductID, p.ProductName, p.CostPrice
        ORDER BY TotalSales DESC;
        
        -- تحليل أوقات الذروة
        SELECT 
            'PeakHours' AS AnalysisType,
            DATEPART(HOUR, i.InvoiceDate) AS SaleHour,
            COUNT(i.InvoiceID) AS InvoiceCount,
            SUM(i.TotalAmount) AS TotalSales,
            AVG(i.TotalAmount) AS AverageInvoiceValue
        FROM Invoices i
        WHERE i.InvoiceDate BETWEEN @FromDate AND @ToDate
            AND i.IsVoided = 0
            AND i.InvoiceType = 'Sale'
        GROUP BY DATEPART(HOUR, i.InvoiceDate)
        ORDER BY SaleHour;
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END

GO
