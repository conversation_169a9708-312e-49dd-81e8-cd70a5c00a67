using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AredooPOS.Models;
using AredooPOS.BLL;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// واجهة إدارة العملاء المتكاملة
    /// توفر إدارة شاملة للعملاء مع سجل المعاملات وكشف الحساب
    /// </summary>
    public partial class CustomerManagementForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly CustomerBLL _customerBLL;
        private readonly DebtBLL _debtBLL;
        private readonly ILogger<CustomerManagementForm> _logger;

        // ألوان النظام
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SuccessColor = Color.FromArgb(46, 204, 113);
        private readonly Color WarningColor = Color.FromArgb(241, 196, 15);
        private readonly Color DangerColor = Color.FromArgb(231, 76, 60);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);

        // البيانات الحالية
        private Customer _selectedCustomer;
        private bool _isEditMode = false;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ واجهة إدارة العملاء
        /// </summary>
        /// <param name="customerBLL">طبقة منطق الأعمال للعملاء</param>
        /// <param name="debtBLL">طبقة منطق الأعمال للديون</param>
        /// <param name="logger">مسجل الأحداث</param>
        public CustomerManagementForm(CustomerBLL customerBLL, DebtBLL debtBLL, ILogger<CustomerManagementForm> logger = null)
        {
            _customerBLL = customerBLL ?? throw new ArgumentNullException(nameof(customerBLL));
            _debtBLL = debtBLL ?? throw new ArgumentNullException(nameof(debtBLL));
            _logger = logger;

            InitializeComponent();
            InitializeArabicUI();
            LoadInitialData();
            SetupEventHandlers();
        }

        /// <summary>
        /// تهيئة الواجهة العربية
        /// </summary>
        private void InitializeArabicUI()
        {
            // إعدادات النموذج الأساسية
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "إدارة العملاء";
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = LightGray;

            // تطبيق الألوان والأنماط
            ApplyThemeColors();
            UpdateUITexts();
        }

        /// <summary>
        /// تطبيق ألوان النظام
        /// </summary>
        private void ApplyThemeColors()
        {
            // شريط العنوان
            pnlHeader.BackColor = PrimaryColor;
            lblTitle.ForeColor = Color.White;

            // أزرار العمليات
            btnAddCustomer.BackColor = SuccessColor;
            btnAddCustomer.ForeColor = Color.White;
            btnAddCustomer.FlatStyle = FlatStyle.Flat;

            btnEditCustomer.BackColor = PrimaryColor;
            btnEditCustomer.ForeColor = Color.White;
            btnEditCustomer.FlatStyle = FlatStyle.Flat;

            btnDeleteCustomer.BackColor = DangerColor;
            btnDeleteCustomer.ForeColor = Color.White;
            btnDeleteCustomer.FlatStyle = FlatStyle.Flat;

            btnSave.BackColor = SuccessColor;
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;

            btnCancel.BackColor = Color.Gray;
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;

            // شبكة البيانات
            dgvCustomers.BackgroundColor = Color.White;
            dgvCustomers.GridColor = LightGray;
            dgvCustomers.DefaultCellStyle.Font = new Font("Tahoma", 9F);
            dgvCustomers.ColumnHeadersDefaultCellStyle.BackColor = PrimaryColor;
            dgvCustomers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvCustomers.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 9F, FontStyle.Bold);

            // شبكة المعاملات
            dgvTransactions.BackgroundColor = Color.White;
            dgvTransactions.GridColor = LightGray;
            dgvTransactions.DefaultCellStyle.Font = new Font("Tahoma", 8F);
            dgvTransactions.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219);
            dgvTransactions.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;

            // لوحات المعلومات
            pnlCustomerInfo.BackColor = Color.White;
            pnlCustomerInfo.BorderStyle = BorderStyle.FixedSingle;

            pnlAccountSummary.BackColor = Color.White;
            pnlAccountSummary.BorderStyle = BorderStyle.FixedSingle;
        }

        /// <summary>
        /// تحديث النصوص في الواجهة
        /// </summary>
        private void UpdateUITexts()
        {
            lblTitle.Text = "إدارة العملاء";

            // أزرار العمليات
            btnAddCustomer.Text = "إضافة عميل جديد";
            btnEditCustomer.Text = "تعديل العميل";
            btnDeleteCustomer.Text = "حذف العميل";
            btnSave.Text = "حفظ";
            btnCancel.Text = "إلغاء";
            btnRefresh.Text = "تحديث";
            btnSearch.Text = "بحث";

            // مجموعات البحث
            grpSearch.Text = "البحث والتصفية";
            lblSearchCustomer.Text = "البحث:";
            lblCustomerType.Text = "نوع العميل:";
            chkActiveOnly.Text = "العملاء النشطين فقط";

            // معلومات العميل
            grpCustomerInfo.Text = "معلومات العميل";
            lblCustomerCode.Text = "كود العميل:";
            lblCustomerName.Text = "اسم العميل:";
            lblCustomerTypeInfo.Text = "نوع العميل:";
            lblPhone.Text = "رقم الهاتف:";
            lblPhone2.Text = "هاتف إضافي:";
            lblEmail.Text = "البريد الإلكتروني:";
            lblAddress.Text = "العنوان:";
            lblCity.Text = "المدينة:";
            lblCreditLimit.Text = "حد الائتمان:";
            lblDiscountPercent.Text = "نسبة الخصم:";
            lblPriceLevel.Text = "مستوى السعر:";
            lblNotes.Text = "ملاحظات:";
            chkIsActive.Text = "نشط";
            chkAllowCredit.Text = "السماح بالائتمان";

            // ملخص الحساب
            grpAccountSummary.Text = "ملخص الحساب";
            lblCurrentBalance.Text = "الرصيد الحالي: 0.00 ر.س";
            lblTotalPurchases.Text = "إجمالي المشتريات: 0.00 ر.س";
            lblTotalPayments.Text = "إجمالي المدفوعات: 0.00 ر.س";
            lblOutstandingDebts.Text = "الديون المستحقة: 0.00 ر.س";
            lblLastPurchase.Text = "آخر شراء: -";
            lblLastPayment.Text = "آخر دفعة: -";

            // سجل المعاملات
            grpTransactions.Text = "سجل المعاملات";

            // إعداد أعمدة الشبكات
            SetupCustomersGridColumns();
            SetupTransactionsGridColumns();
        }

        /// <summary>
        /// إعداد أعمدة شبكة العملاء
        /// </summary>
        private void SetupCustomersGridColumns()
        {
            dgvCustomers.Columns.Clear();
            dgvCustomers.AutoGenerateColumns = false;

            // كود العميل
            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CustomerCode",
                HeaderText = "كود العميل",
                DataPropertyName = "CustomerCode",
                Width = 100,
                ReadOnly = true
            });

            // اسم العميل
            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CustomerName",
                HeaderText = "اسم العميل",
                DataPropertyName = "CustomerName",
                Width = 200,
                ReadOnly = true
            });

            // نوع العميل
            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CustomerType",
                HeaderText = "النوع",
                DataPropertyName = "CustomerType",
                Width = 80,
                ReadOnly = true
            });

            // رقم الهاتف
            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Phone",
                HeaderText = "الهاتف",
                DataPropertyName = "Phone",
                Width = 120,
                ReadOnly = true
            });

            // الرصيد الحالي
            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CurrentBalance",
                HeaderText = "الرصيد الحالي",
                DataPropertyName = "CurrentBalance",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = { Format = "C", Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            // حد الائتمان
            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreditLimit",
                HeaderText = "حد الائتمان",
                DataPropertyName = "CreditLimit",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = { Format = "C", Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            // آخر شراء
            dgvCustomers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LastPurchaseDate",
                HeaderText = "آخر شراء",
                DataPropertyName = "LastPurchaseDate",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = { Format = "yyyy/MM/dd" }
            });

            // الحالة
            dgvCustomers.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                HeaderText = "نشط",
                DataPropertyName = "IsActive",
                Width = 60,
                ReadOnly = true
            });
        }

        /// <summary>
        /// إعداد أعمدة شبكة المعاملات
        /// </summary>
        private void SetupTransactionsGridColumns()
        {
            dgvTransactions.Columns.Clear();
            dgvTransactions.AutoGenerateColumns = false;

            // تاريخ المعاملة
            dgvTransactions.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TransactionDate",
                HeaderText = "التاريخ",
                DataPropertyName = "TransactionDate",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = { Format = "yyyy/MM/dd HH:mm" }
            });

            // نوع المعاملة
            dgvTransactions.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TransactionType",
                HeaderText = "النوع",
                DataPropertyName = "TransactionType",
                Width = 80,
                ReadOnly = true
            });

            // الوصف
            dgvTransactions.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "الوصف",
                DataPropertyName = "Description",
                Width = 200,
                ReadOnly = true
            });

            // المبلغ
            dgvTransactions.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Amount",
                HeaderText = "المبلغ",
                DataPropertyName = "Amount",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = { Format = "C", Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            // الرصيد بعد المعاملة
            dgvTransactions.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "BalanceAfter",
                HeaderText = "الرصيد بعد المعاملة",
                DataPropertyName = "BalanceAfter",
                Width = 140,
                ReadOnly = true,
                DefaultCellStyle = { Format = "C", Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            // المرجع
            dgvTransactions.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Reference",
                HeaderText = "المرجع",
                DataPropertyName = "Reference",
                Width = 120,
                ReadOnly = true
            });
        }

        /// <summary>
        /// تحميل البيانات الأولية
        /// </summary>
        private void LoadInitialData()
        {
            try
            {
                // تحميل أنواع العملاء
                cmbCustomerType.Items.Clear();
                cmbCustomerType.Items.Add("الكل");
                cmbCustomerType.Items.Add(CustomerTypes.Individual);
                cmbCustomerType.Items.Add(CustomerTypes.Company);
                cmbCustomerType.Items.Add(CustomerTypes.Institution);
                cmbCustomerType.Items.Add(CustomerTypes.Government);
                cmbCustomerType.SelectedIndex = 0;

                // تحميل أنواع العملاء في النموذج
                cmbCustomerTypeInfo.Items.Clear();
                cmbCustomerTypeInfo.Items.Add(CustomerTypes.Individual);
                cmbCustomerTypeInfo.Items.Add(CustomerTypes.Company);
                cmbCustomerTypeInfo.Items.Add(CustomerTypes.Institution);
                cmbCustomerTypeInfo.Items.Add(CustomerTypes.Government);
                cmbCustomerTypeInfo.SelectedIndex = 0;

                // تحميل مستويات الأسعار
                cmbPriceLevel.Items.Clear();
                for (int i = 1; i <= 5; i++)
                {
                    cmbPriceLevel.Items.Add($"مستوى {i}");
                }
                cmbPriceLevel.SelectedIndex = 0;

                // تحميل العملاء
                LoadCustomers();

                // تعطيل وضع التحرير
                SetEditMode(false);

                _logger?.LogInformation("تم تحميل البيانات الأولية لواجهة إدارة العملاء");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل البيانات الأولية");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // أحداث الأزرار
            btnAddCustomer.Click += BtnAddCustomer_Click;
            btnEditCustomer.Click += BtnEditCustomer_Click;
            btnDeleteCustomer.Click += BtnDeleteCustomer_Click;
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnRefresh.Click += BtnRefresh_Click;
            btnSearch.Click += BtnSearch_Click;

            // أحداث الشبكة
            dgvCustomers.SelectionChanged += DgvCustomers_SelectionChanged;
            dgvCustomers.CellDoubleClick += DgvCustomers_CellDoubleClick;
            dgvCustomers.CellFormatting += DgvCustomers_CellFormatting;

            // أحداث البحث
            txtSearchCustomer.TextChanged += SearchCriteria_Changed;
            cmbCustomerType.SelectedIndexChanged += SearchCriteria_Changed;
            chkActiveOnly.CheckedChanged += SearchCriteria_Changed;

            // أحداث النموذج
            this.Load += CustomerManagementForm_Load;
            this.KeyDown += CustomerManagementForm_KeyDown;
            this.KeyPreview = true;

            // أحداث التحقق من البيانات
            txtCustomerCode.Leave += ValidateCustomerCode;
            txtEmail.Leave += ValidateEmail;
            numCreditLimit.ValueChanged += NumCreditLimit_ValueChanged;
        }

        #endregion

        #region تحميل وعرض البيانات

        /// <summary>
        /// تحميل العملاء حسب معايير البحث
        /// </summary>
        private void LoadCustomers()
        {
            try
            {
                var criteria = BuildSearchCriteria();
                var customers = _customerBLL.SearchCustomers(criteria);

                dgvCustomers.DataSource = customers;

                // تحديث عدد النتائج
                lblResultsCount.Text = $"عدد النتائج: {customers.Count}";

                _logger?.LogInformation("تم تحميل {Count} عميل", customers.Count);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل العملاء");
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// بناء معايير البحث من الواجهة
        /// </summary>
        /// <returns>معايير البحث</returns>
        private CustomerSearchCriteria BuildSearchCriteria()
        {
            var criteria = new CustomerSearchCriteria();

            // نص البحث
            if (!string.IsNullOrWhiteSpace(txtSearchCustomer.Text))
            {
                criteria.SearchText = txtSearchCustomer.Text.Trim();
            }

            // نوع العميل
            if (cmbCustomerType.SelectedIndex > 0)
            {
                criteria.CustomerType = cmbCustomerType.SelectedItem.ToString();
            }

            // العملاء النشطين فقط
            if (chkActiveOnly.Checked)
            {
                criteria.ActiveOnly = true;
            }

            // حد النتائج
            criteria.Limit = 1000;

            return criteria;
        }

        /// <summary>
        /// تحميل تفاصيل العميل المحدد
        /// </summary>
        /// <param name="customer">العميل</param>
        private void LoadCustomerDetails(Customer customer)
        {
            if (customer == null) return;

            try
            {
                // تحميل البيانات الكاملة للعميل
                var fullCustomer = _customerBLL.GetCustomerById(customer.CustomerID);
                if (fullCustomer == null) return;

                _selectedCustomer = fullCustomer;

                // عرض معلومات العميل
                DisplayCustomerInfo(fullCustomer);

                // تحديث ملخص الحساب
                UpdateAccountSummary(fullCustomer);

                // تحميل سجل المعاملات
                LoadCustomerTransactions(fullCustomer.CustomerID);

                _logger?.LogInformation("تم تحميل تفاصيل العميل {CustomerName}", fullCustomer.CustomerName);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل تفاصيل العميل");
                MessageBox.Show($"خطأ في تحميل تفاصيل العميل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض معلومات العميل في النموذج
        /// </summary>
        /// <param name="customer">العميل</param>
        private void DisplayCustomerInfo(Customer customer)
        {
            txtCustomerCode.Text = customer.CustomerCode;
            txtCustomerName.Text = customer.CustomerName;
            cmbCustomerTypeInfo.Text = customer.CustomerType;
            txtPhone.Text = customer.Phone;
            txtPhone2.Text = customer.Phone2;
            txtEmail.Text = customer.Email;
            txtAddress.Text = customer.Address;
            txtCity.Text = customer.City;
            numCreditLimit.Value = customer.CreditLimit;
            numDiscountPercent.Value = customer.DefaultDiscountPercent;
            cmbPriceLevel.SelectedIndex = customer.PriceLevel - 1;
            txtNotes.Text = customer.Notes;
            chkIsActive.Checked = customer.IsActive;
            chkAllowCredit.Checked = customer.AllowCredit;
        }

        /// <summary>
        /// تحديث ملخص الحساب
        /// </summary>
        /// <param name="customer">العميل</param>
        private void UpdateAccountSummary(Customer customer)
        {
            lblCurrentBalance.Text = $"الرصيد الحالي: {customer.CurrentBalance:C}";
            lblTotalPurchases.Text = $"إجمالي المشتريات: {customer.TotalPurchases:C}";
            lblTotalPayments.Text = $"إجمالي المدفوعات: {customer.TotalPayments:C}";

            // حساب الديون المستحقة
            var outstandingDebts = customer.GetTotalOutstandingDebts();
            lblOutstandingDebts.Text = $"الديون المستحقة: {outstandingDebts:C}";

            // آخر شراء ودفعة
            lblLastPurchase.Text = customer.LastPurchaseDate.HasValue
                ? $"آخر شراء: {customer.LastPurchaseDate.Value:yyyy/MM/dd}"
                : "آخر شراء: -";

            lblLastPayment.Text = customer.LastPaymentDate.HasValue
                ? $"آخر دفعة: {customer.LastPaymentDate.Value:yyyy/MM/dd}"
                : "آخر دفعة: -";

            // تلوين الرصيد حسب الحالة
            if (customer.CurrentBalance > 0)
            {
                lblCurrentBalance.ForeColor = DangerColor; // دين على العميل
            }
            else if (customer.CurrentBalance < 0)
            {
                lblCurrentBalance.ForeColor = SuccessColor; // رصيد للعميل
            }
            else
            {
                lblCurrentBalance.ForeColor = Color.Black; // رصيد صفر
            }

            // تحذير من تجاوز حد الائتمان
            if (customer.IsOverCreditLimit())
            {
                lblCurrentBalance.Font = new Font(lblCurrentBalance.Font, FontStyle.Bold);
                lblCurrentBalance.Text += " (تجاوز الحد)";
            }
        }

        /// <summary>
        /// تحميل سجل معاملات العميل
        /// </summary>
        /// <param name="customerId">رقم العميل</param>
        private void LoadCustomerTransactions(int customerId)
        {
            try
            {
                var transactions = _customerBLL.GetCustomerTransactions(customerId, 100); // آخر 100 معاملة
                dgvTransactions.DataSource = transactions;

                _logger?.LogInformation("تم تحميل {Count} معاملة للعميل", transactions.Count);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل معاملات العميل");
                dgvTransactions.DataSource = null;
            }
        }

        #endregion

        #region معالجات الأحداث

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void CustomerManagementForm_Load(object sender, EventArgs e)
        {
            _logger?.LogInformation("تم تحميل واجهة إدارة العملاء");
        }

        /// <summary>
        /// حدث الضغط على مفاتيح الاختصار
        /// </summary>
        private void CustomerManagementForm_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.F1:
                    BtnAddCustomer_Click(sender, e);
                    break;
                case Keys.F2:
                    BtnEditCustomer_Click(sender, e);
                    break;
                case Keys.F3:
                    BtnDeleteCustomer_Click(sender, e);
                    break;
                case Keys.F5:
                    BtnRefresh_Click(sender, e);
                    break;
                case Keys.Escape:
                    if (_isEditMode)
                        BtnCancel_Click(sender, e);
                    else
                        this.Close();
                    break;
                case Keys.Enter:
                    if (_isEditMode)
                        BtnSave_Click(sender, e);
                    break;
            }
        }

        /// <summary>
        /// حدث تغيير الاختيار في شبكة العملاء
        /// </summary>
        private void DgvCustomers_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvCustomers.CurrentRow?.DataBoundItem is Customer selectedCustomer)
            {
                LoadCustomerDetails(selectedCustomer);
                UpdateButtonStates();
            }
        }

        /// <summary>
        /// حدث النقر المزدوج على شبكة العملاء
        /// </summary>
        private void DgvCustomers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && _selectedCustomer != null && !_isEditMode)
            {
                BtnEditCustomer_Click(sender, e);
            }
        }

        /// <summary>
        /// حدث تنسيق خلايا شبكة العملاء
        /// </summary>
        private void DgvCustomers_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvCustomers.Rows[e.RowIndex].DataBoundItem is Customer customer)
            {
                // تلوين الصفوف حسب حالة العميل
                if (!customer.IsActive)
                {
                    e.CellStyle.BackColor = Color.FromArgb(245, 245, 245); // رمادي فاتح
                    e.CellStyle.ForeColor = Color.Gray;
                }
                else if (customer.IsOverCreditLimit())
                {
                    e.CellStyle.BackColor = Color.FromArgb(255, 235, 235); // أحمر فاتح
                    e.CellStyle.ForeColor = DangerColor;
                }
                else if (customer.HasOverdueDebts())
                {
                    e.CellStyle.BackColor = Color.FromArgb(255, 248, 220); // أصفر فاتح
                    e.CellStyle.ForeColor = Color.FromArgb(138, 109, 59);
                }
            }
        }

        /// <summary>
        /// حدث تغيير معايير البحث
        /// </summary>
        private void SearchCriteria_Changed(object sender, EventArgs e)
        {
            // تأخير البحث لتجنب البحث المتكرر
            searchTimer.Stop();
            searchTimer.Start();
        }

        #endregion

        #region عمليات الأزرار

        /// <summary>
        /// إضافة عميل جديد
        /// </summary>
        private void BtnAddCustomer_Click(object sender, EventArgs e)
        {
            try
            {
                ClearCustomerForm();
                SetEditMode(true);
                _selectedCustomer = null;

                // توليد كود عميل جديد
                txtCustomerCode.Text = GenerateCustomerCode();
                txtCustomerName.Focus();

                _logger?.LogInformation("بدء إضافة عميل جديد");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في بدء إضافة عميل جديد");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تعديل العميل المحدد
        /// </summary>
        private void BtnEditCustomer_Click(object sender, EventArgs e)
        {
            if (_selectedCustomer == null)
            {
                MessageBox.Show("يرجى اختيار عميل أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                SetEditMode(true);
                txtCustomerName.Focus();

                _logger?.LogInformation("بدء تعديل العميل {CustomerName}", _selectedCustomer.CustomerName);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في بدء تعديل العميل");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حذف العميل المحدد
        /// </summary>
        private void BtnDeleteCustomer_Click(object sender, EventArgs e)
        {
            if (_selectedCustomer == null)
            {
                MessageBox.Show("يرجى اختيار عميل أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // التحقق من وجود معاملات للعميل
            if (_selectedCustomer.TotalPurchases > 0 || _selectedCustomer.CurrentBalance != 0)
            {
                MessageBox.Show("لا يمكن حذف العميل لوجود معاملات مالية مرتبطة به", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show(
                $"هل تريد حذف العميل '{_selectedCustomer.CustomerName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد الحذف",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    var success = _customerBLL.DeleteCustomer(_selectedCustomer.CustomerID);
                    if (success)
                    {
                        MessageBox.Show("تم حذف العميل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadCustomers();
                        ClearCustomerForm();
                        _selectedCustomer = null;
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف العميل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "خطأ في حذف العميل");
                    MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// حفظ بيانات العميل
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateCustomerData())
                {
                    return;
                }

                // إنشاء أو تحديث العميل
                var customer = BuildCustomerFromForm();
                bool success;

                if (_selectedCustomer == null) // إضافة جديد
                {
                    var customerId = _customerBLL.AddCustomer(customer);
                    success = customerId > 0;
                    if (success)
                    {
                        customer.CustomerID = customerId;
                        _selectedCustomer = customer;
                        MessageBox.Show("تم إضافة العميل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else // تحديث موجود
                {
                    customer.CustomerID = _selectedCustomer.CustomerID;
                    customer.CreatedDate = _selectedCustomer.CreatedDate;
                    customer.CreatedBy = _selectedCustomer.CreatedBy;
                    success = _customerBLL.UpdateCustomer(customer);
                    if (success)
                    {
                        _selectedCustomer = customer;
                        MessageBox.Show("تم تحديث بيانات العميل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }

                if (success)
                {
                    SetEditMode(false);
                    LoadCustomers();
                    LoadCustomerDetails(_selectedCustomer);
                }
                else
                {
                    MessageBox.Show("فشل في حفظ بيانات العميل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ بيانات العميل");
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء التعديل
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد إلغاء التعديلات؟\n\nسيتم فقدان جميع التغييرات غير المحفوظة.",
                "تأكيد الإلغاء",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                SetEditMode(false);
                if (_selectedCustomer != null)
                {
                    DisplayCustomerInfo(_selectedCustomer);
                }
                else
                {
                    ClearCustomerForm();
                }
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadCustomers();
            if (_selectedCustomer != null)
            {
                LoadCustomerDetails(_selectedCustomer);
            }
        }

        /// <summary>
        /// البحث
        /// </summary>
        private void BtnSearch_Click(object sender, EventArgs e)
        {
            LoadCustomers();
        }

        #endregion

        #region عمليات التحقق والتصديق

        /// <summary>
        /// التحقق من صحة بيانات العميل
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        private bool ValidateCustomerData()
        {
            // التحقق من كود العميل
            if (string.IsNullOrWhiteSpace(txtCustomerCode.Text))
            {
                MessageBox.Show("كود العميل مطلوب", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCustomerCode.Focus();
                return false;
            }

            // التحقق من اسم العميل
            if (string.IsNullOrWhiteSpace(txtCustomerName.Text))
            {
                MessageBox.Show("اسم العميل مطلوب", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCustomerName.Focus();
                return false;
            }

            // التحقق من تكرار كود العميل
            if (_selectedCustomer == null || _selectedCustomer.CustomerCode != txtCustomerCode.Text)
            {
                var existingCustomer = _customerBLL.GetCustomerByCode(txtCustomerCode.Text);
                if (existingCustomer != null)
                {
                    MessageBox.Show("كود العميل موجود مسبقاً", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCustomerCode.Focus();
                    return false;
                }
            }

            // التحقق من البريد الإلكتروني
            if (!string.IsNullOrWhiteSpace(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
            {
                MessageBox.Show("البريد الإلكتروني غير صحيح", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmail.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// التحقق من صحة كود العميل
        /// </summary>
        private void ValidateCustomerCode(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtCustomerCode.Text))
            {
                // التحقق من تكرار الكود
                if (_selectedCustomer == null || _selectedCustomer.CustomerCode != txtCustomerCode.Text)
                {
                    var existingCustomer = _customerBLL.GetCustomerByCode(txtCustomerCode.Text);
                    if (existingCustomer != null)
                    {
                        MessageBox.Show("كود العميل موجود مسبقاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCustomerCode.Focus();
                    }
                }
            }
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        private void ValidateEmail(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
            {
                MessageBox.Show("البريد الإلكتروني غير صحيح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmail.Focus();
            }
        }

        /// <summary>
        /// حدث تغيير حد الائتمان
        /// </summary>
        private void NumCreditLimit_ValueChanged(object sender, EventArgs e)
        {
            // تحديث الرصيد المتاح
            if (_selectedCustomer != null)
            {
                var availableCredit = numCreditLimit.Value - _selectedCustomer.CurrentBalance;
                lblAvailableCredit.Text = $"الرصيد المتاح: {availableCredit:C}";
            }
        }

        #endregion

        #region عمليات مساعدة

        /// <summary>
        /// تعيين وضع التحرير
        /// </summary>
        /// <param name="editMode">وضع التحرير</param>
        private void SetEditMode(bool editMode)
        {
            _isEditMode = editMode;

            // تفعيل/تعطيل الحقول
            pnlCustomerInfo.Enabled = editMode;

            // تفعيل/تعطيل الأزرار
            btnAddCustomer.Enabled = !editMode;
            btnEditCustomer.Enabled = !editMode && _selectedCustomer != null;
            btnDeleteCustomer.Enabled = !editMode && _selectedCustomer != null;
            btnSave.Enabled = editMode;
            btnCancel.Enabled = editMode;

            // تفعيل/تعطيل الشبكة
            dgvCustomers.Enabled = !editMode;

            // تغيير لون الخلفية
            if (editMode)
            {
                pnlCustomerInfo.BackColor = Color.FromArgb(255, 255, 240); // أصفر فاتح
            }
            else
            {
                pnlCustomerInfo.BackColor = Color.White;
            }
        }

        /// <summary>
        /// تحديث حالة الأزرار
        /// </summary>
        private void UpdateButtonStates()
        {
            if (!_isEditMode)
            {
                btnEditCustomer.Enabled = _selectedCustomer != null;
                btnDeleteCustomer.Enabled = _selectedCustomer != null &&
                    _selectedCustomer.TotalPurchases == 0 &&
                    _selectedCustomer.CurrentBalance == 0;
            }
        }

        /// <summary>
        /// مسح نموذج العميل
        /// </summary>
        private void ClearCustomerForm()
        {
            txtCustomerCode.Clear();
            txtCustomerName.Clear();
            cmbCustomerTypeInfo.SelectedIndex = 0;
            txtPhone.Clear();
            txtPhone2.Clear();
            txtEmail.Clear();
            txtAddress.Clear();
            txtCity.Clear();
            numCreditLimit.Value = 0;
            numDiscountPercent.Value = 0;
            cmbPriceLevel.SelectedIndex = 0;
            txtNotes.Clear();
            chkIsActive.Checked = true;
            chkAllowCredit.Checked = true;

            // مسح ملخص الحساب
            lblCurrentBalance.Text = "الرصيد الحالي: 0.00 ر.س";
            lblTotalPurchases.Text = "إجمالي المشتريات: 0.00 ر.س";
            lblTotalPayments.Text = "إجمالي المدفوعات: 0.00 ر.س";
            lblOutstandingDebts.Text = "الديون المستحقة: 0.00 ر.س";
            lblLastPurchase.Text = "آخر شراء: -";
            lblLastPayment.Text = "آخر دفعة: -";

            // مسح سجل المعاملات
            dgvTransactions.DataSource = null;
        }

        /// <summary>
        /// بناء كائن العميل من النموذج
        /// </summary>
        /// <returns>كائن العميل</returns>
        private Customer BuildCustomerFromForm()
        {
            return new Customer
            {
                CustomerCode = txtCustomerCode.Text.Trim(),
                CustomerName = txtCustomerName.Text.Trim(),
                CustomerType = cmbCustomerTypeInfo.Text,
                Phone = txtPhone.Text.Trim(),
                Phone2 = txtPhone2.Text.Trim(),
                Email = txtEmail.Text.Trim(),
                Address = txtAddress.Text.Trim(),
                City = txtCity.Text.Trim(),
                CreditLimit = numCreditLimit.Value,
                DefaultDiscountPercent = numDiscountPercent.Value,
                PriceLevel = cmbPriceLevel.SelectedIndex + 1,
                Notes = txtNotes.Text.Trim(),
                IsActive = chkIsActive.Checked,
                AllowCredit = chkAllowCredit.Checked,
                ModifiedDate = DateTime.Now
            };
        }

        /// <summary>
        /// توليد كود عميل جديد
        /// </summary>
        /// <returns>كود العميل</returns>
        private string GenerateCustomerCode()
        {
            var prefix = "C";
            var timestamp = DateTime.Now.ToString("yyyyMMdd");
            var sequence = DateTime.Now.ToString("HHmmss");
            return $"{prefix}{timestamp}{sequence}";
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        /// <param name="email">البريد الإلكتروني</param>
        /// <returns>true إذا كان صحيحاً</returns>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }

    #region معايير البحث

    /// <summary>
    /// معايير البحث في العملاء
    /// </summary>
    public class CustomerSearchCriteria
    {
        public string SearchText { get; set; }
        public string CustomerType { get; set; }
        public bool ActiveOnly { get; set; }
        public int? Limit { get; set; }
    }

    #endregion
}