using System;
using System.ComponentModel.DataAnnotations;

namespace AredooPOS.Models
{
    /// <summary>
    /// نموذج مدفوعات الديون - يمثل دفعة واحدة لسداد دين
    /// يحتوي على جميع معلومات الدفعة وطريقة السداد
    /// </summary>
    public class DebtPayment
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم الدفعة في قاعدة البيانات (مفتاح أساسي)
        /// </summary>
        public int PaymentID { get; set; }

        /// <summary>
        /// رقم الدين المرتبط (مفتاح خارجي)
        /// </summary>
        [Required(ErrorMessage = "رقم الدين مطلوب")]
        public int DebtID { get; set; }

        /// <summary>
        /// رقم الدفعة المعروض للمستخدم
        /// </summary>
        [Required(ErrorMessage = "رقم الدفعة مطلوب")]
        [StringLength(20, ErrorMessage = "رقم الدفعة لا يجب أن يتجاوز 20 حرف")]
        public string PaymentNumber { get; set; }

        /// <summary>
        /// تاريخ ووقت الدفع
        /// </summary>
        [Required(ErrorMessage = "تاريخ الدفع مطلوب")]
        public DateTime PaymentDate { get; set; }

        /// <summary>
        /// وقت الدفع (منفصل عن التاريخ للدقة)
        /// </summary>
        public TimeSpan PaymentTime { get; set; }

        #endregion

        #region معلومات الدفع

        /// <summary>
        /// مبلغ الدفعة
        /// </summary>
        [Required(ErrorMessage = "مبلغ الدفعة مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "مبلغ الدفعة يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        /// <summary>
        /// طريقة الدفع (نقد، بطاقة، شيك، تحويل)
        /// </summary>
        [Required(ErrorMessage = "طريقة الدفع مطلوبة")]
        [StringLength(20, ErrorMessage = "طريقة الدفع لا يجب أن تتجاوز 20 حرف")]
        public string PaymentMethod { get; set; }

        /// <summary>
        /// العملة المستخدمة
        /// </summary>
        [StringLength(10, ErrorMessage = "العملة لا يجب أن تتجاوز 10 أحرف")]
        public string Currency { get; set; } = "ريال";

        /// <summary>
        /// سعر الصرف (إذا كانت العملة مختلفة)
        /// </summary>
        [Range(0.01, double.MaxValue, ErrorMessage = "سعر الصرف يجب أن يكون أكبر من صفر")]
        public decimal ExchangeRate { get; set; } = 1;

        /// <summary>
        /// المبلغ بالعملة الأساسية
        /// </summary>
        public decimal AmountInBaseCurrency { get; set; }

        #endregion

        #region معلومات البطاقة/الشيك/التحويل

        /// <summary>
        /// نوع البطاقة (فيزا، ماستركارد، مدى)
        /// </summary>
        [StringLength(20, ErrorMessage = "نوع البطاقة لا يجب أن يتجاوز 20 حرف")]
        public string CardType { get; set; }

        /// <summary>
        /// آخر 4 أرقام من البطاقة
        /// </summary>
        [StringLength(4, ErrorMessage = "أرقام البطاقة يجب أن تكون 4 أرقام")]
        public string CardLastFourDigits { get; set; }

        /// <summary>
        /// رمز التفويض للبطاقة
        /// </summary>
        [StringLength(20, ErrorMessage = "رمز التفويض لا يجب أن يتجاوز 20 حرف")]
        public string AuthorizationCode { get; set; }

        /// <summary>
        /// رقم الشيك
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الشيك لا يجب أن يتجاوز 20 حرف")]
        public string CheckNumber { get; set; }

        /// <summary>
        /// اسم البنك (للشيك أو التحويل)
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم البنك لا يجب أن يتجاوز 50 حرف")]
        public string BankName { get; set; }

        /// <summary>
        /// رقم التحويل أو المرجع
        /// </summary>
        [StringLength(50, ErrorMessage = "رقم التحويل لا يجب أن يتجاوز 50 حرف")]
        public string TransferReference { get; set; }

        #endregion

        #region حالة الدفعة

        /// <summary>
        /// حالة الدفعة (مؤكدة، معلقة، مرفوضة، ملغية)
        /// </summary>
        [Required(ErrorMessage = "حالة الدفعة مطلوبة")]
        [StringLength(20, ErrorMessage = "حالة الدفعة لا يجب أن تتجاوز 20 حرف")]
        public string PaymentStatus { get; set; }

        /// <summary>
        /// تاريخ تأكيد الدفعة
        /// </summary>
        public DateTime? ConfirmedDate { get; set; }

        /// <summary>
        /// من أكد الدفعة
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المؤكد لا يجب أن يتجاوز 50 حرف")]
        public string ConfirmedBy { get; set; }

        /// <summary>
        /// سبب الرفض أو الإلغاء (إن وجد)
        /// </summary>
        [StringLength(200, ErrorMessage = "سبب الرفض لا يجب أن يتجاوز 200 حرف")]
        public string RejectionReason { get; set; }

        #endregion

        #region الملاحظات والمعلومات الإضافية

        /// <summary>
        /// ملاحظات الدفعة
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 500 حرف")]
        public string Notes { get; set; }

        /// <summary>
        /// ملاحظات داخلية (لا تظهر للعميل)
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات الداخلية لا يجب أن تتجاوز 500 حرف")]
        public string InternalNotes { get; set; }

        /// <summary>
        /// مرفقات (صور الشيكات، إيصالات التحويل، إلخ)
        /// </summary>
        [StringLength(1000, ErrorMessage = "مسارات المرفقات لا يجب أن تتجاوز 1000 حرف")]
        public string Attachments { get; set; }

        #endregion

        #region معلومات المستخدم والنظام

        /// <summary>
        /// من سجل الدفعة
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المسجل لا يجب أن يتجاوز 50 حرف")]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ تسجيل الدفعة
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// آخر من عدل الدفعة
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المعدل لا يجب أن يتجاوز 50 حرف")]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime ModifiedDate { get; set; }

        /// <summary>
        /// عنوان IP للجهاز المستخدم
        /// </summary>
        [StringLength(45, ErrorMessage = "عنوان IP لا يجب أن يتجاوز 45 حرف")]
        public string IPAddress { get; set; }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ دفعة الدين مع التهيئة الافتراضية
        /// </summary>
        public DebtPayment()
        {
            // تهيئة التواريخ
            PaymentDate = DateTime.Now;
            PaymentTime = DateTime.Now.TimeOfDay;
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;

            // تهيئة القيم الافتراضية
            PaymentMethod = PaymentMethods.Cash;
            PaymentStatus = PaymentStatuses.Confirmed;
            Currency = "ريال";
            ExchangeRate = 1;
        }

        #endregion

        #region العمليات الحسابية

        /// <summary>
        /// حساب المبلغ بالعملة الأساسية
        /// </summary>
        public void CalculateAmountInBaseCurrency()
        {
            AmountInBaseCurrency = Amount * ExchangeRate;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// تأكيد الدفعة
        /// </summary>
        /// <param name="confirmedBy">من أكد الدفعة</param>
        public void ConfirmPayment(string confirmedBy)
        {
            PaymentStatus = PaymentStatuses.Confirmed;
            ConfirmedDate = DateTime.Now;
            ConfirmedBy = confirmedBy;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// رفض الدفعة
        /// </summary>
        /// <param name="reason">سبب الرفض</param>
        /// <param name="rejectedBy">من رفض الدفعة</param>
        public void RejectPayment(string reason, string rejectedBy)
        {
            PaymentStatus = PaymentStatuses.Rejected;
            RejectionReason = reason;
            ModifiedBy = rejectedBy;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// إلغاء الدفعة
        /// </summary>
        /// <param name="reason">سبب الإلغاء</param>
        /// <param name="cancelledBy">من ألغى الدفعة</param>
        public void CancelPayment(string reason, string cancelledBy)
        {
            PaymentStatus = PaymentStatuses.Cancelled;
            RejectionReason = reason;
            ModifiedBy = cancelledBy;
            ModifiedDate = DateTime.Now;
        }

        #endregion

        #region عمليات التحقق والتصديق

        /// <summary>
        /// التحقق من صحة بيانات الدفعة
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return DebtID > 0 &&
                   !string.IsNullOrWhiteSpace(PaymentNumber) &&
                   Amount > 0 &&
                   !string.IsNullOrWhiteSpace(PaymentMethod) &&
                   ExchangeRate > 0;
        }

        /// <summary>
        /// التحقق من إمكانية تعديل الدفعة
        /// </summary>
        /// <returns>true إذا كان بإمكان التعديل</returns>
        public bool CanEdit()
        {
            return PaymentStatus == PaymentStatuses.Pending ||
                   PaymentStatus == PaymentStatuses.Confirmed;
        }

        /// <summary>
        /// التحقق من إمكانية إلغاء الدفعة
        /// </summary>
        /// <returns>true إذا كان بإمكان الإلغاء</returns>
        public bool CanCancel()
        {
            return PaymentStatus != PaymentStatuses.Cancelled &&
                   PaymentStatus != PaymentStatuses.Rejected;
        }

        /// <summary>
        /// التحقق من كون الدفعة مؤكدة
        /// </summary>
        /// <returns>true إذا كانت الدفعة مؤكدة</returns>
        public bool IsConfirmed()
        {
            return PaymentStatus == PaymentStatuses.Confirmed;
        }

        /// <summary>
        /// التحقق من كون الدفعة نقدية
        /// </summary>
        /// <returns>true إذا كانت الدفعة نقدية</returns>
        public bool IsCashPayment()
        {
            return PaymentMethod == PaymentMethods.Cash;
        }

        #endregion

        #region عمليات النسخ والتحويل

        /// <summary>
        /// تحويل الدفعة إلى نص وصفي
        /// </summary>
        /// <returns>وصف نصي للدفعة</returns>
        public override string ToString()
        {
            return $"دفعة رقم {PaymentNumber} - المبلغ: {Amount:C} - {PaymentMethod} - {PaymentDate:yyyy/MM/dd}";
        }

        #endregion
    }

    #region الثوابت والتعدادات

    /// <summary>
    /// طرق الدفع
    /// </summary>
    public static class PaymentMethods
    {
        public const string Cash = "نقد";
        public const string Card = "بطاقة";
        public const string Check = "شيك";
        public const string Transfer = "تحويل";
        public const string Online = "دفع إلكتروني";
        public const string Other = "أخرى";
    }

    /// <summary>
    /// حالات الدفعات
    /// </summary>
    public static class PaymentStatuses
    {
        public const string Pending = "معلقة";
        public const string Confirmed = "مؤكدة";
        public const string Rejected = "مرفوضة";
        public const string Cancelled = "ملغية";
    }

    #endregion
}
