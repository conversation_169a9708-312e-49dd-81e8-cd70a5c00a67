<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net472</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <AssemblyTitle>اختبار أريدو POS</AssemblyTitle>
    <AssemblyDescription>اختبار المكونات الأساسية لنظام أريدو POS</AssemblyDescription>
    <AssemblyCompany>أريدو</AssemblyCompany>
    <AssemblyProduct>أريدو POS Test</AssemblyProduct>
    <AssemblyCopyright>© 2024 أريدو. جميع الحقوق محفوظة.</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <StartupObject>AredooPOS.Test.TestProgram</StartupObject>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <Deterministic>true</Deterministic>
    <LangVersion>latest</LangVersion>
    <Nullable>disable</Nullable>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Management" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="TestProgram.cs" />
    <Compile Include="UI/ArabicLayoutManager.cs" />
    <Compile Include="UI/ArabicResourceManager.cs" />
    <Compile Include="Compatibility/WindowsCompatibilityManager.cs" />
    <Compile Include="Barcode/BarcodeManager.cs" />
    <Compile Include="Printing/ThermalPrintManager.cs" />
    <Compile Include="Printing/ESCPOSCommands.cs" />
    <Compile Include="Printing/RawPrinterHelper.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
