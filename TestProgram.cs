using System;
using System.Globalization;
using System.Threading;
using System.Windows.Forms;
using AredooPOS.UI;
using AredooPOS.Compatibility;

namespace AredooPOS.Test
{
    /// <summary>
    /// برنامج اختبار بسيط لنظام أريدو POS
    /// </summary>
    internal static class TestProgram
    {
        /// <summary>
        /// نقطة الدخول للاختبار
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // تهيئة التطبيق
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // تعيين الثقافة العربية
                SetArabicCulture();

                // فحص التوافق
                if (!CheckCompatibility())
                {
                    MessageBox.Show("النظام غير متوافق", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // إنشاء نموذج اختبار
                var testForm = CreateTestForm();

                // تطبيق التخطيط العربي
                ArabicLayoutManager.ApplyArabicLayout(testForm);
                ArabicResourceManager.ApplyArabicTexts(testForm);

                // تشغيل التطبيق
                Application.Run(testForm);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تعيين الثقافة العربية
        /// </summary>
        private static void SetArabicCulture()
        {
            try
            {
                var arabicCulture = new CultureInfo("ar-SA");
                Thread.CurrentThread.CurrentCulture = arabicCulture;
                Thread.CurrentThread.CurrentUICulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentCulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentUICulture = arabicCulture;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"تحذير: فشل في تعيين الثقافة العربية - {ex.Message}");
            }
        }

        /// <summary>
        /// فحص التوافق
        /// </summary>
        /// <returns>true إذا كان النظام متوافق</returns>
        private static bool CheckCompatibility()
        {
            try
            {
                var compatibilityManager = WindowsCompatibilityManager.Instance;
                
                if (!compatibilityManager.IsCompatible)
                {
                    var criticalIssues = compatibilityManager.Issues.FindAll(i => i.Severity == IssueSeverity.Critical);
                    if (criticalIssues.Count > 0)
                    {
                        var message = "تم اكتشاف مشاكل حرجة في التوافق:\n\n";
                        foreach (var issue in criticalIssues)
                        {
                            message += $"• {issue.Title}: {issue.Description}\n";
                        }
                        
                        MessageBox.Show(message, "مشاكل التوافق", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في فحص التوافق: {ex.Message}");
                return true; // السماح بالمتابعة في حالة الخطأ
            }
        }

        /// <summary>
        /// إنشاء نموذج اختبار
        /// </summary>
        /// <returns>نموذج الاختبار</returns>
        private static Form CreateTestForm()
        {
            var form = new Form
            {
                Text = "اختبار نظام أريدو POS",
                Size = new System.Drawing.Size(800, 600),
                StartPosition = FormStartPosition.CenterScreen,
                WindowState = FormWindowState.Normal
            };

            // إضافة شريط قوائم
            var menuStrip = new MenuStrip();
            
            // قائمة اختبار
            var testMenu = new ToolStripMenuItem("اختبار");
            testMenu.DropDownItems.Add("اختبار التوافق", null, OnTestCompatibility);
            testMenu.DropDownItems.Add("اختبار الباركود", null, OnTestBarcode);
            testMenu.DropDownItems.Add("اختبار الطباعة", null, OnTestPrinting);
            testMenu.DropDownItems.Add(new ToolStripSeparator());
            testMenu.DropDownItems.Add("خروج", null, (s, e) => Application.Exit());
            
            menuStrip.Items.Add(testMenu);
            
            // إضافة محتوى
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = System.Drawing.Color.White
            };

            var welcomeLabel = new Label
            {
                Text = "مرحباً بك في اختبار نظام أريدو POS\n\n" +
                       "هذا اختبار للمكونات الأساسية:\n" +
                       "✓ الواجهة العربية\n" +
                       "✓ التوافق مع Windows\n" +
                       "✓ نظام الباركود\n" +
                       "✓ دعم الطباعة الحرارية\n\n" +
                       "اختر من القائمة أعلاه لاختبار المكونات",
                Font = new System.Drawing.Font("Tahoma", 12, System.Drawing.FontStyle.Regular),
                TextAlign = System.Drawing.ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                ForeColor = System.Drawing.Color.DarkBlue
            };

            panel.Controls.Add(welcomeLabel);
            
            form.MainMenuStrip = menuStrip;
            form.Controls.Add(menuStrip);
            form.Controls.Add(panel);

            return form;
        }

        /// <summary>
        /// اختبار التوافق
        /// </summary>
        private static void OnTestCompatibility(object sender, EventArgs e)
        {
            try
            {
                var compatibilityManager = WindowsCompatibilityManager.Instance;
                var report = compatibilityManager.GenerateCompatibilityReport();

                var message = $"تقرير التوافق:\n\n";
                message += $"نظام التشغيل: {report.WindowsVersion.Name} {report.WindowsVersion.Version}\n";
                message += $"البنية: {report.WindowsVersion.ProcessorArchitecture}\n";
                message += $"الذاكرة: {report.WindowsVersion.TotalPhysicalMemory / (1024 * 1024 * 1024)} GB\n";
                message += $"متوافق: {(report.IsCompatible ? "نعم" : "لا")}\n\n";

                if (report.Issues.Count > 0)
                {
                    message += "المشاكل المكتشفة:\n";
                    foreach (var issue in report.Issues)
                    {
                        message += $"• {issue.Title} ({issue.Severity})\n";
                    }
                }
                else
                {
                    message += "لا توجد مشاكل في التوافق";
                }

                MessageBox.Show(message, "تقرير التوافق", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار التوافق: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار الباركود
        /// </summary>
        private static void OnTestBarcode(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show(
                    "اختبار نظام الباركود:\n\n" +
                    "✓ مدير الباركود متوفر\n" +
                    "✓ دعم أنواع متعددة من الباركود\n" +
                    "✓ إنتاج وقراءة الباركود\n" +
                    "✓ تحقق من صحة البيانات\n\n" +
                    "النظام جاهز لإنتاج الباركود",
                    "اختبار الباركود",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الباركود: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار الطباعة
        /// </summary>
        private static void OnTestPrinting(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show(
                    "اختبار نظام الطباعة:\n\n" +
                    "✓ مدير الطباعة الحرارية متوفر\n" +
                    "✓ دعم أوامر ESC/POS\n" +
                    "✓ طباعة الفواتير العربية\n" +
                    "✓ دعم الطابعات المختلفة\n\n" +
                    "النظام جاهز للطباعة",
                    "اختبار الطباعة",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
