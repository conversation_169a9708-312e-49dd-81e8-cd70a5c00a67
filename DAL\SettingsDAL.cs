using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using AredooPOS.Models.Settings;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AredooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات للإعدادات
    /// تحتوي على جميع العمليات المتعلقة بقاعدة البيانات للإعدادات المختلفة
    /// </summary>
    public class SettingsDAL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly string _connectionString;
        private readonly ILogger<SettingsDAL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة الوصول للبيانات للإعدادات
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public SettingsDAL(string connectionString = null, ILogger<SettingsDAL> logger = null)
        {
            _connectionString = connectionString ?? DatabaseConfig.GetConnectionString();
            _logger = logger;
        }

        #endregion

        #region الإعدادات العامة

        /// <summary>
        /// الحصول على جميع الإعدادات
        /// </summary>
        /// <returns>قائمة الإعدادات</returns>
        public List<SystemSettings> GetAllSettings()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("SELECT * FROM SystemSettings ORDER BY Category, SettingName", connection);

                connection.Open();
                using var reader = command.ExecuteReader();

                var settings = new List<SystemSettings>();
                while (reader.Read())
                {
                    settings.Add(MapSystemSettings(reader));
                }

                return settings;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على جميع الإعدادات");
                throw;
            }
        }

        /// <summary>
        /// الحصول على إعداد بالاسم
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <returns>الإعداد</returns>
        public SystemSettings GetSetting(string settingName)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("SELECT * FROM SystemSettings WHERE SettingName = @SettingName", connection);

                command.Parameters.AddWithValue("@SettingName", settingName);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapSystemSettings(reader);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الإعداد {settingName}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على قيمة إعداد
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>قيمة الإعداد</returns>
        public string GetSettingValue(string settingName, string defaultValue = null)
        {
            try
            {
                var setting = GetSetting(settingName);
                return setting?.SettingValue ?? defaultValue;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على قيمة الإعداد {settingName}");
                return defaultValue;
            }
        }

        /// <summary>
        /// الحصول على الإعدادات حسب الفئة
        /// </summary>
        /// <param name="category">الفئة</param>
        /// <returns>قائمة الإعدادات</returns>
        public List<SystemSettings> GetSettingsByCategory(string category)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("SELECT * FROM SystemSettings WHERE Category = @Category ORDER BY SettingName", connection);

                command.Parameters.AddWithValue("@Category", category);

                connection.Open();
                using var reader = command.ExecuteReader();

                var settings = new List<SystemSettings>();
                while (reader.Read())
                {
                    settings.Add(MapSystemSettings(reader));
                }

                return settings;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على إعدادات الفئة {category}");
                throw;
            }
        }

        /// <summary>
        /// حفظ أو تحديث إعداد
        /// </summary>
        /// <param name="setting">الإعداد</param>
        /// <returns>رقم الإعداد</returns>
        public int SaveSetting(SystemSettings setting)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                
                var existingSetting = GetSetting(setting.SettingName);
                
                if (existingSetting != null)
                {
                    // تحديث الإعداد الموجود
                    using var command = new SqlCommand(@"
                        UPDATE SystemSettings 
                        SET SettingValue = @SettingValue, 
                            Description = @Description,
                            LastUpdated = @LastUpdated,
                            UpdatedBy = @UpdatedBy
                        WHERE SettingName = @SettingName", connection);

                    command.Parameters.AddWithValue("@SettingValue", setting.SettingValue ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@Description", setting.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@LastUpdated", setting.LastUpdated);
                    command.Parameters.AddWithValue("@UpdatedBy", setting.UpdatedBy ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@SettingName", setting.SettingName);

                    connection.Open();
                    command.ExecuteNonQuery();

                    return existingSetting.SettingID;
                }
                else
                {
                    // إضافة إعداد جديد
                    using var command = new SqlCommand(@"
                        INSERT INTO SystemSettings (SettingName, SettingValue, Description, Category, DataType, IsRequired, IsEditable, LastUpdated, UpdatedBy)
                        OUTPUT INSERTED.SettingID
                        VALUES (@SettingName, @SettingValue, @Description, @Category, @DataType, @IsRequired, @IsEditable, @LastUpdated, @UpdatedBy)", connection);

                    command.Parameters.AddWithValue("@SettingName", setting.SettingName);
                    command.Parameters.AddWithValue("@SettingValue", setting.SettingValue ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@Description", setting.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@Category", setting.Category);
                    command.Parameters.AddWithValue("@DataType", setting.DataType);
                    command.Parameters.AddWithValue("@IsRequired", setting.IsRequired);
                    command.Parameters.AddWithValue("@IsEditable", setting.IsEditable);
                    command.Parameters.AddWithValue("@LastUpdated", setting.LastUpdated);
                    command.Parameters.AddWithValue("@UpdatedBy", setting.UpdatedBy ?? (object)DBNull.Value);

                    connection.Open();
                    var settingId = (int)command.ExecuteScalar();

                    _logger?.LogInformation($"تم إضافة إعداد جديد: {setting.SettingName}");
                    return settingId;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حفظ الإعداد {setting.SettingName}");
                throw;
            }
        }

        /// <summary>
        /// حفظ قيمة إعداد
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <param name="settingValue">قيمة الإعداد</param>
        /// <param name="updatedBy">من قام بالتحديث</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SaveSettingValue(string settingName, string settingValue, string updatedBy)
        {
            try
            {
                var setting = GetSetting(settingName);
                if (setting != null)
                {
                    setting.UpdateValue(settingValue, updatedBy);
                    SaveSetting(setting);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حفظ قيمة الإعداد {settingName}");
                return false;
            }
        }

        /// <summary>
        /// حفظ عدة إعدادات
        /// </summary>
        /// <param name="settings">قائمة الإعدادات</param>
        /// <returns>عدد الإعدادات المحفوظة</returns>
        public int SaveMultipleSettings(List<SystemSettings> settings)
        {
            try
            {
                int savedCount = 0;
                foreach (var setting in settings)
                {
                    SaveSetting(setting);
                    savedCount++;
                }

                _logger?.LogInformation($"تم حفظ {savedCount} إعداد");
                return savedCount;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ عدة إعدادات");
                throw;
            }
        }

        #endregion

        #region إعدادات الضريبة

        /// <summary>
        /// الحصول على إعدادات الضريبة
        /// </summary>
        /// <returns>إعدادات الضريبة</returns>
        public TaxSettings GetTaxSettings()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("SELECT * FROM TaxSettings WHERE TaxSettingID = 1", connection);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    var taxSettings = MapTaxSettings(reader);
                    
                    // تحميل معدلات الضريبة
                    taxSettings.TaxRates = GetTaxRates();
                    
                    return taxSettings;
                }

                // إرجاع إعدادات افتراضية إذا لم توجد
                return new TaxSettings();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إعدادات الضريبة");
                throw;
            }
        }

        /// <summary>
        /// حفظ إعدادات الضريبة
        /// </summary>
        /// <param name="taxSettings">إعدادات الضريبة</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SaveTaxSettings(TaxSettings taxSettings)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var transaction = connection.BeginTransaction();

                try
                {
                    // حفظ الإعدادات الأساسية
                    using var command = new SqlCommand(@"
                        MERGE TaxSettings AS target
                        USING (SELECT 1 AS TaxSettingID) AS source ON target.TaxSettingID = source.TaxSettingID
                        WHEN MATCHED THEN
                            UPDATE SET 
                                IsTaxEnabled = @IsTaxEnabled,
                                DefaultTaxRate = @DefaultTaxRate,
                                TaxName = @TaxName,
                                TaxRegistrationNumber = @TaxRegistrationNumber,
                                TaxCalculationMethod = @TaxCalculationMethod,
                                ApplyToAllProducts = @ApplyToAllProducts,
                                MinimumTaxableAmount = @MinimumTaxableAmount,
                                TaxDecimalPlaces = @TaxDecimalPlaces,
                                RoundingMethod = @RoundingMethod
                        WHEN NOT MATCHED THEN
                            INSERT (TaxSettingID, IsTaxEnabled, DefaultTaxRate, TaxName, TaxRegistrationNumber, TaxCalculationMethod, ApplyToAllProducts, MinimumTaxableAmount, TaxDecimalPlaces, RoundingMethod)
                            VALUES (1, @IsTaxEnabled, @DefaultTaxRate, @TaxName, @TaxRegistrationNumber, @TaxCalculationMethod, @ApplyToAllProducts, @MinimumTaxableAmount, @TaxDecimalPlaces, @RoundingMethod);", 
                        connection, transaction);

                    command.Parameters.AddWithValue("@IsTaxEnabled", taxSettings.IsTaxEnabled);
                    command.Parameters.AddWithValue("@DefaultTaxRate", taxSettings.DefaultTaxRate);
                    command.Parameters.AddWithValue("@TaxName", taxSettings.TaxName);
                    command.Parameters.AddWithValue("@TaxRegistrationNumber", taxSettings.TaxRegistrationNumber ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@TaxCalculationMethod", taxSettings.TaxCalculationMethod);
                    command.Parameters.AddWithValue("@ApplyToAllProducts", taxSettings.ApplyToAllProducts);
                    command.Parameters.AddWithValue("@MinimumTaxableAmount", taxSettings.MinimumTaxableAmount);
                    command.Parameters.AddWithValue("@TaxDecimalPlaces", taxSettings.TaxDecimalPlaces);
                    command.Parameters.AddWithValue("@RoundingMethod", taxSettings.RoundingMethod);

                    connection.Open();
                    command.ExecuteNonQuery();

                    // حفظ معدلات الضريبة
                    SaveTaxRates(taxSettings.TaxRates, connection, transaction);

                    transaction.Commit();
                    _logger?.LogInformation("تم حفظ إعدادات الضريبة بنجاح");
                    return true;
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ إعدادات الضريبة");
                return false;
            }
        }

        /// <summary>
        /// الحصول على معدلات الضريبة
        /// </summary>
        /// <returns>قائمة معدلات الضريبة</returns>
        public List<TaxRate> GetTaxRates()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("SELECT * FROM TaxRates WHERE IsActive = 1 ORDER BY Name", connection);

                connection.Open();
                using var reader = command.ExecuteReader();

                var taxRates = new List<TaxRate>();
                while (reader.Read())
                {
                    taxRates.Add(MapTaxRate(reader));
                }

                return taxRates;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على معدلات الضريبة");
                throw;
            }
        }

        /// <summary>
        /// حفظ معدلات الضريبة
        /// </summary>
        /// <param name="taxRates">قائمة معدلات الضريبة</param>
        /// <param name="connection">الاتصال</param>
        /// <param name="transaction">المعاملة</param>
        private void SaveTaxRates(List<TaxRate> taxRates, SqlConnection connection, SqlTransaction transaction)
        {
            // حذف المعدلات الموجودة
            using var deleteCommand = new SqlCommand("DELETE FROM TaxRates", connection, transaction);
            deleteCommand.ExecuteNonQuery();

            // إضافة المعدلات الجديدة
            foreach (var taxRate in taxRates)
            {
                using var insertCommand = new SqlCommand(@"
                    INSERT INTO TaxRates (Name, Rate, ProductID, CategoryID, IsActive, CreatedDate)
                    VALUES (@Name, @Rate, @ProductID, @CategoryID, @IsActive, @CreatedDate)", 
                    connection, transaction);

                insertCommand.Parameters.AddWithValue("@Name", taxRate.Name);
                insertCommand.Parameters.AddWithValue("@Rate", taxRate.Rate);
                insertCommand.Parameters.AddWithValue("@ProductID", taxRate.ProductID ?? (object)DBNull.Value);
                insertCommand.Parameters.AddWithValue("@CategoryID", taxRate.CategoryID ?? (object)DBNull.Value);
                insertCommand.Parameters.AddWithValue("@IsActive", taxRate.IsActive);
                insertCommand.Parameters.AddWithValue("@CreatedDate", taxRate.CreatedDate);

                insertCommand.ExecuteNonQuery();
            }
        }

        #endregion

        #region إعدادات العملة

        /// <summary>
        /// الحصول على إعدادات العملة
        /// </summary>
        /// <returns>إعدادات العملة</returns>
        public CurrencySettings GetCurrencySettings()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("SELECT * FROM CurrencySettings WHERE CurrencySettingID = 1", connection);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    var currencySettings = MapCurrencySettings(reader);
                    
                    // تحميل العملات المدعومة
                    currencySettings.SupportedCurrencies = GetSupportedCurrencies();
                    
                    // تحميل أسعار الصرف
                    currencySettings.ExchangeRates = GetExchangeRates();
                    
                    return currencySettings;
                }

                // إرجاع إعدادات افتراضية إذا لم توجد
                var defaultSettings = new CurrencySettings();
                defaultSettings.SupportedCurrencies = SupportedCurrencies.DefaultCurrencies;
                return defaultSettings;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إعدادات العملة");
                throw;
            }
        }

        /// <summary>
        /// حفظ إعدادات العملة
        /// </summary>
        /// <param name="currencySettings">إعدادات العملة</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SaveCurrencySettings(CurrencySettings currencySettings)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var transaction = connection.BeginTransaction();

                try
                {
                    // حفظ الإعدادات الأساسية
                    using var command = new SqlCommand(@"
                        MERGE CurrencySettings AS target
                        USING (SELECT 1 AS CurrencySettingID) AS source ON target.CurrencySettingID = source.CurrencySettingID
                        WHEN MATCHED THEN
                            UPDATE SET 
                                DefaultCurrencyCode = @DefaultCurrencyCode,
                                DecimalPlaces = @DecimalPlaces,
                                CurrencySymbol = @CurrencySymbol,
                                SymbolPosition = @SymbolPosition,
                                ThousandsSeparator = @ThousandsSeparator,
                                DecimalSeparator = @DecimalSeparator,
                                SupportMultipleCurrencies = @SupportMultipleCurrencies,
                                AutoUpdateExchangeRates = @AutoUpdateExchangeRates,
                                ExchangeRateSource = @ExchangeRateSource,
                                LastExchangeRateUpdate = @LastExchangeRateUpdate
                        WHEN NOT MATCHED THEN
                            INSERT (CurrencySettingID, DefaultCurrencyCode, DecimalPlaces, CurrencySymbol, SymbolPosition, ThousandsSeparator, DecimalSeparator, SupportMultipleCurrencies, AutoUpdateExchangeRates, ExchangeRateSource, LastExchangeRateUpdate)
                            VALUES (1, @DefaultCurrencyCode, @DecimalPlaces, @CurrencySymbol, @SymbolPosition, @ThousandsSeparator, @DecimalSeparator, @SupportMultipleCurrencies, @AutoUpdateExchangeRates, @ExchangeRateSource, @LastExchangeRateUpdate);", 
                        connection, transaction);

                    command.Parameters.AddWithValue("@DefaultCurrencyCode", currencySettings.DefaultCurrencyCode);
                    command.Parameters.AddWithValue("@DecimalPlaces", currencySettings.DecimalPlaces);
                    command.Parameters.AddWithValue("@CurrencySymbol", currencySettings.CurrencySymbol);
                    command.Parameters.AddWithValue("@SymbolPosition", currencySettings.SymbolPosition);
                    command.Parameters.AddWithValue("@ThousandsSeparator", currencySettings.ThousandsSeparator);
                    command.Parameters.AddWithValue("@DecimalSeparator", currencySettings.DecimalSeparator);
                    command.Parameters.AddWithValue("@SupportMultipleCurrencies", currencySettings.SupportMultipleCurrencies);
                    command.Parameters.AddWithValue("@AutoUpdateExchangeRates", currencySettings.AutoUpdateExchangeRates);
                    command.Parameters.AddWithValue("@ExchangeRateSource", currencySettings.ExchangeRateSource);
                    command.Parameters.AddWithValue("@LastExchangeRateUpdate", currencySettings.LastExchangeRateUpdate ?? (object)DBNull.Value);

                    connection.Open();
                    command.ExecuteNonQuery();

                    // حفظ العملات المدعومة
                    SaveSupportedCurrencies(currencySettings.SupportedCurrencies, connection, transaction);

                    // حفظ أسعار الصرف
                    SaveExchangeRates(currencySettings.ExchangeRates, connection, transaction);

                    transaction.Commit();
                    _logger?.LogInformation("تم حفظ إعدادات العملة بنجاح");
                    return true;
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ إعدادات العملة");
                return false;
            }
        }

        /// <summary>
        /// الحصول على العملات المدعومة
        /// </summary>
        /// <returns>قائمة العملات المدعومة</returns>
        public List<Currency> GetSupportedCurrencies()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("SELECT * FROM Currencies WHERE IsActive = 1 ORDER BY DisplayOrder, Code", connection);

                connection.Open();
                using var reader = command.ExecuteReader();

                var currencies = new List<Currency>();
                while (reader.Read())
                {
                    currencies.Add(MapCurrency(reader));
                }

                // إذا لم توجد عملات، إرجاع العملات الافتراضية
                if (!currencies.Any())
                {
                    return SupportedCurrencies.DefaultCurrencies;
                }

                return currencies;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على العملات المدعومة");
                throw;
            }
        }

        /// <summary>
        /// الحصول على أسعار الصرف
        /// </summary>
        /// <returns>قائمة أسعار الصرف</returns>
        public List<ExchangeRate> GetExchangeRates()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("SELECT * FROM ExchangeRates WHERE IsActive = 1 ORDER BY FromCurrency, ToCurrency", connection);

                connection.Open();
                using var reader = command.ExecuteReader();

                var exchangeRates = new List<ExchangeRate>();
                while (reader.Read())
                {
                    exchangeRates.Add(MapExchangeRate(reader));
                }

                return exchangeRates;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على أسعار الصرف");
                throw;
            }
        }

        /// <summary>
        /// حفظ العملات المدعومة
        /// </summary>
        /// <param name="currencies">قائمة العملات</param>
        /// <param name="connection">الاتصال</param>
        /// <param name="transaction">المعاملة</param>
        private void SaveSupportedCurrencies(List<Currency> currencies, SqlConnection connection, SqlTransaction transaction)
        {
            // حذف العملات الموجودة
            using var deleteCommand = new SqlCommand("DELETE FROM Currencies", connection, transaction);
            deleteCommand.ExecuteNonQuery();

            // إضافة العملات الجديدة
            foreach (var currency in currencies)
            {
                using var insertCommand = new SqlCommand(@"
                    INSERT INTO Currencies (Code, Name, NameArabic, Symbol, DecimalPlaces, SymbolPosition, IsActive, DisplayOrder, CreatedDate)
                    VALUES (@Code, @Name, @NameArabic, @Symbol, @DecimalPlaces, @SymbolPosition, @IsActive, @DisplayOrder, @CreatedDate)", 
                    connection, transaction);

                insertCommand.Parameters.AddWithValue("@Code", currency.Code);
                insertCommand.Parameters.AddWithValue("@Name", currency.Name);
                insertCommand.Parameters.AddWithValue("@NameArabic", currency.NameArabic ?? (object)DBNull.Value);
                insertCommand.Parameters.AddWithValue("@Symbol", currency.Symbol);
                insertCommand.Parameters.AddWithValue("@DecimalPlaces", currency.DecimalPlaces);
                insertCommand.Parameters.AddWithValue("@SymbolPosition", currency.SymbolPosition);
                insertCommand.Parameters.AddWithValue("@IsActive", currency.IsActive);
                insertCommand.Parameters.AddWithValue("@DisplayOrder", currency.DisplayOrder);
                insertCommand.Parameters.AddWithValue("@CreatedDate", currency.CreatedDate);

                insertCommand.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// حفظ أسعار الصرف
        /// </summary>
        /// <param name="exchangeRates">قائمة أسعار الصرف</param>
        /// <param name="connection">الاتصال</param>
        /// <param name="transaction">المعاملة</param>
        private void SaveExchangeRates(List<ExchangeRate> exchangeRates, SqlConnection connection, SqlTransaction transaction)
        {
            // حذف الأسعار الموجودة
            using var deleteCommand = new SqlCommand("DELETE FROM ExchangeRates", connection, transaction);
            deleteCommand.ExecuteNonQuery();

            // إضافة الأسعار الجديدة
            foreach (var exchangeRate in exchangeRates)
            {
                using var insertCommand = new SqlCommand(@"
                    INSERT INTO ExchangeRates (FromCurrency, ToCurrency, Rate, RateDate, IsActive, Source, LastUpdated, UpdatedBy, Notes)
                    VALUES (@FromCurrency, @ToCurrency, @Rate, @RateDate, @IsActive, @Source, @LastUpdated, @UpdatedBy, @Notes)", 
                    connection, transaction);

                insertCommand.Parameters.AddWithValue("@FromCurrency", exchangeRate.FromCurrency);
                insertCommand.Parameters.AddWithValue("@ToCurrency", exchangeRate.ToCurrency);
                insertCommand.Parameters.AddWithValue("@Rate", exchangeRate.Rate);
                insertCommand.Parameters.AddWithValue("@RateDate", exchangeRate.RateDate);
                insertCommand.Parameters.AddWithValue("@IsActive", exchangeRate.IsActive);
                insertCommand.Parameters.AddWithValue("@Source", exchangeRate.Source);
                insertCommand.Parameters.AddWithValue("@LastUpdated", exchangeRate.LastUpdated);
                insertCommand.Parameters.AddWithValue("@UpdatedBy", exchangeRate.UpdatedBy ?? (object)DBNull.Value);
                insertCommand.Parameters.AddWithValue("@Notes", exchangeRate.Notes ?? (object)DBNull.Value);

                insertCommand.ExecuteNonQuery();
            }
        }

        #endregion

        #region إعدادات المتجر

        /// <summary>
        /// الحصول على إعدادات المتجر
        /// </summary>
        /// <returns>إعدادات المتجر</returns>
        public StoreSettings GetStoreSettings()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("SELECT * FROM StoreSettings WHERE StoreSettingID = 1", connection);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapStoreSettings(reader);
                }

                // إرجاع إعدادات افتراضية إذا لم توجد
                return new StoreSettings();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إعدادات المتجر");
                throw;
            }
        }

        /// <summary>
        /// حفظ إعدادات المتجر
        /// </summary>
        /// <param name="storeSettings">إعدادات المتجر</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SaveStoreSettings(StoreSettings storeSettings)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand(@"
                    MERGE StoreSettings AS target
                    USING (SELECT 1 AS StoreSettingID) AS source ON target.StoreSettingID = source.StoreSettingID
                    WHEN MATCHED THEN
                        UPDATE SET
                            StoreName = @StoreName,
                            StoreNameEnglish = @StoreNameEnglish,
                            StoreDescription = @StoreDescription,
                            LogoPath = @LogoPath,
                            LogoData = @LogoData,
                            PrimaryPhone = @PrimaryPhone,
                            SecondaryPhone = @SecondaryPhone,
                            FaxNumber = @FaxNumber,
                            Email = @Email,
                            Website = @Website,
                            Address = @Address,
                            City = @City,
                            State = @State,
                            PostalCode = @PostalCode,
                            Country = @Country,
                            CommercialRegistrationNumber = @CommercialRegistrationNumber,
                            LicenseNumber = @LicenseNumber,
                            TaxRegistrationNumber = @TaxRegistrationNumber,
                            BusinessType = @BusinessType,
                            EstablishedDate = @EstablishedDate,
                            WorkingHours = @WorkingHours,
                            AdditionalInfo = @AdditionalInfo,
                            FacebookUrl = @FacebookUrl,
                            TwitterUrl = @TwitterUrl,
                            InstagramUrl = @InstagramUrl,
                            WhatsAppNumber = @WhatsAppNumber,
                            TelegramUrl = @TelegramUrl
                    WHEN NOT MATCHED THEN
                        INSERT (StoreSettingID, StoreName, StoreNameEnglish, StoreDescription, LogoPath, LogoData, PrimaryPhone, SecondaryPhone, FaxNumber, Email, Website, Address, City, State, PostalCode, Country, CommercialRegistrationNumber, LicenseNumber, TaxRegistrationNumber, BusinessType, EstablishedDate, WorkingHours, AdditionalInfo, FacebookUrl, TwitterUrl, InstagramUrl, WhatsAppNumber, TelegramUrl)
                        VALUES (1, @StoreName, @StoreNameEnglish, @StoreDescription, @LogoPath, @LogoData, @PrimaryPhone, @SecondaryPhone, @FaxNumber, @Email, @Website, @Address, @City, @State, @PostalCode, @Country, @CommercialRegistrationNumber, @LicenseNumber, @TaxRegistrationNumber, @BusinessType, @EstablishedDate, @WorkingHours, @AdditionalInfo, @FacebookUrl, @TwitterUrl, @InstagramUrl, @WhatsAppNumber, @TelegramUrl);", connection);

                command.Parameters.AddWithValue("@StoreName", storeSettings.StoreName);
                command.Parameters.AddWithValue("@StoreNameEnglish", storeSettings.StoreNameEnglish ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@StoreDescription", storeSettings.StoreDescription ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@LogoPath", storeSettings.LogoPath ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@LogoData", storeSettings.LogoData ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PrimaryPhone", storeSettings.PrimaryPhone ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@SecondaryPhone", storeSettings.SecondaryPhone ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@FaxNumber", storeSettings.FaxNumber ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Email", storeSettings.Email ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Website", storeSettings.Website ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Address", storeSettings.Address ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@City", storeSettings.City ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@State", storeSettings.State ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PostalCode", storeSettings.PostalCode ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Country", storeSettings.Country ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@CommercialRegistrationNumber", storeSettings.CommercialRegistrationNumber ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@LicenseNumber", storeSettings.LicenseNumber ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@TaxRegistrationNumber", storeSettings.TaxRegistrationNumber ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@BusinessType", storeSettings.BusinessType ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@EstablishedDate", storeSettings.EstablishedDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@WorkingHours", storeSettings.WorkingHours ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@AdditionalInfo", storeSettings.AdditionalInfo ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@FacebookUrl", storeSettings.FacebookUrl ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@TwitterUrl", storeSettings.TwitterUrl ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@InstagramUrl", storeSettings.InstagramUrl ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@WhatsAppNumber", storeSettings.WhatsAppNumber ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@TelegramUrl", storeSettings.TelegramUrl ?? (object)DBNull.Value);

                connection.Open();
                command.ExecuteNonQuery();

                _logger?.LogInformation("تم حفظ إعدادات المتجر بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ إعدادات المتجر");
                return false;
            }
        }

        #endregion

        #region إعدادات الطباعة

        /// <summary>
        /// الحصول على إعدادات الطباعة
        /// </summary>
        /// <returns>إعدادات الطباعة</returns>
        public PrintSettings GetPrintSettings()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("SELECT * FROM PrintSettings WHERE PrintSettingID = 1", connection);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapPrintSettings(reader);
                }

                // إرجاع إعدادات افتراضية إذا لم توجد
                return new PrintSettings();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إعدادات الطباعة");
                throw;
            }
        }

        /// <summary>
        /// حفظ إعدادات الطباعة
        /// </summary>
        /// <param name="printSettings">إعدادات الطباعة</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SavePrintSettings(PrintSettings printSettings)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand(@"
                    MERGE PrintSettings AS target
                    USING (SELECT 1 AS PrintSettingID) AS source ON target.PrintSettingID = source.PrintSettingID
                    WHEN MATCHED THEN
                        UPDATE SET
                            DefaultPrintSize = @DefaultPrintSize,
                            DefaultPrinterName = @DefaultPrinterName,
                            AutoPrint = @AutoPrint,
                            DefaultCopies = @DefaultCopies,
                            PaperWidth = @PaperWidth,
                            PaperHeight = @PaperHeight,
                            LeftMargin = @LeftMargin,
                            RightMargin = @RightMargin,
                            TopMargin = @TopMargin,
                            BottomMargin = @BottomMargin,
                            FontName = @FontName,
                            DefaultFontSize = @DefaultFontSize,
                            HeaderFontSize = @HeaderFontSize,
                            DetailsFontSize = @DetailsFontSize,
                            TotalFontSize = @TotalFontSize,
                            BoldHeaders = @BoldHeaders,
                            BoldTotals = @BoldTotals,
                            PrintStoreLogo = @PrintStoreLogo,
                            PrintStoreInfo = @PrintStoreInfo,
                            PrintDateTime = @PrintDateTime,
                            PrintInvoiceNumber = @PrintInvoiceNumber,
                            PrintCustomerInfo = @PrintCustomerInfo,
                            PrintProductDetails = @PrintProductDetails,
                            PrintPrices = @PrintPrices,
                            PrintTax = @PrintTax,
                            PrintDiscount = @PrintDiscount,
                            PrintTotal = @PrintTotal,
                            PrintPaymentMethod = @PrintPaymentMethod,
                            PrintUserName = @PrintUserName,
                            PrintThankYouMessage = @PrintThankYouMessage,
                            CustomThankYouMessage = @CustomThankYouMessage,
                            PrintBarcode = @PrintBarcode,
                            PrintQRCode = @PrintQRCode,
                            HeaderAlignment = @HeaderAlignment,
                            DetailsAlignment = @DetailsAlignment,
                            TotalAlignment = @TotalAlignment,
                            ProductColumnsCount = @ProductColumnsCount,
                            QuantityColumnWidth = @QuantityColumnWidth,
                            PriceColumnWidth = @PriceColumnWidth,
                            TotalColumnWidth = @TotalColumnWidth,
                            LineSpacing = @LineSpacing,
                            SectionSpacing = @SectionSpacing
                    WHEN NOT MATCHED THEN
                        INSERT (PrintSettingID, DefaultPrintSize, DefaultPrinterName, AutoPrint, DefaultCopies, PaperWidth, PaperHeight, LeftMargin, RightMargin, TopMargin, BottomMargin, FontName, DefaultFontSize, HeaderFontSize, DetailsFontSize, TotalFontSize, BoldHeaders, BoldTotals, PrintStoreLogo, PrintStoreInfo, PrintDateTime, PrintInvoiceNumber, PrintCustomerInfo, PrintProductDetails, PrintPrices, PrintTax, PrintDiscount, PrintTotal, PrintPaymentMethod, PrintUserName, PrintThankYouMessage, CustomThankYouMessage, PrintBarcode, PrintQRCode, HeaderAlignment, DetailsAlignment, TotalAlignment, ProductColumnsCount, QuantityColumnWidth, PriceColumnWidth, TotalColumnWidth, LineSpacing, SectionSpacing)
                        VALUES (1, @DefaultPrintSize, @DefaultPrinterName, @AutoPrint, @DefaultCopies, @PaperWidth, @PaperHeight, @LeftMargin, @RightMargin, @TopMargin, @BottomMargin, @FontName, @DefaultFontSize, @HeaderFontSize, @DetailsFontSize, @TotalFontSize, @BoldHeaders, @BoldTotals, @PrintStoreLogo, @PrintStoreInfo, @PrintDateTime, @PrintInvoiceNumber, @PrintCustomerInfo, @PrintProductDetails, @PrintPrices, @PrintTax, @PrintDiscount, @PrintTotal, @PrintPaymentMethod, @PrintUserName, @PrintThankYouMessage, @CustomThankYouMessage, @PrintBarcode, @PrintQRCode, @HeaderAlignment, @DetailsAlignment, @TotalAlignment, @ProductColumnsCount, @QuantityColumnWidth, @PriceColumnWidth, @TotalColumnWidth, @LineSpacing, @SectionSpacing);", connection);

                // إضافة جميع المعاملات
                command.Parameters.AddWithValue("@DefaultPrintSize", printSettings.DefaultPrintSize);
                command.Parameters.AddWithValue("@DefaultPrinterName", printSettings.DefaultPrinterName ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@AutoPrint", printSettings.AutoPrint);
                command.Parameters.AddWithValue("@DefaultCopies", printSettings.DefaultCopies);
                command.Parameters.AddWithValue("@PaperWidth", printSettings.PaperWidth);
                command.Parameters.AddWithValue("@PaperHeight", printSettings.PaperHeight);
                command.Parameters.AddWithValue("@LeftMargin", printSettings.LeftMargin);
                command.Parameters.AddWithValue("@RightMargin", printSettings.RightMargin);
                command.Parameters.AddWithValue("@TopMargin", printSettings.TopMargin);
                command.Parameters.AddWithValue("@BottomMargin", printSettings.BottomMargin);
                command.Parameters.AddWithValue("@FontName", printSettings.FontName);
                command.Parameters.AddWithValue("@DefaultFontSize", printSettings.DefaultFontSize);
                command.Parameters.AddWithValue("@HeaderFontSize", printSettings.HeaderFontSize);
                command.Parameters.AddWithValue("@DetailsFontSize", printSettings.DetailsFontSize);
                command.Parameters.AddWithValue("@TotalFontSize", printSettings.TotalFontSize);
                command.Parameters.AddWithValue("@BoldHeaders", printSettings.BoldHeaders);
                command.Parameters.AddWithValue("@BoldTotals", printSettings.BoldTotals);
                command.Parameters.AddWithValue("@PrintStoreLogo", printSettings.PrintStoreLogo);
                command.Parameters.AddWithValue("@PrintStoreInfo", printSettings.PrintStoreInfo);
                command.Parameters.AddWithValue("@PrintDateTime", printSettings.PrintDateTime);
                command.Parameters.AddWithValue("@PrintInvoiceNumber", printSettings.PrintInvoiceNumber);
                command.Parameters.AddWithValue("@PrintCustomerInfo", printSettings.PrintCustomerInfo);
                command.Parameters.AddWithValue("@PrintProductDetails", printSettings.PrintProductDetails);
                command.Parameters.AddWithValue("@PrintPrices", printSettings.PrintPrices);
                command.Parameters.AddWithValue("@PrintTax", printSettings.PrintTax);
                command.Parameters.AddWithValue("@PrintDiscount", printSettings.PrintDiscount);
                command.Parameters.AddWithValue("@PrintTotal", printSettings.PrintTotal);
                command.Parameters.AddWithValue("@PrintPaymentMethod", printSettings.PrintPaymentMethod);
                command.Parameters.AddWithValue("@PrintUserName", printSettings.PrintUserName);
                command.Parameters.AddWithValue("@PrintThankYouMessage", printSettings.PrintThankYouMessage);
                command.Parameters.AddWithValue("@CustomThankYouMessage", printSettings.CustomThankYouMessage ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PrintBarcode", printSettings.PrintBarcode);
                command.Parameters.AddWithValue("@PrintQRCode", printSettings.PrintQRCode);
                command.Parameters.AddWithValue("@HeaderAlignment", printSettings.HeaderAlignment);
                command.Parameters.AddWithValue("@DetailsAlignment", printSettings.DetailsAlignment);
                command.Parameters.AddWithValue("@TotalAlignment", printSettings.TotalAlignment);
                command.Parameters.AddWithValue("@ProductColumnsCount", printSettings.ProductColumnsCount);
                command.Parameters.AddWithValue("@QuantityColumnWidth", printSettings.QuantityColumnWidth);
                command.Parameters.AddWithValue("@PriceColumnWidth", printSettings.PriceColumnWidth);
                command.Parameters.AddWithValue("@TotalColumnWidth", printSettings.TotalColumnWidth);
                command.Parameters.AddWithValue("@LineSpacing", printSettings.LineSpacing);
                command.Parameters.AddWithValue("@SectionSpacing", printSettings.SectionSpacing);

                connection.Open();
                command.ExecuteNonQuery();

                _logger?.LogInformation("تم حفظ إعدادات الطباعة بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ إعدادات الطباعة");
                return false;
            }
        }

        #endregion

        #region الدوال المساعدة لتحويل البيانات

        /// <summary>
        /// تحويل قارئ البيانات إلى نموذج الإعدادات العامة
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>نموذج الإعدادات العامة</returns>
        private SystemSettings MapSystemSettings(SqlDataReader reader)
        {
            return new SystemSettings
            {
                SettingID = reader.GetInt32("SettingID"),
                SettingName = reader.GetString("SettingName"),
                SettingValue = reader.IsDBNull("SettingValue") ? null : reader.GetString("SettingValue"),
                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                Category = reader.GetString("Category"),
                DataType = reader.GetString("DataType"),
                IsRequired = reader.GetBoolean("IsRequired"),
                IsEditable = reader.GetBoolean("IsEditable"),
                LastUpdated = reader.GetDateTime("LastUpdated"),
                UpdatedBy = reader.IsDBNull("UpdatedBy") ? null : reader.GetString("UpdatedBy")
            };
        }

        /// <summary>
        /// تحويل قارئ البيانات إلى نموذج إعدادات الضريبة
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>نموذج إعدادات الضريبة</returns>
        private TaxSettings MapTaxSettings(SqlDataReader reader)
        {
            return new TaxSettings
            {
                TaxSettingID = reader.GetInt32("TaxSettingID"),
                IsTaxEnabled = reader.GetBoolean("IsTaxEnabled"),
                DefaultTaxRate = reader.GetDecimal("DefaultTaxRate"),
                TaxName = reader.GetString("TaxName"),
                TaxRegistrationNumber = reader.IsDBNull("TaxRegistrationNumber") ? null : reader.GetString("TaxRegistrationNumber"),
                TaxCalculationMethod = reader.GetString("TaxCalculationMethod"),
                ApplyToAllProducts = reader.GetBoolean("ApplyToAllProducts"),
                MinimumTaxableAmount = reader.GetDecimal("MinimumTaxableAmount"),
                TaxDecimalPlaces = reader.GetInt32("TaxDecimalPlaces"),
                RoundingMethod = reader.GetString("RoundingMethod")
            };
        }

        /// <summary>
        /// تحويل قارئ البيانات إلى نموذج معدل الضريبة
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>نموذج معدل الضريبة</returns>
        private TaxRate MapTaxRate(SqlDataReader reader)
        {
            return new TaxRate
            {
                TaxRateID = reader.GetInt32("TaxRateID"),
                Name = reader.GetString("Name"),
                Rate = reader.GetDecimal("Rate"),
                ProductID = reader.IsDBNull("ProductID") ? null : reader.GetInt32("ProductID"),
                CategoryID = reader.IsDBNull("CategoryID") ? null : reader.GetInt32("CategoryID"),
                IsActive = reader.GetBoolean("IsActive"),
                CreatedDate = reader.GetDateTime("CreatedDate")
            };
        }

        /// <summary>
        /// تحويل قارئ البيانات إلى نموذج إعدادات العملة
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>نموذج إعدادات العملة</returns>
        private CurrencySettings MapCurrencySettings(SqlDataReader reader)
        {
            return new CurrencySettings
            {
                CurrencySettingID = reader.GetInt32("CurrencySettingID"),
                DefaultCurrencyCode = reader.GetString("DefaultCurrencyCode"),
                DecimalPlaces = reader.GetInt32("DecimalPlaces"),
                CurrencySymbol = reader.GetString("CurrencySymbol"),
                SymbolPosition = reader.GetString("SymbolPosition"),
                ThousandsSeparator = reader.GetString("ThousandsSeparator"),
                DecimalSeparator = reader.GetString("DecimalSeparator"),
                SupportMultipleCurrencies = reader.GetBoolean("SupportMultipleCurrencies"),
                AutoUpdateExchangeRates = reader.GetBoolean("AutoUpdateExchangeRates"),
                ExchangeRateSource = reader.GetString("ExchangeRateSource"),
                LastExchangeRateUpdate = reader.IsDBNull("LastExchangeRateUpdate") ? null : reader.GetDateTime("LastExchangeRateUpdate")
            };
        }

        /// <summary>
        /// تحويل قارئ البيانات إلى نموذج العملة
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>نموذج العملة</returns>
        private Currency MapCurrency(SqlDataReader reader)
        {
            return new Currency
            {
                CurrencyID = reader.GetInt32("CurrencyID"),
                Code = reader.GetString("Code"),
                Name = reader.GetString("Name"),
                NameArabic = reader.IsDBNull("NameArabic") ? null : reader.GetString("NameArabic"),
                Symbol = reader.GetString("Symbol"),
                DecimalPlaces = reader.GetInt32("DecimalPlaces"),
                SymbolPosition = reader.GetString("SymbolPosition"),
                IsActive = reader.GetBoolean("IsActive"),
                DisplayOrder = reader.GetInt32("DisplayOrder"),
                CreatedDate = reader.GetDateTime("CreatedDate")
            };
        }

        /// <summary>
        /// تحويل قارئ البيانات إلى نموذج سعر الصرف
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>نموذج سعر الصرف</returns>
        private ExchangeRate MapExchangeRate(SqlDataReader reader)
        {
            return new ExchangeRate
            {
                ExchangeRateID = reader.GetInt32("ExchangeRateID"),
                FromCurrency = reader.GetString("FromCurrency"),
                ToCurrency = reader.GetString("ToCurrency"),
                Rate = reader.GetDecimal("Rate"),
                RateDate = reader.GetDateTime("RateDate"),
                IsActive = reader.GetBoolean("IsActive"),
                Source = reader.GetString("Source"),
                LastUpdated = reader.GetDateTime("LastUpdated"),
                UpdatedBy = reader.IsDBNull("UpdatedBy") ? null : reader.GetString("UpdatedBy"),
                Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes")
            };
        }

        /// <summary>
        /// تحويل قارئ البيانات إلى نموذج إعدادات المتجر
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>نموذج إعدادات المتجر</returns>
        private StoreSettings MapStoreSettings(SqlDataReader reader)
        {
            return new StoreSettings
            {
                StoreSettingID = reader.GetInt32("StoreSettingID"),
                StoreName = reader.GetString("StoreName"),
                StoreNameEnglish = reader.IsDBNull("StoreNameEnglish") ? null : reader.GetString("StoreNameEnglish"),
                StoreDescription = reader.IsDBNull("StoreDescription") ? null : reader.GetString("StoreDescription"),
                LogoPath = reader.IsDBNull("LogoPath") ? null : reader.GetString("LogoPath"),
                LogoData = reader.IsDBNull("LogoData") ? null : (byte[])reader["LogoData"],
                PrimaryPhone = reader.IsDBNull("PrimaryPhone") ? null : reader.GetString("PrimaryPhone"),
                SecondaryPhone = reader.IsDBNull("SecondaryPhone") ? null : reader.GetString("SecondaryPhone"),
                FaxNumber = reader.IsDBNull("FaxNumber") ? null : reader.GetString("FaxNumber"),
                Email = reader.IsDBNull("Email") ? null : reader.GetString("Email"),
                Website = reader.IsDBNull("Website") ? null : reader.GetString("Website"),
                Address = reader.IsDBNull("Address") ? null : reader.GetString("Address"),
                City = reader.IsDBNull("City") ? null : reader.GetString("City"),
                State = reader.IsDBNull("State") ? null : reader.GetString("State"),
                PostalCode = reader.IsDBNull("PostalCode") ? null : reader.GetString("PostalCode"),
                Country = reader.IsDBNull("Country") ? null : reader.GetString("Country"),
                CommercialRegistrationNumber = reader.IsDBNull("CommercialRegistrationNumber") ? null : reader.GetString("CommercialRegistrationNumber"),
                LicenseNumber = reader.IsDBNull("LicenseNumber") ? null : reader.GetString("LicenseNumber"),
                TaxRegistrationNumber = reader.IsDBNull("TaxRegistrationNumber") ? null : reader.GetString("TaxRegistrationNumber"),
                BusinessType = reader.IsDBNull("BusinessType") ? null : reader.GetString("BusinessType"),
                EstablishedDate = reader.IsDBNull("EstablishedDate") ? null : reader.GetDateTime("EstablishedDate"),
                WorkingHours = reader.IsDBNull("WorkingHours") ? null : reader.GetString("WorkingHours"),
                AdditionalInfo = reader.IsDBNull("AdditionalInfo") ? null : reader.GetString("AdditionalInfo"),
                FacebookUrl = reader.IsDBNull("FacebookUrl") ? null : reader.GetString("FacebookUrl"),
                TwitterUrl = reader.IsDBNull("TwitterUrl") ? null : reader.GetString("TwitterUrl"),
                InstagramUrl = reader.IsDBNull("InstagramUrl") ? null : reader.GetString("InstagramUrl"),
                WhatsAppNumber = reader.IsDBNull("WhatsAppNumber") ? null : reader.GetString("WhatsAppNumber"),
                TelegramUrl = reader.IsDBNull("TelegramUrl") ? null : reader.GetString("TelegramUrl")
            };
        }

        /// <summary>
        /// تحويل قارئ البيانات إلى نموذج إعدادات الطباعة
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>نموذج إعدادات الطباعة</returns>
        private PrintSettings MapPrintSettings(SqlDataReader reader)
        {
            return new PrintSettings
            {
                PrintSettingID = reader.GetInt32("PrintSettingID"),
                DefaultPrintSize = reader.GetString("DefaultPrintSize"),
                DefaultPrinterName = reader.IsDBNull("DefaultPrinterName") ? null : reader.GetString("DefaultPrinterName"),
                AutoPrint = reader.GetBoolean("AutoPrint"),
                DefaultCopies = reader.GetInt32("DefaultCopies"),
                PaperWidth = reader.GetInt32("PaperWidth"),
                PaperHeight = reader.GetInt32("PaperHeight"),
                LeftMargin = reader.GetInt32("LeftMargin"),
                RightMargin = reader.GetInt32("RightMargin"),
                TopMargin = reader.GetInt32("TopMargin"),
                BottomMargin = reader.GetInt32("BottomMargin"),
                FontName = reader.GetString("FontName"),
                DefaultFontSize = reader.GetInt32("DefaultFontSize"),
                HeaderFontSize = reader.GetInt32("HeaderFontSize"),
                DetailsFontSize = reader.GetInt32("DetailsFontSize"),
                TotalFontSize = reader.GetInt32("TotalFontSize"),
                BoldHeaders = reader.GetBoolean("BoldHeaders"),
                BoldTotals = reader.GetBoolean("BoldTotals"),
                PrintStoreLogo = reader.GetBoolean("PrintStoreLogo"),
                PrintStoreInfo = reader.GetBoolean("PrintStoreInfo"),
                PrintDateTime = reader.GetBoolean("PrintDateTime"),
                PrintInvoiceNumber = reader.GetBoolean("PrintInvoiceNumber"),
                PrintCustomerInfo = reader.GetBoolean("PrintCustomerInfo"),
                PrintProductDetails = reader.GetBoolean("PrintProductDetails"),
                PrintPrices = reader.GetBoolean("PrintPrices"),
                PrintTax = reader.GetBoolean("PrintTax"),
                PrintDiscount = reader.GetBoolean("PrintDiscount"),
                PrintTotal = reader.GetBoolean("PrintTotal"),
                PrintPaymentMethod = reader.GetBoolean("PrintPaymentMethod"),
                PrintUserName = reader.GetBoolean("PrintUserName"),
                PrintThankYouMessage = reader.GetBoolean("PrintThankYouMessage"),
                CustomThankYouMessage = reader.IsDBNull("CustomThankYouMessage") ? null : reader.GetString("CustomThankYouMessage"),
                PrintBarcode = reader.GetBoolean("PrintBarcode"),
                PrintQRCode = reader.GetBoolean("PrintQRCode"),
                HeaderAlignment = reader.GetString("HeaderAlignment"),
                DetailsAlignment = reader.GetString("DetailsAlignment"),
                TotalAlignment = reader.GetString("TotalAlignment"),
                ProductColumnsCount = reader.GetInt32("ProductColumnsCount"),
                QuantityColumnWidth = reader.GetInt32("QuantityColumnWidth"),
                PriceColumnWidth = reader.GetInt32("PriceColumnWidth"),
                TotalColumnWidth = reader.GetInt32("TotalColumnWidth"),
                LineSpacing = reader.GetInt32("LineSpacing"),
                SectionSpacing = reader.GetInt32("SectionSpacing")
            };
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// تهيئة الإعدادات الافتراضية
        /// </summary>
        /// <param name="updatedBy">من قام بالتهيئة</param>
        /// <returns>عدد الإعدادات المهيأة</returns>
        public int InitializeDefaultSettings(string updatedBy)
        {
            try
            {
                int initializedCount = 0;

                foreach (var defaultSetting in DefaultSettings.Values)
                {
                    var existingSetting = GetSetting(defaultSetting.Key);
                    if (existingSetting == null)
                    {
                        var setting = new SystemSettings
                        {
                            SettingName = defaultSetting.Key,
                            SettingValue = defaultSetting.Value?.ToString(),
                            Category = GetCategoryFromSettingName(defaultSetting.Key),
                            DataType = GetDataTypeFromValue(defaultSetting.Value),
                            IsRequired = true,
                            IsEditable = true,
                            UpdatedBy = updatedBy
                        };

                        SaveSetting(setting);
                        initializedCount++;
                    }
                }

                _logger?.LogInformation($"تم تهيئة {initializedCount} إعداد افتراضي");
                return initializedCount;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تهيئة الإعدادات الافتراضية");
                throw;
            }
        }

        /// <summary>
        /// الحصول على الفئة من اسم الإعداد
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <returns>الفئة</returns>
        private string GetCategoryFromSettingName(string settingName)
        {
            if (settingName.StartsWith("System."))
                return SettingCategories.General;
            if (settingName.StartsWith("Tax."))
                return SettingCategories.Tax;
            if (settingName.StartsWith("Currency."))
                return SettingCategories.Currency;
            if (settingName.StartsWith("Store."))
                return SettingCategories.Store;
            if (settingName.StartsWith("Print."))
                return SettingCategories.Print;

            return SettingCategories.General;
        }

        /// <summary>
        /// الحصول على نوع البيانات من القيمة
        /// </summary>
        /// <param name="value">القيمة</param>
        /// <returns>نوع البيانات</returns>
        private string GetDataTypeFromValue(object value)
        {
            return value switch
            {
                int => SettingDataTypes.Integer,
                decimal => SettingDataTypes.Decimal,
                bool => SettingDataTypes.Boolean,
                DateTime => SettingDataTypes.DateTime,
                _ => SettingDataTypes.String
            };
        }

        /// <summary>
        /// نسخ احتياطي للإعدادات
        /// </summary>
        /// <returns>البيانات المصدرة</returns>
        public string ExportSettings()
        {
            try
            {
                var allSettings = GetAllSettings();
                var taxSettings = GetTaxSettings();
                var currencySettings = GetCurrencySettings();
                var storeSettings = GetStoreSettings();
                var printSettings = GetPrintSettings();

                var exportData = new
                {
                    SystemSettings = allSettings,
                    TaxSettings = taxSettings,
                    CurrencySettings = currencySettings,
                    StoreSettings = storeSettings,
                    PrintSettings = printSettings,
                    ExportDate = DateTime.Now,
                    Version = "1.0"
                };

                return JsonConvert.SerializeObject(exportData, Formatting.Indented);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تصدير الإعدادات");
                throw;
            }
        }

        /// <summary>
        /// استيراد الإعدادات من نسخة احتياطية
        /// </summary>
        /// <param name="jsonData">البيانات المستوردة</param>
        /// <param name="updatedBy">من قام بالاستيراد</param>
        /// <returns>true إذا تم الاستيراد بنجاح</returns>
        public bool ImportSettings(string jsonData, string updatedBy)
        {
            try
            {
                var importData = JsonConvert.DeserializeObject<dynamic>(jsonData);

                // استيراد الإعدادات العامة
                if (importData.SystemSettings != null)
                {
                    var systemSettings = JsonConvert.DeserializeObject<List<SystemSettings>>(importData.SystemSettings.ToString());
                    foreach (var setting in systemSettings)
                    {
                        setting.UpdatedBy = updatedBy;
                        SaveSetting(setting);
                    }
                }

                // استيراد إعدادات الضريبة
                if (importData.TaxSettings != null)
                {
                    var taxSettings = JsonConvert.DeserializeObject<TaxSettings>(importData.TaxSettings.ToString());
                    SaveTaxSettings(taxSettings);
                }

                // استيراد إعدادات العملة
                if (importData.CurrencySettings != null)
                {
                    var currencySettings = JsonConvert.DeserializeObject<CurrencySettings>(importData.CurrencySettings.ToString());
                    SaveCurrencySettings(currencySettings);
                }

                // استيراد إعدادات المتجر
                if (importData.StoreSettings != null)
                {
                    var storeSettings = JsonConvert.DeserializeObject<StoreSettings>(importData.StoreSettings.ToString());
                    SaveStoreSettings(storeSettings);
                }

                // استيراد إعدادات الطباعة
                if (importData.PrintSettings != null)
                {
                    var printSettings = JsonConvert.DeserializeObject<PrintSettings>(importData.PrintSettings.ToString());
                    SavePrintSettings(printSettings);
                }

                _logger?.LogInformation("تم استيراد الإعدادات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في استيراد الإعدادات");
                return false;
            }
        }

        #endregion
    }
}
