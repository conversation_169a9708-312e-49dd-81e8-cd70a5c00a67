using System;
using System.Drawing;
using System.Globalization;
using System.Threading;
using System.Windows.Forms;

namespace AredooCashier
{
    /// <summary>
    /// البرنامج الرئيسي لتطبيق أريدو الكاشير الحديث
    /// </summary>
    internal static class AredooCashierProgram
    {
        /// <summary>
        /// نقطة الدخول الرئيسية للتطبيق
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // تهيئة التطبيق
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // تعيين الثقافة العربية
                SetArabicCulture();

                // تعيين معالج الأخطاء العام
                Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
                Application.ThreadException += OnThreadException;
                AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;

                // عرض شاشة البداية
                ShowSplashScreen();

                // تشغيل التطبيق الرئيسي
                Application.Run(new AredooCashierApp());
            }
            catch (Exception ex)
            {
                ShowErrorMessage("خطأ في بدء التشغيل", ex);
            }
        }

        /// <summary>
        /// تعيين الثقافة العربية
        /// </summary>
        private static void SetArabicCulture()
        {
            try
            {
                var arabicCulture = new CultureInfo("ar-SA");
                Thread.CurrentThread.CurrentCulture = arabicCulture;
                Thread.CurrentThread.CurrentUICulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentCulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentUICulture = arabicCulture;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"تحذير: فشل في تعيين الثقافة العربية - {ex.Message}");
            }
        }

        /// <summary>
        /// عرض شاشة البداية
        /// </summary>
        private static void ShowSplashScreen()
        {
            var splash = new Form
            {
                Text = "أريدو الكاشير",
                Size = new Size(500, 300),
                StartPosition = FormStartPosition.CenterScreen,
                FormBorderStyle = FormBorderStyle.None,
                BackColor = Color.FromArgb(0, 120, 215),
                ShowInTaskbar = false,
                TopMost = true
            };

            var logoLabel = new Label
            {
                Text = "🏪",
                Font = new Font("Segoe UI Emoji", 48),
                ForeColor = Color.White,
                Size = new Size(100, 80),
                Location = new Point(200, 50),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var titleLabel = new Label
            {
                Text = "أريدو الكاشير",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 40),
                Location = new Point(50, 140),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            var subtitleLabel = new Label
            {
                Text = "نظام نقاط البيع الحديث",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(200, 255, 255, 255),
                Size = new Size(400, 30),
                Location = new Point(50, 180),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            var loadingLabel = new Label
            {
                Text = "جاري التحميل...",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(180, 255, 255, 255),
                Size = new Size(400, 20),
                Location = new Point(50, 220),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            splash.Controls.AddRange(new Control[] { logoLabel, titleLabel, subtitleLabel, loadingLabel });

            // عرض الشاشة لمدة ثانيتين
            splash.Show();
            Application.DoEvents();
            System.Threading.Thread.Sleep(2000);
            splash.Close();
        }

        /// <summary>
        /// معالج أخطاء الخيوط
        /// </summary>
        private static void OnThreadException(object sender, ThreadExceptionEventArgs e)
        {
            ShowErrorMessage("خطأ في التطبيق", e.Exception);
        }

        /// <summary>
        /// معالج الأخطاء غير المعالجة
        /// </summary>
        private static void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                ShowErrorMessage("خطأ حرج", ex);
            }
        }

        /// <summary>
        /// عرض رسالة خطأ
        /// </summary>
        private static void ShowErrorMessage(string title, Exception ex)
        {
            var message = $"❌ حدث خطأ في تطبيق أريدو الكاشير:\n\n{ex.Message}";
            
            if (ex.InnerException != null)
            {
                message += $"\n\n🔍 تفاصيل إضافية:\n{ex.InnerException.Message}";
            }

            message += "\n\n📞 يرجى الاتصال بالدعم الفني إذا استمر هذا الخطأ:\n" +
                      "الهاتف: +966 XX XXX XXXX\n" +
                      "البريد: <EMAIL>";

            MessageBox.Show(message, $"خطأ - {title}", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
