using System;
using System.Collections.Generic;
using System.Linq;
using AredooPOS.Models;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.BLL
{
    /// <summary>
    /// طبقة منطق الأعمال لمعالجة المدفوعات
    /// تحتوي على جميع العمليات المتعلقة بمعالجة المدفوعات والمعاملات النقدية
    /// </summary>
    public class PaymentProcessingBLL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly CashTransactionDAL _transactionDAL;
        private readonly CashSessionDAL _sessionDAL;
        private readonly CashRegisterDAL _registerDAL;
        private readonly PaymentMethodDAL _paymentMethodDAL;
        private readonly ILogger<PaymentProcessingBLL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة منطق الأعمال لمعالجة المدفوعات
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public PaymentProcessingBLL(string connectionString = null, ILogger<PaymentProcessingBLL> logger = null)
        {
            _transactionDAL = new CashTransactionDAL(connectionString, null);
            _sessionDAL = new CashSessionDAL(connectionString, null);
            _registerDAL = new CashRegisterDAL(connectionString, null);
            _paymentMethodDAL = new PaymentMethodDAL(connectionString, null);
            _logger = logger;
        }

        #endregion

        #region معالجة المدفوعات

        /// <summary>
        /// معالجة دفعة واحدة
        /// </summary>
        /// <param name="paymentRequest">طلب الدفع</param>
        /// <returns>نتيجة معالجة الدفع</returns>
        public PaymentResult ProcessPayment(PaymentRequest paymentRequest)
        {
            try
            {
                // التحقق من صحة طلب الدفع
                var validationResult = ValidatePaymentRequest(paymentRequest);
                if (!validationResult.IsValid)
                {
                    return new PaymentResult
                    {
                        IsSuccessful = false,
                        ErrorMessage = validationResult.ErrorMessage
                    };
                }

                // الحصول على طريقة الدفع
                var paymentMethod = _paymentMethodDAL.GetPaymentMethodById(paymentRequest.PaymentMethodID);
                if (paymentMethod == null || !paymentMethod.IsActive)
                {
                    return new PaymentResult
                    {
                        IsSuccessful = false,
                        ErrorMessage = "طريقة الدفع غير صحيحة أو غير نشطة"
                    };
                }

                // التحقق من صحة المبلغ لطريقة الدفع
                if (!paymentMethod.IsAmountValid(paymentRequest.Amount))
                {
                    return new PaymentResult
                    {
                        IsSuccessful = false,
                        ErrorMessage = $"المبلغ غير صحيح لطريقة الدفع {paymentMethod.DisplayName}"
                    };
                }

                // حساب رسوم المعالجة
                var processingFee = paymentMethod.CalculateProcessingFee(paymentRequest.Amount);
                var totalAmount = paymentRequest.Amount + processingFee;

                // إنشاء المعاملة النقدية
                var transaction = new CashTransaction
                {
                    SessionID = paymentRequest.SessionID,
                    TransactionType = paymentRequest.TransactionType,
                    Amount = paymentRequest.Amount,
                    PaymentMethodID = paymentRequest.PaymentMethodID,
                    ReferenceNumber = paymentRequest.ReferenceNumber,
                    Description = paymentRequest.Description,
                    Category = paymentRequest.Category,
                    RelatedDocumentType = paymentRequest.RelatedDocumentType,
                    RelatedDocumentID = paymentRequest.RelatedDocumentID,
                    CustomerID = paymentRequest.CustomerID,
                    SupplierID = paymentRequest.SupplierID,
                    UserID = paymentRequest.UserID,
                    CreatedBy = paymentRequest.ProcessedBy,
                    Notes = paymentRequest.Notes
                };

                // إضافة المعاملة
                var transactionId = _transactionDAL.AddCashTransaction(transaction);

                // إضافة رسوم المعالجة إذا كانت موجودة
                if (processingFee > 0)
                {
                    var feeTransaction = new CashTransaction
                    {
                        SessionID = paymentRequest.SessionID,
                        TransactionType = CashTransactionTypes.Expense,
                        Amount = processingFee,
                        PaymentMethodID = paymentRequest.PaymentMethodID,
                        Description = $"رسوم معالجة - {paymentMethod.DisplayName}",
                        Category = "رسوم",
                        RelatedDocumentType = paymentRequest.RelatedDocumentType,
                        RelatedDocumentID = paymentRequest.RelatedDocumentID,
                        UserID = paymentRequest.UserID,
                        CreatedBy = paymentRequest.ProcessedBy
                    };

                    _transactionDAL.AddCashTransaction(feeTransaction);
                }

                _logger?.LogInformation($"تم معالجة دفعة بمبلغ {paymentRequest.Amount:C} بطريقة {paymentMethod.DisplayName}");

                return new PaymentResult
                {
                    IsSuccessful = true,
                    TransactionID = transactionId,
                    ProcessedAmount = paymentRequest.Amount,
                    ProcessingFee = processingFee,
                    TotalAmount = totalAmount,
                    PaymentMethod = paymentMethod,
                    ProcessedDate = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة الدفع");
                return new PaymentResult
                {
                    IsSuccessful = false,
                    ErrorMessage = "حدث خطأ في معالجة الدفع"
                };
            }
        }

        /// <summary>
        /// معالجة دفع متعدد الطرق
        /// </summary>
        /// <param name="multiPaymentRequest">طلب الدفع المتعدد</param>
        /// <returns>نتيجة معالجة الدفع المتعدد</returns>
        public MultiPaymentResult ProcessMultiPayment(MultiPaymentRequest multiPaymentRequest)
        {
            try
            {
                // التحقق من صحة طلب الدفع المتعدد
                var validationResult = ValidateMultiPaymentRequest(multiPaymentRequest);
                if (!validationResult.IsValid)
                {
                    return new MultiPaymentResult
                    {
                        IsSuccessful = false,
                        ErrorMessage = validationResult.ErrorMessage
                    };
                }

                var results = new List<PaymentResult>();
                var totalProcessed = 0m;
                var totalFees = 0m;

                // معالجة كل دفعة على حدة
                foreach (var payment in multiPaymentRequest.Payments)
                {
                    var paymentRequest = new PaymentRequest
                    {
                        SessionID = multiPaymentRequest.SessionID,
                        TransactionType = multiPaymentRequest.TransactionType,
                        Amount = payment.Amount,
                        PaymentMethodID = payment.PaymentMethodID,
                        ReferenceNumber = payment.ReferenceNumber,
                        Description = multiPaymentRequest.Description,
                        Category = multiPaymentRequest.Category,
                        RelatedDocumentType = multiPaymentRequest.RelatedDocumentType,
                        RelatedDocumentID = multiPaymentRequest.RelatedDocumentID,
                        CustomerID = multiPaymentRequest.CustomerID,
                        SupplierID = multiPaymentRequest.SupplierID,
                        UserID = multiPaymentRequest.UserID,
                        ProcessedBy = multiPaymentRequest.ProcessedBy,
                        Notes = payment.Notes
                    };

                    var result = ProcessPayment(paymentRequest);
                    results.Add(result);

                    if (result.IsSuccessful)
                    {
                        totalProcessed += result.ProcessedAmount;
                        totalFees += result.ProcessingFee;
                    }
                    else
                    {
                        // في حالة فشل إحدى الدفعات، يجب إلغاء جميع المعاملات السابقة
                        foreach (var successfulResult in results.Where(r => r.IsSuccessful))
                        {
                            _transactionDAL.VoidCashTransaction(successfulResult.TransactionID, 
                                "إلغاء بسبب فشل في دفعة أخرى", multiPaymentRequest.ProcessedBy);
                        }

                        return new MultiPaymentResult
                        {
                            IsSuccessful = false,
                            ErrorMessage = $"فشل في معالجة إحدى الدفعات: {result.ErrorMessage}",
                            PaymentResults = results
                        };
                    }
                }

                _logger?.LogInformation($"تم معالجة دفع متعدد بإجمالي {totalProcessed:C} من {results.Count} دفعة");

                return new MultiPaymentResult
                {
                    IsSuccessful = true,
                    TotalProcessedAmount = totalProcessed,
                    TotalProcessingFees = totalFees,
                    TotalAmount = totalProcessed + totalFees,
                    PaymentResults = results,
                    ProcessedDate = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة الدفع المتعدد");
                return new MultiPaymentResult
                {
                    IsSuccessful = false,
                    ErrorMessage = "حدث خطأ في معالجة الدفع المتعدد"
                };
            }
        }

        /// <summary>
        /// إلغاء دفعة
        /// </summary>
        /// <param name="transactionID">رقم المعاملة</param>
        /// <param name="reason">سبب الإلغاء</param>
        /// <param name="voidedBy">من ألغى الدفعة</param>
        /// <returns>true إذا تم الإلغاء بنجاح</returns>
        public bool VoidPayment(int transactionID, string reason, string voidedBy)
        {
            try
            {
                var transaction = _transactionDAL.GetCashTransactionById(transactionID);
                if (transaction == null)
                {
                    throw new InvalidOperationException("المعاملة غير موجودة");
                }

                if (transaction.IsVoided)
                {
                    throw new InvalidOperationException("المعاملة ملغاة مسبقاً");
                }

                if (!transaction.CanBeVoided)
                {
                    throw new InvalidOperationException("لا يمكن إلغاء هذه المعاملة");
                }

                var success = _transactionDAL.VoidCashTransaction(transactionID, reason, voidedBy);

                if (success)
                {
                    _logger?.LogInformation($"تم إلغاء المعاملة {transactionID}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إلغاء المعاملة {transactionID}");
                throw;
            }
        }

        #endregion

        #region المعاملات النقدية

        /// <summary>
        /// إضافة مصروف نقدي
        /// </summary>
        /// <param name="expenseRequest">طلب المصروف</param>
        /// <returns>رقم المعاملة</returns>
        public int AddCashExpense(ExpenseRequest expenseRequest)
        {
            try
            {
                // التحقق من صحة طلب المصروف
                ValidateExpenseRequest(expenseRequest);

                var transaction = new CashTransaction
                {
                    SessionID = expenseRequest.SessionID,
                    TransactionType = CashTransactionTypes.Expense,
                    Amount = expenseRequest.Amount,
                    PaymentMethodID = expenseRequest.PaymentMethodID,
                    ReferenceNumber = expenseRequest.ReferenceNumber,
                    Description = expenseRequest.Description,
                    Category = expenseRequest.Category,
                    SupplierID = expenseRequest.SupplierID,
                    UserID = expenseRequest.UserID,
                    CreatedBy = expenseRequest.ProcessedBy,
                    Notes = expenseRequest.Notes
                };

                var transactionId = _transactionDAL.AddCashTransaction(transaction);

                _logger?.LogInformation($"تم إضافة مصروف نقدي بمبلغ {expenseRequest.Amount:C}");
                return transactionId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إضافة المصروف النقدي");
                throw;
            }
        }

        /// <summary>
        /// إضافة سحب نقدي
        /// </summary>
        /// <param name="withdrawalRequest">طلب السحب</param>
        /// <returns>رقم المعاملة</returns>
        public int AddCashWithdrawal(WithdrawalRequest withdrawalRequest)
        {
            try
            {
                // التحقق من صحة طلب السحب
                ValidateWithdrawalRequest(withdrawalRequest);

                var transaction = new CashTransaction
                {
                    SessionID = withdrawalRequest.SessionID,
                    TransactionType = CashTransactionTypes.Withdrawal,
                    Amount = withdrawalRequest.Amount,
                    PaymentMethodID = withdrawalRequest.PaymentMethodID,
                    ReferenceNumber = withdrawalRequest.ReferenceNumber,
                    Description = withdrawalRequest.Description,
                    Category = withdrawalRequest.Category,
                    UserID = withdrawalRequest.UserID,
                    CreatedBy = withdrawalRequest.ProcessedBy,
                    Notes = withdrawalRequest.Notes,
                    RequiresApproval = withdrawalRequest.RequiresApproval
                };

                var transactionId = _transactionDAL.AddCashTransaction(transaction);

                _logger?.LogInformation($"تم إضافة سحب نقدي بمبلغ {withdrawalRequest.Amount:C}");
                return transactionId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إضافة السحب النقدي");
                throw;
            }
        }

        /// <summary>
        /// إضافة إيداع نقدي
        /// </summary>
        /// <param name="depositRequest">طلب الإيداع</param>
        /// <returns>رقم المعاملة</returns>
        public int AddCashDeposit(DepositRequest depositRequest)
        {
            try
            {
                // التحقق من صحة طلب الإيداع
                ValidateDepositRequest(depositRequest);

                var transaction = new CashTransaction
                {
                    SessionID = depositRequest.SessionID,
                    TransactionType = CashTransactionTypes.Deposit,
                    Amount = depositRequest.Amount,
                    PaymentMethodID = depositRequest.PaymentMethodID,
                    ReferenceNumber = depositRequest.ReferenceNumber,
                    Description = depositRequest.Description,
                    Category = depositRequest.Category,
                    UserID = depositRequest.UserID,
                    CreatedBy = depositRequest.ProcessedBy,
                    Notes = depositRequest.Notes
                };

                var transactionId = _transactionDAL.AddCashTransaction(transaction);

                _logger?.LogInformation($"تم إضافة إيداع نقدي بمبلغ {depositRequest.Amount:C}");
                return transactionId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إضافة الإيداع النقدي");
                throw;
            }
        }

        #endregion

        #region التحقق من الصحة

        /// <summary>
        /// التحقق من صحة طلب الدفع
        /// </summary>
        /// <param name="request">طلب الدفع</param>
        /// <returns>نتيجة التحقق</returns>
        private ValidationResult ValidatePaymentRequest(PaymentRequest request)
        {
            if (request == null)
                return new ValidationResult { IsValid = false, ErrorMessage = "طلب الدفع مطلوب" };

            if (request.SessionID <= 0)
                return new ValidationResult { IsValid = false, ErrorMessage = "رقم الجلسة غير صحيح" };

            if (request.Amount <= 0)
                return new ValidationResult { IsValid = false, ErrorMessage = "مبلغ الدفع يجب أن يكون أكبر من صفر" };

            if (request.PaymentMethodID <= 0)
                return new ValidationResult { IsValid = false, ErrorMessage = "طريقة الدفع غير صحيحة" };

            if (request.UserID <= 0)
                return new ValidationResult { IsValid = false, ErrorMessage = "رقم المستخدم غير صحيح" };

            if (string.IsNullOrWhiteSpace(request.ProcessedBy))
                return new ValidationResult { IsValid = false, ErrorMessage = "معالج الدفع مطلوب" };

            // التحقق من وجود الجلسة وأنها مفتوحة
            var session = _sessionDAL.GetCashSessionById(request.SessionID);
            if (session == null)
                return new ValidationResult { IsValid = false, ErrorMessage = "الجلسة غير موجودة" };

            if (!session.IsOpen)
                return new ValidationResult { IsValid = false, ErrorMessage = "الجلسة غير مفتوحة" };

            return new ValidationResult { IsValid = true };
        }

        /// <summary>
        /// التحقق من صحة طلب الدفع المتعدد
        /// </summary>
        /// <param name="request">طلب الدفع المتعدد</param>
        /// <returns>نتيجة التحقق</returns>
        private ValidationResult ValidateMultiPaymentRequest(MultiPaymentRequest request)
        {
            if (request == null)
                return new ValidationResult { IsValid = false, ErrorMessage = "طلب الدفع المتعدد مطلوب" };

            if (request.Payments == null || !request.Payments.Any())
                return new ValidationResult { IsValid = false, ErrorMessage = "يجب تحديد طرق دفع واحدة على الأقل" };

            if (request.Payments.Any(p => p.Amount <= 0))
                return new ValidationResult { IsValid = false, ErrorMessage = "جميع مبالغ الدفع يجب أن تكون أكبر من صفر" };

            var totalAmount = request.Payments.Sum(p => p.Amount);
            if (totalAmount <= 0)
                return new ValidationResult { IsValid = false, ErrorMessage = "إجمالي مبلغ الدفع يجب أن يكون أكبر من صفر" };

            return new ValidationResult { IsValid = true };
        }

        /// <summary>
        /// التحقق من صحة طلب المصروف
        /// </summary>
        /// <param name="request">طلب المصروف</param>
        private void ValidateExpenseRequest(ExpenseRequest request)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (request.Amount <= 0)
                throw new ArgumentException("مبلغ المصروف يجب أن يكون أكبر من صفر");

            if (string.IsNullOrWhiteSpace(request.Description))
                throw new ArgumentException("وصف المصروف مطلوب");
        }

        /// <summary>
        /// التحقق من صحة طلب السحب
        /// </summary>
        /// <param name="request">طلب السحب</param>
        private void ValidateWithdrawalRequest(WithdrawalRequest request)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (request.Amount <= 0)
                throw new ArgumentException("مبلغ السحب يجب أن يكون أكبر من صفر");

            if (string.IsNullOrWhiteSpace(request.Description))
                throw new ArgumentException("وصف السحب مطلوب");
        }

        /// <summary>
        /// التحقق من صحة طلب الإيداع
        /// </summary>
        /// <param name="request">طلب الإيداع</param>
        private void ValidateDepositRequest(DepositRequest request)
        {
            if (request == null)
                throw new ArgumentNullException(nameof(request));

            if (request.Amount <= 0)
                throw new ArgumentException("مبلغ الإيداع يجب أن يكون أكبر من صفر");

            if (string.IsNullOrWhiteSpace(request.Description))
                throw new ArgumentException("وصف الإيداع مطلوب");
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// طلب الدفع
    /// </summary>
    public class PaymentRequest
    {
        public int SessionID { get; set; }
        public string TransactionType { get; set; } = CashTransactionTypes.Sale;
        public decimal Amount { get; set; }
        public int PaymentMethodID { get; set; }
        public string ReferenceNumber { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public string RelatedDocumentType { get; set; }
        public int? RelatedDocumentID { get; set; }
        public int? CustomerID { get; set; }
        public int? SupplierID { get; set; }
        public int UserID { get; set; }
        public string ProcessedBy { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// طلب الدفع المتعدد
    /// </summary>
    public class MultiPaymentRequest
    {
        public int SessionID { get; set; }
        public string TransactionType { get; set; } = CashTransactionTypes.Sale;
        public string Description { get; set; }
        public string Category { get; set; }
        public string RelatedDocumentType { get; set; }
        public int? RelatedDocumentID { get; set; }
        public int? CustomerID { get; set; }
        public int? SupplierID { get; set; }
        public int UserID { get; set; }
        public string ProcessedBy { get; set; }
        public List<PaymentDetail> Payments { get; set; } = new List<PaymentDetail>();
    }

    /// <summary>
    /// تفاصيل الدفع
    /// </summary>
    public class PaymentDetail
    {
        public decimal Amount { get; set; }
        public int PaymentMethodID { get; set; }
        public string ReferenceNumber { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// نتيجة معالجة الدفع
    /// </summary>
    public class PaymentResult
    {
        public bool IsSuccessful { get; set; }
        public string ErrorMessage { get; set; }
        public int TransactionID { get; set; }
        public decimal ProcessedAmount { get; set; }
        public decimal ProcessingFee { get; set; }
        public decimal TotalAmount { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public DateTime ProcessedDate { get; set; }
    }

    /// <summary>
    /// نتيجة معالجة الدفع المتعدد
    /// </summary>
    public class MultiPaymentResult
    {
        public bool IsSuccessful { get; set; }
        public string ErrorMessage { get; set; }
        public decimal TotalProcessedAmount { get; set; }
        public decimal TotalProcessingFees { get; set; }
        public decimal TotalAmount { get; set; }
        public List<PaymentResult> PaymentResults { get; set; } = new List<PaymentResult>();
        public DateTime ProcessedDate { get; set; }
    }

    /// <summary>
    /// طلب المصروف
    /// </summary>
    public class ExpenseRequest
    {
        public int SessionID { get; set; }
        public decimal Amount { get; set; }
        public int PaymentMethodID { get; set; }
        public string ReferenceNumber { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public int? SupplierID { get; set; }
        public int UserID { get; set; }
        public string ProcessedBy { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// طلب السحب
    /// </summary>
    public class WithdrawalRequest
    {
        public int SessionID { get; set; }
        public decimal Amount { get; set; }
        public int PaymentMethodID { get; set; }
        public string ReferenceNumber { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public int UserID { get; set; }
        public string ProcessedBy { get; set; }
        public string Notes { get; set; }
        public bool RequiresApproval { get; set; } = false;
    }

    /// <summary>
    /// طلب الإيداع
    /// </summary>
    public class DepositRequest
    {
        public int SessionID { get; set; }
        public decimal Amount { get; set; }
        public int PaymentMethodID { get; set; }
        public string ReferenceNumber { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public int UserID { get; set; }
        public string ProcessedBy { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// نتيجة التحقق من الصحة
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
    }

    #endregion
}
