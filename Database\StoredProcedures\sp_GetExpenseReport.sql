-- =============================================
-- إجراء مخزن: sp_GetExpenseReport
-- الوصف: الحصول على تقرير المصاريف الشامل
-- المؤلف: نظام أريدو POS
-- تاريخ الإنشاء: 2025-01-11
-- =============================================

CREATE PROCEDURE [dbo].[sp_GetExpenseReport]
    @FromDate DATE,
    @ToDate DATE,
    @ReportType NVARCHAR(50) = 'Daily',
    @CategoryID INT = NULL,
    @SupplierID INT = NULL,
    @Status NVARCHAR(20) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(4000);
    
    BEGIN TRY
        -- التحقق من صحة المدخلات
        IF @FromDate > @ToDate
        BEGIN
            RAISERROR(N'تاريخ البداية يجب أن يكون أقل من أو يساوي تاريخ النهاية', 16, 1);
            RETURN;
        END
        
        -- الإجماليات العامة
        SELECT 
            ISNULL(SUM(e.Amount), 0) AS TotalExpenses,
            COUNT(e.ExpenseID) AS ExpenseCount,
            ISNULL(SUM(CASE WHEN ec.CategoryType = 'Operational' THEN e.Amount ELSE 0 END), 0) AS OperationalExpenses,
            ISNULL(SUM(CASE WHEN ec.CategoryType = 'Administrative' THEN e.Amount ELSE 0 END), 0) AS AdministrativeExpenses,
            ISNULL(SUM(CASE WHEN ec.CategoryType = 'Marketing' THEN e.Amount ELSE 0 END), 0) AS MarketingExpenses,
            ISNULL(SUM(CASE WHEN ec.CategoryType = 'Maintenance' THEN e.Amount ELSE 0 END), 0) AS MaintenanceExpenses,
            ISNULL(SUM(CASE WHEN ec.CategoryType = 'Utility' THEN e.Amount ELSE 0 END), 0) AS UtilityExpenses,
            ISNULL(SUM(CASE WHEN e.PaymentMethod = 'Cash' THEN e.Amount ELSE 0 END), 0) AS CashExpenses,
            ISNULL(SUM(CASE WHEN e.PaymentMethod = 'Card' THEN e.Amount ELSE 0 END), 0) AS CardExpenses,
            ISNULL(SUM(CASE WHEN e.PaymentMethod = 'Transfer' THEN e.Amount ELSE 0 END), 0) AS TransferExpenses,
            ISNULL(SUM(CASE WHEN e.Status = 'Approved' THEN e.Amount ELSE 0 END), 0) AS ApprovedExpenses,
            ISNULL(SUM(CASE WHEN e.Status = 'Pending' THEN e.Amount ELSE 0 END), 0) AS PendingExpenses,
            ISNULL(SUM(CASE WHEN e.Status = 'Rejected' THEN e.Amount ELSE 0 END), 0) AS RejectedExpenses,
            ISNULL(AVG(e.Amount), 0) AS AverageExpense,
            MIN(e.ExpenseDate) AS FirstExpenseDate,
            MAX(e.ExpenseDate) AS LastExpenseDate
        FROM Expenses e
        LEFT JOIN ExpenseCategories ec ON e.CategoryID = ec.CategoryID
        WHERE e.ExpenseDate BETWEEN @FromDate AND @ToDate
            AND (@CategoryID IS NULL OR e.CategoryID = @CategoryID)
            AND (@SupplierID IS NULL OR e.SupplierID = @SupplierID)
            AND (@Status IS NULL OR e.Status = @Status);
        
        -- تفاصيل الفئات
        SELECT 
            ec.CategoryID,
            ec.CategoryName,
            ec.CategoryCode,
            ec.CategoryType,
            ISNULL(SUM(e.Amount), 0) AS TotalAmount,
            COUNT(e.ExpenseID) AS ExpenseCount,
            ISNULL(AVG(e.Amount), 0) AS AverageAmount,
            ISNULL(SUM(CASE WHEN e.Status = 'Approved' THEN e.Amount ELSE 0 END), 0) AS ApprovedAmount,
            ISNULL(SUM(CASE WHEN e.Status = 'Pending' THEN e.Amount ELSE 0 END), 0) AS PendingAmount,
            ISNULL(SUM(CASE WHEN e.Status = 'Rejected' THEN e.Amount ELSE 0 END), 0) AS RejectedAmount,
            MAX(e.ExpenseDate) AS LastExpenseDate,
            CASE 
                WHEN (SELECT SUM(Amount) FROM Expenses WHERE ExpenseDate BETWEEN @FromDate AND @ToDate) > 0
                THEN (SUM(e.Amount) * 100.0) / (SELECT SUM(Amount) FROM Expenses WHERE ExpenseDate BETWEEN @FromDate AND @ToDate)
                ELSE 0
            END AS Percentage
        FROM ExpenseCategories ec
        LEFT JOIN Expenses e ON ec.CategoryID = e.CategoryID
            AND e.ExpenseDate BETWEEN @FromDate AND @ToDate
            AND (@SupplierID IS NULL OR e.SupplierID = @SupplierID)
            AND (@Status IS NULL OR e.Status = @Status)
        GROUP BY ec.CategoryID, ec.CategoryName, ec.CategoryCode, ec.CategoryType
        HAVING SUM(e.Amount) > 0
        ORDER BY TotalAmount DESC;
        
        -- التفاصيل حسب نوع التقرير
        IF @ReportType = 'Daily'
        BEGIN
            WITH DateRange AS (
                SELECT @FromDate AS ReportDate
                UNION ALL
                SELECT DATEADD(DAY, 1, ReportDate)
                FROM DateRange
                WHERE ReportDate < @ToDate
            )
            SELECT 
                dr.ReportDate AS ExpenseDate,
                ISNULL(SUM(e.Amount), 0) AS TotalExpenses,
                COUNT(e.ExpenseID) AS ExpenseCount,
                ISNULL(AVG(e.Amount), 0) AS AverageExpense,
                ISNULL(SUM(CASE WHEN e.Status = 'Approved' THEN e.Amount ELSE 0 END), 0) AS ApprovedAmount,
                ISNULL(SUM(CASE WHEN e.Status = 'Pending' THEN e.Amount ELSE 0 END), 0) AS PendingAmount,
                ISNULL(SUM(CASE WHEN e.Status = 'Rejected' THEN e.Amount ELSE 0 END), 0) AS RejectedAmount
            FROM DateRange dr
            LEFT JOIN Expenses e ON CAST(e.ExpenseDate AS DATE) = dr.ReportDate
                AND (@CategoryID IS NULL OR e.CategoryID = @CategoryID)
                AND (@SupplierID IS NULL OR e.SupplierID = @SupplierID)
                AND (@Status IS NULL OR e.Status = @Status)
            GROUP BY dr.ReportDate
            ORDER BY dr.ReportDate
            OPTION (MAXRECURSION 366);
        END
        
        ELSE IF @ReportType = 'Weekly'
        BEGIN
            SELECT 
                DATEPART(YEAR, e.ExpenseDate) AS Year,
                DATEPART(WEEK, e.ExpenseDate) AS WeekNumber,
                MIN(e.ExpenseDate) AS WeekStart,
                MAX(e.ExpenseDate) AS WeekEnd,
                ISNULL(SUM(e.Amount), 0) AS TotalExpenses,
                COUNT(e.ExpenseID) AS ExpenseCount,
                ISNULL(AVG(e.Amount), 0) AS AverageExpense
            FROM Expenses e
            WHERE e.ExpenseDate BETWEEN @FromDate AND @ToDate
                AND (@CategoryID IS NULL OR e.CategoryID = @CategoryID)
                AND (@SupplierID IS NULL OR e.SupplierID = @SupplierID)
                AND (@Status IS NULL OR e.Status = @Status)
            GROUP BY DATEPART(YEAR, e.ExpenseDate), DATEPART(WEEK, e.ExpenseDate)
            ORDER BY Year, WeekNumber;
        END
        
        ELSE IF @ReportType = 'Monthly'
        BEGIN
            SELECT 
                DATEPART(YEAR, e.ExpenseDate) AS Year,
                DATEPART(MONTH, e.ExpenseDate) AS Month,
                DATENAME(MONTH, e.ExpenseDate) AS MonthName,
                ISNULL(SUM(e.Amount), 0) AS TotalExpenses,
                COUNT(e.ExpenseID) AS ExpenseCount,
                ISNULL(AVG(e.Amount), 0) AS AverageExpense
            FROM Expenses e
            WHERE e.ExpenseDate BETWEEN @FromDate AND @ToDate
                AND (@CategoryID IS NULL OR e.CategoryID = @CategoryID)
                AND (@SupplierID IS NULL OR e.SupplierID = @SupplierID)
                AND (@Status IS NULL OR e.Status = @Status)
            GROUP BY DATEPART(YEAR, e.ExpenseDate), DATEPART(MONTH, e.ExpenseDate), DATENAME(MONTH, e.ExpenseDate)
            ORDER BY Year, Month;
        END
        
        ELSE IF @ReportType = 'BySupplier'
        BEGIN
            SELECT 
                s.SupplierID,
                s.SupplierName,
                s.SupplierPhone,
                s.SupplierEmail,
                ISNULL(SUM(e.Amount), 0) AS TotalExpenses,
                COUNT(e.ExpenseID) AS ExpenseCount,
                ISNULL(AVG(e.Amount), 0) AS AverageExpense,
                MIN(e.ExpenseDate) AS FirstExpense,
                MAX(e.ExpenseDate) AS LastExpense
            FROM Suppliers s
            LEFT JOIN Expenses e ON s.SupplierID = e.SupplierID
                AND e.ExpenseDate BETWEEN @FromDate AND @ToDate
                AND (@CategoryID IS NULL OR e.CategoryID = @CategoryID)
                AND (@Status IS NULL OR e.Status = @Status)
            GROUP BY s.SupplierID, s.SupplierName, s.SupplierPhone, s.SupplierEmail
            HAVING SUM(e.Amount) > 0
            ORDER BY TotalExpenses DESC;
        END
        
        ELSE IF @ReportType = 'ByUser'
        BEGIN
            SELECT 
                u.UserID,
                u.UserName,
                u.FullName,
                ISNULL(SUM(e.Amount), 0) AS TotalExpenses,
                COUNT(e.ExpenseID) AS ExpenseCount,
                ISNULL(AVG(e.Amount), 0) AS AverageExpense,
                MIN(e.ExpenseDate) AS FirstExpense,
                MAX(e.ExpenseDate) AS LastExpense
            FROM Users u
            LEFT JOIN Expenses e ON u.UserID = e.CreatedBy
                AND e.ExpenseDate BETWEEN @FromDate AND @ToDate
                AND (@CategoryID IS NULL OR e.CategoryID = @CategoryID)
                AND (@SupplierID IS NULL OR e.SupplierID = @SupplierID)
                AND (@Status IS NULL OR e.Status = @Status)
            GROUP BY u.UserID, u.UserName, u.FullName
            HAVING SUM(e.Amount) > 0
            ORDER BY TotalExpenses DESC;
        END
        
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END

GO

-- =============================================
-- إجراء مخزن: sp_GetProfitReport
-- الوصف: الحصول على تقرير الأرباح والخسائر
-- =============================================

CREATE PROCEDURE [dbo].[sp_GetProfitReport]
    @FromDate DATE,
    @ToDate DATE,
    @ReportType NVARCHAR(50) = 'Daily'
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(4000);
    
    BEGIN TRY
        -- الإجماليات العامة
        SELECT 
            -- الإيرادات
            ISNULL(SUM(CASE WHEN i.InvoiceType = 'Sale' THEN i.TotalAmount ELSE 0 END), 0) AS TotalSales,
            ISNULL(SUM(CASE WHEN i.InvoiceType = 'Return' THEN i.TotalAmount ELSE 0 END), 0) AS TotalReturns,
            ISNULL(SUM(i.DiscountAmount), 0) AS TotalDiscounts,
            
            -- التكاليف
            ISNULL(SUM(CASE WHEN i.InvoiceType = 'Sale' THEN id.Quantity * p.CostPrice ELSE 0 END), 0) AS CostOfGoodsSold,
            
            -- المصاريف
            ISNULL((SELECT SUM(Amount) FROM Expenses WHERE ExpenseDate BETWEEN @FromDate AND @ToDate AND Status = 'Approved'), 0) AS TotalExpenses,
            ISNULL((SELECT SUM(Amount) FROM Expenses WHERE ExpenseDate BETWEEN @FromDate AND @ToDate AND Status = 'Approved' AND CategoryID IN (SELECT CategoryID FROM ExpenseCategories WHERE CategoryType = 'Selling')), 0) AS SellingExpenses,
            ISNULL((SELECT SUM(Amount) FROM Expenses WHERE ExpenseDate BETWEEN @FromDate AND @ToDate AND Status = 'Approved' AND CategoryID IN (SELECT CategoryID FROM ExpenseCategories WHERE CategoryType = 'Administrative')), 0) AS AdministrativeExpenses,
            ISNULL((SELECT SUM(Amount) FROM Expenses WHERE ExpenseDate BETWEEN @FromDate AND @ToDate AND Status = 'Approved' AND CategoryID IN (SELECT CategoryID FROM ExpenseCategories WHERE CategoryType = 'Marketing')), 0) AS MarketingExpenses,
            ISNULL((SELECT SUM(Amount) FROM Expenses WHERE ExpenseDate BETWEEN @FromDate AND @ToDate AND Status = 'Approved' AND CategoryID IN (SELECT CategoryID FROM ExpenseCategories WHERE CategoryType = 'Financial')), 0) AS FinancialExpenses,
            ISNULL((SELECT SUM(Amount) FROM Expenses WHERE ExpenseDate BETWEEN @FromDate AND @ToDate AND Status = 'Approved' AND CategoryID NOT IN (SELECT CategoryID FROM ExpenseCategories WHERE CategoryType IN ('Selling', 'Administrative', 'Marketing', 'Financial'))), 0) AS OtherExpenses,
            
            -- الضرائب والإيرادات الأخرى
            ISNULL(SUM(i.TaxAmount), 0) AS Taxes,
            0 AS OtherRevenue -- يمكن إضافة جدول للإيرادات الأخرى لاحقاً
            
        FROM Invoices i
        LEFT JOIN InvoiceDetails id ON i.InvoiceID = id.InvoiceID
        LEFT JOIN Products p ON id.ProductID = p.ProductID
        WHERE i.InvoiceDate BETWEEN @FromDate AND @ToDate
            AND i.IsVoided = 0;
        
        -- تفاصيل الأرباح حسب المنتج
        SELECT 
            p.ProductID,
            p.ProductName,
            p.ProductCode,
            c.CategoryName,
            ISNULL(SUM(CASE WHEN i.InvoiceType = 'Sale' THEN id.TotalPrice ELSE 0 END), 0) AS TotalSales,
            ISNULL(SUM(CASE WHEN i.InvoiceType = 'Sale' THEN id.Quantity * p.CostPrice ELSE 0 END), 0) AS TotalCost,
            ISNULL(SUM(CASE WHEN i.InvoiceType = 'Sale' THEN id.TotalPrice - (id.Quantity * p.CostPrice) ELSE 0 END), 0) AS TotalProfit,
            ISNULL(SUM(id.DiscountAmount), 0) AS TotalDiscount,
            ISNULL(SUM(CASE WHEN i.InvoiceType = 'Sale' THEN id.Quantity ELSE 0 END), 0) AS QuantitySold,
            CASE 
                WHEN SUM(CASE WHEN i.InvoiceType = 'Sale' THEN id.TotalPrice ELSE 0 END) > 0
                THEN (SUM(CASE WHEN i.InvoiceType = 'Sale' THEN id.TotalPrice - (id.Quantity * p.CostPrice) ELSE 0 END) * 100.0) / SUM(CASE WHEN i.InvoiceType = 'Sale' THEN id.TotalPrice ELSE 0 END)
                ELSE 0
            END AS ProfitMargin
        FROM Products p
        LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
        LEFT JOIN InvoiceDetails id ON p.ProductID = id.ProductID
        LEFT JOIN Invoices i ON id.InvoiceID = i.InvoiceID 
            AND i.InvoiceDate BETWEEN @FromDate AND @ToDate
            AND i.IsVoided = 0
        GROUP BY p.ProductID, p.ProductName, p.ProductCode, c.CategoryName, p.CostPrice
        HAVING SUM(CASE WHEN i.InvoiceType = 'Sale' THEN id.Quantity ELSE 0 END) > 0
        ORDER BY TotalProfit DESC;
        
        -- التفاصيل حسب نوع التقرير
        IF @ReportType = 'Daily'
        BEGIN
            WITH DateRange AS (
                SELECT @FromDate AS ReportDate
                UNION ALL
                SELECT DATEADD(DAY, 1, ReportDate)
                FROM DateRange
                WHERE ReportDate < @ToDate
            )
            SELECT 
                dr.ReportDate AS ProfitDate,
                ISNULL(SUM(CASE WHEN i.InvoiceType = 'Sale' THEN i.TotalAmount ELSE 0 END), 0) AS DailySales,
                ISNULL(SUM(CASE WHEN i.InvoiceType = 'Sale' THEN id.Quantity * p.CostPrice ELSE 0 END), 0) AS DailyCost,
                ISNULL(SUM(CASE WHEN i.InvoiceType = 'Sale' THEN i.TotalAmount - (id.Quantity * p.CostPrice) ELSE 0 END), 0) AS GrossProfit,
                ISNULL((SELECT SUM(Amount) FROM Expenses WHERE CAST(ExpenseDate AS DATE) = dr.ReportDate AND Status = 'Approved'), 0) AS DailyExpenses,
                ISNULL(SUM(CASE WHEN i.InvoiceType = 'Sale' THEN i.TotalAmount - (id.Quantity * p.CostPrice) ELSE 0 END), 0) - 
                ISNULL((SELECT SUM(Amount) FROM Expenses WHERE CAST(ExpenseDate AS DATE) = dr.ReportDate AND Status = 'Approved'), 0) AS NetProfit
            FROM DateRange dr
            LEFT JOIN Invoices i ON CAST(i.InvoiceDate AS DATE) = dr.ReportDate AND i.IsVoided = 0
            LEFT JOIN InvoiceDetails id ON i.InvoiceID = id.InvoiceID
            LEFT JOIN Products p ON id.ProductID = p.ProductID
            GROUP BY dr.ReportDate
            ORDER BY dr.ReportDate
            OPTION (MAXRECURSION 366);
        END
        
        ELSE IF @ReportType = 'Monthly'
        BEGIN
            SELECT 
                DATEPART(YEAR, i.InvoiceDate) AS Year,
                DATEPART(MONTH, i.InvoiceDate) AS Month,
                DATENAME(MONTH, i.InvoiceDate) AS MonthName,
                ISNULL(SUM(CASE WHEN i.InvoiceType = 'Sale' THEN i.TotalAmount ELSE 0 END), 0) AS MonthlySales,
                ISNULL(SUM(CASE WHEN i.InvoiceType = 'Sale' THEN id.Quantity * p.CostPrice ELSE 0 END), 0) AS MonthlyCost,
                ISNULL(SUM(CASE WHEN i.InvoiceType = 'Sale' THEN i.TotalAmount - (id.Quantity * p.CostPrice) ELSE 0 END), 0) AS GrossProfit,
                ISNULL((SELECT SUM(Amount) FROM Expenses WHERE DATEPART(YEAR, ExpenseDate) = DATEPART(YEAR, i.InvoiceDate) AND DATEPART(MONTH, ExpenseDate) = DATEPART(MONTH, i.InvoiceDate) AND Status = 'Approved'), 0) AS MonthlyExpenses,
                ISNULL(SUM(CASE WHEN i.InvoiceType = 'Sale' THEN i.TotalAmount - (id.Quantity * p.CostPrice) ELSE 0 END), 0) - 
                ISNULL((SELECT SUM(Amount) FROM Expenses WHERE DATEPART(YEAR, ExpenseDate) = DATEPART(YEAR, i.InvoiceDate) AND DATEPART(MONTH, ExpenseDate) = DATEPART(MONTH, i.InvoiceDate) AND Status = 'Approved'), 0) AS NetProfit
            FROM Invoices i
            LEFT JOIN InvoiceDetails id ON i.InvoiceID = id.InvoiceID
            LEFT JOIN Products p ON id.ProductID = p.ProductID
            WHERE i.InvoiceDate BETWEEN @FromDate AND @ToDate
                AND i.IsVoided = 0
            GROUP BY DATEPART(YEAR, i.InvoiceDate), DATEPART(MONTH, i.InvoiceDate), DATENAME(MONTH, i.InvoiceDate)
            ORDER BY Year, Month;
        END
        
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END

GO
