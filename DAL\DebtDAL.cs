using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using AredooPOS.Models;
using AredooPOS.Core.Database;
using Microsoft.Extensions.Logging;

namespace AredooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات للديون
    /// تحتوي على جميع العمليات المتعلقة بالديون في قاعدة البيانات
    /// </summary>
    public class DebtDAL
    {
        #region المتغيرات الخاصة

        private readonly DatabaseManager _databaseManager;
        private readonly ILogger<DebtDAL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة الوصول للبيانات للديون
        /// </summary>
        /// <param name="databaseManager">مدير قاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public DebtDAL(DatabaseManager databaseManager, ILogger<DebtDAL> logger = null)
        {
            _databaseManager = databaseManager ?? throw new ArgumentNullException(nameof(databaseManager));
            _logger = logger;
        }

        #endregion

        #region عمليات الديون الأساسية

        /// <summary>
        /// إضافة دين جديد
        /// </summary>
        /// <param name="debt">بيانات الدين</param>
        /// <returns>رقم الدين المُضاف</returns>
        public int AddDebt(Debt debt)
        {
            try
            {
                _logger?.LogInformation("بدء إضافة دين جديد للعميل {CustomerName}", debt.CustomerName);

                // التحقق من صحة البيانات
                if (!debt.IsValid())
                {
                    throw new ArgumentException("بيانات الدين غير صحيحة");
                }

                // حساب المبالغ
                debt.CalculateAmounts();

                var query = @"
                    INSERT INTO Debts (
                        DebtNumber, DebtType, DebtDate, DueDate,
                        CustomerID, CustomerName, CustomerPhone,
                        InvoiceID, InvoiceNumber, InstallmentID, ExternalReference,
                        OriginalAmount, PaidAmount, RemainingAmount, LateFees, TotalDueAmount,
                        DebtStatus, Priority, OverdueDays, ReminderCount,
                        Description, Notes, InternalNotes,
                        CreatedBy, CreatedDate, ModifiedBy, ModifiedDate
                    ) VALUES (
                        @DebtNumber, @DebtType, @DebtDate, @DueDate,
                        @CustomerID, @CustomerName, @CustomerPhone,
                        @InvoiceID, @InvoiceNumber, @InstallmentID, @ExternalReference,
                        @OriginalAmount, @PaidAmount, @RemainingAmount, @LateFees, @TotalDueAmount,
                        @DebtStatus, @Priority, @OverdueDays, @ReminderCount,
                        @Description, @Notes, @InternalNotes,
                        @CreatedBy, @CreatedDate, @ModifiedBy, @ModifiedDate
                    )";

                var parameters = new[]
                {
                    new SQLiteParameter("@DebtNumber", debt.DebtNumber),
                    new SQLiteParameter("@DebtType", debt.DebtType),
                    new SQLiteParameter("@DebtDate", debt.DebtDate.ToString("yyyy-MM-dd HH:mm:ss")),
                    new SQLiteParameter("@DueDate", debt.DueDate?.ToString("yyyy-MM-dd HH:mm:ss") ?? (object)DBNull.Value),
                    new SQLiteParameter("@CustomerID", debt.CustomerID),
                    new SQLiteParameter("@CustomerName", debt.CustomerName),
                    new SQLiteParameter("@CustomerPhone", debt.CustomerPhone ?? (object)DBNull.Value),
                    new SQLiteParameter("@InvoiceID", debt.InvoiceID ?? (object)DBNull.Value),
                    new SQLiteParameter("@InvoiceNumber", debt.InvoiceNumber ?? (object)DBNull.Value),
                    new SQLiteParameter("@InstallmentID", debt.InstallmentID ?? (object)DBNull.Value),
                    new SQLiteParameter("@ExternalReference", debt.ExternalReference ?? (object)DBNull.Value),
                    new SQLiteParameter("@OriginalAmount", debt.OriginalAmount),
                    new SQLiteParameter("@PaidAmount", debt.PaidAmount),
                    new SQLiteParameter("@RemainingAmount", debt.RemainingAmount),
                    new SQLiteParameter("@LateFees", debt.LateFees),
                    new SQLiteParameter("@TotalDueAmount", debt.TotalDueAmount),
                    new SQLiteParameter("@DebtStatus", debt.DebtStatus),
                    new SQLiteParameter("@Priority", debt.Priority ?? (object)DBNull.Value),
                    new SQLiteParameter("@OverdueDays", debt.OverdueDays),
                    new SQLiteParameter("@ReminderCount", debt.ReminderCount),
                    new SQLiteParameter("@Description", debt.Description ?? (object)DBNull.Value),
                    new SQLiteParameter("@Notes", debt.Notes ?? (object)DBNull.Value),
                    new SQLiteParameter("@InternalNotes", debt.InternalNotes ?? (object)DBNull.Value),
                    new SQLiteParameter("@CreatedBy", debt.CreatedBy ?? (object)DBNull.Value),
                    new SQLiteParameter("@CreatedDate", debt.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss")),
                    new SQLiteParameter("@ModifiedBy", debt.ModifiedBy ?? (object)DBNull.Value),
                    new SQLiteParameter("@ModifiedDate", debt.ModifiedDate.ToString("yyyy-MM-dd HH:mm:ss"))
                };

                var debtId = (int)_databaseManager.ExecuteInsert(query, parameters);
                debt.DebtID = debtId;

                _logger?.LogInformation("تم إضافة الدين بنجاح. رقم الدين: {DebtID}", debtId);
                return debtId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إضافة الدين للعميل {CustomerName}", debt.CustomerName);
                throw;
            }
        }

        /// <summary>
        /// تحديث بيانات دين موجود
        /// </summary>
        /// <param name="debt">بيانات الدين المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateDebt(Debt debt)
        {
            try
            {
                _logger?.LogInformation("بدء تحديث الدين رقم {DebtID}", debt.DebtID);

                // التحقق من صحة البيانات
                if (!debt.IsValid())
                {
                    throw new ArgumentException("بيانات الدين غير صحيحة");
                }

                // حساب المبالغ
                debt.CalculateAmounts();

                var query = @"
                    UPDATE Debts SET
                        DebtNumber = @DebtNumber,
                        DebtType = @DebtType,
                        DebtDate = @DebtDate,
                        DueDate = @DueDate,
                        CustomerID = @CustomerID,
                        CustomerName = @CustomerName,
                        CustomerPhone = @CustomerPhone,
                        InvoiceID = @InvoiceID,
                        InvoiceNumber = @InvoiceNumber,
                        InstallmentID = @InstallmentID,
                        ExternalReference = @ExternalReference,
                        OriginalAmount = @OriginalAmount,
                        PaidAmount = @PaidAmount,
                        RemainingAmount = @RemainingAmount,
                        LateFees = @LateFees,
                        TotalDueAmount = @TotalDueAmount,
                        DebtStatus = @DebtStatus,
                        Priority = @Priority,
                        OverdueDays = @OverdueDays,
                        ReminderCount = @ReminderCount,
                        Description = @Description,
                        Notes = @Notes,
                        InternalNotes = @InternalNotes,
                        ModifiedBy = @ModifiedBy,
                        ModifiedDate = @ModifiedDate
                    WHERE DebtID = @DebtID";

                var parameters = new[]
                {
                    new SQLiteParameter("@DebtID", debt.DebtID),
                    new SQLiteParameter("@DebtNumber", debt.DebtNumber),
                    new SQLiteParameter("@DebtType", debt.DebtType),
                    new SQLiteParameter("@DebtDate", debt.DebtDate.ToString("yyyy-MM-dd HH:mm:ss")),
                    new SQLiteParameter("@DueDate", debt.DueDate?.ToString("yyyy-MM-dd HH:mm:ss") ?? (object)DBNull.Value),
                    new SQLiteParameter("@CustomerID", debt.CustomerID),
                    new SQLiteParameter("@CustomerName", debt.CustomerName),
                    new SQLiteParameter("@CustomerPhone", debt.CustomerPhone ?? (object)DBNull.Value),
                    new SQLiteParameter("@InvoiceID", debt.InvoiceID ?? (object)DBNull.Value),
                    new SQLiteParameter("@InvoiceNumber", debt.InvoiceNumber ?? (object)DBNull.Value),
                    new SQLiteParameter("@InstallmentID", debt.InstallmentID ?? (object)DBNull.Value),
                    new SQLiteParameter("@ExternalReference", debt.ExternalReference ?? (object)DBNull.Value),
                    new SQLiteParameter("@OriginalAmount", debt.OriginalAmount),
                    new SQLiteParameter("@PaidAmount", debt.PaidAmount),
                    new SQLiteParameter("@RemainingAmount", debt.RemainingAmount),
                    new SQLiteParameter("@LateFees", debt.LateFees),
                    new SQLiteParameter("@TotalDueAmount", debt.TotalDueAmount),
                    new SQLiteParameter("@DebtStatus", debt.DebtStatus),
                    new SQLiteParameter("@Priority", debt.Priority ?? (object)DBNull.Value),
                    new SQLiteParameter("@OverdueDays", debt.OverdueDays),
                    new SQLiteParameter("@ReminderCount", debt.ReminderCount),
                    new SQLiteParameter("@Description", debt.Description ?? (object)DBNull.Value),
                    new SQLiteParameter("@Notes", debt.Notes ?? (object)DBNull.Value),
                    new SQLiteParameter("@InternalNotes", debt.InternalNotes ?? (object)DBNull.Value),
                    new SQLiteParameter("@ModifiedBy", debt.ModifiedBy ?? (object)DBNull.Value),
                    new SQLiteParameter("@ModifiedDate", debt.ModifiedDate.ToString("yyyy-MM-dd HH:mm:ss"))
                };

                var rowsAffected = _databaseManager.ExecuteNonQuery(query, parameters);
                var success = rowsAffected > 0;

                if (success)
                {
                    _logger?.LogInformation("تم تحديث الدين بنجاح. رقم الدين: {DebtID}", debt.DebtID);
                }
                else
                {
                    _logger?.LogWarning("لم يتم العثور على الدين للتحديث. رقم الدين: {DebtID}", debt.DebtID);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحديث الدين رقم {DebtID}", debt.DebtID);
                throw;
            }
        }

        /// <summary>
        /// حذف دين
        /// </summary>
        /// <param name="debtId">رقم الدين</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public bool DeleteDebt(int debtId)
        {
            try
            {
                _logger?.LogInformation("بدء حذف الدين رقم {DebtID}", debtId);

                // التحقق من وجود مدفوعات مرتبطة
                var paymentsQuery = "SELECT COUNT(*) FROM DebtPayments WHERE DebtID = @DebtID";
                var paymentsCount = Convert.ToInt32(_databaseManager.ExecuteScalar(paymentsQuery, 
                    new SQLiteParameter("@DebtID", debtId)));

                if (paymentsCount > 0)
                {
                    throw new InvalidOperationException("لا يمكن حذف الدين لوجود مدفوعات مرتبطة به");
                }

                var query = "DELETE FROM Debts WHERE DebtID = @DebtID";
                var parameters = new[] { new SQLiteParameter("@DebtID", debtId) };

                var rowsAffected = _databaseManager.ExecuteNonQuery(query, parameters);
                var success = rowsAffected > 0;

                if (success)
                {
                    _logger?.LogInformation("تم حذف الدين بنجاح. رقم الدين: {DebtID}", debtId);
                }
                else
                {
                    _logger?.LogWarning("لم يتم العثور على الدين للحذف. رقم الدين: {DebtID}", debtId);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حذف الدين رقم {DebtID}", debtId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على دين بالرقم
        /// </summary>
        /// <param name="debtId">رقم الدين</param>
        /// <returns>بيانات الدين أو null إذا لم يوجد</returns>
        public Debt GetDebtById(int debtId)
        {
            try
            {
                var query = @"
                    SELECT * FROM Debts 
                    WHERE DebtID = @DebtID";

                var parameters = new[] { new SQLiteParameter("@DebtID", debtId) };
                var dataTable = _databaseManager.ExecuteQuery(query, parameters);

                if (dataTable.Rows.Count == 0)
                {
                    return null;
                }

                var debt = MapDataRowToDebt(dataTable.Rows[0]);
                
                // تحميل المدفوعات والتذكيرات
                debt.DebtPayments = GetDebtPayments(debtId);
                debt.DebtReminders = GetDebtReminders(debtId);

                return debt;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على الدين رقم {DebtID}", debtId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على دين برقم الدين
        /// </summary>
        /// <param name="debtNumber">رقم الدين</param>
        /// <returns>بيانات الدين أو null إذا لم يوجد</returns>
        public Debt GetDebtByNumber(string debtNumber)
        {
            try
            {
                var query = @"
                    SELECT * FROM Debts 
                    WHERE DebtNumber = @DebtNumber";

                var parameters = new[] { new SQLiteParameter("@DebtNumber", debtNumber) };
                var dataTable = _databaseManager.ExecuteQuery(query, parameters);

                if (dataTable.Rows.Count == 0)
                {
                    return null;
                }

                return MapDataRowToDebt(dataTable.Rows[0]);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على الدين برقم {DebtNumber}", debtNumber);
                throw;
            }
        }

        #endregion

        #region عمليات البحث والاستعلام

        /// <summary>
        /// البحث في الديون
        /// </summary>
        /// <param name="searchCriteria">معايير البحث</param>
        /// <returns>قائمة الديون المطابقة</returns>
        public List<Debt> SearchDebts(DebtSearchCriteria searchCriteria)
        {
            try
            {
                var query = BuildSearchQuery(searchCriteria);
                var parameters = BuildSearchParameters(searchCriteria);

                var dataTable = _databaseManager.ExecuteQuery(query, parameters.ToArray());
                var debts = new List<Debt>();

                foreach (DataRow row in dataTable.Rows)
                {
                    debts.Add(MapDataRowToDebt(row));
                }

                _logger?.LogInformation("تم العثور على {Count} دين مطابق لمعايير البحث", debts.Count);
                return debts;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في البحث في الديون");
                throw;
            }
        }

        /// <summary>
        /// الحصول على ديون العميل
        /// </summary>
        /// <param name="customerId">رقم العميل</param>
        /// <param name="includeFullyPaid">تضمين الديون المدفوعة بالكامل</param>
        /// <returns>قائمة ديون العميل</returns>
        public List<Debt> GetCustomerDebts(int customerId, bool includeFullyPaid = false)
        {
            try
            {
                var query = @"
                    SELECT * FROM Debts 
                    WHERE CustomerID = @CustomerID";

                if (!includeFullyPaid)
                {
                    query += " AND RemainingAmount > 0";
                }

                query += " ORDER BY DebtDate DESC";

                var parameters = new[] { new SQLiteParameter("@CustomerID", customerId) };
                var dataTable = _databaseManager.ExecuteQuery(query, parameters);
                var debts = new List<Debt>();

                foreach (DataRow row in dataTable.Rows)
                {
                    debts.Add(MapDataRowToDebt(row));
                }

                return debts;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على ديون العميل {CustomerID}", customerId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على الديون المتأخرة
        /// </summary>
        /// <param name="daysOverdue">عدد أيام التأخير الأدنى</param>
        /// <returns>قائمة الديون المتأخرة</returns>
        public List<Debt> GetOverdueDebts(int daysOverdue = 1)
        {
            try
            {
                var query = @"
                    SELECT * FROM Debts 
                    WHERE RemainingAmount > 0 
                    AND DueDate IS NOT NULL 
                    AND DATE(DueDate) < DATE('now', '-' || @DaysOverdue || ' days')
                    ORDER BY DueDate ASC";

                var parameters = new[] { new SQLiteParameter("@DaysOverdue", daysOverdue) };
                var dataTable = _databaseManager.ExecuteQuery(query, parameters);
                var debts = new List<Debt>();

                foreach (DataRow row in dataTable.Rows)
                {
                    var debt = MapDataRowToDebt(row);
                    debt.CalculateAmounts(); // إعادة حساب أيام التأخير
                    debts.Add(debt);
                }

                return debts;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على الديون المتأخرة");
                throw;
            }
        }

        #endregion

        #region عمليات مساعدة

        /// <summary>
        /// تحويل صف البيانات إلى كائن دين
        /// </summary>
        /// <param name="row">صف البيانات</param>
        /// <returns>كائن الدين</returns>
        private Debt MapDataRowToDebt(DataRow row)
        {
            return new Debt
            {
                DebtID = Convert.ToInt32(row["DebtID"]),
                DebtNumber = row["DebtNumber"].ToString(),
                DebtType = row["DebtType"].ToString(),
                DebtDate = Convert.ToDateTime(row["DebtDate"]),
                DueDate = row["DueDate"] == DBNull.Value ? null : Convert.ToDateTime(row["DueDate"]),
                CustomerID = Convert.ToInt32(row["CustomerID"]),
                CustomerName = row["CustomerName"].ToString(),
                CustomerPhone = row["CustomerPhone"]?.ToString(),
                InvoiceID = row["InvoiceID"] == DBNull.Value ? null : Convert.ToInt32(row["InvoiceID"]),
                InvoiceNumber = row["InvoiceNumber"]?.ToString(),
                InstallmentID = row["InstallmentID"] == DBNull.Value ? null : Convert.ToInt32(row["InstallmentID"]),
                ExternalReference = row["ExternalReference"]?.ToString(),
                OriginalAmount = Convert.ToDecimal(row["OriginalAmount"]),
                PaidAmount = Convert.ToDecimal(row["PaidAmount"]),
                RemainingAmount = Convert.ToDecimal(row["RemainingAmount"]),
                LateFees = Convert.ToDecimal(row["LateFees"]),
                TotalDueAmount = Convert.ToDecimal(row["TotalDueAmount"]),
                DebtStatus = row["DebtStatus"].ToString(),
                Priority = row["Priority"]?.ToString(),
                OverdueDays = Convert.ToInt32(row["OverdueDays"]),
                ReminderCount = Convert.ToInt32(row["ReminderCount"]),
                Description = row["Description"]?.ToString(),
                Notes = row["Notes"]?.ToString(),
                InternalNotes = row["InternalNotes"]?.ToString(),
                CreatedBy = row["CreatedBy"]?.ToString(),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                ModifiedBy = row["ModifiedBy"]?.ToString(),
                ModifiedDate = Convert.ToDateTime(row["ModifiedDate"])
            };
        }

        /// <summary>
        /// بناء استعلام البحث
        /// </summary>
        /// <param name="criteria">معايير البحث</param>
        /// <returns>استعلام SQL</returns>
        private string BuildSearchQuery(DebtSearchCriteria criteria)
        {
            var query = "SELECT * FROM Debts WHERE 1=1";

            if (!string.IsNullOrWhiteSpace(criteria.DebtNumber))
                query += " AND DebtNumber LIKE @DebtNumber";

            if (!string.IsNullOrWhiteSpace(criteria.CustomerName))
                query += " AND CustomerName LIKE @CustomerName";

            if (criteria.CustomerId.HasValue)
                query += " AND CustomerID = @CustomerID";

            if (!string.IsNullOrWhiteSpace(criteria.DebtType))
                query += " AND DebtType = @DebtType";

            if (!string.IsNullOrWhiteSpace(criteria.DebtStatus))
                query += " AND DebtStatus = @DebtStatus";

            if (criteria.FromDate.HasValue)
                query += " AND DATE(DebtDate) >= @FromDate";

            if (criteria.ToDate.HasValue)
                query += " AND DATE(DebtDate) <= @ToDate";

            if (criteria.MinAmount.HasValue)
                query += " AND OriginalAmount >= @MinAmount";

            if (criteria.MaxAmount.HasValue)
                query += " AND OriginalAmount <= @MaxAmount";

            if (criteria.OnlyOverdue)
                query += " AND DueDate IS NOT NULL AND DATE(DueDate) < DATE('now')";

            if (criteria.OnlyOutstanding)
                query += " AND RemainingAmount > 0";

            query += " ORDER BY DebtDate DESC";

            if (criteria.Limit.HasValue)
                query += $" LIMIT {criteria.Limit.Value}";

            return query;
        }

        /// <summary>
        /// بناء معاملات البحث
        /// </summary>
        /// <param name="criteria">معايير البحث</param>
        /// <returns>قائمة المعاملات</returns>
        private List<SQLiteParameter> BuildSearchParameters(DebtSearchCriteria criteria)
        {
            var parameters = new List<SQLiteParameter>();

            if (!string.IsNullOrWhiteSpace(criteria.DebtNumber))
                parameters.Add(new SQLiteParameter("@DebtNumber", $"%{criteria.DebtNumber}%"));

            if (!string.IsNullOrWhiteSpace(criteria.CustomerName))
                parameters.Add(new SQLiteParameter("@CustomerName", $"%{criteria.CustomerName}%"));

            if (criteria.CustomerId.HasValue)
                parameters.Add(new SQLiteParameter("@CustomerID", criteria.CustomerId.Value));

            if (!string.IsNullOrWhiteSpace(criteria.DebtType))
                parameters.Add(new SQLiteParameter("@DebtType", criteria.DebtType));

            if (!string.IsNullOrWhiteSpace(criteria.DebtStatus))
                parameters.Add(new SQLiteParameter("@DebtStatus", criteria.DebtStatus));

            if (criteria.FromDate.HasValue)
                parameters.Add(new SQLiteParameter("@FromDate", criteria.FromDate.Value.ToString("yyyy-MM-dd")));

            if (criteria.ToDate.HasValue)
                parameters.Add(new SQLiteParameter("@ToDate", criteria.ToDate.Value.ToString("yyyy-MM-dd")));

            if (criteria.MinAmount.HasValue)
                parameters.Add(new SQLiteParameter("@MinAmount", criteria.MinAmount.Value));

            if (criteria.MaxAmount.HasValue)
                parameters.Add(new SQLiteParameter("@MaxAmount", criteria.MaxAmount.Value));

            return parameters;
        }

        /// <summary>
        /// الحصول على مدفوعات الدين
        /// </summary>
        /// <param name="debtId">رقم الدين</param>
        /// <returns>قائمة المدفوعات</returns>
        private List<DebtPayment> GetDebtPayments(int debtId)
        {
            try
            {
                var query = "SELECT * FROM DebtPayments WHERE DebtID = @DebtID ORDER BY PaymentDate DESC";
                var parameters = new[] { new SQLiteParameter("@DebtID", debtId) };
                var dataTable = _databaseManager.ExecuteQuery(query, parameters);
                var payments = new List<DebtPayment>();

                foreach (DataRow row in dataTable.Rows)
                {
                    payments.Add(MapDataRowToDebtPayment(row));
                }

                return payments;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على مدفوعات الدين {DebtID}", debtId);
                return new List<DebtPayment>();
            }
        }

        /// <summary>
        /// الحصول على تذكيرات الدين
        /// </summary>
        /// <param name="debtId">رقم الدين</param>
        /// <returns>قائمة التذكيرات</returns>
        private List<DebtReminder> GetDebtReminders(int debtId)
        {
            try
            {
                var query = "SELECT * FROM DebtReminders WHERE DebtID = @DebtID ORDER BY ReminderDate DESC";
                var parameters = new[] { new SQLiteParameter("@DebtID", debtId) };
                var dataTable = _databaseManager.ExecuteQuery(query, parameters);
                var reminders = new List<DebtReminder>();

                foreach (DataRow row in dataTable.Rows)
                {
                    reminders.Add(MapDataRowToDebtReminder(row));
                }

                return reminders;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على تذكيرات الدين {DebtID}", debtId);
                return new List<DebtReminder>();
            }
        }

        /// <summary>
        /// تحويل صف البيانات إلى كائن دفعة دين
        /// </summary>
        private DebtPayment MapDataRowToDebtPayment(DataRow row)
        {
            return new DebtPayment
            {
                PaymentID = Convert.ToInt32(row["PaymentID"]),
                DebtID = Convert.ToInt32(row["DebtID"]),
                PaymentNumber = row["PaymentNumber"].ToString(),
                PaymentDate = Convert.ToDateTime(row["PaymentDate"]),
                Amount = Convert.ToDecimal(row["Amount"]),
                PaymentMethod = row["PaymentMethod"].ToString(),
                PaymentStatus = row["PaymentStatus"].ToString(),
                Notes = row["Notes"]?.ToString(),
                CreatedBy = row["CreatedBy"]?.ToString(),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"])
            };
        }

        /// <summary>
        /// تحويل صف البيانات إلى كائن تذكير دين
        /// </summary>
        private DebtReminder MapDataRowToDebtReminder(DataRow row)
        {
            return new DebtReminder
            {
                ReminderID = Convert.ToInt32(row["ReminderID"]),
                DebtID = Convert.ToInt32(row["DebtID"]),
                ReminderNumber = row["ReminderNumber"].ToString(),
                ReminderDate = Convert.ToDateTime(row["ReminderDate"]),
                ReminderType = row["ReminderType"].ToString(),
                DeliveryMethod = row["DeliveryMethod"].ToString(),
                Subject = row["Subject"].ToString(),
                Message = row["Message"].ToString(),
                ReminderStatus = row["ReminderStatus"].ToString(),
                DebtAmount = Convert.ToDecimal(row["DebtAmount"]),
                RemainingAmount = Convert.ToDecimal(row["RemainingAmount"]),
                SentBy = row["SentBy"]?.ToString(),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"])
            };
        }

        #endregion
    }

    #region معايير البحث

    /// <summary>
    /// معايير البحث في الديون
    /// </summary>
    public class DebtSearchCriteria
    {
        public string DebtNumber { get; set; }
        public string CustomerName { get; set; }
        public int? CustomerId { get; set; }
        public string DebtType { get; set; }
        public string DebtStatus { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public bool OnlyOverdue { get; set; }
        public bool OnlyOutstanding { get; set; }
        public int? Limit { get; set; }
    }

    #endregion
}
