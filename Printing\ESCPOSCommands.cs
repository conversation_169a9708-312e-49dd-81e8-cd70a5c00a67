using System;
using System.Collections.Generic;
using System.Text;

namespace AredooPOS.Printing
{
    /// <summary>
    /// أوامر ESC/POS للطابعات الحرارية
    /// يحتوي على جميع الأوامر المطلوبة للتحكم في الطابعة
    /// </summary>
    public static class ESCPOSCommands
    {
        #region الثوابت الأساسية

        /// <summary>
        /// رمز ESC
        /// </summary>
        public const byte ESC = 0x1B;

        /// <summary>
        /// رمز GS
        /// </summary>
        public const byte GS = 0x1D;

        /// <summary>
        /// رمز FS
        /// </summary>
        public const byte FS = 0x1C;

        /// <summary>
        /// رمز DLE
        /// </summary>
        public const byte DLE = 0x10;

        /// <summary>
        /// رمز LF (Line Feed)
        /// </summary>
        public const byte LF = 0x0A;

        /// <summary>
        /// رمز CR (Carriage Return)
        /// </summary>
        public const byte CR = 0x0D;

        /// <summary>
        /// رمز HT (Horizontal Tab)
        /// </summary>
        public const byte HT = 0x09;

        /// <summary>
        /// رمز FF (Form Feed)
        /// </summary>
        public const byte FF = 0x0C;

        #endregion

        #region أوامر التهيئة

        /// <summary>
        /// تهيئة الطابعة
        /// </summary>
        public static readonly byte[] Initialize = { ESC, 0x40 };

        /// <summary>
        /// إعادة تعيين الطابعة
        /// </summary>
        public static readonly byte[] Reset = { ESC, 0x3F, 0x0A, 0x00 };

        /// <summary>
        /// تفعيل الطابعة
        /// </summary>
        public static readonly byte[] Enable = { DLE, 0x14, 0x01, 0x00, 0x05 };

        /// <summary>
        /// إلغاء تفعيل الطابعة
        /// </summary>
        public static readonly byte[] Disable = { DLE, 0x14, 0x00, 0x00, 0x05 };

        #endregion

        #region أوامر النص

        /// <summary>
        /// سطر جديد
        /// </summary>
        public static readonly byte[] LineFeed = { LF };

        /// <summary>
        /// إرجاع المؤشر
        /// </summary>
        public static readonly byte[] CarriageReturn = { CR };

        /// <summary>
        /// سطر جديد مع إرجاع المؤشر
        /// </summary>
        public static readonly byte[] NewLine = { CR, LF };

        /// <summary>
        /// تبويب أفقي
        /// </summary>
        public static readonly byte[] Tab = { HT };

        /// <summary>
        /// تعيين النص عريض
        /// </summary>
        /// <param name="enabled">تفعيل أو إلغاء</param>
        /// <returns>الأمر</returns>
        public static byte[] SetBold(bool enabled)
        {
            return new byte[] { ESC, 0x45, (byte)(enabled ? 1 : 0) };
        }

        /// <summary>
        /// تعيين النص مائل
        /// </summary>
        /// <param name="enabled">تفعيل أو إلغاء</param>
        /// <returns>الأمر</returns>
        public static byte[] SetItalic(bool enabled)
        {
            return new byte[] { ESC, 0x34, (byte)(enabled ? 1 : 0) };
        }

        /// <summary>
        /// تعيين النص مسطر
        /// </summary>
        /// <param name="enabled">تفعيل أو إلغاء</param>
        /// <returns>الأمر</returns>
        public static byte[] SetUnderline(bool enabled)
        {
            return new byte[] { ESC, 0x2D, (byte)(enabled ? 1 : 0) };
        }

        /// <summary>
        /// تعيين حجم النص
        /// </summary>
        /// <param name="widthMultiplier">مضاعف العرض (1-8)</param>
        /// <param name="heightMultiplier">مضاعف الارتفاع (1-8)</param>
        /// <returns>الأمر</returns>
        public static byte[] SetTextSize(int widthMultiplier, int heightMultiplier)
        {
            widthMultiplier = Math.Max(1, Math.Min(8, widthMultiplier));
            heightMultiplier = Math.Max(1, Math.Min(8, heightMultiplier));
            
            byte size = (byte)(((widthMultiplier - 1) << 4) | (heightMultiplier - 1));
            return new byte[] { GS, 0x21, size };
        }

        /// <summary>
        /// تعيين لون النص
        /// </summary>
        /// <param name="color">اللون (0 = أسود، 1 = أحمر)</param>
        /// <returns>الأمر</returns>
        public static byte[] SetTextColor(int color)
        {
            return new byte[] { ESC, 0x72, (byte)color };
        }

        #endregion

        #region أوامر المحاذاة

        /// <summary>
        /// أنواع المحاذاة
        /// </summary>
        public enum Alignment
        {
            Left = 0,
            Center = 1,
            Right = 2
        }

        /// <summary>
        /// تعيين محاذاة النص
        /// </summary>
        /// <param name="alignment">نوع المحاذاة</param>
        /// <returns>الأمر</returns>
        public static byte[] SetAlignment(Alignment alignment)
        {
            return new byte[] { ESC, 0x61, (byte)alignment };
        }

        #endregion

        #region أوامر الترميز

        /// <summary>
        /// صفحات الترميز المدعومة
        /// </summary>
        public enum CodePage
        {
            CP437 = 0,      // USA, Standard Europe
            CP850 = 2,      // Multilingual
            CP860 = 3,      // Portuguese
            CP863 = 4,      // Canadian-French
            CP865 = 5,      // Nordic
            CP1252 = 16,    // Latin I
            CP866 = 17,     // Cyrillic #2
            CP852 = 18,     // Latin II
            CP858 = 19,     // Multilingual Latin I + Euro
            CP1256 = 22,    // Arabic
            CP1257 = 23,    // Baltic Rim
            CP1251 = 24,    // Cyrillic
            CP737 = 25,     // Greek
            CP775 = 26,     // Baltic
            CP1254 = 27,    // Turkish
            CP1255 = 28,    // Hebrew
            CP1258 = 29,    // Vietnam
            CP864 = 30,     // Arabic
            CP862 = 31,     // Hebrew
            CP720 = 32      // Arabic
        }

        /// <summary>
        /// تعيين صفحة الترميز
        /// </summary>
        /// <param name="codePage">صفحة الترميز</param>
        /// <returns>الأمر</returns>
        public static byte[] SetCodePage(CodePage codePage)
        {
            return new byte[] { ESC, 0x74, (byte)codePage };
        }

        /// <summary>
        /// تعيين مجموعة الأحرف
        /// </summary>
        /// <param name="characterSet">مجموعة الأحرف</param>
        /// <returns>الأمر</returns>
        public static byte[] SetCharacterSet(int characterSet)
        {
            return new byte[] { ESC, 0x52, (byte)characterSet };
        }

        #endregion

        #region أوامر الباركود

        /// <summary>
        /// أنواع الباركود المدعومة
        /// </summary>
        public enum BarcodeType
        {
            UPC_A = 0,
            UPC_E = 1,
            EAN13 = 2,
            EAN8 = 3,
            CODE39 = 4,
            ITF = 5,
            CODABAR = 6,
            CODE93 = 72,
            CODE128 = 73
        }

        /// <summary>
        /// طباعة باركود
        /// </summary>
        /// <param name="data">البيانات</param>
        /// <param name="type">نوع الباركود</param>
        /// <param name="height">الارتفاع (افتراضي 162)</param>
        /// <param name="width">العرض (افتراضي 3)</param>
        /// <returns>الأمر</returns>
        public static byte[] PrintBarcode(string data, BarcodeType type, int height = 162, int width = 3)
        {
            var commands = new List<byte>();
            
            // تعيين ارتفاع الباركود
            commands.AddRange(new byte[] { GS, 0x68, (byte)height });
            
            // تعيين عرض الباركود
            commands.AddRange(new byte[] { GS, 0x77, (byte)width });
            
            // تعيين موضع النص
            commands.AddRange(new byte[] { GS, 0x48, 0x02 }); // أسفل الباركود
            
            // تعيين خط النص
            commands.AddRange(new byte[] { GS, 0x66, 0x00 });
            
            // طباعة الباركود
            commands.AddRange(new byte[] { GS, 0x6B, (byte)type, (byte)data.Length });
            commands.AddRange(Encoding.ASCII.GetBytes(data));
            
            return commands.ToArray();
        }

        #endregion

        #region أوامر QR Code

        /// <summary>
        /// طباعة QR Code
        /// </summary>
        /// <param name="data">البيانات</param>
        /// <param name="size">الحجم (1-16)</param>
        /// <param name="errorCorrection">مستوى تصحيح الخطأ (0-3)</param>
        /// <returns>الأمر</returns>
        public static byte[] PrintQRCode(string data, int size = 6, int errorCorrection = 1)
        {
            var commands = new List<byte>();
            var dataBytes = Encoding.UTF8.GetBytes(data);
            
            // تعيين نموذج QR Code
            commands.AddRange(new byte[] { GS, 0x28, 0x6B, 0x04, 0x00, 0x31, 0x41, 0x32, 0x00 });
            
            // تعيين حجم الوحدة
            commands.AddRange(new byte[] { GS, 0x28, 0x6B, 0x03, 0x00, 0x31, 0x43, (byte)size });
            
            // تعيين مستوى تصحيح الخطأ
            commands.AddRange(new byte[] { GS, 0x28, 0x6B, 0x03, 0x00, 0x31, 0x45, (byte)(48 + errorCorrection) });
            
            // تخزين البيانات
            var storeCommand = new List<byte> { GS, 0x28, 0x6B };
            var length = dataBytes.Length + 3;
            storeCommand.Add((byte)(length & 0xFF));
            storeCommand.Add((byte)((length >> 8) & 0xFF));
            storeCommand.AddRange(new byte[] { 0x31, 0x50, 0x30 });
            storeCommand.AddRange(dataBytes);
            commands.AddRange(storeCommand);
            
            // طباعة QR Code
            commands.AddRange(new byte[] { GS, 0x28, 0x6B, 0x03, 0x00, 0x31, 0x51, 0x30 });
            
            return commands.ToArray();
        }

        #endregion

        #region أوامر الصور

        /// <summary>
        /// طباعة صورة نقطية
        /// </summary>
        /// <param name="imageData">بيانات الصورة</param>
        /// <param name="width">العرض</param>
        /// <param name="height">الارتفاع</param>
        /// <returns>الأمر</returns>
        public static byte[] PrintBitmap(byte[] imageData, int width, int height)
        {
            var commands = new List<byte>();
            
            // أمر طباعة الصورة النقطية
            commands.AddRange(new byte[] { GS, 0x76, 0x30, 0x00 });
            
            // العرض (بايتين)
            commands.Add((byte)(width & 0xFF));
            commands.Add((byte)((width >> 8) & 0xFF));
            
            // الارتفاع (بايتين)
            commands.Add((byte)(height & 0xFF));
            commands.Add((byte)((height >> 8) & 0xFF));
            
            // بيانات الصورة
            commands.AddRange(imageData);
            
            return commands.ToArray();
        }

        #endregion

        #region أوامر قطع الورق

        /// <summary>
        /// قطع الورق كلياً
        /// </summary>
        public static readonly byte[] CutPaper = { GS, 0x56, 0x00 };

        /// <summary>
        /// قطع الورق جزئياً
        /// </summary>
        public static readonly byte[] CutPaperPartial = { GS, 0x56, 0x01 };

        /// <summary>
        /// قطع الورق مع تغذية
        /// </summary>
        /// <param name="lines">عدد الأسطر للتغذية</param>
        /// <returns>الأمر</returns>
        public static byte[] CutPaperWithFeed(int lines)
        {
            return new byte[] { GS, 0x56, 0x42, (byte)lines };
        }

        #endregion

        #region أوامر الدرج النقدي

        /// <summary>
        /// فتح الدرج النقدي (المنفذ 0)
        /// </summary>
        public static readonly byte[] OpenCashDrawer1 = { ESC, 0x70, 0x00, 0x19, 0xFA };

        /// <summary>
        /// فتح الدرج النقدي (المنفذ 1)
        /// </summary>
        public static readonly byte[] OpenCashDrawer2 = { ESC, 0x70, 0x01, 0x19, 0xFA };

        /// <summary>
        /// فتح الدرج النقدي مخصص
        /// </summary>
        /// <param name="pin">رقم المنفذ (0 أو 1)</param>
        /// <param name="onTime">وقت التشغيل (1-8)</param>
        /// <param name="offTime">وقت الإيقاف (1-8)</param>
        /// <returns>الأمر</returns>
        public static byte[] OpenCashDrawer(int pin, int onTime, int offTime)
        {
            return new byte[] { ESC, 0x70, (byte)pin, (byte)onTime, (byte)offTime };
        }

        #endregion

        #region أوامر حالة الطابعة

        /// <summary>
        /// طلب حالة الطابعة
        /// </summary>
        public static readonly byte[] RequestPrinterStatus = { DLE, 0x04, 0x01 };

        /// <summary>
        /// طلب حالة الورق
        /// </summary>
        public static readonly byte[] RequestPaperStatus = { DLE, 0x04, 0x04 };

        /// <summary>
        /// طلب حالة الدرج النقدي
        /// </summary>
        public static readonly byte[] RequestDrawerStatus = { DLE, 0x04, 0x02 };

        #endregion

        #region دوال مساعدة

        /// <summary>
        /// طباعة خط من الرموز
        /// </summary>
        /// <param name="character">الرمز</param>
        /// <param name="width">العرض</param>
        /// <returns>الأمر</returns>
        public static byte[] PrintLine(string character, int width)
        {
            var line = new StringBuilder();
            for (int i = 0; i < width; i++)
            {
                line.Append(character);
            }
            line.Append("\n");
            
            return Encoding.GetEncoding(1256).GetBytes(line.ToString());
        }

        /// <summary>
        /// طباعة نص مع محاذاة
        /// </summary>
        /// <param name="text">النص</param>
        /// <param name="alignment">المحاذاة</param>
        /// <param name="width">العرض</param>
        /// <returns>الأمر</returns>
        public static byte[] PrintAlignedText(string text, Alignment alignment, int width)
        {
            var commands = new List<byte>();
            commands.AddRange(SetAlignment(alignment));
            
            switch (alignment)
            {
                case Alignment.Center:
                    var padding = (width - text.Length) / 2;
                    text = text.PadLeft(text.Length + padding).PadRight(width);
                    break;
                case Alignment.Right:
                    text = text.PadLeft(width);
                    break;
                default:
                    text = text.PadRight(width);
                    break;
            }
            
            commands.AddRange(Encoding.GetEncoding(1256).GetBytes(text));
            commands.AddRange(LineFeed);
            commands.AddRange(SetAlignment(Alignment.Left));
            
            return commands.ToArray();
        }

        /// <summary>
        /// طباعة نص بتنسيق
        /// </summary>
        /// <param name="text">النص</param>
        /// <param name="bold">عريض</param>
        /// <param name="underline">مسطر</param>
        /// <param name="widthMultiplier">مضاعف العرض</param>
        /// <param name="heightMultiplier">مضاعف الارتفاع</param>
        /// <returns>الأمر</returns>
        public static byte[] PrintFormattedText(string text, bool bold = false, bool underline = false, 
            int widthMultiplier = 1, int heightMultiplier = 1)
        {
            var commands = new List<byte>();
            
            // تطبيق التنسيق
            if (bold) commands.AddRange(SetBold(true));
            if (underline) commands.AddRange(SetUnderline(true));
            if (widthMultiplier > 1 || heightMultiplier > 1)
                commands.AddRange(SetTextSize(widthMultiplier, heightMultiplier));
            
            // طباعة النص
            commands.AddRange(Encoding.GetEncoding(1256).GetBytes(text));
            commands.AddRange(LineFeed);
            
            // إعادة تعيين التنسيق
            if (bold) commands.AddRange(SetBold(false));
            if (underline) commands.AddRange(SetUnderline(false));
            if (widthMultiplier > 1 || heightMultiplier > 1)
                commands.AddRange(SetTextSize(1, 1));
            
            return commands.ToArray();
        }

        /// <summary>
        /// تحويل النص إلى بايتات بالترميز العربي
        /// </summary>
        /// <param name="text">النص</param>
        /// <returns>البايتات</returns>
        public static byte[] GetArabicBytes(string text)
        {
            return Encoding.GetEncoding(1256).GetBytes(text);
        }

        #endregion
    }
}
