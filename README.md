# نظام أريدوو للكاشير المتكامل
## Aredoo POS - Complete Point of Sale System

![نظام أريدوو](https://img.shields.io/badge/Version-1.0.0-blue.svg)
![.NET Framework](https://img.shields.io/badge/.NET%20Framework-4.7.2-purple.svg)
![SQL Server](https://img.shields.io/badge/SQL%20Server-2019+-red.svg)
![License](https://img.shields.io/badge/License-Commercial-green.svg)

---

## 📋 نظرة عامة

نظام أريدوو للكاشير هو نظام نقاط بيع متكامل مصمم خصيصاً للمتاجر العربية. يوفر النظام جميع الميزات المطلوبة لإدارة المبيعات والمخزون والعملاء والتقارير بواجهة عربية سهلة الاستخدام.

### ✨ الميزات الرئيسية

- 🏪 **إدارة المبيعات**: إنشاء فواتير البيع والإرجاع مع دعم طرق دفع متعددة
- 👥 **إدارة العملاء**: قاعدة بيانات شاملة للعملاء مع تتبع المعاملات والديون
- 📦 **إدارة المخزون**: تتبع المنتجات والكميات مع تنبيهات المخزون المنخفض
- 💰 **إدارة الصندوق**: تتبع النقدية والمدفوعات مع تقارير الجلسات
- 💳 **نظام الأقساط**: إدارة المبيعات بالتقسيط مع جدولة المدفوعات
- 💸 **إدارة الديون**: تتبع ديون العملاء والمدفوعات الجزئية
- 📊 **التقارير المتقدمة**: تقارير شاملة للمبيعات والمخزون والأرباح
- 🖨️ **الطباعة الحرارية**: دعم الطابعات الحرارية وطباعة الباركود
- 🔐 **نظام المستخدمين**: إدارة المستخدمين والصلاحيات
- 🌐 **الدعم العربي**: واجهة عربية كاملة مع دعم RTL

---

## 🛠️ المتطلبات التقنية

### متطلبات النظام
- **نظام التشغيل**: Windows 7 أو أحدث
- **المعالج**: Intel Core i3 أو معادل
- **الذاكرة**: 4 GB RAM كحد أدنى
- **التخزين**: 2 GB مساحة فارغة
- **الشاشة**: دقة 1366x768 كحد أدنى

### متطلبات البرمجيات
- **.NET Framework**: 4.7.2 أو أحدث
- **SQL Server**: 2017 أو أحدث (Express مدعوم)
- **Visual Studio**: 2019 أو أحدث (للتطوير)

### الأجهزة الاختيارية
- طابعة حرارية (80mm)
- قارئ باركود
- درج نقدية
- شاشة عرض للعميل

---

## 🚀 التثبيت والإعداد

### 1. تحضير البيئة

```bash
# تحميل وتثبيت SQL Server Express
# تحميل وتثبيت .NET Framework 4.7.2
```

### 2. إعداد قاعدة البيانات

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE AredooPOS;

-- تشغيل سكريبت إنشاء الجداول
-- تشغيل ملف: Database/AredooPOS_Complete.sql

-- إدراج البيانات الأولية
-- تشغيل ملف: Database/InitialData_And_Indexes.sql
```

### 3. تكوين الاتصال

تحديث ملف `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.\\SQLEXPRESS;Database=AredooPOS;Integrated Security=true;"
  }
}
```

### 4. بناء وتشغيل المشروع

```bash
# بناء المشروع
dotnet build

# تشغيل التطبيق
dotnet run
```

---

## 📖 دليل الاستخدام

### تسجيل الدخول الأولي
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### الوحدات الرئيسية

#### 1. 📊 لوحة المعلومات
- عرض إحصائيات المبيعات اليومية
- مؤشرات الأداء الرئيسية
- التنبيهات والإشعارات

#### 2. 💰 إدارة المبيعات
- إنشاء فواتير البيع
- معالجة الإرجاع
- طرق دفع متعددة (نقدي، بطاقة، تحويل)

#### 3. 👥 إدارة العملاء
- إضافة وتعديل بيانات العملاء
- تتبع تاريخ المعاملات
- إدارة حدود الائتمان

#### 4. 📦 إدارة المنتجات
- إضافة وتصنيف المنتجات
- تتبع المخزون
- إدارة الأسعار والتكلفة

#### 5. 💳 نظام الأقساط
- إنشاء خطط التقسيط
- تتبع المدفوعات
- تقارير الأقساط المستحقة

#### 6. 💸 إدارة الديون
- تسجيل الديون اليدوية
- تتبع المدفوعات الجزئية
- تقارير المدينين

---

## 🏗️ هيكل المشروع

```
AredooPOS/
├── 📁 Models/              # نماذج البيانات
│   ├── User.cs
│   ├── Customer.cs
│   ├── Product.cs
│   ├── Invoice.cs
│   └── ...
├── 📁 DAL/                 # طبقة الوصول للبيانات
│   ├── SqlServerConnection.cs
│   ├── UserDAL.cs
│   └── ...
├── 📁 BLL/                 # طبقة منطق الأعمال
│   ├── UserBLL.cs
│   ├── CustomerBLL.cs
│   └── ...
├── 📁 Forms/               # واجهات المستخدم
│   ├── LoginForm.cs
│   ├── MainForm.cs
│   └── ...
├── 📁 Helpers/             # المساعدات والأدوات
│   ├── PrintHelper.cs
│   ├── BarcodeHelper.cs
│   └── ...
├── 📁 Database/            # سكريبتات قاعدة البيانات
│   ├── AredooPOS_Complete.sql
│   └── InitialData_And_Indexes.sql
├── 📁 Resources/           # الموارد والصور
└── 📄 appsettings.json     # ملف الإعدادات
```

---

## 🔧 التخصيص والإعدادات

### إعدادات المتجر
```json
{
  "StoreSettings": {
    "StoreName": "اسم المتجر",
    "Address": "عنوان المتجر",
    "Phone": "رقم الهاتف",
    "TaxNumber": "الرقم الضريبي"
  }
}
```

### إعدادات الطباعة
```json
{
  "PrintSettings": {
    "PrinterType": "Thermal",
    "PaperSize": "80mm",
    "AutoPrint": false
  }
}
```

### إعدادات الضرائب
```json
{
  "BusinessSettings": {
    "TaxRate": 15.0,
    "EnableTax": true
  }
}
```

---

## 📊 قاعدة البيانات

### الجداول الرئيسية
- **Users**: المستخدمين والصلاحيات
- **Customers**: بيانات العملاء
- **Products**: المنتجات والمخزون
- **Invoices**: فواتير البيع
- **InvoiceDetails**: تفاصيل الفواتير
- **Debts**: ديون العملاء
- **Installments**: الأقساط
- **CashSessions**: جلسات الصندوق

### العلاقات
- علاقة واحد لمتعدد بين العملاء والفواتير
- علاقة واحد لمتعدد بين الفواتير وتفاصيل الفواتير
- علاقة واحد لمتعدد بين العملاء والديون

---

## 🔐 الأمان والصلاحيات

### أدوار المستخدمين
- **مدير النظام**: صلاحيات كاملة
- **مدير المتجر**: إدارة المبيعات والتقارير
- **كاشير**: المبيعات والعملاء
- **مشاهد**: عرض التقارير فقط

### ميزات الأمان
- تشفير كلمات المرور
- تتبع جلسات المستخدمين
- تسجيل العمليات (Audit Trail)
- انتهاء صلاحية الجلسات

---

## 📈 التقارير

### تقارير المبيعات
- تقرير المبيعات اليومية
- تقرير المبيعات الشهرية
- تقرير أفضل المنتجات مبيعاً
- تقرير أداء الكاشيرين

### تقارير المخزون
- تقرير حالة المخزون
- تقرير المنتجات منخفضة المخزون
- تقرير حركة المخزون
- تقرير قيمة المخزون

### تقارير العملاء
- تقرير كشف حساب العميل
- تقرير أفضل العملاء
- تقرير الديون المستحقة
- تقرير الأقساط

---

## 🛠️ الصيانة والنسخ الاحتياطي

### النسخ الاحتياطي التلقائي
- نسخ احتياطية يومية تلقائية
- ضغط ملفات النسخ الاحتياطي
- الاحتفاظ بـ 30 نسخة احتياطية

### صيانة قاعدة البيانات
- تحسين الفهارس أسبوعياً
- تنظيف البيانات القديمة
- مراقبة الأداء

---

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الاتصال بقاعدة البيانات
```
الحل: التحقق من إعدادات الاتصال في appsettings.json
```

#### مشكلة في الطباعة
```
الحل: التحقق من تعريف الطابعة وإعدادات الطباعة
```

#### بطء في الأداء
```
الحل: تحسين قاعدة البيانات وتنظيف البيانات القديمة
```

---

## 📞 الدعم الفني

### معلومات الاتصال
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966501234567
- **الموقع**: www.aredoo.com

### ساعات الدعم
- الأحد - الخميس: 9:00 ص - 6:00 م
- الجمعة - السبت: مغلق

---

## 📄 الترخيص

هذا البرنامج محمي بحقوق الطبع والنشر © 2024 أريدوو للتقنية. جميع الحقوق محفوظة.

للحصول على ترخيص تجاري، يرجى الاتصال بنا على: <EMAIL>

---

## 🔄 التحديثات

### الإصدار 1.0.0 (2024-01-01)
- الإصدار الأولي
- جميع الميزات الأساسية
- دعم اللغة العربية
- واجهة مستخدم حديثة

### خطة التطوير المستقبلية
- [ ] تطبيق الهاتف المحمول
- [ ] التكامل مع المتاجر الإلكترونية
- [ ] نظام إدارة علاقات العملاء المتقدم
- [ ] التكامل مع أنظمة المحاسبة
- [ ] دعم المتاجر متعددة الفروع

---

**تم تطوير هذا النظام بعناية فائقة لخدمة المتاجر العربية وتلبية احتياجاتها المحلية.**
