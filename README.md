# برنامج أريدوو - نظام نقاط البيع

## نظرة عامة
برنامج أريدوو هو نظام نقاط بيع متكامل مصمم خصيصاً للشركات والمتاجر في المملكة العربية السعودية. يدعم البرنامج اللغة العربية بالكامل ويوفر جميع الميزات المطلوبة لإدارة المبيعات والمخزون والعملاء.

## الميزات الرئيسية

### 🧾 نظام الفواتير
- إنشاء فواتير بيع (نقد - بطاقة - قسط)
- تعديل الفواتير مع إمكانية إضافة خصومات وضرائب
- طباعة الفاتورة على طابعة حرارية أو A4
- البحث عن الفواتير حسب التاريخ، الرقم، أو اسم العميل
- إمكانية استرجاع/إرجاع فاتورة مع تحديث المخزون

### 📦 إدارة المخزون
- إضافة وتعديل المنتجات
- تتبع كميات المخزون
- تنبيهات المخزون المنخفض
- دعم الباركود

### 👥 إدارة العملاء
- إضافة وتعديل بيانات العملاء
- إدارة حدود الائتمان
- تتبع أرصدة العملاء

### 💰 إدارة المدفوعات
- دعم أنواع دفع متعددة
- إدارة الأقساط والديون
- تتبع المدفوعات

## متطلبات النظام

### الحد الأدنى
- Windows 7 أو أحدث
- .NET Framework 4.8 أو أحدث
- SQL Server Express 2019 أو أحدث
- 4 GB RAM
- 2 GB مساحة فارغة على القرص الصلب

### الموصى به
- Windows 10/11
- .NET 6.0 أو أحدث
- SQL Server 2019 أو أحدث
- 8 GB RAM
- 10 GB مساحة فارغة على القرص الصلب

## التثبيت والإعداد

### 1. تثبيت قاعدة البيانات
```sql
-- تشغيل سكريبت إنشاء قاعدة البيانات
sqlcmd -S .\SQLEXPRESS -i "Database\CreateDatabase.sql"
```

### 2. تكوين سلسلة الاتصال
قم بتعديل ملف `App.config` وتحديث سلسلة الاتصال:
```xml
<connectionStrings>
    <add name="AridooPOS" 
         connectionString="Data Source=YOUR_SERVER;Initial Catalog=AridooPOS;Integrated Security=True" 
         providerName="System.Data.SqlClient" />
</connectionStrings>
```

### 3. بناء المشروع
```bash
dotnet build AridooPOS.csproj
```

### 4. تشغيل التطبيق
```bash
dotnet run
```

## الاستخدام

### إنشاء فاتورة جديدة
1. اضغط على "فاتورة جديدة" أو F2
2. اختر العميل (اختياري)
3. ابحث عن المنتجات وأضفها
4. حدد نوع الدفع
5. احفظ الفاتورة

### البحث عن الفواتير
1. اضغط على "البحث عن الفواتير" أو F3
2. أدخل معايير البحث
3. اضغط "بحث"
4. اختر الفاتورة للعرض أو التعديل

### إرجاع فاتورة
1. ابحث عن الفاتورة المطلوب إرجاعها
2. اختر الفاتورة واضغط "إرجاع"
3. أدخل سبب الإرجاع
4. أكد الإرجاع

## اختصارات لوحة المفاتيح
- `F2`: فاتورة جديدة
- `F3`: البحث عن الفواتير
- `F4`: إدارة المنتجات
- `F5`: إدارة العملاء
- `Delete`: حذف عنصر محدد
- `Enter`: تأكيد/إضافة

## هيكل المشروع
```
AridooPOS/
├── DAL/                 # طبقة الوصول للبيانات
├── BLL/                 # طبقة منطق الأعمال
├── Models/              # نماذج البيانات
├── UI/                  # واجهات المستخدم
├── Reports/             # التقارير
├── Database/            # سكريبتات قاعدة البيانات
└── README.md
```

## المساهمة
نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات
4. إرسال Pull Request

## الدعم
للحصول على الدعم، يرجى:
- فتح Issue في GitHub
- إرسال بريد إلكتروني إلى: <EMAIL>

## الترخيص
هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الإصدارات

### الإصدار 1.0.0
- نظام الفواتير الأساسي
- إدارة المنتجات والعملاء
- البحث والتقارير الأساسية
- دعم الطباعة

### خطط مستقبلية
- نظام المستخدمين والصلاحيات
- تقارير متقدمة
- دعم الفروع المتعددة
- تطبيق الجوال
- التكامل مع أنظمة المحاسبة

---
© 2024 أريدوو. جميع الحقوق محفوظة.
