using System;
using System.Collections.Generic;
using System.Linq;
using AredooPOS.Models.Settings;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.BLL
{
    /// <summary>
    /// طبقة منطق الأعمال للإعدادات
    /// تحتوي على جميع العمليات المتعلقة بإدارة وتطبيق الإعدادات
    /// </summary>
    public class SettingsBLL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly SettingsDAL _settingsDAL;
        private readonly ILogger<SettingsBLL> _logger;

        // كاش للإعدادات لتحسين الأداء
        private static Dictionary<string, SystemSettings> _settingsCache = new Dictionary<string, SystemSettings>();
        private static DateTime _lastCacheUpdate = DateTime.MinValue;
        private static readonly object _cacheLock = new object();
        private static readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5);

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة منطق الأعمال للإعدادات
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public SettingsBLL(string connectionString = null, ILogger<SettingsBLL> logger = null)
        {
            _settingsDAL = new SettingsDAL(connectionString, null);
            _logger = logger;
        }

        #endregion

        #region إدارة الإعدادات العامة

        /// <summary>
        /// الحصول على قيمة إعداد مع الكاش
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>قيمة الإعداد</returns>
        public string GetSettingValue(string settingName, string defaultValue = null)
        {
            try
            {
                lock (_cacheLock)
                {
                    // تحديث الكاش إذا انتهت صلاحيته
                    if (DateTime.Now - _lastCacheUpdate > _cacheExpiry)
                    {
                        RefreshCache();
                    }

                    // البحث في الكاش
                    if (_settingsCache.TryGetValue(settingName, out var cachedSetting))
                    {
                        return cachedSetting.SettingValue ?? defaultValue;
                    }

                    // إذا لم يوجد في الكاش، البحث في قاعدة البيانات
                    var setting = _settingsDAL.GetSetting(settingName);
                    if (setting != null)
                    {
                        _settingsCache[settingName] = setting;
                        return setting.SettingValue ?? defaultValue;
                    }

                    return defaultValue;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على قيمة الإعداد {settingName}");
                return defaultValue;
            }
        }

        /// <summary>
        /// الحصول على قيمة إعداد كرقم صحيح
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>قيمة الإعداد</returns>
        public int GetSettingValueAsInt(string settingName, int defaultValue = 0)
        {
            var value = GetSettingValue(settingName, defaultValue.ToString());
            return int.TryParse(value, out int result) ? result : defaultValue;
        }

        /// <summary>
        /// الحصول على قيمة إعداد كرقم عشري
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>قيمة الإعداد</returns>
        public decimal GetSettingValueAsDecimal(string settingName, decimal defaultValue = 0)
        {
            var value = GetSettingValue(settingName, defaultValue.ToString());
            return decimal.TryParse(value, out decimal result) ? result : defaultValue;
        }

        /// <summary>
        /// الحصول على قيمة إعداد كقيمة منطقية
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>قيمة الإعداد</returns>
        public bool GetSettingValueAsBool(string settingName, bool defaultValue = false)
        {
            var value = GetSettingValue(settingName, defaultValue.ToString());
            return bool.TryParse(value, out bool result) ? result : defaultValue;
        }

        /// <summary>
        /// حفظ قيمة إعداد
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <param name="settingValue">قيمة الإعداد</param>
        /// <param name="updatedBy">من قام بالتحديث</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SaveSettingValue(string settingName, string settingValue, string updatedBy)
        {
            try
            {
                var result = _settingsDAL.SaveSettingValue(settingName, settingValue, updatedBy);
                
                if (result)
                {
                    // تحديث الكاش
                    lock (_cacheLock)
                    {
                        if (_settingsCache.ContainsKey(settingName))
                        {
                            _settingsCache[settingName].UpdateValue(settingValue, updatedBy);
                        }
                    }

                    _logger?.LogInformation($"تم حفظ الإعداد {settingName} بالقيمة {settingValue}");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حفظ الإعداد {settingName}");
                return false;
            }
        }

        /// <summary>
        /// حفظ عدة إعدادات
        /// </summary>
        /// <param name="settings">قاموس الإعدادات</param>
        /// <param name="updatedBy">من قام بالتحديث</param>
        /// <returns>عدد الإعدادات المحفوظة</returns>
        public int SaveMultipleSettings(Dictionary<string, string> settings, string updatedBy)
        {
            try
            {
                int savedCount = 0;
                foreach (var setting in settings)
                {
                    if (SaveSettingValue(setting.Key, setting.Value, updatedBy))
                    {
                        savedCount++;
                    }
                }

                _logger?.LogInformation($"تم حفظ {savedCount} من أصل {settings.Count} إعداد");
                return savedCount;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ عدة إعدادات");
                return 0;
            }
        }

        /// <summary>
        /// تحديث كاش الإعدادات
        /// </summary>
        private void RefreshCache()
        {
            try
            {
                var allSettings = _settingsDAL.GetAllSettings();
                _settingsCache.Clear();
                
                foreach (var setting in allSettings)
                {
                    _settingsCache[setting.SettingName] = setting;
                }

                _lastCacheUpdate = DateTime.Now;
                _logger?.LogDebug("تم تحديث كاش الإعدادات");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحديث كاش الإعدادات");
            }
        }

        /// <summary>
        /// مسح كاش الإعدادات
        /// </summary>
        public void ClearCache()
        {
            lock (_cacheLock)
            {
                _settingsCache.Clear();
                _lastCacheUpdate = DateTime.MinValue;
            }
        }

        #endregion

        #region إدارة إعدادات الضريبة

        /// <summary>
        /// الحصول على إعدادات الضريبة
        /// </summary>
        /// <returns>إعدادات الضريبة</returns>
        public TaxSettings GetTaxSettings()
        {
            try
            {
                return _settingsDAL.GetTaxSettings();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إعدادات الضريبة");
                return new TaxSettings();
            }
        }

        /// <summary>
        /// حفظ إعدادات الضريبة
        /// </summary>
        /// <param name="taxSettings">إعدادات الضريبة</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SaveTaxSettings(TaxSettings taxSettings)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!taxSettings.IsValid())
                {
                    _logger?.LogWarning("إعدادات الضريبة غير صحيحة");
                    return false;
                }

                var result = _settingsDAL.SaveTaxSettings(taxSettings);
                
                if (result)
                {
                    _logger?.LogInformation("تم حفظ إعدادات الضريبة بنجاح");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ إعدادات الضريبة");
                return false;
            }
        }

        /// <summary>
        /// حساب الضريبة للمبلغ
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="productID">رقم المنتج (اختياري)</param>
        /// <param name="categoryID">رقم الفئة (اختياري)</param>
        /// <returns>مبلغ الضريبة</returns>
        public decimal CalculateTax(decimal amount, int? productID = null, int? categoryID = null)
        {
            try
            {
                var taxSettings = GetTaxSettings();
                
                if (!taxSettings.IsTaxEnabled)
                    return 0;

                decimal taxRate = taxSettings.DefaultTaxRate;
                
                // الحصول على نسبة الضريبة للمنتج أو الفئة
                if (productID.HasValue || categoryID.HasValue)
                {
                    taxRate = taxSettings.GetTaxRateForProduct(productID ?? 0, categoryID ?? 0);
                }

                return taxSettings.CalculateTax(amount, taxRate);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حساب الضريبة");
                return 0;
            }
        }

        #endregion

        #region إدارة إعدادات العملة

        /// <summary>
        /// الحصول على إعدادات العملة
        /// </summary>
        /// <returns>إعدادات العملة</returns>
        public CurrencySettings GetCurrencySettings()
        {
            try
            {
                return _settingsDAL.GetCurrencySettings();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إعدادات العملة");
                return new CurrencySettings();
            }
        }

        /// <summary>
        /// حفظ إعدادات العملة
        /// </summary>
        /// <param name="currencySettings">إعدادات العملة</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SaveCurrencySettings(CurrencySettings currencySettings)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!currencySettings.IsValid())
                {
                    _logger?.LogWarning("إعدادات العملة غير صحيحة");
                    return false;
                }

                var result = _settingsDAL.SaveCurrencySettings(currencySettings);
                
                if (result)
                {
                    _logger?.LogInformation("تم حفظ إعدادات العملة بنجاح");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ إعدادات العملة");
                return false;
            }
        }

        /// <summary>
        /// تنسيق المبلغ بالعملة الافتراضية
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ منسق</returns>
        public string FormatCurrency(decimal amount)
        {
            try
            {
                var currencySettings = GetCurrencySettings();
                return currencySettings.FormatAmount(amount);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تنسيق العملة");
                return amount.ToString("N2");
            }
        }

        /// <summary>
        /// تحويل العملة
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="fromCurrency">من عملة</param>
        /// <param name="toCurrency">إلى عملة</param>
        /// <returns>المبلغ محول</returns>
        public decimal ConvertCurrency(decimal amount, string fromCurrency, string toCurrency)
        {
            try
            {
                var currencySettings = GetCurrencySettings();
                return currencySettings.ConvertCurrency(amount, fromCurrency, toCurrency);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحويل العملة");
                return amount;
            }
        }

        #endregion

        #region إدارة إعدادات المتجر

        /// <summary>
        /// الحصول على إعدادات المتجر
        /// </summary>
        /// <returns>إعدادات المتجر</returns>
        public StoreSettings GetStoreSettings()
        {
            try
            {
                return _settingsDAL.GetStoreSettings();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إعدادات المتجر");
                return new StoreSettings();
            }
        }

        /// <summary>
        /// حفظ إعدادات المتجر
        /// </summary>
        /// <param name="storeSettings">إعدادات المتجر</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SaveStoreSettings(StoreSettings storeSettings)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!storeSettings.IsValid())
                {
                    _logger?.LogWarning("إعدادات المتجر غير صحيحة");
                    return false;
                }

                var result = _settingsDAL.SaveStoreSettings(storeSettings);
                
                if (result)
                {
                    _logger?.LogInformation("تم حفظ إعدادات المتجر بنجاح");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ إعدادات المتجر");
                return false;
            }
        }

        #endregion

        #region إدارة إعدادات الطباعة

        /// <summary>
        /// الحصول على إعدادات الطباعة
        /// </summary>
        /// <returns>إعدادات الطباعة</returns>
        public PrintSettings GetPrintSettings()
        {
            try
            {
                return _settingsDAL.GetPrintSettings();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إعدادات الطباعة");
                return new PrintSettings();
            }
        }

        /// <summary>
        /// حفظ إعدادات الطباعة
        /// </summary>
        /// <param name="printSettings">إعدادات الطباعة</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SavePrintSettings(PrintSettings printSettings)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!printSettings.IsValid())
                {
                    _logger?.LogWarning("إعدادات الطباعة غير صحيحة");
                    return false;
                }

                var result = _settingsDAL.SavePrintSettings(printSettings);
                
                if (result)
                {
                    _logger?.LogInformation("تم حفظ إعدادات الطباعة بنجاح");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ إعدادات الطباعة");
                return false;
            }
        }

        #endregion

        #region العمليات المتقدمة

        /// <summary>
        /// تهيئة الإعدادات الافتراضية
        /// </summary>
        /// <param name="updatedBy">من قام بالتهيئة</param>
        /// <returns>true إذا تم التهيئة بنجاح</returns>
        public bool InitializeDefaultSettings(string updatedBy)
        {
            try
            {
                var initializedCount = _settingsDAL.InitializeDefaultSettings(updatedBy);

                // مسح الكاش لإعادة تحميل الإعدادات الجديدة
                ClearCache();

                _logger?.LogInformation($"تم تهيئة {initializedCount} إعداد افتراضي");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تهيئة الإعدادات الافتراضية");
                return false;
            }
        }

        /// <summary>
        /// التحقق من اكتمال الإعدادات الأساسية
        /// </summary>
        /// <returns>true إذا كانت الإعدادات مكتملة</returns>
        public bool ValidateEssentialSettings()
        {
            try
            {
                var issues = new List<string>();

                // التحقق من إعدادات المتجر
                var storeSettings = GetStoreSettings();
                if (string.IsNullOrWhiteSpace(storeSettings.StoreName))
                    issues.Add("اسم المتجر مطلوب");

                // التحقق من إعدادات العملة
                var currencySettings = GetCurrencySettings();
                if (string.IsNullOrWhiteSpace(currencySettings.DefaultCurrencyCode))
                    issues.Add("العملة الافتراضية مطلوبة");

                // التحقق من إعدادات الضريبة
                var taxSettings = GetTaxSettings();
                if (taxSettings.IsTaxEnabled && taxSettings.DefaultTaxRate < 0)
                    issues.Add("نسبة الضريبة غير صحيحة");

                // التحقق من إعدادات الطباعة
                var printSettings = GetPrintSettings();
                if (string.IsNullOrWhiteSpace(printSettings.DefaultPrintSize))
                    issues.Add("حجم الطباعة الافتراضي مطلوب");

                if (issues.Any())
                {
                    _logger?.LogWarning($"مشاكل في الإعدادات: {string.Join(", ", issues)}");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في التحقق من الإعدادات الأساسية");
                return false;
            }
        }

        /// <summary>
        /// الحصول على ملخص الإعدادات
        /// </summary>
        /// <returns>ملخص الإعدادات</returns>
        public SettingsSummary GetSettingsSummary()
        {
            try
            {
                var storeSettings = GetStoreSettings();
                var currencySettings = GetCurrencySettings();
                var taxSettings = GetTaxSettings();
                var printSettings = GetPrintSettings();

                return new SettingsSummary
                {
                    StoreName = storeSettings.StoreName,
                    DefaultCurrency = currencySettings.DefaultCurrencyCode,
                    CurrencySymbol = currencySettings.CurrencySymbol,
                    TaxEnabled = taxSettings.IsTaxEnabled,
                    DefaultTaxRate = taxSettings.DefaultTaxRate,
                    PrintSize = printSettings.DefaultPrintSize,
                    AutoPrint = printSettings.AutoPrint,
                    HasStoreLogo = storeSettings.HasLogo,
                    SupportMultipleCurrencies = currencySettings.SupportMultipleCurrencies,
                    LastUpdated = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على ملخص الإعدادات");
                return new SettingsSummary();
            }
        }

        /// <summary>
        /// تصدير جميع الإعدادات
        /// </summary>
        /// <returns>البيانات المصدرة</returns>
        public string ExportAllSettings()
        {
            try
            {
                return _settingsDAL.ExportSettings();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تصدير الإعدادات");
                throw;
            }
        }

        /// <summary>
        /// استيراد الإعدادات
        /// </summary>
        /// <param name="jsonData">البيانات المستوردة</param>
        /// <param name="updatedBy">من قام بالاستيراد</param>
        /// <returns>true إذا تم الاستيراد بنجاح</returns>
        public bool ImportSettings(string jsonData, string updatedBy)
        {
            try
            {
                var result = _settingsDAL.ImportSettings(jsonData, updatedBy);

                if (result)
                {
                    // مسح الكاش لإعادة تحميل الإعدادات الجديدة
                    ClearCache();
                    _logger?.LogInformation("تم استيراد الإعدادات بنجاح");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في استيراد الإعدادات");
                return false;
            }
        }

        /// <summary>
        /// إعادة تعيين الإعدادات إلى القيم الافتراضية
        /// </summary>
        /// <param name="category">الفئة (اختياري)</param>
        /// <param name="updatedBy">من قام بالإعادة</param>
        /// <returns>true إذا تم الإعادة بنجاح</returns>
        public bool ResetSettingsToDefault(string category, string updatedBy)
        {
            try
            {
                var defaultSettings = DefaultSettings.Values;
                int resetCount = 0;

                foreach (var defaultSetting in defaultSettings)
                {
                    var settingCategory = GetCategoryFromSettingName(defaultSetting.Key);

                    if (string.IsNullOrWhiteSpace(category) || settingCategory == category)
                    {
                        if (SaveSettingValue(defaultSetting.Key, defaultSetting.Value?.ToString(), updatedBy))
                        {
                            resetCount++;
                        }
                    }
                }

                _logger?.LogInformation($"تم إعادة تعيين {resetCount} إعداد إلى القيم الافتراضية");
                return resetCount > 0;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إعادة تعيين الإعدادات");
                return false;
            }
        }

        /// <summary>
        /// الحصول على الفئة من اسم الإعداد
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <returns>الفئة</returns>
        private string GetCategoryFromSettingName(string settingName)
        {
            if (settingName.StartsWith("System."))
                return SettingCategories.General;
            if (settingName.StartsWith("Tax."))
                return SettingCategories.Tax;
            if (settingName.StartsWith("Currency."))
                return SettingCategories.Currency;
            if (settingName.StartsWith("Store."))
                return SettingCategories.Store;
            if (settingName.StartsWith("Print."))
                return SettingCategories.Print;

            return SettingCategories.General;
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// التحقق من وجود إعداد
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <returns>true إذا كان الإعداد موجود</returns>
        public bool SettingExists(string settingName)
        {
            try
            {
                var setting = _settingsDAL.GetSetting(settingName);
                return setting != null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في التحقق من وجود الإعداد {settingName}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على جميع الإعدادات حسب الفئة
        /// </summary>
        /// <param name="category">الفئة</param>
        /// <returns>قائمة الإعدادات</returns>
        public List<SystemSettings> GetSettingsByCategory(string category)
        {
            try
            {
                return _settingsDAL.GetSettingsByCategory(category);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على إعدادات الفئة {category}");
                return new List<SystemSettings>();
            }
        }

        /// <summary>
        /// البحث في الإعدادات
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة الإعدادات المطابقة</returns>
        public List<SystemSettings> SearchSettings(string searchTerm)
        {
            try
            {
                var allSettings = _settingsDAL.GetAllSettings();

                return allSettings.Where(s =>
                    s.SettingName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    (s.Description != null && s.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                    (s.SettingValue != null && s.SettingValue.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                ).ToList();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في البحث في الإعدادات بالمصطلح {searchTerm}");
                return new List<SystemSettings>();
            }
        }

        /// <summary>
        /// الحصول على إحصائيات الإعدادات
        /// </summary>
        /// <returns>إحصائيات الإعدادات</returns>
        public SettingsStatistics GetSettingsStatistics()
        {
            try
            {
                var allSettings = _settingsDAL.GetAllSettings();

                return new SettingsStatistics
                {
                    TotalSettings = allSettings.Count,
                    SettingsByCategory = allSettings.GroupBy(s => s.Category)
                        .ToDictionary(g => g.Key, g => g.Count()),
                    RequiredSettings = allSettings.Count(s => s.IsRequired),
                    EditableSettings = allSettings.Count(s => s.IsEditable),
                    LastUpdated = allSettings.Max(s => s.LastUpdated)
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إحصائيات الإعدادات");
                return new SettingsStatistics();
            }
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// ملخص الإعدادات
    /// </summary>
    public class SettingsSummary
    {
        public string StoreName { get; set; }
        public string DefaultCurrency { get; set; }
        public string CurrencySymbol { get; set; }
        public bool TaxEnabled { get; set; }
        public decimal DefaultTaxRate { get; set; }
        public string PrintSize { get; set; }
        public bool AutoPrint { get; set; }
        public bool HasStoreLogo { get; set; }
        public bool SupportMultipleCurrencies { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// إحصائيات الإعدادات
    /// </summary>
    public class SettingsStatistics
    {
        public int TotalSettings { get; set; }
        public Dictionary<string, int> SettingsByCategory { get; set; } = new Dictionary<string, int>();
        public int RequiredSettings { get; set; }
        public int EditableSettings { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    #endregion
}
