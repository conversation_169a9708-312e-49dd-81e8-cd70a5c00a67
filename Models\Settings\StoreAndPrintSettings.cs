using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Drawing;
using System.IO;

namespace AredooPOS.Models.Settings
{
    /// <summary>
    /// نموذج إعدادات المتجر
    /// يحتوي على جميع المعلومات الأساسية للمتجر
    /// </summary>
    public class StoreSettings
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم إعداد المتجر
        /// </summary>
        public int StoreSettingID { get; set; }

        /// <summary>
        /// اسم المتجر
        /// </summary>
        [Required]
        [StringLength(200)]
        public string StoreName { get; set; }

        /// <summary>
        /// اسم المتجر بالإنجليزية
        /// </summary>
        [StringLength(200)]
        public string StoreNameEnglish { get; set; }

        /// <summary>
        /// وصف المتجر
        /// </summary>
        [StringLength(1000)]
        public string StoreDescription { get; set; }

        /// <summary>
        /// شعار المتجر (مسار الملف)
        /// </summary>
        [StringLength(500)]
        public string LogoPath { get; set; }

        /// <summary>
        /// شعار المتجر (البيانات)
        /// </summary>
        public byte[] LogoData { get; set; }

        /// <summary>
        /// رقم الهاتف الرئيسي
        /// </summary>
        [StringLength(20)]
        public string PrimaryPhone { get; set; }

        /// <summary>
        /// رقم الهاتف الثانوي
        /// </summary>
        [StringLength(20)]
        public string SecondaryPhone { get; set; }

        /// <summary>
        /// رقم الفاكس
        /// </summary>
        [StringLength(20)]
        public string FaxNumber { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        [StringLength(100)]
        [EmailAddress]
        public string Email { get; set; }

        /// <summary>
        /// الموقع الإلكتروني
        /// </summary>
        [StringLength(200)]
        [Url]
        public string Website { get; set; }

        /// <summary>
        /// العنوان الكامل
        /// </summary>
        [StringLength(500)]
        public string Address { get; set; }

        /// <summary>
        /// المدينة
        /// </summary>
        [StringLength(100)]
        public string City { get; set; }

        /// <summary>
        /// المحافظة/الولاية
        /// </summary>
        [StringLength(100)]
        public string State { get; set; }

        /// <summary>
        /// الرمز البريدي
        /// </summary>
        [StringLength(20)]
        public string PostalCode { get; set; }

        /// <summary>
        /// البلد
        /// </summary>
        [StringLength(100)]
        public string Country { get; set; } = "العراق";

        /// <summary>
        /// رقم السجل التجاري
        /// </summary>
        [StringLength(50)]
        public string CommercialRegistrationNumber { get; set; }

        /// <summary>
        /// رقم الترخيص
        /// </summary>
        [StringLength(50)]
        public string LicenseNumber { get; set; }

        /// <summary>
        /// رقم التسجيل الضريبي
        /// </summary>
        [StringLength(50)]
        public string TaxRegistrationNumber { get; set; }

        /// <summary>
        /// نوع النشاط التجاري
        /// </summary>
        [StringLength(200)]
        public string BusinessType { get; set; }

        /// <summary>
        /// تاريخ تأسيس المتجر
        /// </summary>
        public DateTime? EstablishedDate { get; set; }

        /// <summary>
        /// ساعات العمل
        /// </summary>
        [StringLength(500)]
        public string WorkingHours { get; set; }

        /// <summary>
        /// معلومات إضافية
        /// </summary>
        [StringLength(1000)]
        public string AdditionalInfo { get; set; }

        #endregion

        #region إعدادات التواصل الاجتماعي

        /// <summary>
        /// رابط فيسبوك
        /// </summary>
        [StringLength(200)]
        public string FacebookUrl { get; set; }

        /// <summary>
        /// رابط تويتر
        /// </summary>
        [StringLength(200)]
        public string TwitterUrl { get; set; }

        /// <summary>
        /// رابط إنستغرام
        /// </summary>
        [StringLength(200)]
        public string InstagramUrl { get; set; }

        /// <summary>
        /// رقم واتساب
        /// </summary>
        [StringLength(20)]
        public string WhatsAppNumber { get; set; }

        /// <summary>
        /// رابط تليجرام
        /// </summary>
        [StringLength(200)]
        public string TelegramUrl { get; set; }

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// العنوان الكامل منسق
        /// </summary>
        public string FormattedAddress
        {
            get
            {
                var parts = new List<string>();
                
                if (!string.IsNullOrWhiteSpace(Address))
                    parts.Add(Address);
                
                if (!string.IsNullOrWhiteSpace(City))
                    parts.Add(City);
                
                if (!string.IsNullOrWhiteSpace(State))
                    parts.Add(State);
                
                if (!string.IsNullOrWhiteSpace(Country))
                    parts.Add(Country);
                
                return string.Join(", ", parts);
            }
        }

        /// <summary>
        /// معلومات الاتصال منسقة
        /// </summary>
        public string FormattedContactInfo
        {
            get
            {
                var parts = new List<string>();
                
                if (!string.IsNullOrWhiteSpace(PrimaryPhone))
                    parts.Add($"هاتف: {PrimaryPhone}");
                
                if (!string.IsNullOrWhiteSpace(Email))
                    parts.Add($"إيميل: {Email}");
                
                if (!string.IsNullOrWhiteSpace(Website))
                    parts.Add($"موقع: {Website}");
                
                return string.Join(" | ", parts);
            }
        }

        /// <summary>
        /// هل يوجد شعار
        /// </summary>
        public bool HasLogo => LogoData != null && LogoData.Length > 0;

        #endregion

        #region الدوال المساعدة

        /// <summary>
        /// تحميل الشعار من ملف
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        public void LoadLogoFromFile(string filePath)
        {
            if (File.Exists(filePath))
            {
                LogoData = File.ReadAllBytes(filePath);
                LogoPath = filePath;
            }
        }

        /// <summary>
        /// حفظ الشعار إلى ملف
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        public void SaveLogoToFile(string filePath)
        {
            if (HasLogo)
            {
                File.WriteAllBytes(filePath, LogoData);
                LogoPath = filePath;
            }
        }

        /// <summary>
        /// الحصول على الشعار كصورة
        /// </summary>
        /// <returns>الصورة</returns>
        public Image GetLogoAsImage()
        {
            if (!HasLogo)
                return null;

            try
            {
                using var ms = new MemoryStream(LogoData);
                return Image.FromStream(ms);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// تعيين الشعار من صورة
        /// </summary>
        /// <param name="image">الصورة</param>
        public void SetLogoFromImage(Image image)
        {
            if (image == null)
            {
                LogoData = null;
                return;
            }

            try
            {
                using var ms = new MemoryStream();
                image.Save(ms, System.Drawing.Imaging.ImageFormat.Png);
                LogoData = ms.ToArray();
            }
            catch
            {
                LogoData = null;
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            if (string.IsNullOrWhiteSpace(StoreName))
                return false;

            if (!string.IsNullOrWhiteSpace(Email) && !IsValidEmail(Email))
                return false;

            if (!string.IsNullOrWhiteSpace(Website) && !IsValidUrl(Website))
                return false;

            return true;
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        /// <param name="email">البريد الإلكتروني</param>
        /// <returns>true إذا كان صحيح</returns>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة الرابط
        /// </summary>
        /// <param name="url">الرابط</param>
        /// <returns>true إذا كان صحيح</returns>
        private bool IsValidUrl(string url)
        {
            return Uri.TryCreate(url, UriKind.Absolute, out _);
        }

        #endregion
    }

    /// <summary>
    /// نموذج إعدادات الطباعة
    /// يحتوي على جميع الإعدادات المتعلقة بالطباعة
    /// </summary>
    public class PrintSettings
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم إعداد الطباعة
        /// </summary>
        public int PrintSettingID { get; set; }

        /// <summary>
        /// حجم الطباعة الافتراضي
        /// </summary>
        [Required]
        [StringLength(20)]
        public string DefaultPrintSize { get; set; } = "Thermal"; // Thermal, A4, A5, Custom

        /// <summary>
        /// اسم الطابعة الافتراضية
        /// </summary>
        [StringLength(200)]
        public string DefaultPrinterName { get; set; }

        /// <summary>
        /// طباعة تلقائية
        /// </summary>
        public bool AutoPrint { get; set; } = false;

        /// <summary>
        /// عدد النسخ الافتراضي
        /// </summary>
        [Range(1, 10)]
        public int DefaultCopies { get; set; } = 1;

        /// <summary>
        /// عرض الورقة (بالمليمتر)
        /// </summary>
        public int PaperWidth { get; set; } = 80; // للطباعة الحرارية

        /// <summary>
        /// ارتفاع الورقة (بالمليمتر)
        /// </summary>
        public int PaperHeight { get; set; } = 0; // 0 = مستمر

        /// <summary>
        /// الهوامش اليسرى
        /// </summary>
        public int LeftMargin { get; set; } = 5;

        /// <summary>
        /// الهوامش اليمنى
        /// </summary>
        public int RightMargin { get; set; } = 5;

        /// <summary>
        /// الهوامش العلوية
        /// </summary>
        public int TopMargin { get; set; } = 10;

        /// <summary>
        /// الهوامش السفلية
        /// </summary>
        public int BottomMargin { get; set; } = 10;

        #endregion

        #region إعدادات الخط

        /// <summary>
        /// اسم الخط
        /// </summary>
        [StringLength(100)]
        public string FontName { get; set; } = "Arial";

        /// <summary>
        /// حجم الخط الافتراضي
        /// </summary>
        [Range(6, 72)]
        public int DefaultFontSize { get; set; } = 10;

        /// <summary>
        /// حجم خط العنوان
        /// </summary>
        [Range(8, 72)]
        public int HeaderFontSize { get; set; } = 14;

        /// <summary>
        /// حجم خط التفاصيل
        /// </summary>
        [Range(6, 72)]
        public int DetailsFontSize { get; set; } = 9;

        /// <summary>
        /// حجم خط الإجمالي
        /// </summary>
        [Range(8, 72)]
        public int TotalFontSize { get; set; } = 12;

        /// <summary>
        /// خط عريض للعناوين
        /// </summary>
        public bool BoldHeaders { get; set; } = true;

        /// <summary>
        /// خط عريض للإجماليات
        /// </summary>
        public bool BoldTotals { get; set; } = true;

        #endregion

        #region إعدادات المحتوى

        /// <summary>
        /// طباعة شعار المتجر
        /// </summary>
        public bool PrintStoreLogo { get; set; } = true;

        /// <summary>
        /// طباعة معلومات المتجر
        /// </summary>
        public bool PrintStoreInfo { get; set; } = true;

        /// <summary>
        /// طباعة تاريخ ووقت الفاتورة
        /// </summary>
        public bool PrintDateTime { get; set; } = true;

        /// <summary>
        /// طباعة رقم الفاتورة
        /// </summary>
        public bool PrintInvoiceNumber { get; set; } = true;

        /// <summary>
        /// طباعة معلومات العميل
        /// </summary>
        public bool PrintCustomerInfo { get; set; } = true;

        /// <summary>
        /// طباعة تفاصيل المنتجات
        /// </summary>
        public bool PrintProductDetails { get; set; } = true;

        /// <summary>
        /// طباعة الأسعار
        /// </summary>
        public bool PrintPrices { get; set; } = true;

        /// <summary>
        /// طباعة الضريبة
        /// </summary>
        public bool PrintTax { get; set; } = true;

        /// <summary>
        /// طباعة الخصم
        /// </summary>
        public bool PrintDiscount { get; set; } = true;

        /// <summary>
        /// طباعة الإجمالي
        /// </summary>
        public bool PrintTotal { get; set; } = true;

        /// <summary>
        /// طباعة طريقة الدفع
        /// </summary>
        public bool PrintPaymentMethod { get; set; } = true;

        /// <summary>
        /// طباعة اسم المستخدم
        /// </summary>
        public bool PrintUserName { get; set; } = false;

        /// <summary>
        /// طباعة رسالة شكر
        /// </summary>
        public bool PrintThankYouMessage { get; set; } = true;

        /// <summary>
        /// رسالة الشكر المخصصة
        /// </summary>
        [StringLength(500)]
        public string CustomThankYouMessage { get; set; } = "شكراً لزيارتكم";

        /// <summary>
        /// طباعة الباركود
        /// </summary>
        public bool PrintBarcode { get; set; } = false;

        /// <summary>
        /// طباعة QR Code
        /// </summary>
        public bool PrintQRCode { get; set; } = false;

        #endregion

        #region إعدادات التخطيط

        /// <summary>
        /// محاذاة العنوان
        /// </summary>
        [StringLength(10)]
        public string HeaderAlignment { get; set; } = "Center"; // Left, Center, Right

        /// <summary>
        /// محاذاة التفاصيل
        /// </summary>
        [StringLength(10)]
        public string DetailsAlignment { get; set; } = "Left";

        /// <summary>
        /// محاذاة الإجمالي
        /// </summary>
        [StringLength(10)]
        public string TotalAlignment { get; set; } = "Right";

        /// <summary>
        /// عدد الأعمدة للمنتجات
        /// </summary>
        [Range(1, 6)]
        public int ProductColumnsCount { get; set; } = 4;

        /// <summary>
        /// عرض عمود الكمية
        /// </summary>
        [Range(10, 100)]
        public int QuantityColumnWidth { get; set; } = 15;

        /// <summary>
        /// عرض عمود السعر
        /// </summary>
        [Range(10, 100)]
        public int PriceColumnWidth { get; set; } = 20;

        /// <summary>
        /// عرض عمود الإجمالي
        /// </summary>
        [Range(10, 100)]
        public int TotalColumnWidth { get; set; } = 25;

        /// <summary>
        /// المسافة بين الأسطر
        /// </summary>
        [Range(1, 10)]
        public int LineSpacing { get; set; } = 1;

        /// <summary>
        /// المسافة بين الأقسام
        /// </summary>
        [Range(1, 20)]
        public int SectionSpacing { get; set; } = 3;

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// هل الطباعة حرارية
        /// </summary>
        public bool IsThermalPrint => DefaultPrintSize == "Thermal";

        /// <summary>
        /// العرض الفعلي للطباعة
        /// </summary>
        public int EffectivePrintWidth => PaperWidth - LeftMargin - RightMargin;

        /// <summary>
        /// عرض عمود اسم المنتج
        /// </summary>
        public int ProductNameColumnWidth => 100 - QuantityColumnWidth - PriceColumnWidth - TotalColumnWidth;

        #endregion

        #region الدوال المساعدة

        /// <summary>
        /// الحصول على إعدادات الخط
        /// </summary>
        /// <param name="fontType">نوع الخط</param>
        /// <returns>إعدادات الخط</returns>
        public Font GetFont(string fontType)
        {
            var fontSize = fontType switch
            {
                "Header" => HeaderFontSize,
                "Details" => DetailsFontSize,
                "Total" => TotalFontSize,
                _ => DefaultFontSize
            };

            var fontStyle = FontStyle.Regular;
            if ((fontType == "Header" && BoldHeaders) || (fontType == "Total" && BoldTotals))
                fontStyle = FontStyle.Bold;

            return new Font(FontName, fontSize, fontStyle);
        }

        /// <summary>
        /// الحصول على محاذاة النص
        /// </summary>
        /// <param name="alignmentType">نوع المحاذاة</param>
        /// <returns>محاذاة النص</returns>
        public StringAlignment GetAlignment(string alignmentType)
        {
            var alignment = alignmentType switch
            {
                "Header" => HeaderAlignment,
                "Details" => DetailsAlignment,
                "Total" => TotalAlignment,
                _ => "Left"
            };

            return alignment switch
            {
                "Center" => StringAlignment.Center,
                "Right" => StringAlignment.Far,
                _ => StringAlignment.Near
            };
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            if (string.IsNullOrWhiteSpace(DefaultPrintSize))
                return false;

            if (!new[] { "Thermal", "A4", "A5", "Custom" }.Contains(DefaultPrintSize))
                return false;

            if (DefaultCopies < 1 || DefaultCopies > 10)
                return false;

            if (PaperWidth < 50 || PaperWidth > 300)
                return false;

            return true;
        }

        /// <summary>
        /// تطبيق الإعدادات الافتراضية للطباعة الحرارية
        /// </summary>
        public void ApplyThermalDefaults()
        {
            DefaultPrintSize = "Thermal";
            PaperWidth = 80;
            PaperHeight = 0;
            LeftMargin = 2;
            RightMargin = 2;
            TopMargin = 5;
            BottomMargin = 5;
            DefaultFontSize = 9;
            HeaderFontSize = 12;
            DetailsFontSize = 8;
            TotalFontSize = 10;
        }

        /// <summary>
        /// تطبيق الإعدادات الافتراضية للطباعة A4
        /// </summary>
        public void ApplyA4Defaults()
        {
            DefaultPrintSize = "A4";
            PaperWidth = 210;
            PaperHeight = 297;
            LeftMargin = 20;
            RightMargin = 20;
            TopMargin = 25;
            BottomMargin = 25;
            DefaultFontSize = 10;
            HeaderFontSize = 14;
            DetailsFontSize = 9;
            TotalFontSize = 12;
        }

        #endregion
    }

    /// <summary>
    /// أحجام الطباعة المدعومة
    /// </summary>
    public static class PrintSizes
    {
        public const string Thermal = "Thermal";
        public const string A4 = "A4";
        public const string A5 = "A5";
        public const string Custom = "Custom";

        public static readonly Dictionary<string, (int Width, int Height)> Dimensions = new Dictionary<string, (int, int)>
        {
            { Thermal, (80, 0) },
            { A4, (210, 297) },
            { A5, (148, 210) },
            { Custom, (80, 0) }
        };
    }

    /// <summary>
    /// أنواع المحاذاة
    /// </summary>
    public static class TextAlignments
    {
        public const string Left = "Left";
        public const string Center = "Center";
        public const string Right = "Right";
    }
}
