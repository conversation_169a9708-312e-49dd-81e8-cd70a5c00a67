using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using AredooPOS.Models.Reports;
using AredooPOS.Services;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// لوحة تقرير المبيعات
    /// توفر واجهة شاملة لإنشاء وعرض تقارير المبيعات المختلفة
    /// </summary>
    public partial class SalesReportPanel : UserControl, IReportPanel
    {
        #region المتغيرات والخصائص الخاصة

        private readonly ReportsService _reportsService;
        private readonly string _currentUser;
        private readonly ILogger<SalesReportPanel> _logger;

        private SalesReport _currentReport;
        private bool _isGenerating = false;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ لوحة تقرير المبيعات
        /// </summary>
        /// <param name="reportsService">خدمة التقارير</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <param name="logger">مسجل الأحداث</param>
        public SalesReportPanel(ReportsService reportsService, string currentUser, ILogger<SalesReportPanel> logger = null)
        {
            InitializeComponent();
            
            _reportsService = reportsService;
            _currentUser = currentUser;
            _logger = logger;

            InitializePanel();
            SetupEventHandlers();
            LoadDefaultValues();
        }

        /// <summary>
        /// تهيئة اللوحة
        /// </summary>
        private void InitializePanel()
        {
            // تعيين النصوص العربية
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // تهيئة جدول البيانات
            InitializeDataGridView();

            // تعيين القيم الافتراضية
            dtpFromDate.Value = DateTime.Now.AddDays(-30);
            dtpToDate.Value = DateTime.Now;

            // تهيئة قائمة أنواع التقارير
            InitializeReportTypes();

            // تهيئة قائمة صيغ التصدير
            InitializeExportFormats();
        }

        /// <summary>
        /// تهيئة جدول البيانات
        /// </summary>
        private void InitializeDataGridView()
        {
            dgvSalesData.AutoGenerateColumns = false;
            dgvSalesData.AllowUserToAddRows = false;
            dgvSalesData.AllowUserToDeleteRows = false;
            dgvSalesData.ReadOnly = true;
            dgvSalesData.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvSalesData.MultiSelect = false;

            // إضافة الأعمدة الأساسية
            dgvSalesData.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Date",
                HeaderText = "التاريخ",
                DataPropertyName = "SalesDate",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy" }
            });

            dgvSalesData.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalSales",
                HeaderText = "إجمالي المبيعات",
                DataPropertyName = "TotalSales",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            });

            dgvSalesData.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "InvoiceCount",
                HeaderText = "عدد الفواتير",
                DataPropertyName = "InvoiceCount",
                Width = 100
            });

            dgvSalesData.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "AverageInvoiceValue",
                HeaderText = "متوسط الفاتورة",
                DataPropertyName = "AverageInvoiceValue",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            });

            dgvSalesData.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalDiscount",
                HeaderText = "إجمالي الخصم",
                DataPropertyName = "TotalDiscount",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            });
        }

        /// <summary>
        /// تهيئة أنواع التقارير
        /// </summary>
        private void InitializeReportTypes()
        {
            cmbReportType.Items.Clear();
            cmbReportType.Items.Add(new ComboBoxItem("يومي", SalesReportTypes.Daily));
            cmbReportType.Items.Add(new ComboBoxItem("أسبوعي", SalesReportTypes.Weekly));
            cmbReportType.Items.Add(new ComboBoxItem("شهري", SalesReportTypes.Monthly));
            cmbReportType.Items.Add(new ComboBoxItem("سنوي", SalesReportTypes.Yearly));
            cmbReportType.Items.Add(new ComboBoxItem("حسب المنتج", SalesReportTypes.ByProduct));
            cmbReportType.Items.Add(new ComboBoxItem("حسب الفئة", SalesReportTypes.ByCategory));
            cmbReportType.Items.Add(new ComboBoxItem("حسب العميل", SalesReportTypes.ByCustomer));
            cmbReportType.Items.Add(new ComboBoxItem("حسب المستخدم", SalesReportTypes.ByUser));

            cmbReportType.DisplayMember = "Text";
            cmbReportType.ValueMember = "Value";
            cmbReportType.SelectedIndex = 0;
        }

        /// <summary>
        /// تهيئة صيغ التصدير
        /// </summary>
        private void InitializeExportFormats()
        {
            cmbExportFormat.Items.Clear();
            cmbExportFormat.Items.Add(new ComboBoxItem("PDF", "PDF"));
            cmbExportFormat.Items.Add(new ComboBoxItem("Excel", "EXCEL"));
            cmbExportFormat.Items.Add(new ComboBoxItem("CSV", "CSV"));
            cmbExportFormat.Items.Add(new ComboBoxItem("HTML", "HTML"));

            cmbExportFormat.DisplayMember = "Text";
            cmbExportFormat.ValueMember = "Value";
            cmbExportFormat.SelectedIndex = 0;
        }

        /// <summary>
        /// تعيين معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            btnGenerate.Click += BtnGenerate_Click;
            btnExport.Click += BtnExport_Click;
            btnPrint.Click += BtnPrint_Click;
            btnClear.Click += BtnClear_Click;

            cmbReportType.SelectedIndexChanged += CmbReportType_SelectedIndexChanged;
            dtpFromDate.ValueChanged += DateRange_ValueChanged;
            dtpToDate.ValueChanged += DateRange_ValueChanged;

            dgvSalesData.CellDoubleClick += DgvSalesData_CellDoubleClick;
        }

        /// <summary>
        /// تحميل القيم الافتراضية
        /// </summary>
        private void LoadDefaultValues()
        {
            // تحديد التاريخ الافتراضي (آخر 30 يوم)
            dtpFromDate.Value = DateTime.Now.AddDays(-30);
            dtpToDate.Value = DateTime.Now;

            // تحديث ملخص البيانات
            UpdateSummaryLabels();
        }

        #endregion

        #region تنفيذ واجهة IReportPanel

        /// <summary>
        /// إنشاء التقرير
        /// </summary>
        public async Task GenerateReportAsync()
        {
            try
            {
                if (_isGenerating)
                {
                    MessageBox.Show("يوجد تقرير قيد الإنشاء حالياً", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                _isGenerating = true;
                UpdateButtonsState(false);

                // إنشاء طلب التقرير
                var request = CreateSalesReportRequest();

                // إنشاء التقرير
                var result = await _reportsService.GenerateSalesReportAsync(request);

                if (result.IsSuccessful)
                {
                    _currentReport = result.ReportData as SalesReport;
                    DisplayReport(_currentReport);
                    UpdateSummaryLabels();
                    
                    _logger?.LogInformation("تم إنشاء تقرير المبيعات بنجاح");
                }
                else
                {
                    MessageBox.Show($"فشل في إنشاء التقرير: {result.ErrorMessage}", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنشاء تقرير المبيعات");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isGenerating = false;
                UpdateButtonsState(true);
            }
        }

        /// <summary>
        /// تصدير التقرير
        /// </summary>
        public void ExportReport()
        {
            try
            {
                if (_currentReport == null)
                {
                    MessageBox.Show("يرجى إنشاء التقرير أولاً", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var saveDialog = new SaveFileDialog
                {
                    Title = "تصدير تقرير المبيعات",
                    Filter = GetExportFilter(),
                    FileName = $"SalesReport_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    // TODO: تنفيذ التصدير الفعلي
                    MessageBox.Show("تم تصدير التقرير بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تصدير تقرير المبيعات");
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        public void PrintReport()
        {
            try
            {
                if (_currentReport == null)
                {
                    MessageBox.Show("يرجى إنشاء التقرير أولاً", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // TODO: تنفيذ الطباعة
                MessageBox.Show("سيتم تنفيذ الطباعة قريباً", "معلومات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في طباعة تقرير المبيعات");
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث التقرير
        /// </summary>
        public void RefreshReport()
        {
            try
            {
                if (_currentReport != null)
                {
                    _ = GenerateReportAsync();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحديث تقرير المبيعات");
            }
        }

        #endregion

        #region أحداث واجهة المستخدم

        /// <summary>
        /// حدث زر إنشاء التقرير
        /// </summary>
        private async void BtnGenerate_Click(object sender, EventArgs e)
        {
            await GenerateReportAsync();
        }

        /// <summary>
        /// حدث زر تصدير التقرير
        /// </summary>
        private void BtnExport_Click(object sender, EventArgs e)
        {
            ExportReport();
        }

        /// <summary>
        /// حدث زر طباعة التقرير
        /// </summary>
        private void BtnPrint_Click(object sender, EventArgs e)
        {
            PrintReport();
        }

        /// <summary>
        /// حدث زر مسح البيانات
        /// </summary>
        private void BtnClear_Click(object sender, EventArgs e)
        {
            ClearReport();
        }

        /// <summary>
        /// حدث تغيير نوع التقرير
        /// </summary>
        private void CmbReportType_SelectedIndexChanged(object sender, EventArgs e)
        {
            // تحديث واجهة المستخدم حسب نوع التقرير
            UpdateUIForReportType();
        }

        /// <summary>
        /// حدث تغيير نطاق التاريخ
        /// </summary>
        private void DateRange_ValueChanged(object sender, EventArgs e)
        {
            // التحقق من صحة نطاق التاريخ
            ValidateDateRange();
        }

        /// <summary>
        /// حدث النقر المزدوج على جدول البيانات
        /// </summary>
        private void DgvSalesData_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                // عرض تفاصيل إضافية للصف المحدد
                ShowRowDetails(e.RowIndex);
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// إنشاء طلب تقرير المبيعات
        /// </summary>
        /// <returns>طلب التقرير</returns>
        private SalesReportRequest CreateSalesReportRequest()
        {
            var selectedType = cmbReportType.SelectedItem as ComboBoxItem;
            var selectedFormat = cmbExportFormat.SelectedItem as ComboBoxItem;

            return new SalesReportRequest
            {
                FromDate = dtpFromDate.Value.Date,
                ToDate = dtpToDate.Value.Date,
                ReportType = selectedType?.Value?.ToString() ?? SalesReportTypes.Daily,
                ExportFormat = selectedFormat?.Value?.ToString() ?? "PDF",
                FileName = $"SalesReport_{DateTime.Now:yyyyMMdd_HHmmss}",
                GeneratedBy = _currentUser,
                IncludeReturns = chkIncludeReturns.Checked,
                IncludeDiscounts = chkIncludeDiscounts.Checked
            };
        }

        /// <summary>
        /// عرض التقرير
        /// </summary>
        /// <param name="report">تقرير المبيعات</param>
        private void DisplayReport(SalesReport report)
        {
            if (report == null) return;

            // عرض البيانات حسب نوع التقرير
            var reportType = cmbReportType.SelectedItem as ComboBoxItem;
            switch (reportType?.Value?.ToString())
            {
                case SalesReportTypes.Daily:
                    dgvSalesData.DataSource = report.DailySalesDetails;
                    break;
                case SalesReportTypes.ByProduct:
                    dgvSalesData.DataSource = report.ProductSalesDetails;
                    break;
                case SalesReportTypes.ByCategory:
                    dgvSalesData.DataSource = report.CategorySalesDetails;
                    break;
                case SalesReportTypes.ByCustomer:
                    dgvSalesData.DataSource = report.CustomerSalesDetails;
                    break;
                default:
                    dgvSalesData.DataSource = report.DailySalesDetails;
                    break;
            }
        }

        /// <summary>
        /// تحديث تسميات الملخص
        /// </summary>
        private void UpdateSummaryLabels()
        {
            if (_currentReport != null)
            {
                lblTotalSales.Text = $"إجمالي المبيعات: {_currentReport.TotalSales:C}";
                lblTotalInvoices.Text = $"عدد الفواتير: {_currentReport.TotalInvoices}";
                lblAverageInvoice.Text = $"متوسط الفاتورة: {_currentReport.AverageInvoiceValue:C}";
                lblTotalDiscount.Text = $"إجمالي الخصم: {_currentReport.TotalDiscounts:C}";
                lblNetSales.Text = $"صافي المبيعات: {_currentReport.NetSales:C}";
            }
            else
            {
                lblTotalSales.Text = "إجمالي المبيعات: 0.00 ر.س";
                lblTotalInvoices.Text = "عدد الفواتير: 0";
                lblAverageInvoice.Text = "متوسط الفاتورة: 0.00 ر.س";
                lblTotalDiscount.Text = "إجمالي الخصم: 0.00 ر.س";
                lblNetSales.Text = "صافي المبيعات: 0.00 ر.س";
            }
        }

        /// <summary>
        /// تحديث حالة الأزرار
        /// </summary>
        /// <param name="enabled">تفعيل أم لا</param>
        private void UpdateButtonsState(bool enabled)
        {
            btnGenerate.Enabled = enabled;
            btnExport.Enabled = enabled && _currentReport != null;
            btnPrint.Enabled = enabled && _currentReport != null;
        }

        /// <summary>
        /// مسح التقرير
        /// </summary>
        private void ClearReport()
        {
            _currentReport = null;
            dgvSalesData.DataSource = null;
            UpdateSummaryLabels();
            UpdateButtonsState(true);
        }

        /// <summary>
        /// تحديث واجهة المستخدم حسب نوع التقرير
        /// </summary>
        private void UpdateUIForReportType()
        {
            // تخصيص الواجهة حسب نوع التقرير المحدد
            // يمكن إضافة عناصر تحكم إضافية حسب الحاجة
        }

        /// <summary>
        /// التحقق من صحة نطاق التاريخ
        /// </summary>
        private void ValidateDateRange()
        {
            if (dtpFromDate.Value > dtpToDate.Value)
            {
                MessageBox.Show("تاريخ البداية يجب أن يكون أقل من أو يساوي تاريخ النهاية", "خطأ في التاريخ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                dtpFromDate.Value = dtpToDate.Value.AddDays(-1);
            }
        }

        /// <summary>
        /// عرض تفاصيل الصف
        /// </summary>
        /// <param name="rowIndex">رقم الصف</param>
        private void ShowRowDetails(int rowIndex)
        {
            // TODO: عرض نموذج تفاصيل إضافية
        }

        /// <summary>
        /// الحصول على فلتر التصدير
        /// </summary>
        /// <returns>فلتر التصدير</returns>
        private string GetExportFilter()
        {
            return "PDF Files|*.pdf|Excel Files|*.xlsx|CSV Files|*.csv|HTML Files|*.html";
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// عنصر قائمة منسدلة
    /// </summary>
    public class ComboBoxItem
    {
        public string Text { get; set; }
        public object Value { get; set; }

        public ComboBoxItem(string text, object value)
        {
            Text = text;
            Value = value;
        }

        public override string ToString()
        {
            return Text;
        }
    }

    #endregion
}
