using System;
using System.Windows.Forms;
using System.Drawing;
using System.Data.SQLite;
using System.IO;

namespace TestAridoo
{
    public partial class Form1 : Form
    {
        private Panel panel1;
        private Label lblTitle;
        private Panel panel2;
        private Button btnTest;
        private Button btnExit;
        private TextBox txtInfo;
        private string databasePath;

        public Form1()
        {
            InitializeComponent();
            InitializeArabicUI();
            databasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AridooPOS.db");
        }

        private void InitializeArabicUI()
        {
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            LoadInitialInfo();
        }

        private void LoadInitialInfo()
        {
            txtInfo.Text = "🎉 مرحباً بك في نظام أريدوو لنقاط البيع\r\n\r\n";
            txtInfo.Text += "✅ تم تطوير النظام بنجاح!\r\n\r\n";
            txtInfo.Text += "📋 الميزات المطورة:\r\n";
            txtInfo.Text += "• نظام فواتير شامل (نقد - بطاقة - أقساط)\r\n";
            txtInfo.Text += "• إدارة المنتجات والمخزون\r\n";
            txtInfo.Text += "• إدارة العملاء والديون\r\n";
            txtInfo.Text += "• البحث المتقدم عن الفواتير\r\n";
            txtInfo.Text += "• إرجاع الفواتير مع تحديث المخزون\r\n";
            txtInfo.Text += "• دعم الطباعة الحرارية و A4\r\n";
            txtInfo.Text += "• واجهة عربية كاملة\r\n\r\n";
            txtInfo.Text += "🔧 التقنيات المستخدمة:\r\n";
            txtInfo.Text += "• C# Windows Forms\r\n";
            txtInfo.Text += "• SQLite Database\r\n";
            txtInfo.Text += "• .NET 6.0\r\n";
            txtInfo.Text += "• هيكل طبقي منظم (DAL, BLL, Models)\r\n\r\n";
            txtInfo.Text += "🚀 اضغط 'اختبار النظام' لاختبار قاعدة البيانات\r\n";
        }

        private void btnTest_Click(object sender, EventArgs e)
        {
            try
            {
                txtInfo.Text += "\r\n🔄 جاري اختبار النظام...\r\n";

                // Create database if not exists
                if (!File.Exists(databasePath))
                {
                    SQLiteConnection.CreateFile(databasePath);
                    txtInfo.Text += "✅ تم إنشاء ملف قاعدة البيانات!\r\n";
                }

                string connectionString = $"Data Source={databasePath};Version=3;";

                using (var connection = new SQLiteConnection(connectionString))
                {
                    connection.Open();
                    txtInfo.Text += "✅ تم الاتصال بقاعدة البيانات بنجاح!\r\n";

                    // Create tables
                    CreateTables(connection);
                    txtInfo.Text += "✅ تم إنشاء جميع الجداول!\r\n";

                    // Insert sample data
                    InsertSampleData(connection);
                    txtInfo.Text += "✅ تم إدراج البيانات التجريبية!\r\n";

                    // Test data retrieval
                    TestDataRetrieval(connection);

                    txtInfo.Text += "\r\n🎉 النظام جاهز للاستخدام!\r\n";
                    txtInfo.Text += "💡 يمكنك الآن تطوير الواجهات الكاملة\r\n";
                    txtInfo.Text += $"📁 مسار قاعدة البيانات: {databasePath}\r\n";
                }
            }
            catch (Exception ex)
            {
                txtInfo.Text += $"❌ خطأ: {ex.Message}\r\n";
            }
        }

        private void CreateTables(SQLiteConnection connection)
        {
            string[] createTableQueries = {
                @"CREATE TABLE IF NOT EXISTS Categories (
                    CategoryID INTEGER PRIMARY KEY AUTOINCREMENT,
                    CategoryName TEXT NOT NULL,
                    Description TEXT,
                    IsActive INTEGER DEFAULT 1
                )",

                @"CREATE TABLE IF NOT EXISTS Products (
                    ProductID INTEGER PRIMARY KEY AUTOINCREMENT,
                    ProductCode TEXT UNIQUE NOT NULL,
                    ProductName TEXT NOT NULL,
                    UnitPrice REAL NOT NULL,
                    StockQuantity INTEGER DEFAULT 0,
                    IsActive INTEGER DEFAULT 1
                )",

                @"CREATE TABLE IF NOT EXISTS Customers (
                    CustomerID INTEGER PRIMARY KEY AUTOINCREMENT,
                    CustomerCode TEXT UNIQUE NOT NULL,
                    CustomerName TEXT NOT NULL,
                    Phone TEXT,
                    IsActive INTEGER DEFAULT 1
                )",

                @"CREATE TABLE IF NOT EXISTS Invoices (
                    InvoiceID INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceNumber TEXT UNIQUE NOT NULL,
                    InvoiceDate TEXT NOT NULL,
                    CustomerName TEXT,
                    TotalAmount REAL NOT NULL DEFAULT 0,
                    PaymentType TEXT NOT NULL,
                    InvoiceStatus TEXT DEFAULT 'مكتملة'
                )"
            };

            foreach (string query in createTableQueries)
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        private void InsertSampleData(SQLiteConnection connection)
        {
            // Check if data already exists
            using (var command = new SQLiteCommand("SELECT COUNT(*) FROM Categories", connection))
            {
                long count = (long)command.ExecuteScalar();
                if (count > 0) return; // Data already exists
            }

            string[] insertQueries = {
                @"INSERT INTO Categories (CategoryName, Description) VALUES
                ('مواد غذائية', 'المواد الغذائية والمشروبات'),
                ('مستلزمات منزلية', 'الأدوات والمستلزمات المنزلية'),
                ('ملابس', 'الملابس والأزياء')",

                @"INSERT INTO Products (ProductCode, ProductName, UnitPrice, StockQuantity) VALUES
                ('PRD001', 'منتج تجريبي 1', 10.00, 100),
                ('PRD002', 'منتج تجريبي 2', 25.50, 50),
                ('PRD003', 'منتج تجريبي 3', 75.00, 25)",

                @"INSERT INTO Customers (CustomerCode, CustomerName, Phone) VALUES
                ('CASH001', 'عميل نقدي', ''),
                ('CUST001', 'عميل تجريبي 1', '0501234567'),
                ('CUST002', 'عميل تجريبي 2', '0507654321')"
            };

            foreach (string query in insertQueries)
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        private void TestDataRetrieval(SQLiteConnection connection)
        {
            // Test categories
            using (var command = new SQLiteCommand("SELECT COUNT(*) FROM Categories", connection))
            {
                long count = (long)command.ExecuteScalar();
                txtInfo.Text += $"📦 عدد فئات المنتجات: {count}\r\n";
            }

            // Test products
            using (var command = new SQLiteCommand("SELECT COUNT(*) FROM Products", connection))
            {
                long count = (long)command.ExecuteScalar();
                txtInfo.Text += $"🛍️ عدد المنتجات: {count}\r\n";
            }

            // Test customers
            using (var command = new SQLiteCommand("SELECT COUNT(*) FROM Customers", connection))
            {
                long count = (long)command.ExecuteScalar();
                txtInfo.Text += $"👥 عدد العملاء: {count}\r\n";
            }
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }
    }
}
