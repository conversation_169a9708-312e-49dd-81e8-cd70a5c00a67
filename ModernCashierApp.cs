using System;
using System.Drawing;
using System.Globalization;
using System.Threading;
using System.Windows.Forms;
using AredooCashier.UI;

namespace AredooCashier
{
    /// <summary>
    /// التطبيق الرئيسي الحديث والبسيط
    /// </summary>
    public partial class ModernCashierApp : Form
    {
        #region المتغيرات

        private Panel _topPanel;
        private Panel _sidePanel;
        private Panel _contentPanel;
        private Label _titleLabel;
        private Label _timeLabel;
        private System.Windows.Forms.Timer _clockTimer;
        private UserControl _currentView;

        // الواجهات
        private ModernDashboardView _dashboardView;
        private ModernInvoiceView _invoiceView;
        private ModernCustomersView _customersView;

        #endregion

        #region البناء والتهيئة

        public ModernCashierApp()
        {
            InitializeComponent();
            SetupArabicCulture();
            CreateViews();
            ShowDashboard();
            StartClock();
        }

        private void InitializeComponent()
        {
            // إعدادات النموذج
            Text = "أريدو الكاشير الحديث";
            Size = new Size(1200, 800);
            StartPosition = FormStartPosition.CenterScreen;
            WindowState = FormWindowState.Maximized;
            BackColor = ModernDesign.Colors.Background;
            Font = ModernDesign.Fonts.Body;
            RightToLeft = RightToLeft.Yes;
            MinimumSize = new Size(1024, 768);

            CreateLayout();
        }

        private void CreateLayout()
        {
            // الشريط العلوي
            _topPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = ModernDesign.Colors.Primary
            };

            // الشريط الجانبي
            _sidePanel = new Panel
            {
                Width = 250,
                Dock = DockStyle.Right,
                BackColor = ModernDesign.Colors.Surface
            };

            // منطقة المحتوى
            _contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = ModernDesign.Colors.Background,
                Padding = new Padding(ModernDesign.Spacing.Medium)
            };

            CreateTopBar();
            CreateSidebar();

            Controls.Add(_contentPanel);
            Controls.Add(_sidePanel);
            Controls.Add(_topPanel);
        }

        private void CreateTopBar()
        {
            // عنوان التطبيق
            _titleLabel = new Label
            {
                Text = "🏪 أريدو الكاشير الحديث",
                Font = ModernDesign.Fonts.Heading,
                ForeColor = Color.White,
                Location = new Point(20, 15),
                AutoSize = true,
                RightToLeft = RightToLeft.Yes
            };

            // الساعة
            _timeLabel = new Label
            {
                Text = DateTime.Now.ToString("HH:mm:ss"),
                Font = ModernDesign.Fonts.Body,
                ForeColor = Color.White,
                Location = new Point(_topPanel.Width - 120, 20),
                Size = new Size(100, 20),
                TextAlign = ContentAlignment.MiddleLeft,
                Anchor = AnchorStyles.Top | AnchorStyles.Left
            };

            _topPanel.Controls.Add(_titleLabel);
            _topPanel.Controls.Add(_timeLabel);
        }

        private void CreateSidebar()
        {
            var yPosition = ModernDesign.Spacing.Large;

            // عنوان القائمة
            var menuTitle = ModernDesign.CreateModernLabel(
                "القائمة الرئيسية",
                ModernDesign.Fonts.Subheading,
                ModernDesign.Colors.TextPrimary
            );
            menuTitle.Size = new Size(_sidePanel.Width - 40, 30);
            menuTitle.Location = new Point(20, yPosition);
            _sidePanel.Controls.Add(menuTitle);
            yPosition += 50;

            // أزرار القائمة
            var menuItems = new[]
            {
                new { Text = "📊 لوحة المعلومات", Action = new Action(ShowDashboard) },
                new { Text = "💰 إنشاء فاتورة", Action = new Action(ShowInvoice) },
                new { Text = "👥 إدارة العملاء", Action = new Action(ShowCustomers) },
                new { Text = "📦 إدارة المنتجات", Action = new Action(ShowProducts) },
                new { Text = "📊 التقارير", Action = new Action(ShowReports) },
                new { Text = "⚙️ الإعدادات", Action = new Action(ShowSettings) }
            };

            foreach (var item in menuItems)
            {
                var button = ModernDesign.CreateModernButton(
                    item.Text,
                    ModernDesign.Colors.Surface,
                    ModernDesign.Colors.TextPrimary,
                    (s, e) => item.Action()
                );
                button.Size = new Size(_sidePanel.Width - 40, ModernDesign.Sizes.ButtonHeight);
                button.Location = new Point(20, yPosition);
                button.TextAlign = ContentAlignment.MiddleRight;
                button.FlatAppearance.MouseOverBackColor = ModernDesign.Colors.Hover;
                _sidePanel.Controls.Add(button);
                yPosition += ModernDesign.Sizes.ButtonHeight + ModernDesign.Spacing.Small;
            }

            // رسم حدود الشريط الجانبي
            _sidePanel.Paint += (s, e) =>
            {
                using (var pen = new Pen(ModernDesign.Colors.Border, 1))
                {
                    e.Graphics.DrawLine(pen, 0, 0, 0, _sidePanel.Height);
                }
            };
        }

        private void SetupArabicCulture()
        {
            try
            {
                var culture = new CultureInfo("ar-SA");
                Thread.CurrentThread.CurrentCulture = culture;
                Thread.CurrentThread.CurrentUICulture = culture;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"تحذير: {ex.Message}");
            }
        }

        private void CreateViews()
        {
            _dashboardView = new ModernDashboardView();
            _invoiceView = new ModernInvoiceView();
            _customersView = new ModernCustomersView();
        }

        private void StartClock()
        {
            _clockTimer = new System.Windows.Forms.Timer { Interval = 1000 };
            _clockTimer.Tick += (s, e) => _timeLabel.Text = DateTime.Now.ToString("HH:mm:ss");
            _clockTimer.Start();
        }

        #endregion

        #region التنقل بين الواجهات

        private void ShowView(UserControl view)
        {
            if (_currentView != null)
            {
                _contentPanel.Controls.Remove(_currentView);
            }

            _currentView = view;
            _currentView.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(_currentView);
            _currentView.BringToFront();
        }

        private void ShowDashboard()
        {
            ShowView(_dashboardView);
        }

        private void ShowInvoice()
        {
            ShowView(_invoiceView);
        }

        private void ShowCustomers()
        {
            ShowView(_customersView);
        }

        private void ShowProducts()
        {
            ShowSimpleMessage("📦 إدارة المنتجات", "هذه الواجهة قيد التطوير وستكون متاحة قريباً!");
        }

        private void ShowReports()
        {
            ShowSimpleMessage("📊 التقارير", "واجهة التقارير قيد التطوير وستكون متاحة قريباً!");
        }

        private void ShowSettings()
        {
            ShowSimpleMessage("⚙️ الإعدادات", "واجهة الإعدادات قيد التطوير وستكون متاحة قريباً!");
        }

        private void ShowSimpleMessage(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion

        #region تنظيف الموارد

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            _clockTimer?.Stop();
            _clockTimer?.Dispose();
            base.OnFormClosed(e);
        }

        #endregion
    }

    /// <summary>
    /// البرنامج الرئيسي الحديث
    /// </summary>
    internal static class ModernProgram
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // تعيين الثقافة العربية
                var culture = new CultureInfo("ar-SA");
                Thread.CurrentThread.CurrentCulture = culture;
                Thread.CurrentThread.CurrentUICulture = culture;

                Application.Run(new ModernCashierApp());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في بدء التشغيل:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
