using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace AredooPOS.Models.Reports
{
    /// <summary>
    /// نموذج تقرير الديون
    /// يحتوي على جميع البيانات المتعلقة بتقارير الديون والذمم المدينة
    /// </summary>
    public class DebtReport
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم التقرير
        /// </summary>
        public int ReportID { get; set; }

        /// <summary>
        /// نوع التقرير
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ReportType { get; set; }

        /// <summary>
        /// تاريخ بداية التقرير
        /// </summary>
        [Required]
        public DateTime FromDate { get; set; }

        /// <summary>
        /// تاريخ نهاية التقرير
        /// </summary>
        [Required]
        public DateTime ToDate { get; set; }

        /// <summary>
        /// تاريخ إنشاء التقرير
        /// </summary>
        public DateTime GeneratedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// من أنشأ التقرير
        /// </summary>
        [Required]
        [StringLength(50)]
        public string GeneratedBy { get; set; }

        #endregion

        #region الإجماليات العامة

        /// <summary>
        /// إجمالي الديون
        /// </summary>
        public decimal TotalDebt { get; set; }

        /// <summary>
        /// إجمالي المبلغ المدفوع
        /// </summary>
        public decimal TotalPaidAmount { get; set; }

        /// <summary>
        /// إجمالي المبلغ المستحق
        /// </summary>
        public decimal TotalOutstandingAmount { get; set; }

        /// <summary>
        /// عدد العملاء المدينين
        /// </summary>
        public int DebtorCustomersCount { get; set; }

        /// <summary>
        /// عدد الفواتير المدينة
        /// </summary>
        public int OutstandingInvoicesCount { get; set; }

        /// <summary>
        /// متوسط الدين لكل عميل
        /// </summary>
        public decimal AverageDebtPerCustomer => DebtorCustomersCount > 0 ? TotalOutstandingAmount / DebtorCustomersCount : 0;

        #endregion

        #region تصنيف الديون حسب العمر

        /// <summary>
        /// الديون الحالية (0-30 يوم)
        /// </summary>
        public decimal CurrentDebt { get; set; }

        /// <summary>
        /// الديون 31-60 يوم
        /// </summary>
        public decimal Debt30To60Days { get; set; }

        /// <summary>
        /// الديون 61-90 يوم
        /// </summary>
        public decimal Debt61To90Days { get; set; }

        /// <summary>
        /// الديون 91-120 يوم
        /// </summary>
        public decimal Debt91To120Days { get; set; }

        /// <summary>
        /// الديون أكثر من 120 يوم
        /// </summary>
        public decimal DebtOver120Days { get; set; }

        #endregion

        #region تصنيف الديون حسب المبلغ

        /// <summary>
        /// الديون الصغيرة (أقل من 1000)
        /// </summary>
        public decimal SmallDebts { get; set; }

        /// <summary>
        /// الديون المتوسطة (1000-5000)
        /// </summary>
        public decimal MediumDebts { get; set; }

        /// <summary>
        /// الديون الكبيرة (5000-20000)
        /// </summary>
        public decimal LargeDebts { get; set; }

        /// <summary>
        /// الديون الضخمة (أكثر من 20000)
        /// </summary>
        public decimal HugeDebts { get; set; }

        #endregion

        #region حالة التحصيل

        /// <summary>
        /// الديون قيد المتابعة
        /// </summary>
        public decimal DebtsUnderFollow { get; set; }

        /// <summary>
        /// الديون المتعثرة
        /// </summary>
        public decimal BadDebts { get; set; }

        /// <summary>
        /// الديون المشطوبة
        /// </summary>
        public decimal WrittenOffDebts { get; set; }

        /// <summary>
        /// الديون المحصلة جزئياً
        /// </summary>
        public decimal PartiallyCollectedDebts { get; set; }

        #endregion

        #region قوائم التفاصيل

        /// <summary>
        /// تفاصيل الديون حسب العميل
        /// </summary>
        public List<CustomerDebtDetail> CustomerDebtDetails { get; set; } = new List<CustomerDebtDetail>();

        /// <summary>
        /// تفاصيل الديون حسب الفاتورة
        /// </summary>
        public List<InvoiceDebtDetail> InvoiceDebtDetails { get; set; } = new List<InvoiceDebtDetail>();

        /// <summary>
        /// تفاصيل الديون حسب العمر
        /// </summary>
        public List<AgingDebtDetail> AgingDebtDetails { get; set; } = new List<AgingDebtDetail>();

        /// <summary>
        /// تفاصيل المدفوعات
        /// </summary>
        public List<DebtPaymentDetail> PaymentDetails { get; set; } = new List<DebtPaymentDetail>();

        /// <summary>
        /// تفاصيل الديون اليومية
        /// </summary>
        public List<DailyDebtDetail> DailyDebtDetails { get; set; } = new List<DailyDebtDetail>();

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// نسبة التحصيل
        /// </summary>
        public decimal CollectionPercentage => TotalDebt > 0 ? (TotalPaidAmount / TotalDebt) * 100 : 0;

        /// <summary>
        /// نسبة الديون المتعثرة
        /// </summary>
        public decimal BadDebtPercentage => TotalDebt > 0 ? (BadDebts / TotalDebt) * 100 : 0;

        /// <summary>
        /// أكبر عميل مديونية
        /// </summary>
        public CustomerDebtDetail TopDebtorCustomer => CustomerDebtDetails?.OrderByDescending(c => c.OutstandingAmount).FirstOrDefault();

        /// <summary>
        /// أقدم دين
        /// </summary>
        public InvoiceDebtDetail OldestDebt => InvoiceDebtDetails?.OrderBy(i => i.InvoiceDate).FirstOrDefault();

        /// <summary>
        /// متوسط عمر الديون
        /// </summary>
        public double AverageDebtAge
        {
            get
            {
                var validDebts = InvoiceDebtDetails?.Where(i => i.DaysOutstanding > 0);
                return validDebts?.Any() == true ? validDebts.Average(i => i.DaysOutstanding) : 0;
            }
        }

        /// <summary>
        /// معدل دوران الذمم المدينة
        /// </summary>
        public decimal AccountsReceivableTurnover
        {
            get
            {
                var totalSales = CustomerDebtDetails?.Sum(c => c.TotalSales) ?? 0;
                return TotalOutstandingAmount > 0 ? totalSales / TotalOutstandingAmount : 0;
            }
        }

        #endregion

        #region الدوال المساعدة

        /// <summary>
        /// حساب الإجماليات من التفاصيل
        /// </summary>
        public void CalculateTotals()
        {
            if (CustomerDebtDetails?.Any() == true)
            {
                TotalDebt = CustomerDebtDetails.Sum(c => c.TotalDebt);
                TotalPaidAmount = CustomerDebtDetails.Sum(c => c.PaidAmount);
                TotalOutstandingAmount = CustomerDebtDetails.Sum(c => c.OutstandingAmount);
                DebtorCustomersCount = CustomerDebtDetails.Count(c => c.OutstandingAmount > 0);
            }

            if (InvoiceDebtDetails?.Any() == true)
            {
                OutstandingInvoicesCount = InvoiceDebtDetails.Count(i => i.OutstandingAmount > 0);
            }

            // حساب الديون حسب العمر
            CalculateDebtByAge();

            // حساب الديون حسب المبلغ
            CalculateDebtByAmount();
        }

        /// <summary>
        /// حساب الديون حسب العمر
        /// </summary>
        private void CalculateDebtByAge()
        {
            if (InvoiceDebtDetails?.Any() == true)
            {
                CurrentDebt = InvoiceDebtDetails.Where(i => i.DaysOutstanding <= 30).Sum(i => i.OutstandingAmount);
                Debt30To60Days = InvoiceDebtDetails.Where(i => i.DaysOutstanding > 30 && i.DaysOutstanding <= 60).Sum(i => i.OutstandingAmount);
                Debt61To90Days = InvoiceDebtDetails.Where(i => i.DaysOutstanding > 60 && i.DaysOutstanding <= 90).Sum(i => i.OutstandingAmount);
                Debt91To120Days = InvoiceDebtDetails.Where(i => i.DaysOutstanding > 90 && i.DaysOutstanding <= 120).Sum(i => i.OutstandingAmount);
                DebtOver120Days = InvoiceDebtDetails.Where(i => i.DaysOutstanding > 120).Sum(i => i.OutstandingAmount);
            }
        }

        /// <summary>
        /// حساب الديون حسب المبلغ
        /// </summary>
        private void CalculateDebtByAmount()
        {
            if (CustomerDebtDetails?.Any() == true)
            {
                SmallDebts = CustomerDebtDetails.Where(c => c.OutstandingAmount < 1000).Sum(c => c.OutstandingAmount);
                MediumDebts = CustomerDebtDetails.Where(c => c.OutstandingAmount >= 1000 && c.OutstandingAmount < 5000).Sum(c => c.OutstandingAmount);
                LargeDebts = CustomerDebtDetails.Where(c => c.OutstandingAmount >= 5000 && c.OutstandingAmount < 20000).Sum(c => c.OutstandingAmount);
                HugeDebts = CustomerDebtDetails.Where(c => c.OutstandingAmount >= 20000).Sum(c => c.OutstandingAmount);
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            if (FromDate > ToDate)
                return false;

            if (string.IsNullOrWhiteSpace(ReportType))
                return false;

            if (string.IsNullOrWhiteSpace(GeneratedBy))
                return false;

            return true;
        }

        /// <summary>
        /// الحصول على ملخص التقرير
        /// </summary>
        /// <returns>ملخص التقرير</returns>
        public string GetSummary()
        {
            return $"تقرير الديون من {FromDate:dd/MM/yyyy} إلى {ToDate:dd/MM/yyyy}\n" +
                   $"إجمالي الديون: {TotalDebt:C}\n" +
                   $"المبلغ المستحق: {TotalOutstandingAmount:C}\n" +
                   $"عدد العملاء المدينين: {DebtorCustomersCount}\n" +
                   $"نسبة التحصيل: {CollectionPercentage:F2}%";
        }

        /// <summary>
        /// الحصول على تحليل المخاطر
        /// </summary>
        /// <returns>تحليل المخاطر</returns>
        public string GetRiskAnalysis()
        {
            var riskLevel = "منخفض";
            
            if (BadDebtPercentage > 10)
                riskLevel = "عالي";
            else if (BadDebtPercentage > 5)
                riskLevel = "متوسط";
            
            return $"مستوى المخاطر: {riskLevel}\n" +
                   $"نسبة الديون المتعثرة: {BadDebtPercentage:F2}%\n" +
                   $"متوسط عمر الديون: {AverageDebtAge:F0} يوم\n" +
                   $"معدل دوران الذمم: {AccountsReceivableTurnover:F2}";
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// تفاصيل ديون العميل
    /// </summary>
    public class CustomerDebtDetail
    {
        public int CustomerID { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public string CustomerEmail { get; set; }
        public decimal TotalDebt { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public decimal TotalSales { get; set; }
        public int InvoiceCount { get; set; }
        public DateTime FirstInvoiceDate { get; set; }
        public DateTime LastInvoiceDate { get; set; }
        public DateTime LastPaymentDate { get; set; }
        public int DaysLastPayment { get; set; }
        public string CreditStatus { get; set; }
        public decimal CreditLimit { get; set; }
        public string RiskLevel { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// تفاصيل ديون الفاتورة
    /// </summary>
    public class InvoiceDebtDetail
    {
        public int InvoiceID { get; set; }
        public string InvoiceNumber { get; set; }
        public int CustomerID { get; set; }
        public string CustomerName { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public decimal InvoiceAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public int DaysOutstanding { get; set; }
        public string Status { get; set; }
        public string PaymentTerms { get; set; }
        public DateTime LastPaymentDate { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// تفاصيل الديون حسب العمر
    /// </summary>
    public class AgingDebtDetail
    {
        public string AgeGroup { get; set; }
        public int MinDays { get; set; }
        public int MaxDays { get; set; }
        public decimal TotalAmount { get; set; }
        public int InvoiceCount { get; set; }
        public int CustomerCount { get; set; }
        public decimal Percentage { get; set; }
        public decimal AverageAmount { get; set; }
    }

    /// <summary>
    /// تفاصيل مدفوعات الديون
    /// </summary>
    public class DebtPaymentDetail
    {
        public int PaymentID { get; set; }
        public int CustomerID { get; set; }
        public string CustomerName { get; set; }
        public int InvoiceID { get; set; }
        public string InvoiceNumber { get; set; }
        public DateTime PaymentDate { get; set; }
        public decimal PaymentAmount { get; set; }
        public string PaymentMethod { get; set; }
        public string ReferenceNumber { get; set; }
        public string Notes { get; set; }
        public string ReceivedBy { get; set; }
    }

    /// <summary>
    /// تفاصيل الديون اليومية
    /// </summary>
    public class DailyDebtDetail
    {
        public DateTime ReportDate { get; set; }
        public decimal NewDebts { get; set; }
        public decimal CollectedAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public int NewInvoices { get; set; }
        public int PaymentCount { get; set; }
        public decimal CollectionRate => NewDebts > 0 ? (CollectedAmount / NewDebts) * 100 : 0;
        public string DayName => ReportDate.ToString("dddd");
    }

    #endregion

    #region التعدادات

    /// <summary>
    /// أنواع تقارير الديون
    /// </summary>
    public static class DebtReportTypes
    {
        public const string Outstanding = "Outstanding";
        public const string Aging = "Aging";
        public const string ByCustomer = "ByCustomer";
        public const string ByInvoice = "ByInvoice";
        public const string Collection = "Collection";
        public const string BadDebt = "BadDebt";
        public const string Daily = "Daily";
        public const string Monthly = "Monthly";
        public const string Risk = "Risk";
    }

    /// <summary>
    /// حالات الديون
    /// </summary>
    public static class DebtStatus
    {
        public const string Current = "Current";
        public const string Overdue = "Overdue";
        public const string UnderFollow = "UnderFollow";
        public const string BadDebt = "BadDebt";
        public const string WrittenOff = "WrittenOff";
        public const string Collected = "Collected";
    }

    /// <summary>
    /// مستويات مخاطر العملاء
    /// </summary>
    public static class CustomerRiskLevels
    {
        public const string Low = "Low";
        public const string Medium = "Medium";
        public const string High = "High";
        public const string Critical = "Critical";
        public const string Blacklisted = "Blacklisted";
    }

    #endregion
}
