using System;
using System.Drawing;
using System.Windows.Forms;
using AredooPOS.Services;
using AredooPOS.Core.Configuration;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// واجهة إعدادات النظام
    /// توفر إدارة شاملة لجميع إعدادات النظام والتذكيرات
    /// </summary>
    public partial class SystemSettingsForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly ConfigurationManager _configManager;
        private readonly ReminderService _reminderService;
        private readonly ILogger<SystemSettingsForm> _logger;

        // ألوان النظام
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SuccessColor = Color.FromArgb(46, 204, 113);
        private readonly Color WarningColor = Color.FromArgb(241, 196, 15);
        private readonly Color DangerColor = Color.FromArgb(231, 76, 60);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);

        // الإعدادات الحالية
        private SystemConfiguration _currentConfig;
        private ReminderSettings _currentReminderSettings;
        private bool _hasChanges = false;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ واجهة إعدادات النظام
        /// </summary>
        /// <param name="configManager">مدير الإعدادات</param>
        /// <param name="reminderService">خدمة التذكيرات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public SystemSettingsForm(ConfigurationManager configManager, ReminderService reminderService, ILogger<SystemSettingsForm> logger = null)
        {
            _configManager = configManager ?? throw new ArgumentNullException(nameof(configManager));
            _reminderService = reminderService ?? throw new ArgumentNullException(nameof(reminderService));
            _logger = logger;

            InitializeComponent();
            InitializeArabicUI();
            LoadCurrentSettings();
            SetupEventHandlers();
        }

        /// <summary>
        /// تهيئة الواجهة العربية
        /// </summary>
        private void InitializeArabicUI()
        {
            // إعدادات النموذج الأساسية
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "إعدادات النظام";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = LightGray;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            // تطبيق الألوان والأنماط
            ApplyThemeColors();
            UpdateUITexts();
        }

        /// <summary>
        /// تطبيق ألوان النظام
        /// </summary>
        private void ApplyThemeColors()
        {
            // شريط العنوان
            pnlHeader.BackColor = PrimaryColor;
            lblTitle.ForeColor = Color.White;

            // أزرار العمليات
            btnSave.BackColor = SuccessColor;
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;

            btnCancel.BackColor = Color.Gray;
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;

            btnReset.BackColor = WarningColor;
            btnReset.ForeColor = Color.White;
            btnReset.FlatStyle = FlatStyle.Flat;

            btnTestConnection.BackColor = PrimaryColor;
            btnTestConnection.ForeColor = Color.White;
            btnTestConnection.FlatStyle = FlatStyle.Flat;

            // علامات التبويب
            tabControl.BackColor = Color.White;
            
            // لوحات الإعدادات
            foreach (TabPage tab in tabControl.TabPages)
            {
                tab.BackColor = Color.White;
                foreach (Control control in tab.Controls)
                {
                    if (control is GroupBox groupBox)
                    {
                        groupBox.BackColor = Color.White;
                    }
                }
            }
        }

        /// <summary>
        /// تحديث النصوص في الواجهة
        /// </summary>
        private void UpdateUITexts()
        {
            lblTitle.Text = "إعدادات النظام";
            
            // أزرار العمليات
            btnSave.Text = "حفظ الإعدادات";
            btnCancel.Text = "إلغاء";
            btnReset.Text = "إعادة تعيين";
            btnTestConnection.Text = "اختبار الاتصال";

            // علامات التبويب
            tabGeneral.Text = "الإعدادات العامة";
            tabDatabase.Text = "قاعدة البيانات";
            tabReminders.Text = "التذكيرات";
            tabSMS.Text = "الرسائل النصية";
            tabEmail.Text = "البريد الإلكتروني";
            tabSecurity.Text = "الأمان";
            tabBackup.Text = "النسخ الاحتياطي";

            // الإعدادات العامة
            grpCompanyInfo.Text = "معلومات الشركة";
            lblCompanyName.Text = "اسم الشركة:";
            lblCompanyAddress.Text = "عنوان الشركة:";
            lblCompanyPhone.Text = "هاتف الشركة:";
            lblCompanyEmail.Text = "بريد الشركة الإلكتروني:";
            lblTaxNumber.Text = "الرقم الضريبي:";

            grpSystemSettings.Text = "إعدادات النظام";
            lblDefaultCurrency.Text = "العملة الافتراضية:";
            lblDateFormat.Text = "تنسيق التاريخ:";
            lblNumberFormat.Text = "تنسيق الأرقام:";
            chkAutoBackup.Text = "النسخ الاحتياطي التلقائي";
            chkAutoUpdate.Text = "التحديث التلقائي";

            // إعدادات قاعدة البيانات
            grpDatabaseConnection.Text = "اتصال قاعدة البيانات";
            lblDatabaseType.Text = "نوع قاعدة البيانات:";
            lblConnectionString.Text = "نص الاتصال:";
            lblBackupPath.Text = "مسار النسخ الاحتياطي:";
            chkAutoOptimize.Text = "تحسين تلقائي لقاعدة البيانات";

            // إعدادات التذكيرات
            grpReminderSettings.Text = "إعدادات التذكيرات";
            chkEnableReminders.Text = "تفعيل التذكيرات التلقائية";
            lblMinAmount.Text = "الحد الأدنى للمبلغ:";
            lblMaxReminders.Text = "الحد الأقصى للتذكيرات:";
            lblDaysBeforeDue.Text = "أيام قبل الاستحقاق:";
            lblFirstWarningDays.Text = "أيام الإنذار الأول:";
            lblFinalWarningDays.Text = "أيام الإنذار الأخير:";

            // إعدادات الرسائل النصية
            grpSMSSettings.Text = "إعدادات الرسائل النصية";
            chkEnableSMS.Text = "تفعيل الرسائل النصية";
            lblSMSProvider.Text = "مزود الخدمة:";
            lblSMSUsername.Text = "اسم المستخدم:";
            lblSMSPassword.Text = "كلمة المرور:";
            lblSMSSender.Text = "اسم المرسل:";

            // إعدادات البريد الإلكتروني
            grpEmailSettings.Text = "إعدادات البريد الإلكتروني";
            chkEnableEmail.Text = "تفعيل البريد الإلكتروني";
            lblSMTPServer.Text = "خادم SMTP:";
            lblSMTPPort.Text = "المنفذ:";
            lblEmailUsername.Text = "اسم المستخدم:";
            lblEmailPassword.Text = "كلمة المرور:";
            chkUseSSL.Text = "استخدام SSL";

            // إعدادات الأمان
            grpSecuritySettings.Text = "إعدادات الأمان";
            chkRequireLogin.Text = "طلب تسجيل الدخول";
            lblSessionTimeout.Text = "انتهاء الجلسة (دقيقة):";
            lblPasswordPolicy.Text = "سياسة كلمة المرور:";
            chkAuditLog.Text = "تسجيل العمليات";
            chkDataEncryption.Text = "تشفير البيانات";

            // إعدادات النسخ الاحتياطي
            grpBackupSettings.Text = "إعدادات النسخ الاحتياطي";
            chkAutoBackupEnabled.Text = "تفعيل النسخ الاحتياطي التلقائي";
            lblBackupInterval.Text = "فترة النسخ (ساعة):";
            lblBackupLocation.Text = "مكان النسخ:";
            lblMaxBackups.Text = "الحد الأقصى للنسخ:";
            chkCompressBackup.Text = "ضغط النسخ الاحتياطية";
        }

        /// <summary>
        /// تحميل الإعدادات الحالية
        /// </summary>
        private void LoadCurrentSettings()
        {
            try
            {
                // تحميل إعدادات النظام
                _currentConfig = _configManager.GetSystemConfiguration();
                _currentReminderSettings = _reminderService.Settings;

                // عرض الإعدادات في الواجهة
                DisplayGeneralSettings();
                DisplayDatabaseSettings();
                DisplayReminderSettings();
                DisplaySMSSettings();
                DisplayEmailSettings();
                DisplaySecuritySettings();
                DisplayBackupSettings();

                _hasChanges = false;
                UpdateSaveButtonState();

                _logger?.LogInformation("تم تحميل الإعدادات الحالية");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل الإعدادات");
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // أحداث الأزرار
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnReset.Click += BtnReset_Click;
            btnTestConnection.Click += BtnTestConnection_Click;

            // أحداث تغيير القيم
            SetupChangeEvents();

            // أحداث النموذج
            this.Load += SystemSettingsForm_Load;
            this.FormClosing += SystemSettingsForm_FormClosing;
        }

        /// <summary>
        /// إعداد أحداث تغيير القيم
        /// </summary>
        private void SetupChangeEvents()
        {
            // الإعدادات العامة
            txtCompanyName.TextChanged += OnSettingChanged;
            txtCompanyAddress.TextChanged += OnSettingChanged;
            txtCompanyPhone.TextChanged += OnSettingChanged;
            txtCompanyEmail.TextChanged += OnSettingChanged;
            txtTaxNumber.TextChanged += OnSettingChanged;
            cmbDefaultCurrency.SelectedIndexChanged += OnSettingChanged;
            cmbDateFormat.SelectedIndexChanged += OnSettingChanged;
            cmbNumberFormat.SelectedIndexChanged += OnSettingChanged;
            chkAutoBackup.CheckedChanged += OnSettingChanged;
            chkAutoUpdate.CheckedChanged += OnSettingChanged;

            // إعدادات قاعدة البيانات
            cmbDatabaseType.SelectedIndexChanged += OnSettingChanged;
            txtConnectionString.TextChanged += OnSettingChanged;
            txtBackupPath.TextChanged += OnSettingChanged;
            chkAutoOptimize.CheckedChanged += OnSettingChanged;

            // إعدادات التذكيرات
            chkEnableReminders.CheckedChanged += OnSettingChanged;
            numMinAmount.ValueChanged += OnSettingChanged;
            numMaxReminders.ValueChanged += OnSettingChanged;
            numDaysBeforeDue.ValueChanged += OnSettingChanged;
            numFirstWarningDays.ValueChanged += OnSettingChanged;
            numFinalWarningDays.ValueChanged += OnSettingChanged;

            // إعدادات الرسائل النصية
            chkEnableSMS.CheckedChanged += OnSettingChanged;
            cmbSMSProvider.SelectedIndexChanged += OnSettingChanged;
            txtSMSUsername.TextChanged += OnSettingChanged;
            txtSMSPassword.TextChanged += OnSettingChanged;
            txtSMSSender.TextChanged += OnSettingChanged;

            // إعدادات البريد الإلكتروني
            chkEnableEmail.CheckedChanged += OnSettingChanged;
            txtSMTPServer.TextChanged += OnSettingChanged;
            numSMTPPort.ValueChanged += OnSettingChanged;
            txtEmailUsername.TextChanged += OnSettingChanged;
            txtEmailPassword.TextChanged += OnSettingChanged;
            chkUseSSL.CheckedChanged += OnSettingChanged;

            // إعدادات الأمان
            chkRequireLogin.CheckedChanged += OnSettingChanged;
            numSessionTimeout.ValueChanged += OnSettingChanged;
            cmbPasswordPolicy.SelectedIndexChanged += OnSettingChanged;
            chkAuditLog.CheckedChanged += OnSettingChanged;
            chkDataEncryption.CheckedChanged += OnSettingChanged;

            // إعدادات النسخ الاحتياطي
            chkAutoBackupEnabled.CheckedChanged += OnSettingChanged;
            numBackupInterval.ValueChanged += OnSettingChanged;
            txtBackupLocation.TextChanged += OnSettingChanged;
            numMaxBackups.ValueChanged += OnSettingChanged;
            chkCompressBackup.CheckedChanged += OnSettingChanged;
        }

        #endregion

        #region عرض الإعدادات

        /// <summary>
        /// عرض الإعدادات العامة
        /// </summary>
        private void DisplayGeneralSettings()
        {
            txtCompanyName.Text = _currentConfig.CompanyName;
            txtCompanyAddress.Text = _currentConfig.CompanyAddress;
            txtCompanyPhone.Text = _currentConfig.CompanyPhone;
            txtCompanyEmail.Text = _currentConfig.CompanyEmail;
            txtTaxNumber.Text = _currentConfig.TaxNumber;
            cmbDefaultCurrency.Text = _currentConfig.DefaultCurrency;
            cmbDateFormat.Text = _currentConfig.DateFormat;
            cmbNumberFormat.Text = _currentConfig.NumberFormat;
            chkAutoBackup.Checked = _currentConfig.AutoBackup;
            chkAutoUpdate.Checked = _currentConfig.AutoUpdate;
        }

        /// <summary>
        /// عرض إعدادات قاعدة البيانات
        /// </summary>
        private void DisplayDatabaseSettings()
        {
            cmbDatabaseType.Text = _currentConfig.DatabaseType;
            txtConnectionString.Text = _currentConfig.ConnectionString;
            txtBackupPath.Text = _currentConfig.BackupPath;
            chkAutoOptimize.Checked = _currentConfig.AutoOptimizeDatabase;
        }

        /// <summary>
        /// عرض إعدادات التذكيرات
        /// </summary>
        private void DisplayReminderSettings()
        {
            chkEnableReminders.Checked = _currentReminderSettings.EnableAutomaticReminders;
            numMinAmount.Value = _currentReminderSettings.MinAmountForReminder;
            numMaxReminders.Value = _currentReminderSettings.MaxRemindersPerDebt;
            numDaysBeforeDue.Value = _currentReminderSettings.DaysBeforeDueForReminder;
            numFirstWarningDays.Value = _currentReminderSettings.DaysForFirstWarning;
            numFinalWarningDays.Value = _currentReminderSettings.DaysForFinalWarning;
        }

        /// <summary>
        /// عرض إعدادات الرسائل النصية
        /// </summary>
        private void DisplaySMSSettings()
        {
            chkEnableSMS.Checked = _currentConfig.SMSEnabled;
            cmbSMSProvider.Text = _currentConfig.SMSProvider;
            txtSMSUsername.Text = _currentConfig.SMSUsername;
            txtSMSPassword.Text = _currentConfig.SMSPassword;
            txtSMSSender.Text = _currentConfig.SMSSenderName;
        }

        /// <summary>
        /// عرض إعدادات البريد الإلكتروني
        /// </summary>
        private void DisplayEmailSettings()
        {
            chkEnableEmail.Checked = _currentConfig.EmailEnabled;
            txtSMTPServer.Text = _currentConfig.SMTPServer;
            numSMTPPort.Value = _currentConfig.SMTPPort;
            txtEmailUsername.Text = _currentConfig.EmailUsername;
            txtEmailPassword.Text = _currentConfig.EmailPassword;
            chkUseSSL.Checked = _currentConfig.UseSSL;
        }

        /// <summary>
        /// عرض إعدادات الأمان
        /// </summary>
        private void DisplaySecuritySettings()
        {
            chkRequireLogin.Checked = _currentConfig.RequireLogin;
            numSessionTimeout.Value = _currentConfig.SessionTimeoutMinutes;
            cmbPasswordPolicy.Text = _currentConfig.PasswordPolicy;
            chkAuditLog.Checked = _currentConfig.EnableAuditLog;
            chkDataEncryption.Checked = _currentConfig.EnableDataEncryption;
        }

        /// <summary>
        /// عرض إعدادات النسخ الاحتياطي
        /// </summary>
        private void DisplayBackupSettings()
        {
            chkAutoBackupEnabled.Checked = _currentConfig.AutoBackupEnabled;
            numBackupInterval.Value = _currentConfig.BackupIntervalHours;
            txtBackupLocation.Text = _currentConfig.BackupLocation;
            numMaxBackups.Value = _currentConfig.MaxBackupFiles;
            chkCompressBackup.Checked = _currentConfig.CompressBackups;
        }

        #endregion

        #region معالجات الأحداث

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void SystemSettingsForm_Load(object sender, EventArgs e)
        {
            _logger?.LogInformation("تم تحميل واجهة إعدادات النظام");
        }

        /// <summary>
        /// حدث إغلاق النموذج
        /// </summary>
        private void SystemSettingsForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_hasChanges)
            {
                var result = MessageBox.Show(
                    "لديك تغييرات غير محفوظة. هل تريد حفظها قبل الإغلاق؟",
                    "تغييرات غير محفوظة",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question);

                switch (result)
                {
                    case DialogResult.Yes:
                        if (!SaveSettings())
                        {
                            e.Cancel = true;
                        }
                        break;
                    case DialogResult.Cancel:
                        e.Cancel = true;
                        break;
                }
            }
        }

        /// <summary>
        /// حدث تغيير الإعدادات
        /// </summary>
        private void OnSettingChanged(object sender, EventArgs e)
        {
            _hasChanges = true;
            UpdateSaveButtonState();
        }

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (SaveSettings())
            {
                MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
            }
        }

        /// <summary>
        /// إلغاء التغييرات
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            if (_hasChanges)
            {
                var result = MessageBox.Show(
                    "هل تريد إلغاء جميع التغييرات؟",
                    "تأكيد الإلغاء",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    return;
                }
            }

            this.DialogResult = DialogResult.Cancel;
        }

        /// <summary>
        /// إعادة تعيين الإعدادات
        /// </summary>
        private void BtnReset_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                "تأكيد إعادة التعيين",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                ResetToDefaults();
            }
        }

        /// <summary>
        /// اختبار اتصال قاعدة البيانات
        /// </summary>
        private void BtnTestConnection_Click(object sender, EventArgs e)
        {
            try
            {
                var connectionString = txtConnectionString.Text;
                if (string.IsNullOrWhiteSpace(connectionString))
                {
                    MessageBox.Show("يرجى إدخال نص الاتصال أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // اختبار الاتصال
                var success = _configManager.TestDatabaseConnection(connectionString);

                if (success)
                {
                    MessageBox.Show("تم الاتصال بقاعدة البيانات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("فشل في الاتصال بقاعدة البيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في اختبار اتصال قاعدة البيانات");
                MessageBox.Show($"خطأ في اختبار الاتصال: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region عمليات الحفظ والتحديث

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        private bool SaveSettings()
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateSettings())
                {
                    return false;
                }

                // تحديث الإعدادات من الواجهة
                UpdateConfigurationFromUI();
                UpdateReminderSettingsFromUI();

                // حفظ الإعدادات
                _configManager.SaveSystemConfiguration(_currentConfig);
                _reminderService.Settings = _currentReminderSettings;

                _hasChanges = false;
                UpdateSaveButtonState();

                _logger?.LogInformation("تم حفظ إعدادات النظام بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ الإعدادات");
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة الإعدادات
        /// </summary>
        /// <returns>true إذا كانت الإعدادات صحيحة</returns>
        private bool ValidateSettings()
        {
            // التحقق من اسم الشركة
            if (string.IsNullOrWhiteSpace(txtCompanyName.Text))
            {
                MessageBox.Show("اسم الشركة مطلوب", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                tabControl.SelectedTab = tabGeneral;
                txtCompanyName.Focus();
                return false;
            }

            // التحقق من نص الاتصال
            if (string.IsNullOrWhiteSpace(txtConnectionString.Text))
            {
                MessageBox.Show("نص اتصال قاعدة البيانات مطلوب", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                tabControl.SelectedTab = tabDatabase;
                txtConnectionString.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// تحديث الإعدادات من الواجهة
        /// </summary>
        private void UpdateConfigurationFromUI()
        {
            // الإعدادات العامة
            _currentConfig.CompanyName = txtCompanyName.Text.Trim();
            _currentConfig.CompanyAddress = txtCompanyAddress.Text.Trim();
            _currentConfig.CompanyPhone = txtCompanyPhone.Text.Trim();
            _currentConfig.CompanyEmail = txtCompanyEmail.Text.Trim();
            _currentConfig.TaxNumber = txtTaxNumber.Text.Trim();

            // إعدادات قاعدة البيانات
            _currentConfig.ConnectionString = txtConnectionString.Text.Trim();
            _currentConfig.BackupPath = txtBackupPath.Text.Trim();
        }

        /// <summary>
        /// تحديث إعدادات التذكيرات من الواجهة
        /// </summary>
        private void UpdateReminderSettingsFromUI()
        {
            _currentReminderSettings.EnableAutomaticReminders = chkEnableReminders.Checked;
            _currentReminderSettings.MinAmountForReminder = numMinAmount.Value;
            _currentReminderSettings.MaxRemindersPerDebt = (int)numMaxReminders.Value;
            _currentReminderSettings.CompanyName = _currentConfig.CompanyName;
        }

        /// <summary>
        /// إعادة تعيين الإعدادات إلى القيم الافتراضية
        /// </summary>
        private void ResetToDefaults()
        {
            try
            {
                _currentConfig = new SystemConfiguration();
                _currentReminderSettings = new ReminderSettings();

                // عرض الإعدادات الافتراضية
                DisplayGeneralSettings();
                DisplayDatabaseSettings();
                DisplayReminderSettings();
                DisplaySMSSettings();
                DisplayEmailSettings();
                DisplaySecuritySettings();
                DisplayBackupSettings();

                _hasChanges = true;
                UpdateSaveButtonState();

                _logger?.LogInformation("تم إعادة تعيين الإعدادات إلى القيم الافتراضية");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إعادة تعيين الإعدادات");
                MessageBox.Show($"خطأ في إعادة التعيين: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث حالة زر الحفظ
        /// </summary>
        private void UpdateSaveButtonState()
        {
            btnSave.Enabled = _hasChanges;
            btnSave.Text = _hasChanges ? "حفظ الإعدادات *" : "حفظ الإعدادات";
        }

        #endregion
    }

    #region إعدادات النظام

    /// <summary>
    /// إعدادات النظام
    /// </summary>
    public class SystemConfiguration
    {
        // معلومات الشركة
        public string CompanyName { get; set; } = "شركة أريدو";
        public string CompanyAddress { get; set; } = "";
        public string CompanyPhone { get; set; } = "";
        public string CompanyEmail { get; set; } = "";
        public string TaxNumber { get; set; } = "";

        // إعدادات النظام العامة
        public string DefaultCurrency { get; set; } = "ريال سعودي";
        public string DateFormat { get; set; } = "yyyy/MM/dd";
        public string NumberFormat { get; set; } = "N2";
        public bool AutoBackup { get; set; } = true;
        public bool AutoUpdate { get; set; } = false;

        // إعدادات قاعدة البيانات
        public string DatabaseType { get; set; } = "SQLite";
        public string ConnectionString { get; set; } = "Data Source=AredooPOS.db";
        public string BackupPath { get; set; } = "Backups";
        public bool AutoOptimizeDatabase { get; set; } = true;

        // إعدادات الرسائل النصية
        public bool SMSEnabled { get; set; } = false;
        public string SMSProvider { get; set; } = "";
        public string SMSUsername { get; set; } = "";
        public string SMSPassword { get; set; } = "";
        public string SMSSenderName { get; set; } = "";

        // إعدادات البريد الإلكتروني
        public bool EmailEnabled { get; set; } = false;
        public string SMTPServer { get; set; } = "";
        public int SMTPPort { get; set; } = 587;
        public string EmailUsername { get; set; } = "";
        public string EmailPassword { get; set; } = "";
        public bool UseSSL { get; set; } = true;

        // إعدادات الأمان
        public bool RequireLogin { get; set; } = false;
        public int SessionTimeoutMinutes { get; set; } = 60;
        public string PasswordPolicy { get; set; } = "عادي";
        public bool EnableAuditLog { get; set; } = true;
        public bool EnableDataEncryption { get; set; } = false;

        // إعدادات النسخ الاحتياطي
        public bool AutoBackupEnabled { get; set; } = true;
        public int BackupIntervalHours { get; set; } = 24;
        public string BackupLocation { get; set; } = "Backups";
        public int MaxBackupFiles { get; set; } = 30;
        public bool CompressBackups { get; set; } = true;
    }

    #endregion
}
