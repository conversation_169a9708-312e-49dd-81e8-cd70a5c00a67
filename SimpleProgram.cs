using System;
using System.Windows.Forms;
using System.Drawing;

namespace AridooPOS
{
    internal static class SimpleProgram
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            // تعيين الثقافة العربية
            System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo("ar-SA");
            System.Threading.Thread.CurrentThread.CurrentCulture = new System.Globalization.CultureInfo("ar-SA");
            
            Application.Run(new SimpleMainForm());
        }
    }

    public partial class SimpleMainForm : Form
    {
        public SimpleMainForm()
        {
            InitializeComponent();
            InitializeArabicUI();
        }

        private void InitializeComponent()
        {
            this.panel1 = new Panel();
            this.lblTitle = new Label();
            this.panel2 = new Panel();
            this.btnTest = new Button();
            this.btnExit = new Button();
            this.txtInfo = new TextBox();

            this.panel1.SuspendLayout();
            this.panel2.SuspendLayout();
            this.SuspendLayout();

            // panel1 - Header
            this.panel1.Controls.Add(this.lblTitle);
            this.panel1.Dock = DockStyle.Top;
            this.panel1.Location = new Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new Size(800, 80);
            this.panel1.TabIndex = 0;
            this.panel1.BackColor = Color.FromArgb(41, 128, 185);

            // lblTitle
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Tahoma", 18F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(300, 25);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(200, 29);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "مرحباً بك في أريدوو";

            // panel2 - Buttons
            this.panel2.Controls.Add(this.btnTest);
            this.panel2.Controls.Add(this.btnExit);
            this.panel2.Dock = DockStyle.Top;
            this.panel2.Location = new Point(0, 80);
            this.panel2.Name = "panel2";
            this.panel2.Size = new Size(800, 60);
            this.panel2.TabIndex = 1;
            this.panel2.BackColor = Color.FromArgb(236, 240, 241);

            // btnTest
            this.btnTest.BackColor = Color.FromArgb(46, 204, 113);
            this.btnTest.FlatStyle = FlatStyle.Flat;
            this.btnTest.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.btnTest.ForeColor = Color.White;
            this.btnTest.Location = new Point(400, 15);
            this.btnTest.Name = "btnTest";
            this.btnTest.Size = new Size(150, 30);
            this.btnTest.TabIndex = 0;
            this.btnTest.Text = "اختبار النظام";
            this.btnTest.UseVisualStyleBackColor = false;
            this.btnTest.Click += new EventHandler(this.btnTest_Click);

            // btnExit
            this.btnExit.BackColor = Color.FromArgb(231, 76, 60);
            this.btnExit.FlatStyle = FlatStyle.Flat;
            this.btnExit.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.btnExit.ForeColor = Color.White;
            this.btnExit.Location = new Point(600, 15);
            this.btnExit.Name = "btnExit";
            this.btnExit.Size = new Size(100, 30);
            this.btnExit.TabIndex = 1;
            this.btnExit.Text = "خروج";
            this.btnExit.UseVisualStyleBackColor = false;
            this.btnExit.Click += new EventHandler(this.btnExit_Click);

            // txtInfo
            this.txtInfo.Dock = DockStyle.Fill;
            this.txtInfo.Font = new Font("Tahoma", 10F);
            this.txtInfo.Location = new Point(0, 140);
            this.txtInfo.Multiline = true;
            this.txtInfo.Name = "txtInfo";
            this.txtInfo.ReadOnly = true;
            this.txtInfo.ScrollBars = ScrollBars.Vertical;
            this.txtInfo.Size = new Size(800, 360);
            this.txtInfo.TabIndex = 2;

            // SimpleMainForm
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 500);
            this.Controls.Add(this.txtInfo);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel1);
            this.Name = "SimpleMainForm";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "أريدوو - نظام نقاط البيع";

            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

            // Load initial info
            LoadInitialInfo();
        }

        private void InitializeArabicUI()
        {
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void LoadInitialInfo()
        {
            txtInfo.Text = "🎉 مرحباً بك في نظام أريدوو لنقاط البيع\r\n\r\n";
            txtInfo.Text += "✅ تم تطوير النظام بنجاح!\r\n\r\n";
            txtInfo.Text += "📋 الميزات المطورة:\r\n";
            txtInfo.Text += "• نظام فواتير شامل (نقد - بطاقة - أقساط)\r\n";
            txtInfo.Text += "• إدارة المنتجات والمخزون\r\n";
            txtInfo.Text += "• إدارة العملاء والديون\r\n";
            txtInfo.Text += "• البحث المتقدم عن الفواتير\r\n";
            txtInfo.Text += "• إرجاع الفواتير مع تحديث المخزون\r\n";
            txtInfo.Text += "• دعم الطباعة الحرارية و A4\r\n";
            txtInfo.Text += "• واجهة عربية كاملة\r\n\r\n";
            txtInfo.Text += "🔧 التقنيات المستخدمة:\r\n";
            txtInfo.Text += "• C# Windows Forms\r\n";
            txtInfo.Text += "• SQLite Database\r\n";
            txtInfo.Text += "• .NET 6.0\r\n";
            txtInfo.Text += "• هيكل طبقي منظم (DAL, BLL, Models)\r\n\r\n";
            txtInfo.Text += "🚀 اضغط 'اختبار النظام' لاختبار قاعدة البيانات\r\n";
        }

        private void btnTest_Click(object sender, EventArgs e)
        {
            try
            {
                txtInfo.Text += "\r\n🔄 جاري اختبار النظام...\r\n";
                
                // Test SQLite connection
                txtInfo.Text += "📊 إنشاء قاعدة بيانات SQLite...\r\n";
                
                // This will create the database and tables
                bool testResult = AridooPOS.DAL.SQLiteConnection.TestConnection();
                
                if (testResult)
                {
                    txtInfo.Text += "✅ تم إنشاء قاعدة البيانات بنجاح!\r\n";
                    txtInfo.Text += "✅ تم إنشاء جميع الجداول!\r\n";
                    txtInfo.Text += "✅ تم إدراج البيانات الأولية!\r\n";
                    
                    // Test data retrieval
                    var categories = AridooPOS.DAL.DatabaseConnection.ExecuteQuery("SELECT COUNT(*) as Count FROM Categories");
                    if (categories.Rows.Count > 0)
                    {
                        txtInfo.Text += $"📦 عدد فئات المنتجات: {categories.Rows[0]["Count"]}\r\n";
                    }
                    
                    var products = AridooPOS.DAL.DatabaseConnection.ExecuteQuery("SELECT COUNT(*) as Count FROM Products");
                    if (products.Rows.Count > 0)
                    {
                        txtInfo.Text += $"🛍️ عدد المنتجات: {products.Rows[0]["Count"]}\r\n";
                    }
                    
                    var customers = AridooPOS.DAL.DatabaseConnection.ExecuteQuery("SELECT COUNT(*) as Count FROM Customers");
                    if (customers.Rows.Count > 0)
                    {
                        txtInfo.Text += $"👥 عدد العملاء: {customers.Rows[0]["Count"]}\r\n";
                    }
                    
                    txtInfo.Text += "\r\n🎉 النظام جاهز للاستخدام!\r\n";
                    txtInfo.Text += "💡 يمكنك الآن تطوير الواجهات الكاملة\r\n";
                }
                else
                {
                    txtInfo.Text += "❌ فشل في إنشاء قاعدة البيانات\r\n";
                }
            }
            catch (Exception ex)
            {
                txtInfo.Text += $"❌ خطأ: {ex.Message}\r\n";
            }
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        #region Designer Variables
        private Panel panel1;
        private Label lblTitle;
        private Panel panel2;
        private Button btnTest;
        private Button btnExit;
        private TextBox txtInfo;
        #endregion
    }
}
