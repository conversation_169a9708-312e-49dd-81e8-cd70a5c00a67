using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace AredooPOS.Models
{
    /// <summary>
    /// نموذج المستخدم
    /// يمثل مستخدم النظام مع جميع المعلومات والصلاحيات
    /// </summary>
    public class User
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم المستخدم في قاعدة البيانات (مفتاح أساسي)
        /// </summary>
        public int UserID { get; set; }

        /// <summary>
        /// اسم المستخدم للدخول
        /// </summary>
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [StringLength(50, ErrorMessage = "اسم المستخدم لا يجب أن يتجاوز 50 حرف")]
        public string Username { get; set; }

        /// <summary>
        /// كلمة المرور المشفرة
        /// </summary>
        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [StringLength(255, ErrorMessage = "كلمة المرور لا يجب أن تتجاوز 255 حرف")]
        public string PasswordHash { get; set; }

        /// <summary>
        /// ملح التشفير
        /// </summary>
        [StringLength(255, ErrorMessage = "ملح التشفير لا يجب أن يتجاوز 255 حرف")]
        public string PasswordSalt { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100, ErrorMessage = "البريد الإلكتروني لا يجب أن يتجاوز 100 حرف")]
        public string Email { get; set; }

        #endregion

        #region المعلومات الشخصية

        /// <summary>
        /// الاسم الأول
        /// </summary>
        [Required(ErrorMessage = "الاسم الأول مطلوب")]
        [StringLength(50, ErrorMessage = "الاسم الأول لا يجب أن يتجاوز 50 حرف")]
        public string FirstName { get; set; }

        /// <summary>
        /// الاسم الأخير
        /// </summary>
        [Required(ErrorMessage = "الاسم الأخير مطلوب")]
        [StringLength(50, ErrorMessage = "الاسم الأخير لا يجب أن يتجاوز 50 حرف")]
        public string LastName { get; set; }

        /// <summary>
        /// الاسم الكامل
        /// </summary>
        public string FullName => $"{FirstName} {LastName}";

        /// <summary>
        /// رقم الهاتف
        /// </summary>
        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        [StringLength(20, ErrorMessage = "رقم الهاتف لا يجب أن يتجاوز 20 حرف")]
        public string PhoneNumber { get; set; }

        /// <summary>
        /// تاريخ الميلاد
        /// </summary>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// الجنس
        /// </summary>
        [StringLength(10, ErrorMessage = "الجنس لا يجب أن يتجاوز 10 أحرف")]
        public string Gender { get; set; }

        /// <summary>
        /// العنوان
        /// </summary>
        [StringLength(200, ErrorMessage = "العنوان لا يجب أن يتجاوز 200 حرف")]
        public string Address { get; set; }

        #endregion

        #region معلومات العمل

        /// <summary>
        /// رقم الموظف
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الموظف لا يجب أن يتجاوز 20 حرف")]
        public string EmployeeNumber { get; set; }

        /// <summary>
        /// المسمى الوظيفي
        /// </summary>
        [StringLength(100, ErrorMessage = "المسمى الوظيفي لا يجب أن يتجاوز 100 حرف")]
        public string JobTitle { get; set; }

        /// <summary>
        /// القسم
        /// </summary>
        [StringLength(50, ErrorMessage = "القسم لا يجب أن يتجاوز 50 حرف")]
        public string Department { get; set; }

        /// <summary>
        /// تاريخ التوظيف
        /// </summary>
        public DateTime? HireDate { get; set; }

        /// <summary>
        /// الراتب الأساسي
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الراتب يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal? Salary { get; set; }

        #endregion

        #region الصلاحيات والأدوار

        /// <summary>
        /// رقم الدور (مفتاح خارجي)
        /// </summary>
        [Required(ErrorMessage = "دور المستخدم مطلوب")]
        public int RoleID { get; set; }

        /// <summary>
        /// اسم الدور (للعرض)
        /// </summary>
        public string RoleName { get; set; }

        /// <summary>
        /// مستوى الصلاحية
        /// </summary>
        public int PermissionLevel { get; set; }

        /// <summary>
        /// هل المستخدم مدير نظام
        /// </summary>
        public bool IsSystemAdmin { get; set; }

        /// <summary>
        /// هل يمكن للمستخدم الوصول للنظام
        /// </summary>
        public bool CanAccessSystem { get; set; } = true;

        /// <summary>
        /// هل يمكن للمستخدم إدارة المستخدمين الآخرين
        /// </summary>
        public bool CanManageUsers { get; set; }

        /// <summary>
        /// هل يمكن للمستخدم عرض التقارير
        /// </summary>
        public bool CanViewReports { get; set; }

        /// <summary>
        /// هل يمكن للمستخدم إدارة المنتجات
        /// </summary>
        public bool CanManageProducts { get; set; }

        /// <summary>
        /// هل يمكن للمستخدم إجراء المبيعات
        /// </summary>
        public bool CanProcessSales { get; set; } = true;

        #endregion

        #region إعدادات الأمان

        /// <summary>
        /// هل المستخدم نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل الحساب مقفل
        /// </summary>
        public bool IsLocked { get; set; }

        /// <summary>
        /// تاريخ قفل الحساب
        /// </summary>
        public DateTime? LockedDate { get; set; }

        /// <summary>
        /// سبب قفل الحساب
        /// </summary>
        [StringLength(200, ErrorMessage = "سبب القفل لا يجب أن يتجاوز 200 حرف")]
        public string LockReason { get; set; }

        /// <summary>
        /// عدد محاولات تسجيل الدخول الفاشلة
        /// </summary>
        public int FailedLoginAttempts { get; set; }

        /// <summary>
        /// تاريخ آخر محاولة دخول فاشلة
        /// </summary>
        public DateTime? LastFailedLoginDate { get; set; }

        /// <summary>
        /// تاريخ آخر تسجيل دخول ناجح
        /// </summary>
        public DateTime? LastLoginDate { get; set; }

        /// <summary>
        /// عنوان IP لآخر تسجيل دخول
        /// </summary>
        [StringLength(45, ErrorMessage = "عنوان IP لا يجب أن يتجاوز 45 حرف")]
        public string LastLoginIP { get; set; }

        /// <summary>
        /// تاريخ انتهاء كلمة المرور
        /// </summary>
        public DateTime? PasswordExpiryDate { get; set; }

        /// <summary>
        /// هل يجب تغيير كلمة المرور في الدخول التالي
        /// </summary>
        public bool MustChangePassword { get; set; }

        #endregion

        #region الصورة والمرفقات

        /// <summary>
        /// مسار صورة المستخدم
        /// </summary>
        [StringLength(500, ErrorMessage = "مسار الصورة لا يجب أن يتجاوز 500 حرف")]
        public string ProfileImagePath { get; set; }

        #endregion

        #region الملاحظات والمعلومات الإضافية

        /// <summary>
        /// ملاحظات عامة
        /// </summary>
        [StringLength(1000, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 1000 حرف")]
        public string Notes { get; set; }

        /// <summary>
        /// إعدادات المستخدم (JSON)
        /// </summary>
        public string UserSettings { get; set; }

        #endregion

        #region معلومات النظام

        /// <summary>
        /// منشئ السجل
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المنشئ لا يجب أن يتجاوز 50 حرف")]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ إنشاء السجل
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// آخر من عدل السجل
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المعدل لا يجب أن يتجاوز 50 حرف")]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime ModifiedDate { get; set; }

        #endregion

        #region القوائم المرتبطة

        /// <summary>
        /// دور المستخدم
        /// </summary>
        public Role Role { get; set; }

        /// <summary>
        /// جلسات المستخدم
        /// </summary>
        public List<UserSession> UserSessions { get; set; }

        /// <summary>
        /// أنشطة المستخدم
        /// </summary>
        public List<UserActivity> UserActivities { get; set; }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ المستخدم مع التهيئة الافتراضية
        /// </summary>
        public User()
        {
            // تهيئة القوائم
            UserSessions = new List<UserSession>();
            UserActivities = new List<UserActivity>();

            // تهيئة التواريخ
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;

            // تهيئة القيم الافتراضية
            IsActive = true;
            IsLocked = false;
            IsSystemAdmin = false;
            CanAccessSystem = true;
            CanProcessSales = true;
            FailedLoginAttempts = 0;
            MustChangePassword = false;
            PermissionLevel = 1;
        }

        #endregion

        #region العمليات والتحقق

        /// <summary>
        /// التحقق من صحة بيانات المستخدم
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Username) &&
                   !string.IsNullOrWhiteSpace(FirstName) &&
                   !string.IsNullOrWhiteSpace(LastName) &&
                   !string.IsNullOrWhiteSpace(PasswordHash) &&
                   RoleID > 0;
        }

        /// <summary>
        /// التحقق من إمكانية تسجيل الدخول
        /// </summary>
        /// <returns>true إذا كان يمكن تسجيل الدخول</returns>
        public bool CanLogin()
        {
            return IsActive && 
                   !IsLocked && 
                   CanAccessSystem &&
                   (PasswordExpiryDate == null || PasswordExpiryDate > DateTime.Now);
        }

        /// <summary>
        /// التحقق من انتهاء صلاحية كلمة المرور
        /// </summary>
        /// <returns>true إذا انتهت صلاحية كلمة المرور</returns>
        public bool IsPasswordExpired()
        {
            return PasswordExpiryDate.HasValue && PasswordExpiryDate.Value <= DateTime.Now;
        }

        /// <summary>
        /// التحقق من الحاجة لتغيير كلمة المرور
        /// </summary>
        /// <returns>true إذا كان يجب تغيير كلمة المرور</returns>
        public bool ShouldChangePassword()
        {
            return MustChangePassword || IsPasswordExpired();
        }

        /// <summary>
        /// قفل الحساب
        /// </summary>
        /// <param name="reason">سبب القفل</param>
        /// <param name="lockedBy">من قام بالقفل</param>
        public void LockAccount(string reason, string lockedBy)
        {
            IsLocked = true;
            LockedDate = DateTime.Now;
            LockReason = reason;
            ModifiedBy = lockedBy;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// إلغاء قفل الحساب
        /// </summary>
        /// <param name="unlockedBy">من قام بإلغاء القفل</param>
        public void UnlockAccount(string unlockedBy)
        {
            IsLocked = false;
            LockedDate = null;
            LockReason = null;
            FailedLoginAttempts = 0;
            LastFailedLoginDate = null;
            ModifiedBy = unlockedBy;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// تسجيل محاولة دخول فاشلة
        /// </summary>
        /// <param name="ipAddress">عنوان IP</param>
        /// <param name="maxAttempts">الحد الأقصى للمحاولات</param>
        public void RecordFailedLogin(string ipAddress, int maxAttempts = 5)
        {
            FailedLoginAttempts++;
            LastFailedLoginDate = DateTime.Now;
            
            if (FailedLoginAttempts >= maxAttempts)
            {
                LockAccount($"تم قفل الحساب بسبب {maxAttempts} محاولات دخول فاشلة", "النظام");
            }
        }

        /// <summary>
        /// تسجيل دخول ناجح
        /// </summary>
        /// <param name="ipAddress">عنوان IP</param>
        public void RecordSuccessfulLogin(string ipAddress)
        {
            LastLoginDate = DateTime.Now;
            LastLoginIP = ipAddress;
            FailedLoginAttempts = 0;
            LastFailedLoginDate = null;
        }

        /// <summary>
        /// تحديث كلمة المرور
        /// </summary>
        /// <param name="newPasswordHash">كلمة المرور الجديدة المشفرة</param>
        /// <param name="newSalt">الملح الجديد</param>
        /// <param name="expiryDays">عدد أيام انتهاء الصلاحية</param>
        public void UpdatePassword(string newPasswordHash, string newSalt, int expiryDays = 90)
        {
            PasswordHash = newPasswordHash;
            PasswordSalt = newSalt;
            PasswordExpiryDate = DateTime.Now.AddDays(expiryDays);
            MustChangePassword = false;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// التحقق من صلاحية معينة
        /// </summary>
        /// <param name="permission">اسم الصلاحية</param>
        /// <returns>true إذا كان لديه الصلاحية</returns>
        public bool HasPermission(string permission)
        {
            if (IsSystemAdmin)
                return true;

            return permission.ToLower() switch
            {
                "manage_users" => CanManageUsers,
                "view_reports" => CanViewReports,
                "manage_products" => CanManageProducts,
                "process_sales" => CanProcessSales,
                "access_system" => CanAccessSystem,
                _ => false
            };
        }

        /// <summary>
        /// الحصول على العمر
        /// </summary>
        /// <returns>العمر بالسنوات</returns>
        public int? GetAge()
        {
            if (!DateOfBirth.HasValue)
                return null;

            var today = DateTime.Today;
            var age = today.Year - DateOfBirth.Value.Year;
            
            if (DateOfBirth.Value.Date > today.AddYears(-age))
                age--;
                
            return age;
        }

        /// <summary>
        /// الحصول على مدة الخدمة
        /// </summary>
        /// <returns>مدة الخدمة بالسنوات</returns>
        public double? GetServiceYears()
        {
            if (!HireDate.HasValue)
                return null;

            return (DateTime.Now - HireDate.Value).TotalDays / 365.25;
        }

        #endregion

        #region عمليات النسخ والتحويل

        /// <summary>
        /// تحويل المستخدم إلى نص وصفي
        /// </summary>
        /// <returns>وصف نصي للمستخدم</returns>
        public override string ToString()
        {
            return $"{Username} - {FullName} ({RoleName})";
        }

        /// <summary>
        /// إنشاء نسخة مبسطة من المستخدم (بدون معلومات حساسة)
        /// </summary>
        /// <returns>نسخة مبسطة من المستخدم</returns>
        public UserSummary ToSummary()
        {
            return new UserSummary
            {
                UserID = this.UserID,
                Username = this.Username,
                FullName = this.FullName,
                Email = this.Email,
                RoleName = this.RoleName,
                IsActive = this.IsActive,
                LastLoginDate = this.LastLoginDate
            };
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// ملخص المستخدم (بدون معلومات حساسة)
    /// </summary>
    public class UserSummary
    {
        public int UserID { get; set; }
        public string Username { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }
        public string RoleName { get; set; }
        public bool IsActive { get; set; }
        public DateTime? LastLoginDate { get; set; }
    }

    /// <summary>
    /// الجنس
    /// </summary>
    public static class Gender
    {
        public const string Male = "ذكر";
        public const string Female = "أنثى";
    }

    #endregion
}
