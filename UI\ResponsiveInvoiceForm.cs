using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// نموذج الفاتورة المتجاوب مع جدول منتجات متكيف
    /// </summary>
    public class ResponsiveInvoiceForm : UserControl
    {
        #region المتغيرات

        private Panel _headerContainer;
        private Panel _customerContainer;
        private Panel _productsContainer;
        private Panel _totalsContainer;
        private Panel _paymentContainer;
        private Panel _actionsContainer;

        // معلومات العميل المتجاوبة
        private ComboBox _customerComboBox;
        private Button _addCustomerButton;
        private Label _customerInfoLabel;

        // جدول المنتجات المتجاوب
        private DataGridView _productsGrid;
        private Button _addProductButton;
        private TextBox _barcodeTextBox;

        // الإجماليات المتجاوبة
        private Label _subtotalLabel;
        private Label _taxLabel;
        private Label _discountLabel;
        private Label _totalLabel;
        private NumericUpDown _discountNumeric;

        // طرق الدفع المتجاوبة
        private RadioButton _cashRadio;
        private RadioButton _cardRadio;
        private RadioButton _installmentRadio;
        private Panel _installmentPanel;
        private NumericUpDown _installmentMonthsNumeric;

        // الأزرار المتجاوبة
        private Button _saveButton;
        private Button _printButton;
        private Button _clearButton;

        // البيانات
        private List<ResponsiveInvoiceItem> _invoiceItems;
        private decimal _subtotal;
        private decimal _tax;
        private decimal _discount;
        private decimal _total;

        #endregion

        #region الأحداث

        public event EventHandler InvoiceSaved;
        public event EventHandler InvoicePrinted;
        public event EventHandler InvoiceCleared;

        #endregion

        #region البناء والتهيئة

        public ResponsiveInvoiceForm()
        {
            _invoiceItems = new List<ResponsiveInvoiceItem>();
            InitializeComponent();
            SetupResponsiveDesign();
            SetupEvents();
        }

        private void InitializeComponent()
        {
            // إعدادات النموذج المتجاوبة
            BackColor = ResponsiveDesignSystem.Colors.Background;
            Dock = DockStyle.Fill;
            AutoScroll = true;
            Padding = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetLargeSpacing());

            CreateResponsiveContainers();
            CreateHeaderSection();
            CreateCustomerSection();
            CreateProductsSection();
            CreateTotalsSection();
            CreatePaymentSection();
            CreateActionsSection();
        }

        private void CreateResponsiveContainers()
        {
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetLargeSpacing();
            var sectionHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight() * 3;

            // حاوي الرأس
            _headerContainer = ResponsiveLayoutSystem.CreateResponsiveRow();
            _headerContainer.Height = sectionHeight;

            // حاوي العميل
            _customerContainer = ResponsiveLayoutSystem.CreateResponsiveRow();
            _customerContainer.Height = sectionHeight;
            _customerContainer.Location = new Point(0, sectionHeight + spacing);

            // حاوي المنتجات
            _productsContainer = ResponsiveLayoutSystem.CreateResponsiveRow();
            _productsContainer.Height = sectionHeight * 3;
            _productsContainer.Location = new Point(0, (sectionHeight * 2) + (spacing * 2));

            // حاوي الإجماليات
            _totalsContainer = ResponsiveLayoutSystem.CreateResponsiveColumn(6);
            _totalsContainer.Height = sectionHeight * 2;
            _totalsContainer.Location = new Point(0, (sectionHeight * 5) + (spacing * 3));
            _totalsContainer.Tag = 6; // عدد الأعمدة

            // حاوي طرق الدفع
            _paymentContainer = ResponsiveLayoutSystem.CreateResponsiveColumn(6);
            _paymentContainer.Height = sectionHeight * 2;
            _paymentContainer.Location = new Point(0, (sectionHeight * 5) + (spacing * 3));
            _paymentContainer.Tag = 6; // عدد الأعمدة

            // حاوي الأزرار
            _actionsContainer = ResponsiveLayoutSystem.CreateResponsiveRow();
            _actionsContainer.Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetLargeButtonHeight() + spacing;
            _actionsContainer.Location = new Point(0, (sectionHeight * 7) + (spacing * 4));

            Controls.AddRange(new Control[] 
            { 
                _headerContainer, _customerContainer, _productsContainer, 
                _totalsContainer, _paymentContainer, _actionsContainer 
            });
        }

        private void CreateHeaderSection()
        {
            var headerCard = new Panel
            {
                BackColor = ResponsiveDesignSystem.Colors.Surface,
                Dock = DockStyle.Fill,
                Padding = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing())
            };

            var titleLabel = new Label
            {
                Text = "💰 إنشاء فاتورة جديدة",
                Font = ResponsiveDesignSystem.Fonts.GetHeading3(),
                ForeColor = ResponsiveDesignSystem.Colors.Primary,
                Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight(),
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            var invoiceNumberLabel = new Label
            {
                Text = $"رقم الفاتورة: INV-{DateTime.Now:yyyyMMdd}-001",
                Font = ResponsiveDesignSystem.Fonts.GetBody(),
                ForeColor = ResponsiveDesignSystem.Colors.TextSecondary,
                Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetInputHeight(),
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            var dateLabel = new Label
            {
                Text = $"التاريخ: {DateTime.Now:yyyy/MM/dd HH:mm}",
                Font = ResponsiveDesignSystem.Fonts.GetBody(),
                ForeColor = ResponsiveDesignSystem.Colors.TextSecondary,
                Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetInputHeight(),
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            headerCard.Controls.AddRange(new Control[] { dateLabel, invoiceNumberLabel, titleLabel });
            headerCard.Paint += (s, e) => ResponsiveDesignSystem.DrawResponsiveCard(e.Graphics, headerCard.ClientRectangle, ResponsiveDesignSystem.Colors.Surface);

            _headerContainer.Controls.Add(headerCard);
        }

        private void CreateCustomerSection()
        {
            var customerCard = new Panel
            {
                BackColor = ResponsiveDesignSystem.Colors.Surface,
                Dock = DockStyle.Fill,
                Padding = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing())
            };

            var customerLabel = new Label
            {
                Text = "👤 معلومات العميل",
                Font = ResponsiveDesignSystem.Fonts.GetHeading5(),
                ForeColor = ResponsiveDesignSystem.Colors.TextPrimary,
                Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight(),
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            var customerControlsPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent
            };

            // إنشاء العناصر المتجاوبة
            CreateCustomerControls(customerControlsPanel);

            customerCard.Controls.AddRange(new Control[] { customerControlsPanel, customerLabel });
            customerCard.Paint += (s, e) => ResponsiveDesignSystem.DrawResponsiveCard(e.Graphics, customerCard.ClientRectangle, ResponsiveDesignSystem.Colors.Surface);

            _customerContainer.Controls.Add(customerCard);
        }

        private void CreateCustomerControls(Panel container)
        {
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();
            var controlHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetInputHeight();

            _customerComboBox = new ComboBox
            {
                Font = ResponsiveDesignSystem.Fonts.GetBody(),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes,
                Height = controlHeight,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            _addCustomerButton = new Button
            {
                Text = "➕ إضافة عميل",
                Font = ResponsiveDesignSystem.Fonts.GetButton(),
                FlatStyle = FlatStyle.Flat,
                BackColor = ResponsiveDesignSystem.Colors.Secondary,
                ForeColor = ResponsiveDesignSystem.Colors.TextOnPrimary,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes,
                Height = controlHeight
            };
            _addCustomerButton.FlatAppearance.BorderSize = 0;

            _customerInfoLabel = new Label
            {
                Text = "لم يتم تحديد عميل",
                Font = ResponsiveDesignSystem.Fonts.GetCaption(),
                ForeColor = ResponsiveDesignSystem.Colors.TextSecondary,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes,
                Height = controlHeight,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            // ترتيب العناصر بشكل متجاوب
            ArrangeCustomerControls(container, spacing, controlHeight);
        }

        private void ArrangeCustomerControls(Panel container, int spacing, int controlHeight)
        {
            var screenSize = ResponsiveLayoutSystem.GetCurrentScreenSize();
            var buttonWidth = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight() * 3;

            if (screenSize >= ResponsiveLayoutSystem.ScreenSize.Medium)
            {
                // ترتيب أفقي للشاشات الكبيرة
                var comboWidth = container.Width - buttonWidth - (spacing * 3);
                
                _customerComboBox.Size = new Size(comboWidth, controlHeight);
                _customerComboBox.Location = new Point(spacing, spacing);

                _addCustomerButton.Size = new Size(buttonWidth, controlHeight);
                _addCustomerButton.Location = new Point(comboWidth + (spacing * 2), spacing);

                _customerInfoLabel.Size = new Size(container.Width - (spacing * 2), controlHeight);
                _customerInfoLabel.Location = new Point(spacing, controlHeight + (spacing * 2));
            }
            else
            {
                // ترتيب عمودي للشاشات الصغيرة
                var controlWidth = container.Width - (spacing * 2);
                var y = spacing;

                _customerComboBox.Size = new Size(controlWidth, controlHeight);
                _customerComboBox.Location = new Point(spacing, y);
                y += controlHeight + spacing;

                _addCustomerButton.Size = new Size(controlWidth, controlHeight);
                _addCustomerButton.Location = new Point(spacing, y);
                y += controlHeight + spacing;

                _customerInfoLabel.Size = new Size(controlWidth, controlHeight);
                _customerInfoLabel.Location = new Point(spacing, y);
            }

            container.Controls.AddRange(new Control[] { _customerComboBox, _addCustomerButton, _customerInfoLabel });
        }

        private void CreateProductsSection()
        {
            var productsCard = new Panel
            {
                BackColor = ResponsiveDesignSystem.Colors.Surface,
                Dock = DockStyle.Fill,
                Padding = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing())
            };

            var productsLabel = new Label
            {
                Text = "📦 المنتجات",
                Font = ResponsiveDesignSystem.Fonts.GetHeading5(),
                ForeColor = ResponsiveDesignSystem.Colors.TextPrimary,
                Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight(),
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            var productsControlsPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent
            };

            CreateProductsControls(productsControlsPanel);

            productsCard.Controls.AddRange(new Control[] { productsControlsPanel, productsLabel });
            productsCard.Paint += (s, e) => ResponsiveDesignSystem.DrawResponsiveCard(e.Graphics, productsCard.ClientRectangle, ResponsiveDesignSystem.Colors.Surface);

            _productsContainer.Controls.Add(productsCard);
        }

        private void CreateProductsControls(Panel container)
        {
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();
            var controlHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetInputHeight();

            // حقل الباركود
            _barcodeTextBox = new TextBox
            {
                Text = "امسح الباركود أو أدخل كود المنتج",
                Font = ResponsiveDesignSystem.Fonts.GetBody(),
                RightToLeft = RightToLeft.Yes,
                Height = controlHeight,
                ForeColor = ResponsiveDesignSystem.Colors.TextSecondary
            };

            _barcodeTextBox.GotFocus += (s, e) =>
            {
                if (_barcodeTextBox.Text == "امسح الباركود أو أدخل كود المنتج")
                {
                    _barcodeTextBox.Text = "";
                    _barcodeTextBox.ForeColor = ResponsiveDesignSystem.Colors.TextPrimary;
                }
            };

            _addProductButton = new Button
            {
                Text = "➕ إضافة منتج",
                Font = ResponsiveDesignSystem.Fonts.GetButton(),
                FlatStyle = FlatStyle.Flat,
                BackColor = ResponsiveDesignSystem.Colors.Primary,
                ForeColor = ResponsiveDesignSystem.Colors.TextOnPrimary,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes,
                Height = controlHeight
            };
            _addProductButton.FlatAppearance.BorderSize = 0;

            // جدول المنتجات المتجاوب
            CreateResponsiveProductsGrid(container, spacing, controlHeight);

            // ترتيب عناصر التحكم
            ArrangeProductsControls(container, spacing, controlHeight);
        }

        private void CreateResponsiveProductsGrid(Panel container, int spacing, int controlHeight)
        {
            _productsGrid = new DataGridView
            {
                BackgroundColor = ResponsiveDesignSystem.Colors.Surface,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                EnableHeadersVisualStyles = false,
                RowHeadersVisible = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                Font = ResponsiveDesignSystem.Fonts.GetBody(),
                RightToLeft = RightToLeft.Yes,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            SetupResponsiveProductsGrid();
        }

        private void SetupResponsiveProductsGrid()
        {
            _productsGrid.Columns.Clear();

            var columns = new[]
            {
                new { Name = "Name", Header = "اسم المنتج", Width = 40 },
                new { Name = "Price", Header = "السعر", Width = 15 },
                new { Name = "Quantity", Header = "الكمية", Width = 15 },
                new { Name = "Discount", Header = "الخصم %", Width = 15 },
                new { Name = "Total", Header = "الإجمالي", Width = 15 }
            };

            foreach (var col in columns)
            {
                var column = new DataGridViewTextBoxColumn
                {
                    Name = col.Name,
                    HeaderText = col.Header,
                    FillWeight = col.Width,
                    ReadOnly = col.Name == "Total" || col.Name == "Name" || col.Name == "Price"
                };
                _productsGrid.Columns.Add(column);
            }

            // تنسيق الرأس المتجاوب
            _productsGrid.ColumnHeadersDefaultCellStyle.BackColor = ResponsiveDesignSystem.Colors.Primary;
            _productsGrid.ColumnHeadersDefaultCellStyle.ForeColor = ResponsiveDesignSystem.Colors.TextOnPrimary;
            _productsGrid.ColumnHeadersDefaultCellStyle.Font = ResponsiveDesignSystem.Fonts.GetBody();
            _productsGrid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _productsGrid.ColumnHeadersHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight();

            // تنسيق الصفوف المتجاوب
            _productsGrid.DefaultCellStyle.BackColor = ResponsiveDesignSystem.Colors.Surface;
            _productsGrid.DefaultCellStyle.ForeColor = ResponsiveDesignSystem.Colors.TextPrimary;
            _productsGrid.DefaultCellStyle.SelectionBackColor = ResponsiveDesignSystem.Colors.PrimaryLight;
            _productsGrid.DefaultCellStyle.SelectionForeColor = ResponsiveDesignSystem.Colors.TextPrimary;
            _productsGrid.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _productsGrid.RowTemplate.Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetInputHeight();
        }

        private void ArrangeProductsControls(Panel container, int spacing, int controlHeight)
        {
            var screenSize = ResponsiveLayoutSystem.GetCurrentScreenSize();
            var buttonWidth = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight() * 3;

            if (screenSize >= ResponsiveLayoutSystem.ScreenSize.Medium)
            {
                // ترتيب أفقي للشاشات الكبيرة
                var barcodeWidth = container.Width - buttonWidth - (spacing * 3);

                _barcodeTextBox.Size = new Size(barcodeWidth, controlHeight);
                _barcodeTextBox.Location = new Point(spacing, spacing);

                _addProductButton.Size = new Size(buttonWidth, controlHeight);
                _addProductButton.Location = new Point(barcodeWidth + (spacing * 2), spacing);
            }
            else
            {
                // ترتيب عمودي للشاشات الصغيرة
                var controlWidth = container.Width - (spacing * 2);

                _barcodeTextBox.Size = new Size(controlWidth, controlHeight);
                _barcodeTextBox.Location = new Point(spacing, spacing);

                _addProductButton.Size = new Size(controlWidth, controlHeight);
                _addProductButton.Location = new Point(spacing, controlHeight + (spacing * 2));
            }

            // جدول المنتجات
            var gridY = screenSize >= ResponsiveLayoutSystem.ScreenSize.Medium ? 
                controlHeight + (spacing * 2) : 
                (controlHeight * 2) + (spacing * 3);

            _productsGrid.Size = new Size(container.Width - (spacing * 2), container.Height - gridY - spacing);
            _productsGrid.Location = new Point(spacing, gridY);

            container.Controls.AddRange(new Control[] { _barcodeTextBox, _addProductButton, _productsGrid });
        }

        private void CreateTotalsSection()
        {
            var totalsCard = new Panel
            {
                BackColor = ResponsiveDesignSystem.Colors.Surface,
                Dock = DockStyle.Fill,
                Padding = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing())
            };

            var totalsLabel = new Label
            {
                Text = "💰 الإجماليات",
                Font = ResponsiveDesignSystem.Fonts.GetHeading5(),
                ForeColor = ResponsiveDesignSystem.Colors.TextPrimary,
                Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight(),
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            CreateTotalsControls(totalsCard);

            totalsCard.Controls.Add(totalsLabel);
            totalsCard.Paint += (s, e) => ResponsiveDesignSystem.DrawResponsiveCard(e.Graphics, totalsCard.ClientRectangle, ResponsiveDesignSystem.Colors.Surface);

            _totalsContainer.Controls.Add(totalsCard);
        }

        private void CreateTotalsControls(Panel container)
        {
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();
            var labelHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetInputHeight();
            var y = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight() + spacing;

            // المجموع الفرعي
            _subtotalLabel = CreateTotalLabel("المجموع الفرعي:", "0.00 ر.س", y);
            y += labelHeight + spacing;

            // الضريبة
            _taxLabel = CreateTotalLabel("الضريبة (15%):", "0.00 ر.س", y);
            y += labelHeight + spacing;

            // الخصم
            var discountPanel = new Panel
            {
                Size = new Size(container.Width - (spacing * 2), labelHeight),
                Location = new Point(spacing, y),
                BackColor = Color.Transparent
            };

            var discountLabel = new Label
            {
                Text = "الخصم:",
                Font = ResponsiveDesignSystem.Fonts.GetBody(),
                ForeColor = ResponsiveDesignSystem.Colors.TextPrimary,
                Size = new Size(100, labelHeight),
                Location = new Point(discountPanel.Width - 100, 0),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            _discountNumeric = new NumericUpDown
            {
                Size = new Size(80, labelHeight),
                Location = new Point(discountPanel.Width - 200, 0),
                Font = ResponsiveDesignSystem.Fonts.GetNumbers(),
                Maximum = 100,
                DecimalPlaces = 2
            };

            _discountLabel = new Label
            {
                Text = "0.00 ر.س",
                Font = ResponsiveDesignSystem.Fonts.GetNumbersLarge(),
                ForeColor = ResponsiveDesignSystem.Colors.Warning,
                Size = new Size(100, labelHeight),
                Location = new Point(spacing, 0),
                TextAlign = ContentAlignment.MiddleLeft
            };

            discountPanel.Controls.AddRange(new Control[] { discountLabel, _discountNumeric, _discountLabel });
            y += labelHeight + spacing;

            // الإجمالي النهائي
            _totalLabel = CreateTotalLabel("الإجمالي النهائي:", "0.00 ر.س", y, true);

            container.Controls.AddRange(new Control[] { _subtotalLabel, _taxLabel, discountPanel, _totalLabel });
        }

        private Label CreateTotalLabel(string text, string value, int y, bool isTotal = false)
        {
            var container = new Panel
            {
                Size = new Size(_totalsContainer.Width - ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing() * 2,
                               ResponsiveLayoutSystem.ResponsiveDimensions.GetInputHeight()),
                Location = new Point(ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing(), y),
                BackColor = Color.Transparent
            };

            var titleLabel = new Label
            {
                Text = text,
                Font = isTotal ? ResponsiveDesignSystem.Fonts.GetHeading5() : ResponsiveDesignSystem.Fonts.GetBody(),
                ForeColor = isTotal ? ResponsiveDesignSystem.Colors.Primary : ResponsiveDesignSystem.Colors.TextPrimary,
                Size = new Size(container.Width / 2, container.Height),
                Location = new Point(container.Width / 2, 0),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            var valueLabel = new Label
            {
                Text = value,
                Font = isTotal ? ResponsiveDesignSystem.Fonts.GetNumbersLarge() : ResponsiveDesignSystem.Fonts.GetNumbers(),
                ForeColor = isTotal ? ResponsiveDesignSystem.Colors.Primary : ResponsiveDesignSystem.Colors.TextSecondary,
                Size = new Size(container.Width / 2, container.Height),
                Location = new Point(0, 0),
                TextAlign = ContentAlignment.MiddleLeft
            };

            container.Controls.AddRange(new Control[] { titleLabel, valueLabel });
            return valueLabel; // إرجاع تسمية القيمة للتحديث لاحقاً
        }

        private void CreatePaymentSection()
        {
            var paymentCard = new Panel
            {
                BackColor = ResponsiveDesignSystem.Colors.Surface,
                Dock = DockStyle.Fill,
                Padding = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing())
            };

            var paymentLabel = new Label
            {
                Text = "💳 طريقة الدفع",
                Font = ResponsiveDesignSystem.Fonts.GetHeading5(),
                ForeColor = ResponsiveDesignSystem.Colors.TextPrimary,
                Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight(),
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            CreatePaymentControls(paymentCard);

            paymentCard.Controls.Add(paymentLabel);
            paymentCard.Paint += (s, e) => ResponsiveDesignSystem.DrawResponsiveCard(e.Graphics, paymentCard.ClientRectangle, ResponsiveDesignSystem.Colors.Surface);

            _paymentContainer.Controls.Add(paymentCard);
        }

        private void CreatePaymentControls(Panel container)
        {
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();
            var radioHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetInputHeight();
            var y = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight() + spacing;

            _cashRadio = new RadioButton
            {
                Text = "💵 نقدي",
                Font = ResponsiveDesignSystem.Fonts.GetBody(),
                ForeColor = ResponsiveDesignSystem.Colors.TextPrimary,
                Size = new Size(container.Width - (spacing * 2), radioHeight),
                Location = new Point(spacing, y),
                RightToLeft = RightToLeft.Yes,
                Checked = true
            };
            y += radioHeight + spacing;

            _cardRadio = new RadioButton
            {
                Text = "💳 بطاقة ائتمان",
                Font = ResponsiveDesignSystem.Fonts.GetBody(),
                ForeColor = ResponsiveDesignSystem.Colors.TextPrimary,
                Size = new Size(container.Width - (spacing * 2), radioHeight),
                Location = new Point(spacing, y),
                RightToLeft = RightToLeft.Yes
            };
            y += radioHeight + spacing;

            _installmentRadio = new RadioButton
            {
                Text = "📅 أقساط",
                Font = ResponsiveDesignSystem.Fonts.GetBody(),
                ForeColor = ResponsiveDesignSystem.Colors.TextPrimary,
                Size = new Size(container.Width - (spacing * 2), radioHeight),
                Location = new Point(spacing, y),
                RightToLeft = RightToLeft.Yes
            };
            y += radioHeight + spacing;

            // لوحة الأقساط
            _installmentPanel = new Panel
            {
                Size = new Size(container.Width - (spacing * 2), radioHeight),
                Location = new Point(spacing, y),
                BackColor = Color.Transparent,
                Visible = false
            };

            var monthsLabel = new Label
            {
                Text = "عدد الأشهر:",
                Font = ResponsiveDesignSystem.Fonts.GetBody(),
                ForeColor = ResponsiveDesignSystem.Colors.TextPrimary,
                Size = new Size(100, radioHeight),
                Location = new Point(_installmentPanel.Width - 100, 0),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            _installmentMonthsNumeric = new NumericUpDown
            {
                Size = new Size(80, radioHeight),
                Location = new Point(_installmentPanel.Width - 200, 0),
                Font = ResponsiveDesignSystem.Fonts.GetNumbers(),
                Minimum = 2,
                Maximum = 24,
                Value = 3
            };

            _installmentPanel.Controls.AddRange(new Control[] { monthsLabel, _installmentMonthsNumeric });

            _installmentRadio.CheckedChanged += (s, e) => _installmentPanel.Visible = _installmentRadio.Checked;

            container.Controls.AddRange(new Control[] { _cashRadio, _cardRadio, _installmentRadio, _installmentPanel });
        }

        private void CreateActionsSection()
        {
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();
            var buttonHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetLargeButtonHeight();

            _saveButton = new Button
            {
                Text = "💾 حفظ الفاتورة",
                Font = ResponsiveDesignSystem.Fonts.GetButtonLarge(),
                FlatStyle = FlatStyle.Flat,
                BackColor = ResponsiveDesignSystem.Colors.Success,
                ForeColor = ResponsiveDesignSystem.Colors.TextOnPrimary,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes,
                Height = buttonHeight,
                Tag = 4 // عدد الأعمدة
            };
            _saveButton.FlatAppearance.BorderSize = 0;

            _printButton = new Button
            {
                Text = "🖨️ طباعة",
                Font = ResponsiveDesignSystem.Fonts.GetButtonLarge(),
                FlatStyle = FlatStyle.Flat,
                BackColor = ResponsiveDesignSystem.Colors.Primary,
                ForeColor = ResponsiveDesignSystem.Colors.TextOnPrimary,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes,
                Height = buttonHeight,
                Tag = 4 // عدد الأعمدة
            };
            _printButton.FlatAppearance.BorderSize = 0;

            _clearButton = new Button
            {
                Text = "🗑️ مسح",
                Font = ResponsiveDesignSystem.Fonts.GetButtonLarge(),
                FlatStyle = FlatStyle.Flat,
                BackColor = ResponsiveDesignSystem.Colors.Error,
                ForeColor = ResponsiveDesignSystem.Colors.TextOnPrimary,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes,
                Height = buttonHeight,
                Tag = 4 // عدد الأعمدة
            };
            _clearButton.FlatAppearance.BorderSize = 0;

            _actionsContainer.Controls.AddRange(new Control[] { _saveButton, _printButton, _clearButton });
        }

        private void SetupResponsiveDesign()
        {
            RightToLeft = RightToLeft.Yes;
            Resize += OnResize;
        }

        private void SetupEvents()
        {
            _saveButton.Click += (s, e) => InvoiceSaved?.Invoke(this, e);
            _printButton.Click += (s, e) => InvoicePrinted?.Invoke(this, e);
            _clearButton.Click += (s, e) => InvoiceCleared?.Invoke(this, e);
        }

        private void OnResize(object sender, EventArgs e)
        {
            ResponsiveLayoutSystem.HandleResize(this.FindForm());
            UpdateResponsiveLayout();
        }

        private void UpdateResponsiveLayout()
        {
            ResponsiveLayoutSystem.UpdateRowLayout(_headerContainer);
            ResponsiveLayoutSystem.UpdateRowLayout(_customerContainer);
            ResponsiveLayoutSystem.UpdateRowLayout(_productsContainer);
            ResponsiveLayoutSystem.UpdateRowLayout(_actionsContainer);
        }

        #endregion
    }

    #region الكلاسات المساعدة

    public class ResponsiveInvoiceItem
    {
        public string ProductName { get; set; }
        public decimal Price { get; set; }
        public int Quantity { get; set; }
        public decimal Discount { get; set; }
        public decimal Total => (Price * Quantity) * (1 - Discount / 100);
    }

    #endregion
}
