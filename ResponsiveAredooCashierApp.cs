using System;
using System.Drawing;
using System.Windows.Forms;
using System.Globalization;
using System.Threading;
using AredooCashier.UI;

namespace AredooCashier
{
    /// <summary>
    /// التطبيق الرئيسي المتجاوب لكاشير أريدو
    /// يدعم جميع أحجام الشاشات من 1366x768 إلى 4K
    /// </summary>
    public partial class ResponsiveAredooCashierApp : Form
    {
        #region المتغيرات

        private ResponsiveTopBar _topBar;
        private ResponsiveSidebar _sidebar;
        private Panel _contentPanel;
        private UserControl _currentView;

        // الواجهات المتجاوبة
        private ResponsiveDashboard _dashboardView;
        private ResponsiveInvoiceForm _invoiceView;
        private ResponsiveCustomersView _customersView;
        private UserControl _productsView;
        private UserControl _reportsView;
        private UserControl _settingsView;

        // معلومات التطبيق
        private string _currentUser = "أحمد محمد الكاشير";
        private string _storeName = "متجر الأمل التجاري";
        private DateTime _sessionStartTime = DateTime.Now;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ التطبيق الرئيسي المتجاوب
        /// </summary>
        public ResponsiveAredooCashierApp()
        {
            try
            {
                InitializeComponent();
                SetupArabicCulture();
                SetupResponsiveDesign();
                SetupEvents();
                LoadDashboard();

                // تأخير رسالة الترحيب لتجنب مشاكل التحميل
                this.Load += (s, e) => ShowWelcomeMessage();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة التطبيق: {ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تهيئة المكونات المتجاوبة
        /// </summary>
        private void InitializeComponent()
        {
            // إعدادات النموذج الرئيسي المتجاوبة
            Text = "أريدو الكاشير - نظام نقاط البيع المتجاوب";
            StartPosition = FormStartPosition.CenterScreen;
            WindowState = FormWindowState.Maximized;
            BackColor = ResponsiveDesignSystem.Colors.Background;
            Font = ResponsiveDesignSystem.Fonts.GetBody();
            Icon = SystemIcons.Application;

            // تطبيق الحد الأدنى للحجم بناءً على حجم الشاشة
            var screenSize = ResponsiveLayoutSystem.GetCurrentScreenSize();
            MinimumSize = screenSize switch
            {
                ResponsiveLayoutSystem.ScreenSize.Small => new Size(1024, 768),
                ResponsiveLayoutSystem.ScreenSize.Medium => new Size(1280, 800),
                ResponsiveLayoutSystem.ScreenSize.Large => new Size(1600, 900),
                ResponsiveLayoutSystem.ScreenSize.ExtraLarge => new Size(1920, 1080),
                _ => new Size(1024, 768)
            };

            CreateResponsiveComponents();
            ArrangeResponsiveLayout();
        }

        /// <summary>
        /// إنشاء المكونات المتجاوبة
        /// </summary>
        private void CreateResponsiveComponents()
        {
            // الشريط العلوي المتجاوب
            _topBar = new ResponsiveTopBar
            {
                UserName = _currentUser,
                UserRole = "كاشير رئيسي",
                StoreName = _storeName,
                IsOnline = true
            };

            // الشريط الجانبي المتجاوب
            _sidebar = new ResponsiveSidebar();

            // لوحة المحتوى المتجاوبة
            _contentPanel = new Panel
            {
                BackColor = ResponsiveDesignSystem.Colors.Background,
                Padding = new Padding(0)
            };

            // إنشاء الواجهات المتجاوبة
            CreateResponsiveViews();
        }

        /// <summary>
        /// إنشاء الواجهات المتجاوبة
        /// </summary>
        private void CreateResponsiveViews()
        {
            // لوحة المعلومات المتجاوبة
            _dashboardView = new ResponsiveDashboard();
            _dashboardView.NewInvoiceClicked += (s, e) => LoadInvoiceView();
            _dashboardView.AddCustomerClicked += (s, e) => LoadCustomersView();
            _dashboardView.AddProductClicked += (s, e) => LoadProductsView();
            _dashboardView.OpenDrawerClicked += (s, e) => OpenCashDrawer();
            _dashboardView.CloseSessionClicked += (s, e) => CloseSession();

            // نموذج الفاتورة المتجاوب
            _invoiceView = new ResponsiveInvoiceForm();
            _invoiceView.InvoiceSaved += OnInvoiceSaved;
            _invoiceView.InvoicePrinted += OnInvoicePrinted;
            _invoiceView.InvoiceCleared += OnInvoiceCleared;

            // إدارة العملاء المتجاوبة
            _customersView = new ResponsiveCustomersView();
            _customersView.CustomerSelected += OnCustomerSelected;
            _customersView.CustomerAdded += OnCustomerAdded;
            _customersView.CustomerEdited += OnCustomerEdited;
            _customersView.CustomerDeleted += OnCustomerDeleted;

            // الواجهات الأخرى (مبسطة ومتجاوبة)
            _productsView = CreateResponsivePlaceholderView("📦 إدارة المنتجات", "هنا سيتم عرض قائمة المنتجات وإدارة المخزون بشكل متجاوب");
            _reportsView = CreateResponsivePlaceholderView("📊 التقارير والإحصائيات", "هنا سيتم عرض التقارير المختلفة والرسوم البيانية المتجاوبة");
            _settingsView = CreateResponsivePlaceholderView("⚙️ الإعدادات", "هنا سيتم عرض إعدادات النظام والتخصيص المتجاوب");
        }

        /// <summary>
        /// إنشاء واجهة مؤقتة متجاوبة
        /// </summary>
        private UserControl CreateResponsivePlaceholderView(string title, string description)
        {
            var view = new UserControl
            {
                Dock = DockStyle.Fill,
                BackColor = ResponsiveDesignSystem.Colors.Background,
                RightToLeft = RightToLeft.Yes
            };

            var container = ResponsiveLayoutSystem.CreateResponsiveContainer();
            var card = new Panel
            {
                BackColor = ResponsiveDesignSystem.Colors.Surface,
                Anchor = AnchorStyles.None
            };

            // حساب حجم البطاقة بناءً على حجم الشاشة
            var scaleFactor = ResponsiveLayoutSystem.GetScaleFactor();
            var cardWidth = (int)(600 * scaleFactor);
            var cardHeight = (int)(300 * scaleFactor);
            card.Size = new Size(cardWidth, cardHeight);

            var titleLabel = new Label
            {
                Text = title,
                Font = ResponsiveDesignSystem.Fonts.GetHeading2(),
                ForeColor = ResponsiveDesignSystem.Colors.Primary,
                Size = new Size(card.Width - 40, 60),
                Location = new Point(20, 30),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            var descLabel = new Label
            {
                Text = description,
                Font = ResponsiveDesignSystem.Fonts.GetBodyLarge(),
                ForeColor = ResponsiveDesignSystem.Colors.TextSecondary,
                Size = new Size(card.Width - 40, 100),
                Location = new Point(20, 110),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            var comingSoonLabel = new Label
            {
                Text = "🚧 قريباً - قيد التطوير",
                Font = ResponsiveDesignSystem.Fonts.GetHeading4(),
                ForeColor = ResponsiveDesignSystem.Colors.Warning,
                Size = new Size(card.Width - 40, 50),
                Location = new Point(20, 230),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            card.Controls.AddRange(new Control[] { titleLabel, descLabel, comingSoonLabel });
            card.Paint += (s, e) => ResponsiveDesignSystem.DrawResponsiveCard(e.Graphics, card.ClientRectangle, ResponsiveDesignSystem.Colors.Surface);

            // توسيط البطاقة
            view.Resize += (s, e) =>
            {
                card.Location = new Point((view.Width - card.Width) / 2, (view.Height - card.Height) / 2);
            };

            container.Controls.Add(card);
            view.Controls.Add(container);
            return view;
        }

        /// <summary>
        /// ترتيب التخطيط المتجاوب
        /// </summary>
        private void ArrangeResponsiveLayout()
        {
            // ترتيب العناصر بالتخطيط المتجاوب
            _contentPanel.Dock = DockStyle.Fill;
            _sidebar.Dock = DockStyle.Left;
            _topBar.Dock = DockStyle.Top;

            // إضافة العناصر بالترتيب الصحيح
            Controls.Add(_contentPanel);
            Controls.Add(_sidebar);
            Controls.Add(_topBar);

            // تطبيق التخطيط المتجاوب
            ResponsiveLayoutSystem.ApplyResponsiveLayout(this);
        }

        /// <summary>
        /// إعداد الثقافة العربية
        /// </summary>
        private void SetupArabicCulture()
        {
            try
            {
                var arabicCulture = new CultureInfo("ar-SA");
                Thread.CurrentThread.CurrentCulture = arabicCulture;
                Thread.CurrentThread.CurrentUICulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentCulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentUICulture = arabicCulture;

                // تطبيق التخطيط العربي المتجاوب
                RightToLeft = RightToLeft.Yes;
                // إزالة RightToLeftLayout لتجنب مشاكل التخطيط
                // RightToLeftLayout = true;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ بدلاً من عرض MessageBox في البداية
                Console.WriteLine($"تحذير: فشل في تعيين الثقافة العربية - {ex.Message}");
            }
        }

        /// <summary>
        /// إعداد التصميم المتجاوب
        /// </summary>
        private void SetupResponsiveDesign()
        {
            // تطبيق التصميم المتجاوب على النموذج
            SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
            
            // معالجة تغيير حجم النافذة
            Resize += OnFormResize;
            Load += OnFormLoad;
        }

        /// <summary>
        /// إعداد الأحداث
        /// </summary>
        private void SetupEvents()
        {
            // أحداث الشريط العلوي
            _topBar.SettingsClicked += OnSettingsClicked;
            _topBar.LogoutClicked += OnLogoutClicked;
            _topBar.NotificationClicked += OnNotificationClicked;

            // أحداث الشريط الجانبي
            _sidebar.ItemSelected += OnSidebarItemSelected;

            // أحداث النموذج
            FormClosing += OnFormClosing;
        }

        #endregion

        #region التنقل بين الواجهات المتجاوبة

        /// <summary>
        /// تحميل واجهة في لوحة المحتوى المتجاوبة
        /// </summary>
        private void LoadResponsiveView(UserControl view)
        {
            if (_currentView != null)
            {
                _contentPanel.Controls.Remove(_currentView);
            }

            _currentView = view;
            _currentView.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(_currentView);
            _currentView.BringToFront();

            // تطبيق التخطيط المتجاوب على الواجهة الجديدة
            ResponsiveLayoutSystem.ApplyResponsiveLayout(_currentView);
        }

        /// <summary>
        /// تحميل لوحة المعلومات المتجاوبة
        /// </summary>
        private void LoadDashboard()
        {
            LoadResponsiveView(_dashboardView);
        }

        /// <summary>
        /// تحميل نموذج الفاتورة المتجاوب
        /// </summary>
        private void LoadInvoiceView()
        {
            LoadResponsiveView(_invoiceView);
        }

        /// <summary>
        /// تحميل واجهة العملاء المتجاوبة
        /// </summary>
        private void LoadCustomersView()
        {
            LoadResponsiveView(_customersView);
        }

        /// <summary>
        /// تحميل واجهة المنتجات المتجاوبة
        /// </summary>
        private void LoadProductsView()
        {
            LoadResponsiveView(_productsView);
        }

        /// <summary>
        /// تحميل واجهة التقارير المتجاوبة
        /// </summary>
        private void LoadReportsView()
        {
            LoadResponsiveView(_reportsView);
        }

        /// <summary>
        /// تحميل واجهة الإعدادات المتجاوبة
        /// </summary>
        private void LoadSettingsView()
        {
            LoadResponsiveView(_settingsView);
        }

        #endregion

        #region معالجات الأحداث

        /// <summary>
        /// تحديد عنصر من الشريط الجانبي
        /// </summary>
        private void OnSidebarItemSelected(object sender, ResponsiveSidebarItemEventArgs e)
        {
            switch (e.Item.Id)
            {
                case "dashboard":
                    LoadDashboard();
                    break;
                case "sales":
                case "invoices":
                    LoadInvoiceView();
                    break;
                case "customers":
                    LoadCustomersView();
                    break;
                case "products":
                    LoadProductsView();
                    break;
                case "reports":
                    LoadReportsView();
                    break;
                case "installments":
                case "debts":
                case "settings":
                    LoadSettingsView();
                    break;
            }
        }

        /// <summary>
        /// النقر على زر الإعدادات
        /// </summary>
        private void OnSettingsClicked(object sender, EventArgs e)
        {
            LoadSettingsView();
        }

        /// <summary>
        /// النقر على زر تسجيل الخروج
        /// </summary>
        private void OnLogoutClicked(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد تسجيل الخروج من النظام؟\n\nسيتم حفظ جميع البيانات تلقائياً.",
                "تأكيد تسجيل الخروج",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                SaveSessionData();
                Application.Exit();
            }
        }

        /// <summary>
        /// النقر على إشعار
        /// </summary>
        private void OnNotificationClicked(object sender, NotificationClickedEventArgs e)
        {
            var notification = e.Notification;
            var message = $"📢 {notification.Title}\n\n{notification.Message}\n\nالوقت: {notification.Timestamp:yyyy/MM/dd HH:mm}";

            MessageBox.Show(message, "تفاصيل الإشعار", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// تغيير حجم النموذج
        /// </summary>
        private void OnFormResize(object sender, EventArgs e)
        {
            ResponsiveLayoutSystem.HandleResize(this);

            // تحديث تخطيط الواجهة الحالية
            if (_currentView != null)
            {
                ResponsiveLayoutSystem.ApplyResponsiveLayout(_currentView);
            }
        }

        /// <summary>
        /// تحميل النموذج
        /// </summary>
        private void OnFormLoad(object sender, EventArgs e)
        {
            // تطبيق التخطيط المتجاوب الأولي
            ResponsiveLayoutSystem.ApplyResponsiveLayout(this);
        }

        /// <summary>
        /// إغلاق النموذج
        /// </summary>
        private void OnFormClosing(object sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد إغلاق نظام أريدو الكاشير المتجاوب؟\n\nسيتم حفظ جميع البيانات تلقائياً.",
                "تأكيد الإغلاق",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.No)
            {
                e.Cancel = true;
            }
            else
            {
                SaveSessionData();
            }
        }

        #endregion

        #region معالجات أحداث الفاتورة

        /// <summary>
        /// حفظ الفاتورة
        /// </summary>
        private void OnInvoiceSaved(object sender, EventArgs e)
        {
            MessageBox.Show("✅ تم حفظ الفاتورة بنجاح!\n\nتم تحديث المخزون والحسابات تلقائياً.",
                "نجح الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// طباعة الفاتورة
        /// </summary>
        private void OnInvoicePrinted(object sender, EventArgs e)
        {
            MessageBox.Show("🖨️ تم إرسال الفاتورة للطباعة!\n\nيرجى التأكد من جاهزية الطابعة.",
                "نجحت الطباعة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// مسح الفاتورة
        /// </summary>
        private void OnInvoiceCleared(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد مسح جميع بيانات الفاتورة؟\n\nلن يمكن التراجع عن هذا الإجراء.",
                "تأكيد المسح",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                MessageBox.Show("🗑️ تم مسح الفاتورة بنجاح!",
                    "تم المسح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        #endregion

        #region معالجات أحداث العملاء

        /// <summary>
        /// تحديد عميل
        /// </summary>
        private void OnCustomerSelected(object sender, ResponsiveCustomerEventArgs e)
        {
            // يمكن استخدام هذا الحدث لتحديث واجهات أخرى
        }

        /// <summary>
        /// إضافة عميل جديد
        /// </summary>
        private void OnCustomerAdded(object sender, ResponsiveCustomerEventArgs e)
        {
            MessageBox.Show($"✅ تم إضافة العميل '{e.Customer.Name}' بنجاح!",
                "تمت الإضافة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// تعديل عميل
        /// </summary>
        private void OnCustomerEdited(object sender, ResponsiveCustomerEventArgs e)
        {
            MessageBox.Show($"✅ تم تحديث بيانات العميل '{e.Customer.Name}' بنجاح!",
                "تم التحديث", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// حذف عميل
        /// </summary>
        private void OnCustomerDeleted(object sender, ResponsiveCustomerEventArgs e)
        {
            MessageBox.Show($"🗑️ تم حذف العميل '{e.Customer.Name}' بنجاح!",
                "تم الحذف", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion

        #region الوظائف المساعدة

        /// <summary>
        /// فتح درج النقود
        /// </summary>
        private void OpenCashDrawer()
        {
            MessageBox.Show("💵 تم فتح درج النقود!\n\nيرجى عد النقود والتأكد من المبلغ.",
                "درج النقود", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// إغلاق الجلسة
        /// </summary>
        private void CloseSession()
        {
            var sessionDuration = DateTime.Now - _sessionStartTime;
            var message = $"📊 ملخص الجلسة:\n\n" +
                         $"⏰ مدة الجلسة: {sessionDuration.Hours:D2}:{sessionDuration.Minutes:D2}\n" +
                         $"👤 الكاشير: {_currentUser}\n" +
                         $"🏪 المتجر: {_storeName}\n" +
                         $"📅 التاريخ: {DateTime.Now:yyyy/MM/dd}\n\n" +
                         $"هل تريد إغلاق الجلسة وتسجيل الخروج؟";

            var result = MessageBox.Show(message, "إغلاق الجلسة",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                SaveSessionData();
                Application.Exit();
            }
        }

        /// <summary>
        /// حفظ بيانات الجلسة
        /// </summary>
        private void SaveSessionData()
        {
            try
            {
                // هنا يمكن إضافة كود حفظ البيانات في قاعدة البيانات
                // أو ملفات النسخ الاحتياطي

                var sessionData = new
                {
                    User = _currentUser,
                    Store = _storeName,
                    StartTime = _sessionStartTime,
                    EndTime = DateTime.Now,
                    Duration = DateTime.Now - _sessionStartTime
                };

                // محاكاة حفظ البيانات
                System.Threading.Thread.Sleep(500);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تحذير: فشل في حفظ بيانات الجلسة - {ex.Message}",
                    "خطأ في الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// عرض رسالة الترحيب المتجاوبة
        /// </summary>
        private void ShowWelcomeMessage()
        {
            var screenSize = ResponsiveLayoutSystem.GetCurrentScreenSize();
            var screenInfo = screenSize switch
            {
                ResponsiveLayoutSystem.ScreenSize.Small => "شاشة صغيرة (1366x768)",
                ResponsiveLayoutSystem.ScreenSize.Medium => "شاشة متوسطة (1920x1080)",
                ResponsiveLayoutSystem.ScreenSize.Large => "شاشة كبيرة (2560x1440)",
                ResponsiveLayoutSystem.ScreenSize.ExtraLarge => "شاشة عالية الدقة (4K)",
                _ => "شاشة قياسية"
            };

            var scaleFactor = ResponsiveLayoutSystem.GetScaleFactor();
            var dpiInfo = ResponsiveLayoutSystem.GetDpiScaleFactor();

            var message = $"🎉 مرحباً بك في نظام أريدو الكاشير المتجاوب!\n\n" +
                         $"✨ تم تطوير هذا النظام خصيصاً للتجار العرب\n" +
                         $"🎨 تصميم متجاوب يتكيف مع جميع أحجام الشاشات\n" +
                         $"🌐 دعم كامل للغة العربية مع RTL\n" +
                         $"📱 واجهة سهلة الاستخدام ومناسبة للشاشات اللمسية\n\n" +
                         $"📊 معلومات الشاشة الحالية:\n" +
                         $"🖥️ النوع: {screenInfo}\n" +
                         $"📏 معامل التكبير: {scaleFactor:F1}x\n" +
                         $"🔍 دقة العرض: {dpiInfo:F1}x DPI\n\n" +
                         $"🚀 ابدأ رحلتك في إدارة نقاط البيع بكفاءة عالية!";

            MessageBox.Show(message, "مرحباً بك في أريدو المتجاوب",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion
    }
}
