using System;
using System.Drawing;
using System.Windows.Forms;
using AridooPOS.DAL;

namespace AridooPOS.UI
{
    public partial class SimpleInvoiceForm : Form
    {
        public SimpleInvoiceForm()
        {
            InitializeComponent();
            InitializeArabicUI();
            LoadTestData();
        }

        private void InitializeComponent()
        {
            this.panel1 = new Panel();
            this.lblTitle = new Label();
            this.panel2 = new Panel();
            this.lblMessage = new Label();
            this.btnTestConnection = new Button();
            this.btnClose = new Button();
            this.txtResults = new TextBox();

            this.panel1.SuspendLayout();
            this.panel2.SuspendLayout();
            this.SuspendLayout();

            // panel1 - Header
            this.panel1.Controls.Add(this.lblTitle);
            this.panel1.Dock = DockStyle.Top;
            this.panel1.Location = new Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new Size(600, 60);
            this.panel1.TabIndex = 0;
            this.panel1.BackColor = Color.FromArgb(52, 152, 219);

            // lblTitle
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Tahoma", 16F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(200, 20);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(200, 27);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "اختبار نظام أريدوو";

            // panel2 - Controls
            this.panel2.Controls.Add(this.lblMessage);
            this.panel2.Controls.Add(this.btnTestConnection);
            this.panel2.Controls.Add(this.btnClose);
            this.panel2.Dock = DockStyle.Top;
            this.panel2.Location = new Point(0, 60);
            this.panel2.Name = "panel2";
            this.panel2.Size = new Size(600, 80);
            this.panel2.TabIndex = 1;
            this.panel2.BackColor = Color.FromArgb(236, 240, 241);

            // lblMessage
            this.lblMessage.AutoSize = true;
            this.lblMessage.Font = new Font("Tahoma", 12F);
            this.lblMessage.Location = new Point(150, 15);
            this.lblMessage.Name = "lblMessage";
            this.lblMessage.Size = new Size(300, 19);
            this.lblMessage.TabIndex = 0;
            this.lblMessage.Text = "مرحباً بك في نظام أريدوو لنقاط البيع";

            // btnTestConnection
            this.btnTestConnection.BackColor = Color.FromArgb(46, 204, 113);
            this.btnTestConnection.FlatStyle = FlatStyle.Flat;
            this.btnTestConnection.ForeColor = Color.White;
            this.btnTestConnection.Location = new Point(350, 45);
            this.btnTestConnection.Name = "btnTestConnection";
            this.btnTestConnection.Size = new Size(120, 25);
            this.btnTestConnection.TabIndex = 1;
            this.btnTestConnection.Text = "اختبار الاتصال";
            this.btnTestConnection.UseVisualStyleBackColor = false;
            this.btnTestConnection.Click += new EventHandler(this.btnTestConnection_Click);

            // btnClose
            this.btnClose.BackColor = Color.FromArgb(231, 76, 60);
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.ForeColor = Color.White;
            this.btnClose.Location = new Point(480, 45);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new Size(80, 25);
            this.btnClose.TabIndex = 2;
            this.btnClose.Text = "إغلاق";
            this.btnClose.UseVisualStyleBackColor = false;
            this.btnClose.Click += new EventHandler(this.btnClose_Click);

            // txtResults
            this.txtResults.Dock = DockStyle.Fill;
            this.txtResults.Font = new Font("Tahoma", 10F);
            this.txtResults.Location = new Point(0, 140);
            this.txtResults.Multiline = true;
            this.txtResults.Name = "txtResults";
            this.txtResults.ReadOnly = true;
            this.txtResults.ScrollBars = ScrollBars.Vertical;
            this.txtResults.Size = new Size(600, 260);
            this.txtResults.TabIndex = 2;

            // SimpleInvoiceForm
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(600, 400);
            this.Controls.Add(this.txtResults);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel1);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.Name = "SimpleInvoiceForm";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "اختبار نظام أريدوو";

            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void InitializeArabicUI()
        {
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void LoadTestData()
        {
            txtResults.Text = "جاري تحميل النظام...\r\n";
            txtResults.Text += "تم إنشاء قاعدة البيانات بنجاح\r\n";
            txtResults.Text += "تم تحميل البيانات الأولية\r\n";
            txtResults.Text += "النظام جاهز للاستخدام\r\n\r\n";
            txtResults.Text += "الميزات المتاحة:\r\n";
            txtResults.Text += "- إنشاء فواتير جديدة\r\n";
            txtResults.Text += "- البحث عن الفواتير\r\n";
            txtResults.Text += "- إدارة المنتجات\r\n";
            txtResults.Text += "- إدارة العملاء\r\n";
            txtResults.Text += "- إرجاع الفواتير\r\n\r\n";
        }

        private void btnTestConnection_Click(object sender, EventArgs e)
        {
            try
            {
                txtResults.Text += "جاري اختبار الاتصال بقاعدة البيانات...\r\n";
                
                bool connectionTest = DatabaseConnection.TestConnection();
                if (connectionTest)
                {
                    txtResults.Text += "✓ تم الاتصال بقاعدة البيانات بنجاح\r\n";
                    
                    // اختبار قراءة البيانات
                    var categories = DatabaseConnection.ExecuteQuery("SELECT COUNT(*) as Count FROM Categories");
                    if (categories.Rows.Count > 0)
                    {
                        txtResults.Text += $"✓ تم العثور على {categories.Rows[0]["Count"]} فئة منتجات\r\n";
                    }
                    
                    var products = DatabaseConnection.ExecuteQuery("SELECT COUNT(*) as Count FROM Products");
                    if (products.Rows.Count > 0)
                    {
                        txtResults.Text += $"✓ تم العثور على {products.Rows[0]["Count"]} منتج\r\n";
                    }
                    
                    var customers = DatabaseConnection.ExecuteQuery("SELECT COUNT(*) as Count FROM Customers");
                    if (customers.Rows.Count > 0)
                    {
                        txtResults.Text += $"✓ تم العثور على {customers.Rows[0]["Count"]} عميل\r\n";
                    }
                    
                    txtResults.Text += "\r\n✓ النظام جاهز للاستخدام!\r\n";
                    txtResults.Text += "يمكنك الآن إغلاق هذه النافذة والبدء في استخدام النظام\r\n";
                }
                else
                {
                    txtResults.Text += "✗ فشل في الاتصال بقاعدة البيانات\r\n";
                }
            }
            catch (Exception ex)
            {
                txtResults.Text += $"✗ خطأ: {ex.Message}\r\n";
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #region Designer Variables
        private Panel panel1;
        private Label lblTitle;
        private Panel panel2;
        private Label lblMessage;
        private Button btnTestConnection;
        private Button btnClose;
        private TextBox txtResults;
        #endregion
    }
}
