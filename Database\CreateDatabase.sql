-- إنشاء قاعدة بيانات أريدوو
USE master;
GO

-- حذف قاعدة البيانات إذا كانت موجودة
IF EXISTS (SELECT name FROM sys.databases WHERE name = N'AridooPOS')
BEGIN
    ALTER DATABASE AridooPOS SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE AridooPOS;
END
GO

-- إنشاء قاعدة البيانات الجديدة
CREATE DATABASE AridooPOS
ON 
( NAME = 'AridooPOS_Data',
  FILENAME = 'C:\AridooPOS\AridooPOS_Data.mdf',
  SIZE = 100MB,
  MAXSIZE = 1GB,
  FILEGROWTH = 10MB )
LOG ON 
( NAME = 'AridooPOS_Log',
  FILENAME = 'C:\AridooPOS\AridooPOS_Log.ldf',
  SIZE = 10MB,
  MAXSIZE = 100MB,
  FILEGROWTH = 5MB );
GO

-- استخدام قاعدة البيانات الجديدة
USE AridooPOS;
GO

-- تعيين الترتيب العربي
ALTER DATABASE AridooPOS COLLATE Arabic_CI_AS;
GO

-- إنشاء جدول العملاء
CREATE TABLE Customers (
    CustomerID INT IDENTITY(1,1) PRIMARY KEY,
    CustomerCode NVARCHAR(20) UNIQUE NOT NULL,
    CustomerName NVARCHAR(100) NOT NULL,
    Phone NVARCHAR(20),
    Address NVARCHAR(200),
    Email NVARCHAR(100),
    CreditLimit DECIMAL(18,2) DEFAULT 0,
    CurrentBalance DECIMAL(18,2) DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    ModifiedDate DATETIME DEFAULT GETDATE()
);

-- إنشاء جدول المنتجات
CREATE TABLE Products (
    ProductID INT IDENTITY(1,1) PRIMARY KEY,
    ProductCode NVARCHAR(20) UNIQUE NOT NULL,
    ProductName NVARCHAR(100) NOT NULL,
    ProductNameEn NVARCHAR(100),
    CategoryID INT,
    UnitPrice DECIMAL(18,2) NOT NULL,
    CostPrice DECIMAL(18,2) DEFAULT 0,
    StockQuantity INT DEFAULT 0,
    MinStockLevel INT DEFAULT 0,
    Barcode NVARCHAR(50),
    TaxRate DECIMAL(5,2) DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    ModifiedDate DATETIME DEFAULT GETDATE()
);

-- إنشاء جدول الفئات
CREATE TABLE Categories (
    CategoryID INT IDENTITY(1,1) PRIMARY KEY,
    CategoryName NVARCHAR(50) NOT NULL,
    Description NVARCHAR(200),
    IsActive BIT DEFAULT 1
);

-- إنشاء جدول الفواتير الرئيسي
CREATE TABLE Invoices (
    InvoiceID INT IDENTITY(1,1) PRIMARY KEY,
    InvoiceNumber NVARCHAR(20) UNIQUE NOT NULL,
    InvoiceDate DATETIME NOT NULL DEFAULT GETDATE(),
    CustomerID INT,
    CustomerName NVARCHAR(100),
    SubTotal DECIMAL(18,2) NOT NULL DEFAULT 0,
    DiscountAmount DECIMAL(18,2) DEFAULT 0,
    DiscountPercent DECIMAL(5,2) DEFAULT 0,
    TaxAmount DECIMAL(18,2) DEFAULT 0,
    TotalAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
    PaidAmount DECIMAL(18,2) DEFAULT 0,
    RemainingAmount DECIMAL(18,2) DEFAULT 0,
    PaymentType NVARCHAR(20) NOT NULL, -- نقد، بطاقة، قسط، مختلط
    InvoiceStatus NVARCHAR(20) DEFAULT 'مكتملة', -- مكتملة، ملغية، مرتجعة، معلقة
    Notes NVARCHAR(500),
    CashierName NVARCHAR(50),
    IsReturned BIT DEFAULT 0,
    ReturnedDate DATETIME NULL,
    CreatedBy NVARCHAR(50),
    CreatedDate DATETIME DEFAULT GETDATE(),
    ModifiedDate DATETIME DEFAULT GETDATE(),
    
    FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID)
);

-- إنشاء جدول تفاصيل الفواتير
CREATE TABLE InvoiceDetails (
    InvoiceDetailID INT IDENTITY(1,1) PRIMARY KEY,
    InvoiceID INT NOT NULL,
    ProductID INT NOT NULL,
    ProductCode NVARCHAR(20),
    ProductName NVARCHAR(100),
    Quantity DECIMAL(10,3) NOT NULL,
    UnitPrice DECIMAL(18,2) NOT NULL,
    DiscountAmount DECIMAL(18,2) DEFAULT 0,
    DiscountPercent DECIMAL(5,2) DEFAULT 0,
    TaxRate DECIMAL(5,2) DEFAULT 0,
    TaxAmount DECIMAL(18,2) DEFAULT 0,
    LineTotal DECIMAL(18,2) NOT NULL,

    FOREIGN KEY (InvoiceID) REFERENCES Invoices(InvoiceID) ON DELETE CASCADE,
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- إنشاء جدول المدفوعات
CREATE TABLE Payments (
    PaymentID INT IDENTITY(1,1) PRIMARY KEY,
    InvoiceID INT NOT NULL,
    PaymentDate DATETIME DEFAULT GETDATE(),
    PaymentType NVARCHAR(20) NOT NULL, -- نقد، بطاقة، شيك، تحويل
    Amount DECIMAL(18,2) NOT NULL,
    Reference NVARCHAR(50), -- رقم المرجع للبطاقة أو الشيك
    Notes NVARCHAR(200),
    CreatedBy NVARCHAR(50),
    CreatedDate DATETIME DEFAULT GETDATE(),

    FOREIGN KEY (InvoiceID) REFERENCES Invoices(InvoiceID)
);

-- إنشاء جدول الأقساط
CREATE TABLE Installments (
    InstallmentID INT IDENTITY(1,1) PRIMARY KEY,
    InvoiceID INT NOT NULL,
    InstallmentNumber INT NOT NULL,
    DueDate DATETIME NOT NULL,
    Amount DECIMAL(18,2) NOT NULL,
    PaidAmount DECIMAL(18,2) DEFAULT 0,
    RemainingAmount DECIMAL(18,2) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'مستحق', -- مستحق، مدفوع، متأخر
    PaidDate DATETIME NULL,
    Notes NVARCHAR(200),
    CreatedDate DATETIME DEFAULT GETDATE(),

    FOREIGN KEY (InvoiceID) REFERENCES Invoices(InvoiceID)
);

-- إنشاء جدول إرجاع الفواتير
CREATE TABLE InvoiceReturns (
    ReturnID INT IDENTITY(1,1) PRIMARY KEY,
    OriginalInvoiceID INT NOT NULL,
    ReturnInvoiceID INT NOT NULL,
    ReturnDate DATETIME DEFAULT GETDATE(),
    ReturnType NVARCHAR(20) NOT NULL, -- كامل، جزئي
    TotalReturnAmount DECIMAL(18,2) NOT NULL,
    Reason NVARCHAR(200),
    ProcessedBy NVARCHAR(50),
    CreatedDate DATETIME DEFAULT GETDATE(),

    FOREIGN KEY (OriginalInvoiceID) REFERENCES Invoices(InvoiceID),
    FOREIGN KEY (ReturnInvoiceID) REFERENCES Invoices(InvoiceID)
);

-- إنشاء جدول تفاصيل الإرجاع
CREATE TABLE ReturnDetails (
    ReturnDetailID INT IDENTITY(1,1) PRIMARY KEY,
    ReturnID INT NOT NULL,
    ProductID INT NOT NULL,
    OriginalQuantity DECIMAL(10,3) NOT NULL,
    ReturnedQuantity DECIMAL(10,3) NOT NULL,
    UnitPrice DECIMAL(18,2) NOT NULL,
    LineTotal DECIMAL(18,2) NOT NULL,

    FOREIGN KEY (ReturnID) REFERENCES InvoiceReturns(ReturnID) ON DELETE CASCADE,
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- إضافة الفهارس لتحسين الأداء
CREATE INDEX IX_Invoices_Date ON Invoices(InvoiceDate);
CREATE INDEX IX_Invoices_Customer ON Invoices(CustomerID);
CREATE INDEX IX_Invoices_Number ON Invoices(InvoiceNumber);
CREATE INDEX IX_InvoiceDetails_Invoice ON InvoiceDetails(InvoiceID);
CREATE INDEX IX_InvoiceDetails_Product ON InvoiceDetails(ProductID);
CREATE INDEX IX_Payments_Invoice ON Payments(InvoiceID);
CREATE INDEX IX_Installments_Invoice ON Installments(InvoiceID);
CREATE INDEX IX_Installments_DueDate ON Installments(DueDate);
CREATE INDEX IX_Products_Code ON Products(ProductCode);
CREATE INDEX IX_Products_Barcode ON Products(Barcode);
CREATE INDEX IX_Customers_Code ON Customers(CustomerCode);

-- إضافة قيود التحقق
ALTER TABLE Products ADD CONSTRAINT CK_Products_UnitPrice CHECK (UnitPrice >= 0);
ALTER TABLE Products ADD CONSTRAINT CK_Products_StockQuantity CHECK (StockQuantity >= 0);
ALTER TABLE Invoices ADD CONSTRAINT CK_Invoices_TotalAmount CHECK (TotalAmount >= 0);
ALTER TABLE InvoiceDetails ADD CONSTRAINT CK_InvoiceDetails_Quantity CHECK (Quantity > 0);
ALTER TABLE InvoiceDetails ADD CONSTRAINT CK_InvoiceDetails_UnitPrice CHECK (UnitPrice >= 0);
ALTER TABLE Payments ADD CONSTRAINT CK_Payments_Amount CHECK (Amount > 0);
ALTER TABLE Installments ADD CONSTRAINT CK_Installments_Amount CHECK (Amount > 0);

-- إدراج البيانات الأولية
-- إدراج فئات المنتجات الأساسية
INSERT INTO Categories (CategoryName, Description) VALUES
(N'مواد غذائية', N'المواد الغذائية والمشروبات'),
(N'مستلزمات منزلية', N'الأدوات والمستلزمات المنزلية'),
(N'ملابس', N'الملابس والأزياء'),
(N'إلكترونيات', N'الأجهزة الإلكترونية'),
(N'أدوات مكتبية', N'القرطاسية والأدوات المكتبية'),
(N'متنوعة', N'منتجات متنوعة أخرى');

-- إدراج عميل افتراضي للمبيعات النقدية
INSERT INTO Customers (CustomerCode, CustomerName, Phone, Address) VALUES
(N'CASH001', N'عميل نقدي', N'', N'مبيعات نقدية');

-- إدراج منتجات تجريبية
INSERT INTO Products (ProductCode, ProductName, CategoryID, UnitPrice, CostPrice, StockQuantity, Barcode) VALUES
(N'PRD001', N'منتج تجريبي 1', 1, 10.00, 7.50, 100, N'1234567890123'),
(N'PRD002', N'منتج تجريبي 2', 2, 25.50, 18.00, 50, N'1234567890124'),
(N'PRD003', N'منتج تجريبي 3', 3, 75.00, 55.00, 25, N'1234567890125');

-- إضافة العلاقة الخارجية للمنتجات والفئات
ALTER TABLE Products ADD CONSTRAINT FK_Products_Categories
FOREIGN KEY (CategoryID) REFERENCES Categories(CategoryID);

GO

-- إنشاء إجراءات مخزنة أساسية
-- إجراء لإنشاء رقم فاتورة جديد
CREATE PROCEDURE sp_GetNextInvoiceNumber
AS
BEGIN
    DECLARE @NextNumber INT;
    DECLARE @InvoiceNumber NVARCHAR(20);

    SELECT @NextNumber = ISNULL(MAX(CAST(SUBSTRING(InvoiceNumber, 4, LEN(InvoiceNumber)-3) AS INT)), 0) + 1
    FROM Invoices
    WHERE InvoiceNumber LIKE 'INV%';

    SET @InvoiceNumber = 'INV' + RIGHT('000000' + CAST(@NextNumber AS NVARCHAR), 6);

    SELECT @InvoiceNumber AS NextInvoiceNumber;
END
GO
