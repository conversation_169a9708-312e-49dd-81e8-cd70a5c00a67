using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;

namespace AredooPOS.UI
{
    /// <summary>
    /// مدير التخطيط العربي
    /// يدير تخطيط الواجهات لدعم اللغة العربية وRTL
    /// </summary>
    public static class ArabicLayoutManager
    {
        #region الثوابت والإعدادات

        /// <summary>
        /// الخطوط العربية المدعومة
        /// </summary>
        public static readonly Dictionary<string, string> ArabicFonts = new Dictionary<string, string>
        {
            { "Default", "Tahoma" },
            { "Modern", "Segoe UI" },
            { "Traditional", "Arabic Typesetting" },
            { "Simplified", "Simplified Arabic" },
            { "Naskh", "Traditional Arabic" },
            { "Kufi", "Andalus" }
        };

        /// <summary>
        /// أحجام الخطوط الافتراضية
        /// </summary>
        public static readonly Dictionary<string, float> DefaultFontSizes = new Dictionary<string, float>
        {
            { "Small", 8.25f },
            { "Normal", 9.75f },
            { "Medium", 11.25f },
            { "Large", 12.75f },
            { "ExtraLarge", 14.25f },
            { "Title", 16.0f },
            { "Header", 18.0f }
        };

        /// <summary>
        /// الألوان المناسبة للثقافة العربية
        /// </summary>
        public static readonly Dictionary<string, Color> ArabicColors = new Dictionary<string, Color>
        {
            { "Primary", Color.FromArgb(41, 128, 185) },
            { "Secondary", Color.FromArgb(52, 152, 219) },
            { "Success", Color.FromArgb(39, 174, 96) },
            { "Warning", Color.FromArgb(241, 196, 15) },
            { "Danger", Color.FromArgb(231, 76, 60) },
            { "Info", Color.FromArgb(142, 68, 173) },
            { "Light", Color.FromArgb(236, 240, 241) },
            { "Dark", Color.FromArgb(44, 62, 80) },
            { "Background", Color.FromArgb(250, 250, 250) },
            { "Surface", Color.White },
            { "Border", Color.FromArgb(189, 195, 199) },
            { "Text", Color.FromArgb(44, 62, 80) },
            { "TextSecondary", Color.FromArgb(127, 140, 141) }
        };

        #endregion

        #region تطبيق التخطيط العربي

        /// <summary>
        /// تطبيق التخطيط العربي على النموذج
        /// </summary>
        /// <param name="form">النموذج</param>
        /// <param name="fontName">اسم الخط</param>
        /// <param name="fontSize">حجم الخط</param>
        public static void ApplyArabicLayout(Form form, string fontName = "Default", string fontSize = "Normal")
        {
            if (form == null) return;

            try
            {
                // تعيين الاتجاه من اليمين لليسار
                form.RightToLeft = RightToLeft.Yes;
                form.RightToLeftLayout = true;

                // تعيين الخط العربي
                var font = GetArabicFont(fontName, fontSize);
                ApplyFontToControl(form, font);

                // تطبيق الألوان العربية
                ApplyArabicColors(form);

                // تعديل تخطيط العناصر
                AdjustControlsLayout(form);

                // تعيين الثقافة العربية
                SetArabicCulture(form);

                // تطبيق التخطيط على العناصر الفرعية
                ApplyLayoutToChildren(form);
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ دون إيقاف التطبيق
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق التخطيط العربي: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق التخطيط العربي على عنصر تحكم
        /// </summary>
        /// <param name="control">عنصر التحكم</param>
        /// <param name="fontName">اسم الخط</param>
        /// <param name="fontSize">حجم الخط</param>
        public static void ApplyArabicLayout(Control control, string fontName = "Default", string fontSize = "Normal")
        {
            if (control == null) return;

            try
            {
                // تعيين الاتجاه من اليمين لليسار
                control.RightToLeft = RightToLeft.Yes;

                // تعيين الخط العربي
                var font = GetArabicFont(fontName, fontSize);
                control.Font = font;

                // تطبيق الألوان
                ApplyArabicColors(control);

                // تعديل تخطيط العنصر
                AdjustControlLayout(control);

                // تطبيق التخطيط على العناصر الفرعية
                ApplyLayoutToChildren(control);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق التخطيط العربي على العنصر: {ex.Message}");
            }
        }

        #endregion

        #region إدارة الخطوط

        /// <summary>
        /// الحصول على خط عربي
        /// </summary>
        /// <param name="fontName">اسم الخط</param>
        /// <param name="fontSize">حجم الخط</param>
        /// <returns>الخط</returns>
        public static Font GetArabicFont(string fontName = "Default", string fontSize = "Normal")
        {
            try
            {
                var actualFontName = ArabicFonts.ContainsKey(fontName) ? ArabicFonts[fontName] : ArabicFonts["Default"];
                var actualFontSize = DefaultFontSizes.ContainsKey(fontSize) ? DefaultFontSizes[fontSize] : DefaultFontSizes["Normal"];

                // التحقق من توفر الخط في النظام
                if (!IsFontInstalled(actualFontName))
                {
                    actualFontName = ArabicFonts["Default"];
                }

                return new Font(actualFontName, actualFontSize, FontStyle.Regular, GraphicsUnit.Point, 178);
            }
            catch
            {
                // إرجاع خط افتراضي في حالة الخطأ
                return new Font("Tahoma", 9.75f, FontStyle.Regular, GraphicsUnit.Point, 178);
            }
        }

        /// <summary>
        /// التحقق من تثبيت الخط في النظام
        /// </summary>
        /// <param name="fontName">اسم الخط</param>
        /// <returns>true إذا كان الخط مثبت</returns>
        public static bool IsFontInstalled(string fontName)
        {
            try
            {
                using var testFont = new Font(fontName, 12);
                return testFont.Name.Equals(fontName, StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// تطبيق الخط على عنصر التحكم وجميع العناصر الفرعية
        /// </summary>
        /// <param name="control">عنصر التحكم</param>
        /// <param name="font">الخط</param>
        private static void ApplyFontToControl(Control control, Font font)
        {
            if (control == null || font == null) return;

            try
            {
                control.Font = font;

                foreach (Control child in control.Controls)
                {
                    ApplyFontToControl(child, font);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الخط: {ex.Message}");
            }
        }

        #endregion

        #region إدارة الألوان

        /// <summary>
        /// تطبيق الألوان العربية على النموذج
        /// </summary>
        /// <param name="form">النموذج</param>
        private static void ApplyArabicColors(Form form)
        {
            if (form == null) return;

            try
            {
                form.BackColor = ArabicColors["Background"];
                form.ForeColor = ArabicColors["Text"];

                // تطبيق الألوان على العناصر الخاصة
                ApplyColorsToSpecialControls(form);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الألوان: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق الألوان العربية على عنصر التحكم
        /// </summary>
        /// <param name="control">عنصر التحكم</param>
        private static void ApplyArabicColors(Control control)
        {
            if (control == null) return;

            try
            {
                // تطبيق الألوان حسب نوع العنصر
                switch (control)
                {
                    case Button button:
                        ApplyButtonColors(button);
                        break;

                    case TextBox textBox:
                        ApplyTextBoxColors(textBox);
                        break;

                    case ComboBox comboBox:
                        ApplyComboBoxColors(comboBox);
                        break;

                    case DataGridView dataGridView:
                        ApplyDataGridViewColors(dataGridView);
                        break;

                    case Panel panel:
                        ApplyPanelColors(panel);
                        break;

                    case GroupBox groupBox:
                        ApplyGroupBoxColors(groupBox);
                        break;

                    case Label label:
                        ApplyLabelColors(label);
                        break;

                    default:
                        control.BackColor = ArabicColors["Surface"];
                        control.ForeColor = ArabicColors["Text"];
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق ألوان العنصر: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق ألوان الأزرار
        /// </summary>
        private static void ApplyButtonColors(Button button)
        {
            button.BackColor = ArabicColors["Primary"];
            button.ForeColor = Color.White;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ArabicColors["Secondary"];
        }

        /// <summary>
        /// تطبيق ألوان صناديق النص
        /// </summary>
        private static void ApplyTextBoxColors(TextBox textBox)
        {
            textBox.BackColor = ArabicColors["Surface"];
            textBox.ForeColor = ArabicColors["Text"];
            textBox.BorderStyle = BorderStyle.FixedSingle;
        }

        /// <summary>
        /// تطبيق ألوان القوائم المنسدلة
        /// </summary>
        private static void ApplyComboBoxColors(ComboBox comboBox)
        {
            comboBox.BackColor = ArabicColors["Surface"];
            comboBox.ForeColor = ArabicColors["Text"];
            comboBox.FlatStyle = FlatStyle.Flat;
        }

        /// <summary>
        /// تطبيق ألوان جداول البيانات
        /// </summary>
        private static void ApplyDataGridViewColors(DataGridView dataGridView)
        {
            dataGridView.BackgroundColor = ArabicColors["Background"];
            dataGridView.DefaultCellStyle.BackColor = ArabicColors["Surface"];
            dataGridView.DefaultCellStyle.ForeColor = ArabicColors["Text"];
            dataGridView.DefaultCellStyle.SelectionBackColor = ArabicColors["Primary"];
            dataGridView.DefaultCellStyle.SelectionForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = ArabicColors["Dark"];
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dataGridView.EnableHeadersVisualStyles = false;
            dataGridView.BorderStyle = BorderStyle.None;
            dataGridView.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dataGridView.GridColor = ArabicColors["Border"];
        }

        /// <summary>
        /// تطبيق ألوان اللوحات
        /// </summary>
        private static void ApplyPanelColors(Panel panel)
        {
            panel.BackColor = ArabicColors["Surface"];
        }

        /// <summary>
        /// تطبيق ألوان مجموعات العناصر
        /// </summary>
        private static void ApplyGroupBoxColors(GroupBox groupBox)
        {
            groupBox.BackColor = ArabicColors["Background"];
            groupBox.ForeColor = ArabicColors["Text"];
        }

        /// <summary>
        /// تطبيق ألوان التسميات
        /// </summary>
        private static void ApplyLabelColors(Label label)
        {
            label.BackColor = Color.Transparent;
            label.ForeColor = ArabicColors["Text"];
        }

        /// <summary>
        /// تطبيق الألوان على العناصر الخاصة
        /// </summary>
        private static void ApplyColorsToSpecialControls(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                ApplyArabicColors(control);
                ApplyColorsToSpecialControls(control);
            }
        }

        #endregion

        #region تعديل التخطيط

        /// <summary>
        /// تعديل تخطيط العناصر للاتجاه العربي
        /// </summary>
        /// <param name="form">النموذج</param>
        private static void AdjustControlsLayout(Form form)
        {
            try
            {
                // تعديل تخطيط شريط القوائم
                AdjustMenuStripLayout(form);

                // تعديل تخطيط شريط الأدوات
                AdjustToolStripLayout(form);

                // تعديل تخطيط شريط الحالة
                AdjustStatusStripLayout(form);

                // تعديل تخطيط العناصر الأخرى
                foreach (Control control in form.Controls)
                {
                    AdjustControlLayout(control);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تعديل التخطيط: {ex.Message}");
            }
        }

        /// <summary>
        /// تعديل تخطيط عنصر تحكم واحد
        /// </summary>
        /// <param name="control">عنصر التحكم</param>
        private static void AdjustControlLayout(Control control)
        {
            if (control == null) return;

            try
            {
                switch (control)
                {
                    case TableLayoutPanel tableLayout:
                        AdjustTableLayoutPanel(tableLayout);
                        break;

                    case FlowLayoutPanel flowLayout:
                        AdjustFlowLayoutPanel(flowLayout);
                        break;

                    case SplitContainer splitContainer:
                        AdjustSplitContainer(splitContainer);
                        break;

                    case TabControl tabControl:
                        AdjustTabControl(tabControl);
                        break;

                    case DataGridView dataGridView:
                        AdjustDataGridView(dataGridView);
                        break;
                }

                // تطبيق التعديلات على العناصر الفرعية
                foreach (Control child in control.Controls)
                {
                    AdjustControlLayout(child);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تعديل تخطيط العنصر: {ex.Message}");
            }
        }

        /// <summary>
        /// تعديل تخطيط شريط القوائم
        /// </summary>
        private static void AdjustMenuStripLayout(Form form)
        {
            var menuStrip = form.Controls.OfType<MenuStrip>().FirstOrDefault();
            if (menuStrip != null)
            {
                menuStrip.RightToLeft = RightToLeft.Yes;
                menuStrip.LayoutDirection = ToolStripLayoutDirection.Horizontal;
            }
        }

        /// <summary>
        /// تعديل تخطيط شريط الأدوات
        /// </summary>
        private static void AdjustToolStripLayout(Form form)
        {
            var toolStrips = form.Controls.OfType<ToolStrip>();
            foreach (var toolStrip in toolStrips)
            {
                toolStrip.RightToLeft = RightToLeft.Yes;
                toolStrip.LayoutDirection = ToolStripLayoutDirection.Horizontal;
            }
        }

        /// <summary>
        /// تعديل تخطيط شريط الحالة
        /// </summary>
        private static void AdjustStatusStripLayout(Form form)
        {
            var statusStrip = form.Controls.OfType<StatusStrip>().FirstOrDefault();
            if (statusStrip != null)
            {
                statusStrip.RightToLeft = RightToLeft.Yes;
                statusStrip.LayoutDirection = ToolStripLayoutDirection.Horizontal;
            }
        }

        /// <summary>
        /// تعديل تخطيط جدول التخطيط
        /// </summary>
        private static void AdjustTableLayoutPanel(TableLayoutPanel tableLayout)
        {
            tableLayout.RightToLeft = RightToLeft.Yes;
        }

        /// <summary>
        /// تعديل تخطيط لوحة التدفق
        /// </summary>
        private static void AdjustFlowLayoutPanel(FlowLayoutPanel flowLayout)
        {
            flowLayout.RightToLeft = RightToLeft.Yes;
            flowLayout.FlowDirection = FlowDirection.RightToLeft;
        }

        /// <summary>
        /// تعديل تخطيط الحاوي المقسم
        /// </summary>
        private static void AdjustSplitContainer(SplitContainer splitContainer)
        {
            splitContainer.RightToLeft = RightToLeft.Yes;
        }

        /// <summary>
        /// تعديل تخطيط عناصر التبويب
        /// </summary>
        private static void AdjustTabControl(TabControl tabControl)
        {
            tabControl.RightToLeft = RightToLeft.Yes;
            tabControl.RightToLeftLayout = true;
        }

        /// <summary>
        /// تعديل تخطيط جدول البيانات
        /// </summary>
        private static void AdjustDataGridView(DataGridView dataGridView)
        {
            dataGridView.RightToLeft = RightToLeft.Yes;
            
            // تعديل محاذاة الأعمدة
            foreach (DataGridViewColumn column in dataGridView.Columns)
            {
                if (column.ValueType == typeof(string))
                {
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                }
                else if (column.ValueType == typeof(decimal) || column.ValueType == typeof(double) || column.ValueType == typeof(int))
                {
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
                }
            }
        }

        #endregion

        #region إعدادات الثقافة

        /// <summary>
        /// تعيين الثقافة العربية
        /// </summary>
        /// <param name="form">النموذج</param>
        private static void SetArabicCulture(Form form)
        {
            try
            {
                var arabicCulture = new CultureInfo("ar-SA");
                System.Threading.Thread.CurrentThread.CurrentCulture = arabicCulture;
                System.Threading.Thread.CurrentThread.CurrentUICulture = arabicCulture;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تعيين الثقافة العربية: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق التخطيط على العناصر الفرعية
        /// </summary>
        /// <param name="parent">العنصر الأب</param>
        private static void ApplyLayoutToChildren(Control parent)
        {
            foreach (Control child in parent.Controls)
            {
                child.RightToLeft = RightToLeft.Yes;
                ApplyArabicColors(child);
                AdjustControlLayout(child);
                ApplyLayoutToChildren(child);
            }
        }

        #endregion

        #region دوال مساعدة

        /// <summary>
        /// الحصول على لون بدرجة شفافية
        /// </summary>
        /// <param name="colorName">اسم اللون</param>
        /// <param name="alpha">درجة الشفافية</param>
        /// <returns>اللون مع الشفافية</returns>
        public static Color GetColorWithAlpha(string colorName, int alpha)
        {
            if (ArabicColors.ContainsKey(colorName))
            {
                var color = ArabicColors[colorName];
                return Color.FromArgb(alpha, color.R, color.G, color.B);
            }
            return Color.Transparent;
        }

        /// <summary>
        /// الحصول على لون أفتح
        /// </summary>
        /// <param name="colorName">اسم اللون</param>
        /// <param name="factor">معامل التفتيح</param>
        /// <returns>اللون الأفتح</returns>
        public static Color GetLighterColor(string colorName, float factor = 0.2f)
        {
            if (ArabicColors.ContainsKey(colorName))
            {
                var color = ArabicColors[colorName];
                return ControlPaint.Light(color, factor);
            }
            return Color.White;
        }

        /// <summary>
        /// الحصول على لون أغمق
        /// </summary>
        /// <param name="colorName">اسم اللون</param>
        /// <param name="factor">معامل التغميق</param>
        /// <returns>اللون الأغمق</returns>
        public static Color GetDarkerColor(string colorName, float factor = 0.2f)
        {
            if (ArabicColors.ContainsKey(colorName))
            {
                var color = ArabicColors[colorName];
                return ControlPaint.Dark(color, factor);
            }
            return Color.Black;
        }

        #endregion
    }
}
