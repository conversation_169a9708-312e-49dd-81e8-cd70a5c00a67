<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- معلومات المشروع الأساسية -->
    <OutputType>WinExe</OutputType>
    <TargetFramework>net472</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <Nullable>disable</Nullable>
    <ImplicitUsings>disable</ImplicitUsings>
    <LangVersion>latest</LangVersion>

    <!-- معلومات التطبيق -->
    <AssemblyTitle>أريدو POS - نظام نقاط البيع</AssemblyTitle>
    <AssemblyDescription>نظام شامل لإدارة نقاط البيع يدعم اللغة العربية والطابعات الحرارية</AssemblyDescription>
    <AssemblyCompany>أريدو</AssemblyCompany>
    <AssemblyProduct>أريدو POS</AssemblyProduct>
    <AssemblyCopyright>© 2024 أريدو. جميع الحقوق محفوظة.</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>

    <!-- إعدادات التطبيق -->
    <ApplicationIcon>Assets\icon.ico</ApplicationIcon>
    <StartupObject>AredooPOS.Program</StartupObject>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\AredooPOS.xml</DocumentationFile>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <Deterministic>true</Deterministic>
  </PropertyGroup>

  <!-- المكتبات المطلوبة -->
  <ItemGroup>
    <!-- إدارة التكوين والحقن -->
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />

    <!-- نظام التسجيل -->
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="7.0.0" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="7.0.1" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />

    <!-- قاعدة البيانات -->
    <PackageReference Include="System.Data.SqlClient" Version="4.8.5" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.1" />

    <!-- الرسوميات والواجهات -->
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />

    <!-- الاتصالات والأجهزة -->
    <PackageReference Include="System.IO.Ports" Version="7.0.0" />
    <PackageReference Include="System.Management" Version="7.0.2" />

    <!-- معالجة البيانات -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <!-- مراجع النظام -->
  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Security" />
    <Reference Include="System.Configuration" />
  </ItemGroup>

  <!-- ملفات التكوين -->
  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.*.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <DependentUpon>appsettings.json</DependentUpon>
    </None>
  </ItemGroup>

  <!-- الموارد والأصول -->
  <ItemGroup>
    <Content Include="Assets\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <!-- المجلدات -->
  <ItemGroup>
    <Folder Include="Assets\" />
    <Folder Include="Resources\" />
    <Folder Include="Logs\" />
    <Folder Include="Backups\" />
    <Folder Include="Reports\" />
    <Folder Include="Barcodes\" />
    <Folder Include="Database\" />
    <Folder Include="Temp\" />
  </ItemGroup>

  <!-- تعطيل الموارد الافتراضية لتجنب التكرار -->
  <PropertyGroup>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
  </PropertyGroup>

  <!-- الموارد المدمجة -->
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

  <!-- إنشاء المجلدات المطلوبة -->
  <Target Name="CreateDirectories" BeforeTargets="Build">
    <MakeDir Directories="$(OutputPath)Logs" Condition="!Exists('$(OutputPath)Logs')" />
    <MakeDir Directories="$(OutputPath)Backups" Condition="!Exists('$(OutputPath)Backups')" />
    <MakeDir Directories="$(OutputPath)Reports" Condition="!Exists('$(OutputPath)Reports')" />
    <MakeDir Directories="$(OutputPath)Barcodes" Condition="!Exists('$(OutputPath)Barcodes')" />
    <MakeDir Directories="$(OutputPath)Database" Condition="!Exists('$(OutputPath)Database')" />
    <MakeDir Directories="$(OutputPath)Temp" Condition="!Exists('$(OutputPath)Temp')" />
  </Target>

  <!-- إجراءات ما بعد البناء -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="echo تم بناء مشروع أريدو POS بنجاح" />
  </Target>

</Project>
