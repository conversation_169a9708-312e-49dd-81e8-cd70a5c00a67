<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- معلومات المشروع الأساسية -->
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <Nullable>disable</Nullable>
    <ImplicitUsings>disable</ImplicitUsings>
    
    <!-- معلومات التطبيق -->
    <AssemblyTitle>أريدوو - نظام نقاط البيع</AssemblyTitle>
    <AssemblyDescription>نظام نقاط البيع المتكامل باللغة العربية مع دعم الفواتير والمخزون والعملاء</AssemblyDescription>
    <AssemblyCompany>أريدوو</AssemblyCompany>
    <AssemblyProduct>Aredoo POS System</AssemblyProduct>
    <AssemblyCopyright>© 2024 أريدوو. جميع الحقوق محفوظة.</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    
    <!-- إعدادات التطبيق -->
    <ApplicationIcon>Resources\aredoo-icon.ico</ApplicationIcon>
    <StartupObject>AredooPOS.Program</StartupObject>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\AredooPOS.xml</DocumentationFile>
  </PropertyGroup>

  <!-- المكتبات المطلوبة -->
  <ItemGroup>
    <PackageReference Include="System.Data.SQLite" Version="1.0.118" />
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Serilog" Version="3.0.1" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
  </ItemGroup>

  <!-- الموارد -->
  <ItemGroup>
    <EmbeddedResource Include="Resources\**" />
  </ItemGroup>

  <!-- ملفات التكوين -->
  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Database\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
