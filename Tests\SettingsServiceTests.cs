using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using AredooPOS.Services;
using AredooPOS.Models.Settings;
using Microsoft.Extensions.Logging;
using Moq;

namespace AredooPOS.Tests
{
    /// <summary>
    /// اختبارات وحدة خدمة الإعدادات
    /// </summary>
    [TestClass]
    public class SettingsServiceTests
    {
        #region المتغيرات والإعداد

        private SettingsService _settingsService;
        private Mock<ILogger<SettingsService>> _mockLogger;
        private string _testConnectionString;

        [TestInitialize]
        public void Setup()
        {
            _mockLogger = new Mock<ILogger<SettingsService>>();
            _testConnectionString = "Server=(localdb)\\MSSQLLocalDB;Database=AredooPOS_Test;Integrated Security=true;";
            _settingsService = new SettingsService(_testConnectionString, _mockLogger.Object);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _settingsService?.ClearSettingsCache();
        }

        #endregion

        #region اختبارات الإعدادات العامة غير المتزامنة

        [TestMethod]
        public async Task GetSettingValueAsync_ExistingSetting_ReturnsCorrectValue()
        {
            // Arrange
            var settingName = "System.ApplicationName";
            var expectedValue = "أريدو POS";

            // First save a test setting
            await _settingsService.SaveSettingValueAsync(settingName, expectedValue, "TestUser");

            // Act
            var result = await _settingsService.GetSettingValueAsync(settingName);

            // Assert
            Assert.AreEqual(expectedValue, result);
        }

        [TestMethod]
        public async Task GetSettingValueAsync_NonExistentSetting_ReturnsDefaultValue()
        {
            // Arrange
            var settingName = "NonExistent.Setting";
            var defaultValue = "Default Value";

            // Act
            var result = await _settingsService.GetSettingValueAsync(settingName, defaultValue);

            // Assert
            Assert.AreEqual(defaultValue, result);
        }

        [TestMethod]
        public async Task GetSettingValueAsIntAsync_ValidIntegerSetting_ReturnsCorrectValue()
        {
            // Arrange
            var settingName = "Test.Integer.Setting";
            var expectedValue = 42;

            // First save a test setting
            await _settingsService.SaveSettingValueAsync(settingName, expectedValue.ToString(), "TestUser");

            // Act
            var result = await _settingsService.GetSettingValueAsIntAsync(settingName);

            // Assert
            Assert.AreEqual(expectedValue, result);
        }

        [TestMethod]
        public async Task GetSettingValueAsDecimalAsync_ValidDecimalSetting_ReturnsCorrectValue()
        {
            // Arrange
            var settingName = "Test.Decimal.Setting";
            var expectedValue = 15.75m;

            // First save a test setting
            await _settingsService.SaveSettingValueAsync(settingName, expectedValue.ToString(), "TestUser");

            // Act
            var result = await _settingsService.GetSettingValueAsDecimalAsync(settingName);

            // Assert
            Assert.AreEqual(expectedValue, result);
        }

        [TestMethod]
        public async Task GetSettingValueAsBoolAsync_ValidBooleanSetting_ReturnsCorrectValue()
        {
            // Arrange
            var settingName = "Test.Boolean.Setting";
            var expectedValue = true;

            // First save a test setting
            await _settingsService.SaveSettingValueAsync(settingName, expectedValue.ToString(), "TestUser");

            // Act
            var result = await _settingsService.GetSettingValueAsBoolAsync(settingName);

            // Assert
            Assert.AreEqual(expectedValue, result);
        }

        [TestMethod]
        public async Task SaveSettingValueAsync_ValidData_ReturnsTrue()
        {
            // Arrange
            var settingName = "Test.Save.Setting";
            var settingValue = "Test Value";
            var updatedBy = "TestUser";

            // Act
            var result = await _settingsService.SaveSettingValueAsync(settingName, settingValue, updatedBy);

            // Assert
            Assert.IsTrue(result);

            // Verify the value was saved
            var savedValue = await _settingsService.GetSettingValueAsync(settingName);
            Assert.AreEqual(settingValue, savedValue);
        }

        [TestMethod]
        public async Task SaveMultipleSettingsAsync_ValidData_ReturnsCorrectCount()
        {
            // Arrange
            var settings = new Dictionary<string, string>
            {
                { "Test.Multiple.Setting1", "Value1" },
                { "Test.Multiple.Setting2", "Value2" },
                { "Test.Multiple.Setting3", "Value3" }
            };
            var updatedBy = "TestUser";

            // Act
            var result = await _settingsService.SaveMultipleSettingsAsync(settings, updatedBy);

            // Assert
            Assert.AreEqual(settings.Count, result);

            // Verify all values were saved
            foreach (var setting in settings)
            {
                var savedValue = await _settingsService.GetSettingValueAsync(setting.Key);
                Assert.AreEqual(setting.Value, savedValue);
            }
        }

        #endregion

        #region اختبارات الإعدادات المتخصصة

        [TestMethod]
        public async Task GetTaxSettingsAsync_ReturnsValidTaxSettings()
        {
            // Act
            var result = await _settingsService.GetTaxSettingsAsync();

            // Assert
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public async Task SaveTaxSettingsAsync_ValidSettings_ReturnsTrue()
        {
            // Arrange
            var taxSettings = new TaxSettings
            {
                IsTaxEnabled = true,
                DefaultTaxRate = 15.0m,
                TaxName = "ضريبة القيمة المضافة",
                TaxCalculationMethod = "Inclusive",
                ApplyToAllProducts = true,
                MinimumTaxableAmount = 0,
                TaxDecimalPlaces = 2,
                RoundingMethod = "Round"
            };

            // Act
            var result = await _settingsService.SaveTaxSettingsAsync(taxSettings);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public async Task GetCurrencySettingsAsync_ReturnsValidCurrencySettings()
        {
            // Act
            var result = await _settingsService.GetCurrencySettingsAsync();

            // Assert
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public async Task SaveCurrencySettingsAsync_ValidSettings_ReturnsTrue()
        {
            // Arrange
            var currencySettings = new CurrencySettings
            {
                DefaultCurrencyCode = "SAR",
                DecimalPlaces = 2,
                CurrencySymbol = "ر.س",
                SymbolPosition = "After",
                ThousandsSeparator = ",",
                DecimalSeparator = ".",
                SupportMultipleCurrencies = false
            };

            // Act
            var result = await _settingsService.SaveCurrencySettingsAsync(currencySettings);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public async Task GetStoreSettingsAsync_ReturnsValidStoreSettings()
        {
            // Act
            var result = await _settingsService.GetStoreSettingsAsync();

            // Assert
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public async Task SaveStoreSettingsAsync_ValidSettings_ReturnsTrue()
        {
            // Arrange
            var storeSettings = new StoreSettings
            {
                StoreName = "متجر الاختبار",
                StoreNameEnglish = "Test Store",
                PrimaryPhone = "0123456789",
                Email = "<EMAIL>",
                Address = "عنوان الاختبار",
                City = "الرياض"
            };

            // Act
            var result = await _settingsService.SaveStoreSettingsAsync(storeSettings);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public async Task GetPrintSettingsAsync_ReturnsValidPrintSettings()
        {
            // Act
            var result = await _settingsService.GetPrintSettingsAsync();

            // Assert
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public async Task SavePrintSettingsAsync_ValidSettings_ReturnsTrue()
        {
            // Arrange
            var printSettings = new PrintSettings
            {
                DefaultPrintSize = "A4",
                AutoPrint = false,
                DefaultCopies = 1,
                FontName = "Arial",
                DefaultFontSize = 12,
                PrintStoreLogo = true,
                PrintStoreInfo = true
            };

            // Act
            var result = await _settingsService.SavePrintSettingsAsync(printSettings);

            // Assert
            Assert.IsTrue(result);
        }

        #endregion

        #region اختبارات العمليات المتقدمة

        [TestMethod]
        public async Task InitializeDefaultSettingsAsync_ReturnsTrue()
        {
            // Arrange
            var updatedBy = "TestUser";

            // Act
            var result = await _settingsService.InitializeDefaultSettingsAsync(updatedBy);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public async Task ValidateEssentialSettingsAsync_WithValidSettings_ReturnsTrue()
        {
            // Arrange
            // Set up essential settings
            var storeSettings = new StoreSettings { StoreName = "Test Store" };
            var currencySettings = new CurrencySettings { DefaultCurrencyCode = "SAR" };
            var printSettings = new PrintSettings { DefaultPrintSize = "A4" };

            await _settingsService.SaveStoreSettingsAsync(storeSettings);
            await _settingsService.SaveCurrencySettingsAsync(currencySettings);
            await _settingsService.SavePrintSettingsAsync(printSettings);

            // Act
            var result = await _settingsService.ValidateEssentialSettingsAsync();

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public async Task GetSettingsSummaryAsync_ReturnsValidSummary()
        {
            // Act
            var result = await _settingsService.GetSettingsSummaryAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.StoreName);
            Assert.IsNotNull(result.DefaultCurrency);
        }

        [TestMethod]
        public async Task ExportAllSettingsAsync_ReturnsValidJsonString()
        {
            // Act
            var result = await _settingsService.ExportAllSettingsAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Length > 0);
        }

        [TestMethod]
        public async Task ImportSettingsAsync_ValidJsonData_ReturnsTrue()
        {
            // Arrange
            var exportedData = await _settingsService.ExportAllSettingsAsync();
            var updatedBy = "TestUser";

            // Act
            var result = await _settingsService.ImportSettingsAsync(exportedData, updatedBy);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public async Task ResetSettingsToDefaultAsync_ValidCategory_ReturnsTrue()
        {
            // Arrange
            var category = "General";
            var updatedBy = "TestUser";

            // Act
            var result = await _settingsService.ResetSettingsToDefaultAsync(category, updatedBy);

            // Assert
            Assert.IsTrue(result);
        }

        #endregion

        #region اختبارات الإعدادات المؤقتة

        [TestMethod]
        public void SetSessionSetting_ValidData_StoresCorrectly()
        {
            // Arrange
            var key = "TestSessionKey";
            var value = "TestSessionValue";

            // Act
            _settingsService.SetSessionSetting(key, value);
            var result = _settingsService.GetSessionSetting<string>(key);

            // Assert
            Assert.AreEqual(value, result);
        }

        [TestMethod]
        public void GetSessionSetting_NonExistentKey_ReturnsDefaultValue()
        {
            // Arrange
            var key = "NonExistentKey";
            var defaultValue = "DefaultValue";

            // Act
            var result = _settingsService.GetSessionSetting(key, defaultValue);

            // Assert
            Assert.AreEqual(defaultValue, result);
        }

        [TestMethod]
        public void RemoveSessionSetting_ExistingKey_ReturnsTrue()
        {
            // Arrange
            var key = "TestRemoveKey";
            var value = "TestValue";

            _settingsService.SetSessionSetting(key, value);

            // Act
            var result = _settingsService.RemoveSessionSetting(key);

            // Assert
            Assert.IsTrue(result);

            // Verify the setting was removed
            var retrievedValue = _settingsService.GetSessionSetting<string>(key);
            Assert.IsNull(retrievedValue);
        }

        [TestMethod]
        public void ClearSessionSettings_ClearsAllSessionSettings()
        {
            // Arrange
            _settingsService.SetSessionSetting("Key1", "Value1");
            _settingsService.SetSessionSetting("Key2", "Value2");

            // Act
            _settingsService.ClearSessionSettings();

            // Assert
            var value1 = _settingsService.GetSessionSetting<string>("Key1");
            var value2 = _settingsService.GetSessionSetting<string>("Key2");

            Assert.IsNull(value1);
            Assert.IsNull(value2);
        }

        #endregion

        #region اختبارات العمليات المساعدة

        [TestMethod]
        public async Task CalculateTaxAsync_ValidAmount_ReturnsCorrectTax()
        {
            // Arrange
            var amount = 100.0m;
            var expectedTaxRate = 15.0m;

            // First set up tax settings
            var taxSettings = new TaxSettings
            {
                IsTaxEnabled = true,
                DefaultTaxRate = expectedTaxRate,
                TaxCalculationMethod = "Exclusive"
            };
            await _settingsService.SaveTaxSettingsAsync(taxSettings);

            // Act
            var result = await _settingsService.CalculateTaxAsync(amount);

            // Assert
            var expectedTax = amount * (expectedTaxRate / 100);
            Assert.AreEqual(expectedTax, result);
        }

        [TestMethod]
        public async Task FormatCurrencyAsync_ValidAmount_ReturnsFormattedString()
        {
            // Arrange
            var amount = 1234.56m;

            // First set up currency settings
            var currencySettings = new CurrencySettings
            {
                DefaultCurrencyCode = "SAR",
                DecimalPlaces = 2,
                CurrencySymbol = "ر.س",
                SymbolPosition = "After",
                ThousandsSeparator = ",",
                DecimalSeparator = "."
            };
            await _settingsService.SaveCurrencySettingsAsync(currencySettings);

            // Act
            var result = await _settingsService.FormatCurrencyAsync(amount);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Contains("1,234.56") || result.Contains("1234.56"));
        }

        [TestMethod]
        public async Task ConvertCurrencyAsync_ValidData_ReturnsConvertedAmount()
        {
            // Arrange
            var amount = 100.0m;
            var fromCurrency = "USD";
            var toCurrency = "SAR";

            // Act
            var result = await _settingsService.ConvertCurrencyAsync(amount, fromCurrency, toCurrency);

            // Assert
            Assert.IsTrue(result >= 0);
        }

        [TestMethod]
        public void ClearSettingsCache_ClearsCache()
        {
            // Arrange
            // This test verifies that the cache clearing method doesn't throw exceptions

            // Act & Assert
            Assert.DoesNotThrow(() => _settingsService.ClearSettingsCache());
        }

        #endregion

        #region اختبارات الأحداث

        [TestMethod]
        public async Task SettingChanged_EventFired_WhenSettingSaved()
        {
            // Arrange
            var eventFired = false;
            var settingName = "Test.Event.Setting";
            var settingValue = "Event Test Value";

            _settingsService.SettingChanged += (sender, args) =>
            {
                eventFired = true;
                Assert.AreEqual(settingName, args.SettingName);
                Assert.AreEqual(settingValue, args.NewValue);
            };

            // Act
            await _settingsService.SaveSettingValueAsync(settingName, settingValue, "TestUser");

            // Assert
            Assert.IsTrue(eventFired);
        }

        [TestMethod]
        public async Task SettingsSaved_EventFired_WhenMultipleSettingsSaved()
        {
            // Arrange
            var eventFired = false;
            var settings = new Dictionary<string, string>
            {
                { "Test.Event.Setting1", "Value1" },
                { "Test.Event.Setting2", "Value2" }
            };

            _settingsService.SettingsSaved += (sender, args) =>
            {
                eventFired = true;
                Assert.AreEqual(settings.Count, args.SavedCount);
            };

            // Act
            await _settingsService.SaveMultipleSettingsAsync(settings, "TestUser");

            // Assert
            Assert.IsTrue(eventFired);
        }

        #endregion

        #region اختبارات معالجة الأخطاء

        [TestMethod]
        public async Task SaveSettingValueAsync_InvalidData_HandlesGracefully()
        {
            // Arrange
            var settingName = "";
            var settingValue = "Test Value";
            var updatedBy = "TestUser";

            // Act
            var result = await _settingsService.SaveSettingValueAsync(settingName, settingValue, updatedBy);

            // Assert
            // Should handle invalid data gracefully and return false
            Assert.IsFalse(result);
        }

        [TestMethod]
        public async Task GetSettingValueAsync_DatabaseError_ReturnsDefaultValue()
        {
            // Arrange
            var settingName = "Test.Error.Setting";
            var defaultValue = "Default Value";

            // Create a service with invalid connection string to simulate database error
            var invalidService = new SettingsService("invalid_connection_string", _mockLogger.Object);

            // Act
            var result = await invalidService.GetSettingValueAsync(settingName, defaultValue);

            // Assert
            Assert.AreEqual(defaultValue, result);
        }

        #endregion
    }
}

namespace AredooPOS.Tests
{
    /// <summary>
    /// اختبارات وحدة خدمة النسخ الاحتياطي
    /// </summary>
    [TestClass]
    public class BackupServiceTests
    {
        #region المتغيرات والإعداد

        private BackupService _backupService;
        private Mock<ILogger<BackupService>> _mockLogger;
        private string _testConnectionString;
        private BackupSettings _testBackupSettings;
        private string _testBackupDirectory;

        [TestInitialize]
        public void Setup()
        {
            _mockLogger = new Mock<ILogger<BackupService>>();
            _testConnectionString = "Server=(localdb)\\MSSQLLocalDB;Database=AredooPOS_Test;Integrated Security=true;";

            _testBackupDirectory = Path.Combine(Path.GetTempPath(), "AredooPOS_Test_Backups");
            _testBackupSettings = new BackupSettings
            {
                BackupDirectory = _testBackupDirectory,
                EncryptBackups = false, // Disable encryption for testing
                MaxBackupCount = 5,
                BackupTimeoutMinutes = 10,
                RestoreTimeoutMinutes = 15
            };

            _backupService = new BackupService(_testConnectionString, _testBackupSettings, _mockLogger.Object);

            // Create test backup directory
            if (!Directory.Exists(_testBackupDirectory))
            {
                Directory.CreateDirectory(_testBackupDirectory);
            }
        }

        [TestCleanup]
        public void Cleanup()
        {
            // Clean up test backup directory
            if (Directory.Exists(_testBackupDirectory))
            {
                try
                {
                    Directory.Delete(_testBackupDirectory, true);
                }
                catch
                {
                    // Ignore cleanup errors
                }
            }
        }

        #endregion

        #region اختبارات النسخ الاحتياطي

        [TestMethod]
        public async Task CreateFullBackupAsync_ValidParameters_ReturnsSuccessfulBackupInfo()
        {
            // Arrange
            var backupName = "TestBackup";

            // Act
            var result = await _backupService.CreateFullBackupAsync(backupName, true, false); // Include settings, exclude files

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.IsSuccessful);
            Assert.AreEqual(backupName, result.BackupName);
            Assert.AreEqual(BackupType.Full, result.BackupType);
            Assert.IsTrue(File.Exists(result.BackupFilePath));
        }

        [TestMethod]
        public async Task CreateFullBackupAsync_WithEncryption_CreatesEncryptedBackup()
        {
            // Arrange
            var encryptedSettings = new BackupSettings
            {
                BackupDirectory = _testBackupDirectory,
                EncryptBackups = true,
                EncryptionPassword = "TestPassword123"
            };
            var encryptedBackupService = new BackupService(_testConnectionString, encryptedSettings, _mockLogger.Object);
            var backupName = "EncryptedTestBackup";

            // Act
            var result = await encryptedBackupService.CreateFullBackupAsync(backupName, true, false);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.IsSuccessful);
            Assert.IsTrue(result.IsEncrypted);
            Assert.IsTrue(result.BackupFilePath.EndsWith(".encrypted"));
        }

        [TestMethod]
        public async Task CreateFullBackupAsync_BackupProgress_FiresProgressEvents()
        {
            // Arrange
            var progressEventsFired = 0;
            var backupName = "ProgressTestBackup";

            _backupService.BackupProgress += (sender, args) =>
            {
                progressEventsFired++;
                Assert.IsTrue(args.ProgressPercentage >= 0 && args.ProgressPercentage <= 100);
                Assert.IsNotNull(args.Message);
            };

            // Act
            var result = await _backupService.CreateFullBackupAsync(backupName, true, false);

            // Assert
            Assert.IsTrue(progressEventsFired > 0);
            Assert.IsTrue(result.IsSuccessful);
        }

        [TestMethod]
        public async Task CreateFullBackupAsync_BackupStartedAndCompleted_FiresEvents()
        {
            // Arrange
            var backupStartedFired = false;
            var backupCompletedFired = false;
            var backupName = "EventTestBackup";

            _backupService.BackupStarted += (sender, args) =>
            {
                backupStartedFired = true;
                Assert.AreEqual(backupName, args.BackupInfo.BackupName);
            };

            _backupService.BackupCompleted += (sender, args) =>
            {
                backupCompletedFired = true;
                Assert.IsTrue(args.BackupInfo.IsSuccessful);
            };

            // Act
            var result = await _backupService.CreateFullBackupAsync(backupName, true, false);

            // Assert
            Assert.IsTrue(backupStartedFired);
            Assert.IsTrue(backupCompletedFired);
            Assert.IsTrue(result.IsSuccessful);
        }

        #endregion

        #region اختبارات الاستعادة

        [TestMethod]
        public async Task RestoreBackupAsync_ValidBackupFile_ReturnsSuccessfulRestoreInfo()
        {
            // Arrange
            // First create a backup
            var backupResult = await _backupService.CreateFullBackupAsync("RestoreTestBackup", true, false);
            Assert.IsTrue(backupResult.IsSuccessful);

            // Act
            var restoreResult = await _backupService.RestoreBackupAsync(backupResult.BackupFilePath, false, true, false);

            // Assert
            Assert.IsNotNull(restoreResult);
            Assert.IsTrue(restoreResult.IsSuccessful);
            Assert.AreEqual(backupResult.BackupFilePath, restoreResult.BackupFilePath);
        }

        [TestMethod]
        public async Task RestoreBackupAsync_EncryptedBackup_RestoresSuccessfully()
        {
            // Arrange
            var encryptedSettings = new BackupSettings
            {
                BackupDirectory = _testBackupDirectory,
                EncryptBackups = true,
                EncryptionPassword = "TestPassword123"
            };
            var encryptedBackupService = new BackupService(_testConnectionString, encryptedSettings, _mockLogger.Object);

            // Create encrypted backup
            var backupResult = await encryptedBackupService.CreateFullBackupAsync("EncryptedRestoreTest", true, false);
            Assert.IsTrue(backupResult.IsSuccessful);
            Assert.IsTrue(backupResult.IsEncrypted);

            // Act
            var restoreResult = await encryptedBackupService.RestoreBackupAsync(backupResult.BackupFilePath, false, true, false);

            // Assert
            Assert.IsNotNull(restoreResult);
            Assert.IsTrue(restoreResult.IsSuccessful);
            Assert.IsTrue(restoreResult.IsEncrypted);
        }

        [TestMethod]
        public async Task RestoreBackupAsync_RestoreProgress_FiresProgressEvents()
        {
            // Arrange
            var progressEventsFired = 0;
            var backupResult = await _backupService.CreateFullBackupAsync("ProgressRestoreTest", true, false);

            _backupService.RestoreProgress += (sender, args) =>
            {
                progressEventsFired++;
                Assert.IsTrue(args.ProgressPercentage >= 0 && args.ProgressPercentage <= 100);
                Assert.IsNotNull(args.Message);
            };

            // Act
            var restoreResult = await _backupService.RestoreBackupAsync(backupResult.BackupFilePath, false, true, false);

            // Assert
            Assert.IsTrue(progressEventsFired > 0);
            Assert.IsTrue(restoreResult.IsSuccessful);
        }

        [TestMethod]
        public async Task RestoreBackupAsync_InvalidBackupFile_ThrowsException()
        {
            // Arrange
            var invalidBackupPath = Path.Combine(_testBackupDirectory, "NonExistentBackup.zip");

            // Act & Assert
            await Assert.ThrowsExceptionAsync<FileNotFoundException>(async () =>
            {
                await _backupService.RestoreBackupAsync(invalidBackupPath, false, true, false);
            });
        }

        #endregion

        #region اختبارات العمليات المساعدة

        [TestMethod]
        public async Task GetBackupHistoryAsync_ReturnsBackupHistory()
        {
            // Arrange
            // Create a backup to have some history
            await _backupService.CreateFullBackupAsync("HistoryTestBackup", true, false);

            // Act
            var history = await _backupService.GetBackupHistoryAsync();

            // Assert
            Assert.IsNotNull(history);
            Assert.IsTrue(history.Count >= 1);
        }

        [TestMethod]
        public async Task DeleteBackupAsync_ExistingBackup_ReturnsTrue()
        {
            // Arrange
            var backupResult = await _backupService.CreateFullBackupAsync("DeleteTestBackup", true, false);
            Assert.IsTrue(File.Exists(backupResult.BackupFilePath));

            // Act
            var deleteResult = await _backupService.DeleteBackupAsync(backupResult.BackupFilePath);

            // Assert
            Assert.IsTrue(deleteResult);
            Assert.IsFalse(File.Exists(backupResult.BackupFilePath));
        }

        [TestMethod]
        public async Task DeleteBackupAsync_NonExistentBackup_ReturnsFalse()
        {
            // Arrange
            var nonExistentPath = Path.Combine(_testBackupDirectory, "NonExistent.zip");

            // Act
            var result = await _backupService.DeleteBackupAsync(nonExistentPath);

            // Assert
            Assert.IsFalse(result);
        }

        #endregion

        #region اختبارات إعدادات النسخ الاحتياطي

        [TestMethod]
        public void BackupSettings_DefaultValues_AreValid()
        {
            // Arrange
            var settings = new BackupSettings();

            // Assert
            Assert.IsNotNull(settings.BackupDirectory);
            Assert.IsTrue(settings.MaxBackupCount > 0);
            Assert.IsTrue(settings.BackupTimeoutMinutes > 0);
            Assert.IsTrue(settings.RestoreTimeoutMinutes > 0);
        }

        [TestMethod]
        public void BackupInfo_DefaultValues_AreValid()
        {
            // Arrange
            var backupInfo = new BackupInfo();

            // Assert
            Assert.AreEqual(BackupType.Full, backupInfo.BackupType);
            Assert.IsFalse(backupInfo.IsSuccessful);
            Assert.IsFalse(backupInfo.IsEncrypted);
        }

        [TestMethod]
        public void RestoreInfo_DefaultValues_AreValid()
        {
            // Arrange
            var restoreInfo = new RestoreInfo();

            // Assert
            Assert.IsFalse(restoreInfo.IsSuccessful);
            Assert.IsFalse(restoreInfo.IsEncrypted);
            Assert.IsFalse(restoreInfo.RestoreDatabase);
            Assert.IsFalse(restoreInfo.RestoreSettings);
            Assert.IsFalse(restoreInfo.RestoreFiles);
        }

        #endregion

        #region اختبارات معالجة الأخطاء

        [TestMethod]
        public async Task CreateFullBackupAsync_InvalidConnectionString_FiresFailedEvent()
        {
            // Arrange
            var failedEventFired = false;
            var invalidBackupService = new BackupService("invalid_connection", _testBackupSettings, _mockLogger.Object);

            invalidBackupService.BackupFailed += (sender, args) =>
            {
                failedEventFired = true;
                Assert.IsNotNull(args.ErrorMessage);
            };

            // Act & Assert
            await Assert.ThrowsExceptionAsync<Exception>(async () =>
            {
                await invalidBackupService.CreateFullBackupAsync("FailedBackup", true, false);
            });

            Assert.IsTrue(failedEventFired);
        }

        [TestMethod]
        public async Task RestoreBackupAsync_CorruptedBackup_FiresFailedEvent()
        {
            // Arrange
            var failedEventFired = false;
            var corruptedBackupPath = Path.Combine(_testBackupDirectory, "CorruptedBackup.zip");

            // Create a corrupted backup file
            await File.WriteAllTextAsync(corruptedBackupPath, "This is not a valid zip file");

            _backupService.RestoreFailed += (sender, args) =>
            {
                failedEventFired = true;
                Assert.IsNotNull(args.ErrorMessage);
            };

            // Act & Assert
            await Assert.ThrowsExceptionAsync<Exception>(async () =>
            {
                await _backupService.RestoreBackupAsync(corruptedBackupPath, false, true, false);
            });

            Assert.IsTrue(failedEventFired);
        }

        #endregion

        #region اختبارات الأداء

        [TestMethod]
        public async Task CreateFullBackupAsync_LargeBackup_CompletesWithinTimeout()
        {
            // Arrange
            var startTime = DateTime.Now;
            var timeoutMinutes = _testBackupSettings.BackupTimeoutMinutes;

            // Act
            var result = await _backupService.CreateFullBackupAsync("PerformanceTestBackup", true, false);

            // Assert
            var duration = DateTime.Now - startTime;
            Assert.IsTrue(duration.TotalMinutes < timeoutMinutes);
            Assert.IsTrue(result.IsSuccessful);
        }

        [TestMethod]
        public async Task RestoreBackupAsync_LargeRestore_CompletesWithinTimeout()
        {
            // Arrange
            var backupResult = await _backupService.CreateFullBackupAsync("RestorePerformanceTest", true, false);
            var startTime = DateTime.Now;
            var timeoutMinutes = _testBackupSettings.RestoreTimeoutMinutes;

            // Act
            var restoreResult = await _backupService.RestoreBackupAsync(backupResult.BackupFilePath, false, true, false);

            // Assert
            var duration = DateTime.Now - startTime;
            Assert.IsTrue(duration.TotalMinutes < timeoutMinutes);
            Assert.IsTrue(restoreResult.IsSuccessful);
        }

        #endregion
    }
}
