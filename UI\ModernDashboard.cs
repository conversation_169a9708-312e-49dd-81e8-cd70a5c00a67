using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// لوحة المعلومات الرئيسية الحديثة
    /// </summary>
    public class ModernDashboard : UserControl
    {
        #region المتغيرات

        private Panel _statsPanel;
        private Panel _chartsPanel;
        private Panel _alertsPanel;
        private Panel _quickActionsPanel;

        // بطاقات الإحصائيات
        private DashboardCard _todaySalesCard;
        private DashboardCard _totalCustomersCard;
        private DashboardCard _lowStockCard;
        private DashboardCard _pendingInstallmentsCard;

        // الرسوم البيانية
        private SimpleChart _salesChart;
        private SimpleChart _productsChart;

        // قائمة التنبيهات
        private ListView _alertsList;

        // الأزرار السريعة
        private Button _newInvoiceButton;
        private Button _addCustomerButton;
        private Button _addProductButton;
        private Button _openDrawerButton;

        #endregion

        #region الأحداث

        public event EventHandler NewInvoiceClicked;
        public event EventHandler AddCustomerClicked;
        public event EventHandler AddProductClicked;
        public event EventHandler OpenDrawerClicked;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ لوحة المعلومات
        /// </summary>
        public ModernDashboard()
        {
            InitializeComponent();
            SetupDesign();
            LoadSampleData();
        }

        /// <summary>
        /// تهيئة المكونات
        /// </summary>
        private void InitializeComponent()
        {
            // إعدادات لوحة المعلومات
            BackColor = ModernDesignSystem.Colors.Background;
            Dock = DockStyle.Fill;
            AutoScroll = true;
            Padding = new Padding(ModernDesignSystem.Spacing.Large);

            // لوحة الإحصائيات
            _statsPanel = new Panel
            {
                Size = new Size(Width - 48, 140),
                Location = new Point(24, 24),
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            // لوحة الرسوم البيانية
            _chartsPanel = new Panel
            {
                Size = new Size(Width - 48, 300),
                Location = new Point(24, 180),
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            // لوحة التنبيهات
            _alertsPanel = new Panel
            {
                Size = new Size((Width - 72) / 2, 250),
                Location = new Point(24, 500),
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.Top | AnchorStyles.Left
            };

            // لوحة الإجراءات السريعة
            _quickActionsPanel = new Panel
            {
                Size = new Size((Width - 72) / 2, 250),
                Location = new Point((Width / 2) + 12, 500),
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };

            // إنشاء بطاقات الإحصائيات
            CreateStatsCards();

            // إنشاء الرسوم البيانية
            CreateCharts();

            // إنشاء قائمة التنبيهات
            CreateAlertsList();

            // إنشاء الأزرار السريعة
            CreateQuickActions();

            // إضافة اللوحات
            Controls.AddRange(new Control[] { _statsPanel, _chartsPanel, _alertsPanel, _quickActionsPanel });
        }

        /// <summary>
        /// إنشاء بطاقات الإحصائيات
        /// </summary>
        private void CreateStatsCards()
        {
            var cardWidth = (_statsPanel.Width - 60) / 4;

            _todaySalesCard = new DashboardCard
            {
                Title = "مبيعات اليوم",
                Value = "15,750 ر.س",
                Icon = "💰",
                Color = ModernDesignSystem.Colors.Success,
                Size = new Size(cardWidth, 120),
                Location = new Point(0, 0)
            };

            _totalCustomersCard = new DashboardCard
            {
                Title = "إجمالي العملاء",
                Value = "1,234",
                Icon = "👥",
                Color = ModernDesignSystem.Colors.Primary,
                Size = new Size(cardWidth, 120),
                Location = new Point(cardWidth + 20, 0)
            };

            _lowStockCard = new DashboardCard
            {
                Title = "منتجات منخفضة",
                Value = "23",
                Icon = "📦",
                Color = ModernDesignSystem.Colors.Warning,
                Size = new Size(cardWidth, 120),
                Location = new Point((cardWidth + 20) * 2, 0)
            };

            _pendingInstallmentsCard = new DashboardCard
            {
                Title = "أقساط معلقة",
                Value = "8,500 ر.س",
                Icon = "📅",
                Color = ModernDesignSystem.Colors.Error,
                Size = new Size(cardWidth, 120),
                Location = new Point((cardWidth + 20) * 3, 0)
            };

            _statsPanel.Controls.AddRange(new Control[] 
            { 
                _todaySalesCard, _totalCustomersCard, _lowStockCard, _pendingInstallmentsCard 
            });
        }

        /// <summary>
        /// إنشاء الرسوم البيانية
        /// </summary>
        private void CreateCharts()
        {
            var chartWidth = (_chartsPanel.Width - 40) / 2;

            // رسم بياني للمبيعات
            _salesChart = new SimpleChart
            {
                Title = "مبيعات الأسبوع",
                ChartType = ChartType.Line,
                Size = new Size(chartWidth, 280),
                Location = new Point(0, 0),
                BackColor = ModernDesignSystem.Colors.Surface
            };

            // رسم بياني للمنتجات
            _productsChart = new SimpleChart
            {
                Title = "أفضل المنتجات",
                ChartType = ChartType.Bar,
                Size = new Size(chartWidth, 280),
                Location = new Point(chartWidth + 20, 0),
                BackColor = ModernDesignSystem.Colors.Surface
            };

            _chartsPanel.Controls.AddRange(new Control[] { _salesChart, _productsChart });
        }

        /// <summary>
        /// إنشاء قائمة التنبيهات
        /// </summary>
        private void CreateAlertsList()
        {
            var alertsCard = new Panel
            {
                Size = _alertsPanel.Size,
                Location = new Point(0, 0),
                BackColor = ModernDesignSystem.Colors.Surface
            };

            // عنوان التنبيهات
            var alertsTitle = new Label
            {
                Text = "التنبيهات والإشعارات",
                Font = ModernDesignSystem.Fonts.Heading4,
                ForeColor = ModernDesignSystem.Colors.TextPrimary,
                Size = new Size(alertsCard.Width - 40, 30),
                Location = new Point(20, 15),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // قائمة التنبيهات
            _alertsList = new ListView
            {
                Size = new Size(alertsCard.Width - 40, alertsCard.Height - 60),
                Location = new Point(20, 50),
                View = View.Details,
                FullRowSelect = true,
                GridLines = false,
                HeaderStyle = ColumnHeaderStyle.None,
                BorderStyle = BorderStyle.None,
                BackColor = ModernDesignSystem.Colors.Surface,
                Font = ModernDesignSystem.Fonts.Body,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            _alertsList.Columns.Add("", alertsCard.Width - 60);

            alertsCard.Controls.AddRange(new Control[] { alertsTitle, _alertsList });
            alertsCard.Paint += (s, e) => ModernDesignSystem.DrawModernCard(e.Graphics, alertsCard.ClientRectangle, ModernDesignSystem.Colors.Surface);

            _alertsPanel.Controls.Add(alertsCard);
        }

        /// <summary>
        /// إنشاء الأزرار السريعة
        /// </summary>
        private void CreateQuickActions()
        {
            var actionsCard = new Panel
            {
                Size = _quickActionsPanel.Size,
                Location = new Point(0, 0),
                BackColor = ModernDesignSystem.Colors.Surface
            };

            // عنوان الإجراءات السريعة
            var actionsTitle = new Label
            {
                Text = "الإجراءات السريعة",
                Font = ModernDesignSystem.Fonts.Heading4,
                ForeColor = ModernDesignSystem.Colors.TextPrimary,
                Size = new Size(actionsCard.Width - 40, 30),
                Location = new Point(20, 15),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // الأزرار
            _newInvoiceButton = CreateQuickActionButton("إنشاء فاتورة", "💰", ModernDesignSystem.Colors.Primary, 0);
            _addCustomerButton = CreateQuickActionButton("إضافة عميل", "👤", ModernDesignSystem.Colors.Success, 1);
            _addProductButton = CreateQuickActionButton("إضافة منتج", "📦", ModernDesignSystem.Colors.Info, 2);
            _openDrawerButton = CreateQuickActionButton("فتح الدرج", "💵", ModernDesignSystem.Colors.Warning, 3);

            // ربط الأحداث
            _newInvoiceButton.Click += (s, e) => NewInvoiceClicked?.Invoke(this, e);
            _addCustomerButton.Click += (s, e) => AddCustomerClicked?.Invoke(this, e);
            _addProductButton.Click += (s, e) => AddProductClicked?.Invoke(this, e);
            _openDrawerButton.Click += (s, e) => OpenDrawerClicked?.Invoke(this, e);

            actionsCard.Controls.AddRange(new Control[] 
            { 
                actionsTitle, _newInvoiceButton, _addCustomerButton, _addProductButton, _openDrawerButton 
            });
            actionsCard.Paint += (s, e) => ModernDesignSystem.DrawModernCard(e.Graphics, actionsCard.ClientRectangle, ModernDesignSystem.Colors.Surface);

            _quickActionsPanel.Controls.Add(actionsCard);
        }

        /// <summary>
        /// إنشاء زر إجراء سريع
        /// </summary>
        private Button CreateQuickActionButton(string text, string icon, Color color, int index)
        {
            var button = new Button
            {
                Text = $"{icon} {text}",
                Font = ModernDesignSystem.Fonts.ButtonLarge,
                Size = new Size(_quickActionsPanel.Width - 60, 40),
                Location = new Point(20, 60 + (index * 50)),
                FlatStyle = FlatStyle.Flat,
                BackColor = color,
                ForeColor = ModernDesignSystem.Colors.TextOnPrimary,
                TextAlign = ContentAlignment.MiddleCenter,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ModernDesignSystem.LightenColor(color, 0.1f);
            button.FlatAppearance.MouseDownBackColor = ModernDesignSystem.DarkenColor(color, 0.1f);

            return button;
        }

        /// <summary>
        /// إعداد التصميم
        /// </summary>
        private void SetupDesign()
        {
            RightToLeft = RightToLeft.Yes;
        }

        /// <summary>
        /// تحميل البيانات التجريبية
        /// </summary>
        private void LoadSampleData()
        {
            // إضافة تنبيهات تجريبية
            var alerts = new[]
            {
                "⚠️ منتج 'شامبو الأطفال' وصل للحد الأدنى",
                "💳 قسط العميل 'أحمد محمد' مستحق اليوم",
                "📦 وصول شحنة جديدة من المنتجات",
                "💰 تم تحقيق هدف المبيعات اليومي",
                "🔔 تذكير: إغلاق الصندوق في 6:00 م"
            };

            foreach (var alert in alerts)
            {
                _alertsList.Items.Add(new ListViewItem(alert));
            }

            // تحديث بيانات الرسوم البيانية
            _salesChart.UpdateData(new[] { 1200.0, 1800.0, 1500.0, 2200.0, 1900.0, 2500.0, 2100.0 });
            _productsChart.UpdateData(new[] { 45.0, 38.0, 32.0, 28.0, 25.0 });
        }

        #endregion
    }

    #region المكونات المساعدة

    /// <summary>
    /// بطاقة لوحة المعلومات
    /// </summary>
    public class DashboardCard : UserControl
    {
        public string Title { get; set; }
        public string Value { get; set; }
        public string Icon { get; set; }
        public Color Color { get; set; }

        public DashboardCard()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
            BackColor = ModernDesignSystem.Colors.Surface;
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // رسم البطاقة
            ModernDesignSystem.DrawModernCard(g, ClientRectangle, BackColor);

            // رسم الأيقونة
            var iconRect = new Rectangle(Width - 60, 15, 40, 40);
            using (var iconBrush = new SolidBrush(Color))
            {
                var iconFont = new Font("Segoe UI Emoji", 24);
                var iconFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };
                g.DrawString(Icon, iconFont, iconBrush, iconRect, iconFormat);
            }

            // رسم القيمة
            var valueRect = new Rectangle(20, 15, Width - 100, 35);
            using (var valueBrush = new SolidBrush(ModernDesignSystem.Colors.TextPrimary))
            {
                var valueFormat = new StringFormat
                {
                    Alignment = StringAlignment.Far,
                    LineAlignment = StringAlignment.Center,
                    FormatFlags = StringFormatFlags.DirectionRightToLeft
                };
                g.DrawString(Value, ModernDesignSystem.Fonts.NumbersLarge, valueBrush, valueRect, valueFormat);
            }

            // رسم العنوان
            var titleRect = new Rectangle(20, 55, Width - 100, 25);
            using (var titleBrush = new SolidBrush(ModernDesignSystem.Colors.TextSecondary))
            {
                var titleFormat = new StringFormat
                {
                    Alignment = StringAlignment.Far,
                    LineAlignment = StringAlignment.Center,
                    FormatFlags = StringFormatFlags.DirectionRightToLeft
                };
                g.DrawString(Title, ModernDesignSystem.Fonts.Body, titleBrush, titleRect, titleFormat);
            }
        }
    }

    /// <summary>
    /// رسم بياني بسيط
    /// </summary>
    public class SimpleChart : UserControl
    {
        public string Title { get; set; }
        public ChartType ChartType { get; set; }
        private double[] _data;

        public SimpleChart()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
            BackColor = ModernDesignSystem.Colors.Surface;
        }

        public void UpdateData(double[] data)
        {
            _data = data;
            Invalidate();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // رسم البطاقة
            ModernDesignSystem.DrawModernCard(g, ClientRectangle, BackColor);

            // رسم العنوان
            var titleRect = new Rectangle(20, 15, Width - 40, 30);
            using (var titleBrush = new SolidBrush(ModernDesignSystem.Colors.TextPrimary))
            {
                var titleFormat = new StringFormat
                {
                    Alignment = StringAlignment.Far,
                    LineAlignment = StringAlignment.Center,
                    FormatFlags = StringFormatFlags.DirectionRightToLeft
                };
                g.DrawString(Title, ModernDesignSystem.Fonts.Heading4, titleBrush, titleRect, titleFormat);
            }

            // رسم البيانات
            if (_data != null && _data.Length > 0)
            {
                var chartRect = new Rectangle(30, 60, Width - 60, Height - 100);

                if (ChartType == ChartType.Line)
                    DrawLineChart(g, chartRect);
                else if (ChartType == ChartType.Bar)
                    DrawBarChart(g, chartRect);
            }
        }

        private void DrawLineChart(Graphics g, Rectangle bounds)
        {
            if (_data.Length < 2) return;

            var maxValue = _data.Max();
            var points = new PointF[_data.Length];

            for (int i = 0; i < _data.Length; i++)
            {
                var x = bounds.X + (i * bounds.Width / (_data.Length - 1));
                var y = bounds.Bottom - (_data[i] / maxValue * bounds.Height);
                points[i] = new PointF(x, (float)y);
            }

            using (var pen = new Pen(ModernDesignSystem.Colors.Primary, 3))
            {
                g.DrawLines(pen, points);
            }

            // رسم النقاط
            foreach (var point in points)
            {
                using (var brush = new SolidBrush(ModernDesignSystem.Colors.Primary))
                {
                    g.FillEllipse(brush, point.X - 4, point.Y - 4, 8, 8);
                }
            }
        }

        private void DrawBarChart(Graphics g, Rectangle bounds)
        {
            var maxValue = _data.Max();
            var barWidth = bounds.Width / _data.Length - 10;

            for (int i = 0; i < _data.Length; i++)
            {
                var barHeight = (int)(_data[i] / maxValue * bounds.Height);
                var barRect = new Rectangle(
                    bounds.X + (i * (barWidth + 10)),
                    bounds.Bottom - barHeight,
                    barWidth,
                    barHeight
                );

                var color = i % 2 == 0 ? ModernDesignSystem.Colors.Primary : ModernDesignSystem.Colors.Secondary;
                using (var brush = new SolidBrush(color))
                {
                    g.FillRectangle(brush, barRect);
                }
            }
        }
    }

    /// <summary>
    /// أنواع الرسوم البيانية
    /// </summary>
    public enum ChartType
    {
        Line,
        Bar,
        Pie
    }

    #endregion
}
