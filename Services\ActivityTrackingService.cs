using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Timers;
using AredooPOS.Models;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Services
{
    /// <summary>
    /// خدمة تتبع الأنشطة والجلسات
    /// تقوم بمراقبة وتسجيل جميع أنشطة المستخدمين في النظام
    /// </summary>
    public class ActivityTrackingService : IDisposable
    {
        #region المتغيرات والخصائص الخاصة

        private readonly UserActivityDAL _activityDAL;
        private readonly UserSessionDAL _sessionDAL;
        private readonly ILogger<ActivityTrackingService> _logger;
        private readonly Timer _cleanupTimer;
        private readonly Timer _sessionMonitorTimer;

        // إعدادات الخدمة
        private readonly ActivityTrackingSettings _settings;

        // حالة الخدمة
        private bool _isRunning;
        private bool _disposed;

        // قائمة المراقبين
        private readonly List<IActivityObserver> _observers;

        #endregion

        #region الأحداث

        /// <summary>
        /// حدث عند تسجيل نشاط جديد
        /// </summary>
        public event EventHandler<ActivityLoggedEventArgs> ActivityLogged;

        /// <summary>
        /// حدث عند اكتشاف نشاط مشبوه
        /// </summary>
        public event EventHandler<SuspiciousActivityEventArgs> SuspiciousActivityDetected;

        /// <summary>
        /// حدث عند انتهاء صلاحية جلسة
        /// </summary>
        public event EventHandler<SessionExpiredEventArgs> SessionExpired;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ خدمة تتبع الأنشطة
        /// </summary>
        /// <param name="settings">إعدادات الخدمة</param>
        /// <param name="logger">مسجل الأحداث</param>
        public ActivityTrackingService(ActivityTrackingSettings settings = null, ILogger<ActivityTrackingService> logger = null)
        {
            _settings = settings ?? new ActivityTrackingSettings();
            _logger = logger;
            _activityDAL = new UserActivityDAL();
            _sessionDAL = new UserSessionDAL();
            _observers = new List<IActivityObserver>();

            // إعداد مؤقت التنظيف
            _cleanupTimer = new Timer(_settings.CleanupIntervalHours * 60 * 60 * 1000); // تحويل إلى ميلي ثانية
            _cleanupTimer.Elapsed += CleanupTimer_Elapsed;
            _cleanupTimer.AutoReset = true;

            // إعداد مؤقت مراقبة الجلسات
            _sessionMonitorTimer = new Timer(_settings.SessionMonitorIntervalMinutes * 60 * 1000);
            _sessionMonitorTimer.Elapsed += SessionMonitorTimer_Elapsed;
            _sessionMonitorTimer.AutoReset = true;

            _logger?.LogInformation("تم تهيئة خدمة تتبع الأنشطة");
        }

        #endregion

        #region إدارة الخدمة

        /// <summary>
        /// بدء خدمة تتبع الأنشطة
        /// </summary>
        public void Start()
        {
            if (_isRunning)
                return;

            try
            {
                if (_settings.EnableCleanup)
                    _cleanupTimer.Start();

                if (_settings.EnableSessionMonitoring)
                    _sessionMonitorTimer.Start();

                _isRunning = true;
                _logger?.LogInformation("تم بدء خدمة تتبع الأنشطة");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في بدء خدمة تتبع الأنشطة");
                throw;
            }
        }

        /// <summary>
        /// إيقاف خدمة تتبع الأنشطة
        /// </summary>
        public void Stop()
        {
            if (!_isRunning)
                return;

            try
            {
                _cleanupTimer.Stop();
                _sessionMonitorTimer.Stop();
                _isRunning = false;
                _logger?.LogInformation("تم إيقاف خدمة تتبع الأنشطة");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إيقاف خدمة تتبع الأنشطة");
                throw;
            }
        }

        /// <summary>
        /// التحقق من حالة الخدمة
        /// </summary>
        public bool IsRunning => _isRunning;

        #endregion

        #region تسجيل الأنشطة

        /// <summary>
        /// تسجيل نشاط مستخدم
        /// </summary>
        /// <param name="activity">بيانات النشاط</param>
        /// <returns>رقم النشاط المسجل</returns>
        public async Task<int> LogActivityAsync(UserActivity activity)
        {
            try
            {
                if (activity == null)
                    throw new ArgumentNullException(nameof(activity));

                // التحقق من صحة البيانات
                if (!activity.IsValid())
                    throw new ArgumentException("بيانات النشاط غير صحيحة");

                // فحص النشاط للكشف عن الأنشطة المشبوهة
                await AnalyzeActivityAsync(activity);

                // تسجيل النشاط
                var activityId = _activityDAL.AddUserActivity(activity);
                activity.ActivityID = activityId;

                // إشعار المراقبين
                await NotifyObserversAsync(activity);

                // إثارة الحدث
                ActivityLogged?.Invoke(this, new ActivityLoggedEventArgs { Activity = activity });

                _logger?.LogDebug($"تم تسجيل النشاط {activityId} للمستخدم {activity.UserID}");
                return activityId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تسجيل النشاط");
                throw;
            }
        }

        /// <summary>
        /// تسجيل نشاط مستخدم (متزامن)
        /// </summary>
        /// <param name="activity">بيانات النشاط</param>
        /// <returns>رقم النشاط المسجل</returns>
        public int LogActivity(UserActivity activity)
        {
            return LogActivityAsync(activity).GetAwaiter().GetResult();
        }

        /// <summary>
        /// تسجيل نشاط سريع
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="activityType">نوع النشاط</param>
        /// <param name="description">وصف النشاط</param>
        /// <param name="module">الوحدة</param>
        /// <param name="action">الإجراء</param>
        /// <param name="sessionId">رقم الجلسة</param>
        /// <param name="ipAddress">عنوان IP</param>
        /// <param name="isSecuritySensitive">هل النشاط حساس أمنياً</param>
        /// <returns>رقم النشاط المسجل</returns>
        public async Task<int> LogQuickActivityAsync(int userId, string activityType, string description, 
            string module = null, string action = null, int? sessionId = null, string ipAddress = null, 
            bool isSecuritySensitive = false)
        {
            var activity = new UserActivity(userId, activityType, description, module, action)
            {
                SessionID = sessionId,
                IPAddress = ipAddress,
                IsSecuritySensitive = isSecuritySensitive,
                RequiresReview = isSecuritySensitive,
                Severity = isSecuritySensitive ? ActivitySeverity.High : ActivitySeverity.Low
            };

            return await LogActivityAsync(activity);
        }

        #endregion

        #region تحليل الأنشطة

        /// <summary>
        /// تحليل النشاط للكشف عن الأنشطة المشبوهة
        /// </summary>
        /// <param name="activity">النشاط المراد تحليله</param>
        private async Task AnalyzeActivityAsync(UserActivity activity)
        {
            try
            {
                var suspiciousReasons = new List<string>();

                // فحص الأنشطة المتكررة
                if (await CheckRepeatedActivityAsync(activity))
                    suspiciousReasons.Add("نشاط متكرر بشكل مشبوه");

                // فحص الأنشطة في أوقات غير عادية
                if (CheckUnusualTiming(activity))
                    suspiciousReasons.Add("نشاط في وقت غير عادي");

                // فحص الأنشطة من عناوين IP مختلفة
                if (await CheckMultipleIPAddressesAsync(activity))
                    suspiciousReasons.Add("نشاط من عناوين IP متعددة");

                // فحص الأنشطة الفاشلة المتكررة
                if (await CheckRepeatedFailuresAsync(activity))
                    suspiciousReasons.Add("محاولات فاشلة متكررة");

                // إذا تم اكتشاف نشاط مشبوه
                if (suspiciousReasons.Count > 0)
                {
                    activity.IsSuspicious = true;
                    activity.SuspiciousReason = string.Join(", ", suspiciousReasons);
                    activity.RequiresReview = true;
                    activity.Severity = ActivitySeverity.Critical;

                    // إثارة حدث النشاط المشبوه
                    SuspiciousActivityDetected?.Invoke(this, new SuspiciousActivityEventArgs
                    {
                        Activity = activity,
                        Reasons = suspiciousReasons
                    });

                    _logger?.LogWarning($"تم اكتشاف نشاط مشبوه للمستخدم {activity.UserID}: {string.Join(", ", suspiciousReasons)}");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحليل النشاط");
            }
        }

        /// <summary>
        /// فحص الأنشطة المتكررة
        /// </summary>
        private async Task<bool> CheckRepeatedActivityAsync(UserActivity activity)
        {
            try
            {
                var recentActivities = _activityDAL.GetUserActivities(
                    activity.UserID, 
                    DateTime.Now.AddMinutes(-_settings.RepeatedActivityWindowMinutes), 
                    DateTime.Now, 
                    activity.ActivityType);

                return recentActivities.Count > _settings.MaxRepeatedActivities;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// فحص التوقيت غير العادي
        /// </summary>
        private bool CheckUnusualTiming(UserActivity activity)
        {
            var hour = activity.ActivityDateTime.Hour;
            return hour < _settings.NormalWorkingHoursStart || hour > _settings.NormalWorkingHoursEnd;
        }

        /// <summary>
        /// فحص عناوين IP متعددة
        /// </summary>
        private async Task<bool> CheckMultipleIPAddressesAsync(UserActivity activity)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(activity.IPAddress))
                    return false;

                var recentActivities = _activityDAL.GetUserActivities(
                    activity.UserID, 
                    DateTime.Now.AddHours(-_settings.MultipleIPCheckHours), 
                    DateTime.Now);

                var uniqueIPs = recentActivities
                    .Where(a => !string.IsNullOrWhiteSpace(a.IPAddress))
                    .Select(a => a.IPAddress)
                    .Distinct()
                    .Count();

                return uniqueIPs > _settings.MaxUniqueIPAddresses;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// فحص المحاولات الفاشلة المتكررة
        /// </summary>
        private async Task<bool> CheckRepeatedFailuresAsync(UserActivity activity)
        {
            try
            {
                if (activity.IsSuccessful)
                    return false;

                var recentFailures = _activityDAL.GetUserActivities(
                    activity.UserID, 
                    DateTime.Now.AddMinutes(-_settings.FailedAttemptsWindowMinutes), 
                    DateTime.Now, 
                    activity.ActivityType)
                    .Where(a => !a.IsSuccessful)
                    .ToList();

                return recentFailures.Count > _settings.MaxFailedAttempts;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region مراقبة الجلسات

        /// <summary>
        /// مراقبة الجلسات المنتهية الصلاحية
        /// </summary>
        private async void SessionMonitorTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            await MonitorSessionsAsync();
        }

        /// <summary>
        /// مراقبة الجلسات
        /// </summary>
        private async Task MonitorSessionsAsync()
        {
            try
            {
                var activeSessions = _sessionDAL.GetActiveSessions();
                var expiredSessions = new List<UserSession>();

                foreach (var session in activeSessions)
                {
                    if (session.IsExpired())
                    {
                        // إنهاء الجلسة المنتهية الصلاحية
                        _sessionDAL.EndUserSession(session.SessionID, SessionEndReasons.Expired);
                        expiredSessions.Add(session);

                        // تسجيل النشاط
                        await LogQuickActivityAsync(session.UserID, ActivityTypes.Logout, 
                            "انتهت صلاحية الجلسة تلقائياً", SystemModules.Authentication, 
                            "انتهاء صلاحية الجلسة", session.SessionID, session.IPAddress);

                        // إثارة الحدث
                        SessionExpired?.Invoke(this, new SessionExpiredEventArgs { Session = session });
                    }
                }

                if (expiredSessions.Count > 0)
                {
                    _logger?.LogInformation($"تم إنهاء {expiredSessions.Count} جلسة منتهية الصلاحية");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في مراقبة الجلسات");
            }
        }

        #endregion

        #region التنظيف والصيانة

        /// <summary>
        /// تنظيف البيانات القديمة
        /// </summary>
        private async void CleanupTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            await PerformCleanupAsync();
        }

        /// <summary>
        /// تنفيذ عملية التنظيف
        /// </summary>
        private async Task PerformCleanupAsync()
        {
            try
            {
                _logger?.LogInformation("بدء عملية تنظيف البيانات القديمة");

                // تنظيف الأنشطة القديمة
                var deletedActivities = _activityDAL.CleanupOldActivities(
                    _settings.ActivityRetentionDays, 
                    _settings.KeepSecuritySensitiveActivities);

                // تنظيف الجلسات القديمة
                var deletedSessions = _sessionDAL.CleanupExpiredSessions(_settings.SessionRetentionDays);

                _logger?.LogInformation($"تم حذف {deletedActivities} نشاط و {deletedSessions} جلسة قديمة");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في عملية التنظيف");
            }
        }

        /// <summary>
        /// تنظيف يدوي للبيانات
        /// </summary>
        /// <returns>ملخص عملية التنظيف</returns>
        public async Task<CleanupSummary> PerformManualCleanupAsync()
        {
            try
            {
                var summary = new CleanupSummary();

                // تنظيف الأنشطة
                summary.DeletedActivities = _activityDAL.CleanupOldActivities(
                    _settings.ActivityRetentionDays, 
                    _settings.KeepSecuritySensitiveActivities);

                // تنظيف الجلسات
                summary.DeletedSessions = _sessionDAL.CleanupExpiredSessions(_settings.SessionRetentionDays);

                summary.CleanupDate = DateTime.Now;
                summary.IsSuccessful = true;

                _logger?.LogInformation($"تنظيف يدوي: تم حذف {summary.DeletedActivities} نشاط و {summary.DeletedSessions} جلسة");

                return summary;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في التنظيف اليدوي");
                return new CleanupSummary { IsSuccessful = false, ErrorMessage = ex.Message };
            }
        }

        #endregion

        #region إدارة المراقبين

        /// <summary>
        /// إضافة مراقب للأنشطة
        /// </summary>
        /// <param name="observer">المراقب</param>
        public void AddObserver(IActivityObserver observer)
        {
            if (observer != null && !_observers.Contains(observer))
            {
                _observers.Add(observer);
                _logger?.LogInformation($"تم إضافة مراقب أنشطة: {observer.GetType().Name}");
            }
        }

        /// <summary>
        /// إزالة مراقب للأنشطة
        /// </summary>
        /// <param name="observer">المراقب</param>
        public void RemoveObserver(IActivityObserver observer)
        {
            if (observer != null && _observers.Contains(observer))
            {
                _observers.Remove(observer);
                _logger?.LogInformation($"تم إزالة مراقب أنشطة: {observer.GetType().Name}");
            }
        }

        /// <summary>
        /// إشعار جميع المراقبين
        /// </summary>
        /// <param name="activity">النشاط</param>
        private async Task NotifyObserversAsync(UserActivity activity)
        {
            var tasks = _observers.Select(observer => 
                Task.Run(() => observer.OnActivityLogged(activity)));
            
            await Task.WhenAll(tasks);
        }

        #endregion

        #region الإحصائيات

        /// <summary>
        /// الحصول على إحصائيات الأنشطة
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>إحصائيات الأنشطة</returns>
        public ActivityStatistics GetActivityStatistics(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                return _activityDAL.GetActivityStatistics(fromDate, toDate);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إحصائيات الأنشطة");
                throw;
            }
        }

        /// <summary>
        /// الحصول على إحصائيات الجلسات
        /// </summary>
        /// <returns>إحصائيات الجلسات</returns>
        public SessionStatistics GetSessionStatistics()
        {
            try
            {
                return _sessionDAL.GetSessionStatistics();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إحصائيات الجلسات");
                throw;
            }
        }

        #endregion

        #region التنظيف والإغلاق

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        /// <param name="disposing">هل يتم التنظيف</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                Stop();
                _cleanupTimer?.Dispose();
                _sessionMonitorTimer?.Dispose();
                _observers.Clear();
                _disposed = true;
                
                _logger?.LogInformation("تم تنظيف خدمة تتبع الأنشطة");
            }
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// إعدادات خدمة تتبع الأنشطة
    /// </summary>
    public class ActivityTrackingSettings
    {
        public bool EnableCleanup { get; set; } = true;
        public bool EnableSessionMonitoring { get; set; } = true;
        public bool KeepSecuritySensitiveActivities { get; set; } = true;
        
        public int CleanupIntervalHours { get; set; } = 24;
        public int SessionMonitorIntervalMinutes { get; set; } = 5;
        public int ActivityRetentionDays { get; set; } = 365;
        public int SessionRetentionDays { get; set; } = 90;
        
        // إعدادات كشف الأنشطة المشبوهة
        public int RepeatedActivityWindowMinutes { get; set; } = 5;
        public int MaxRepeatedActivities { get; set; } = 10;
        public int NormalWorkingHoursStart { get; set; } = 8;
        public int NormalWorkingHoursEnd { get; set; } = 18;
        public int MultipleIPCheckHours { get; set; } = 1;
        public int MaxUniqueIPAddresses { get; set; } = 3;
        public int FailedAttemptsWindowMinutes { get; set; } = 15;
        public int MaxFailedAttempts { get; set; } = 5;
    }

    /// <summary>
    /// ملخص عملية التنظيف
    /// </summary>
    public class CleanupSummary
    {
        public bool IsSuccessful { get; set; }
        public string ErrorMessage { get; set; }
        public int DeletedActivities { get; set; }
        public int DeletedSessions { get; set; }
        public DateTime CleanupDate { get; set; }
    }

    /// <summary>
    /// واجهة مراقب الأنشطة
    /// </summary>
    public interface IActivityObserver
    {
        Task OnActivityLogged(UserActivity activity);
    }

    /// <summary>
    /// بيانات حدث تسجيل النشاط
    /// </summary>
    public class ActivityLoggedEventArgs : EventArgs
    {
        public UserActivity Activity { get; set; }
    }

    /// <summary>
    /// بيانات حدث النشاط المشبوه
    /// </summary>
    public class SuspiciousActivityEventArgs : EventArgs
    {
        public UserActivity Activity { get; set; }
        public List<string> Reasons { get; set; }
    }

    /// <summary>
    /// بيانات حدث انتهاء الجلسة
    /// </summary>
    public class SessionExpiredEventArgs : EventArgs
    {
        public UserSession Session { get; set; }
    }

    #endregion
}
