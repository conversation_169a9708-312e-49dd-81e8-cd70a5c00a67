<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net472</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <AssemblyTitle>اختبار أريدو الكاشير المتجاوب</AssemblyTitle>
    <AssemblyDescription>اختبار بسيط للتطبيق المتجاوب</AssemblyDescription>
    <StartupObject>AredooCashier.SimpleTestProgram</StartupObject>
    <LangVersion>latest</LangVersion>
    <Nullable>disable</Nullable>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>

  <!-- مراجع النظام -->
  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="Microsoft.CSharp" />
  </ItemGroup>

  <!-- ملفات الكود -->
  <ItemGroup>
    <Compile Include="SimpleResponsiveTest.cs" />
    <Compile Include="ResponsiveAredooCashierApp.cs" />
    <Compile Include="UI\ResponsiveLayoutSystem.cs" />
    <Compile Include="UI\ResponsiveDesignSystem.cs" />
    <Compile Include="UI\ResponsiveTopBar.cs" />
    <Compile Include="UI\ResponsiveSidebar.cs" />
    <Compile Include="UI\ResponsiveDashboard.cs" />
    <Compile Include="UI\ResponsiveInvoiceForm.cs" />
    <Compile Include="UI\ResponsiveCustomersView.cs" />
  </ItemGroup>

</Project>
