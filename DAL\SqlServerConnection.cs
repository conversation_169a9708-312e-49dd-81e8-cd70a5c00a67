using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;

namespace AredooPOS.DAL
{
    /// <summary>
    /// فئة إدارة الاتصال بقاعدة بيانات SQL Server
    /// تتولى إنشاء وإدارة الاتصالات مع SQL Server
    /// </summary>
    public class SqlServerConnection
    {
        #region المتغيرات الثابتة

        // سلسلة الاتصال الافتراضية
        private static readonly string DefaultConnectionString = 
            @"Data Source=.\SQLEXPRESS;Initial Catalog=AredooPOS;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False";

        #endregion

        #region الخصائص

        /// <summary>
        /// الحصول على سلسلة الاتصال من ملف التكوين أو استخدام الافتراضية
        /// </summary>
        public static string ConnectionString
        {
            get
            {
                try
                {
                    // محاولة قراءة سلسلة الاتصال من App.config
                    string connectionString = ConfigurationManager.ConnectionStrings["AredooPOS"]?.ConnectionString;
                    
                    // إذا لم توجد، استخدم الافتراضية
                    return string.IsNullOrEmpty(connectionString) ? DefaultConnectionString : connectionString;
                }
                catch
                {
                    // في حالة حدوث خطأ، استخدم الافتراضية
                    return DefaultConnectionString;
                }
            }
        }

        #endregion

        #region طرق الاتصال

        /// <summary>
        /// إنشاء اتصال جديد بقاعدة البيانات
        /// </summary>
        /// <returns>كائن SqlConnection جديد</returns>
        public static SqlConnection GetConnection()
        {
            return new SqlConnection(ConnectionString);
        }

        /// <summary>
        /// إنشاء اتصال مفتوح بقاعدة البيانات
        /// </summary>
        /// <returns>كائن SqlConnection مفتوح</returns>
        public static SqlConnection GetOpenConnection()
        {
            var connection = new SqlConnection(ConnectionString);
            connection.Open();
            return connection;
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        /// <returns>true إذا كان الاتصال ناجحاً، false إذا فشل</returns>
        public static bool TestConnection()
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    return connection.State == ConnectionState.Open;
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                System.Diagnostics.Debug.WriteLine($"خطأ في اختبار الاتصال: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار الاتصال مع إرجاع رسالة الخطأ
        /// </summary>
        /// <param name="errorMessage">رسالة الخطأ في حالة فشل الاتصال</param>
        /// <returns>true إذا كان الاتصال ناجحاً، false إذا فشل</returns>
        public static bool TestConnection(out string errorMessage)
        {
            errorMessage = string.Empty;
            
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    
                    if (connection.State == ConnectionState.Open)
                    {
                        errorMessage = "الاتصال ناجح";
                        return true;
                    }
                    else
                    {
                        errorMessage = "فشل في فتح الاتصال";
                        return false;
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                // أخطاء SQL Server محددة
                switch (sqlEx.Number)
                {
                    case 2:
                        errorMessage = "لا يمكن الوصول إلى الخادم. تأكد من أن SQL Server يعمل.";
                        break;
                    case 18456:
                        errorMessage = "فشل في المصادقة. تحقق من اسم المستخدم وكلمة المرور.";
                        break;
                    case 4060:
                        errorMessage = "قاعدة البيانات غير موجودة أو لا يمكن الوصول إليها.";
                        break;
                    default:
                        errorMessage = $"خطأ في قاعدة البيانات: {sqlEx.Message}";
                        break;
                }
                return false;
            }
            catch (Exception ex)
            {
                errorMessage = $"خطأ عام: {ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع عدد الصفوف المتأثرة
        /// </summary>
        /// <param name="query">الاستعلام المراد تنفيذه</param>
        /// <param name="parameters">المعاملات (اختيارية)</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public static int ExecuteNonQuery(string query, params SqlParameter[] parameters)
        {
            try
            {
                using (var connection = GetOpenConnection())
                using (var command = new SqlCommand(query, connection))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }
                    
                    return command.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الاستعلام: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع قيمة واحدة
        /// </summary>
        /// <param name="query">الاستعلام المراد تنفيذه</param>
        /// <param name="parameters">المعاملات (اختيارية)</param>
        /// <returns>القيمة المرجعة من الاستعلام</returns>
        public static object ExecuteScalar(string query, params SqlParameter[] parameters)
        {
            try
            {
                using (var connection = GetOpenConnection())
                using (var command = new SqlCommand(query, connection))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }
                    
                    return command.ExecuteScalar();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الاستعلام: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع DataTable
        /// </summary>
        /// <param name="query">الاستعلام المراد تنفيذه</param>
        /// <param name="parameters">المعاملات (اختيارية)</param>
        /// <returns>DataTable يحتوي على النتائج</returns>
        public static DataTable ExecuteDataTable(string query, params SqlParameter[] parameters)
        {
            try
            {
                using (var connection = GetOpenConnection())
                using (var command = new SqlCommand(query, connection))
                using (var adapter = new SqlDataAdapter(command))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters);
                    }
                    
                    var dataTable = new DataTable();
                    adapter.Fill(dataTable);
                    return dataTable;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الاستعلام: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء معامل SQL
        /// </summary>
        /// <param name="name">اسم المعامل</param>
        /// <param name="value">قيمة المعامل</param>
        /// <returns>SqlParameter</returns>
        public static SqlParameter CreateParameter(string name, object value)
        {
            return new SqlParameter(name, value ?? DBNull.Value);
        }

        /// <summary>
        /// إنشاء معامل SQL مع نوع البيانات
        /// </summary>
        /// <param name="name">اسم المعامل</param>
        /// <param name="value">قيمة المعامل</param>
        /// <param name="dbType">نوع البيانات</param>
        /// <returns>SqlParameter</returns>
        public static SqlParameter CreateParameter(string name, object value, SqlDbType dbType)
        {
            return new SqlParameter(name, dbType) { Value = value ?? DBNull.Value };
        }

        /// <summary>
        /// التحقق من وجود قاعدة البيانات
        /// </summary>
        /// <returns>true إذا كانت قاعدة البيانات موجودة</returns>
        public static bool DatabaseExists()
        {
            try
            {
                var query = "SELECT COUNT(*) FROM sys.databases WHERE name = 'AredooPOS'";
                
                // استخدام اتصال master للتحقق من وجود قاعدة البيانات
                var masterConnectionString = ConnectionString.Replace("Initial Catalog=AredooPOS", "Initial Catalog=master");
                
                using (var connection = new SqlConnection(masterConnectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        var count = Convert.ToInt32(command.ExecuteScalar());
                        return count > 0;
                    }
                }
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}
