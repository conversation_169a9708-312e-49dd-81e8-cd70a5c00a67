using System;
using System.Drawing;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// نموذج الفاتورة الحديث
    /// </summary>
    public class ModernInvoiceView : UserControl
    {
        #region المتغيرات

        private Panel _headerPanel;
        private Panel _customerPanel;
        private Panel _itemsPanel;
        private Panel _totalsPanel;
        private Panel _actionsPanel;

        private ComboBox _customerCombo;
        private DataGridView _itemsGrid;
        private TextBox _barcodeText;
        private Panel _subtotalLabel;
        private Panel _taxLabel;
        private Panel _totalLabel;

        #endregion

        #region البناء والتهيئة

        public ModernInvoiceView()
        {
            InitializeComponent();
            CreateContent();
            LoadSampleData();
        }

        private void InitializeComponent()
        {
            BackColor = ModernDesign.Colors.Background;
            Dock = DockStyle.Fill;
            RightToLeft = RightToLeft.Yes;
            AutoScroll = true;
        }

        private void CreateContent()
        {
            var yPosition = ModernDesign.Spacing.Large;

            // رأس الفاتورة
            CreateHeader(yPosition);
            yPosition += 80;

            // معلومات العميل
            CreateCustomerSection(yPosition);
            yPosition += 100;

            // جدول المنتجات
            CreateItemsSection(yPosition);
            yPosition += 300;

            // الإجماليات
            CreateTotalsSection(yPosition);
            yPosition += 150;

            // أزرار الإجراءات
            CreateActionsSection(yPosition);
        }

        private void CreateHeader(int yPosition)
        {
            _headerPanel = ModernDesign.CreateModernPanel(ModernDesign.Colors.Surface);
            _headerPanel.Size = new Size(Width - 40, 60);
            _headerPanel.Location = new Point(20, yPosition);
            _headerPanel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;

            var titleLabel = ModernDesign.CreateModernLabel(
                "💰 إنشاء فاتورة جديدة",
                ModernDesign.Fonts.Title,
                ModernDesign.Colors.Primary
            );
            titleLabel.Size = new Size(_headerPanel.Width - 40, 30);
            titleLabel.Location = new Point(20, 15);

            var invoiceNumberLabel = ModernDesign.CreateModernLabel(
                $"رقم الفاتورة: INV-{DateTime.Now:yyyyMMdd}-001",
                ModernDesign.Fonts.Body,
                ModernDesign.Colors.TextSecondary
            );
            invoiceNumberLabel.Size = new Size(200, 20);
            invoiceNumberLabel.Location = new Point(20, 45);

            var dateLabel = ModernDesign.CreateModernLabel(
                $"التاريخ: {DateTime.Now:yyyy/MM/dd}",
                ModernDesign.Fonts.Body,
                ModernDesign.Colors.TextSecondary
            );
            dateLabel.Size = new Size(150, 20);
            dateLabel.Location = new Point(_headerPanel.Width - 170, 45);

            _headerPanel.Controls.AddRange(new Control[] { titleLabel, invoiceNumberLabel, dateLabel });
            Controls.Add(_headerPanel);
        }

        private void CreateCustomerSection(int yPosition)
        {
            _customerPanel = ModernDesign.CreateModernPanel(ModernDesign.Colors.Surface);
            _customerPanel.Size = new Size(Width - 40, 80);
            _customerPanel.Location = new Point(20, yPosition);
            _customerPanel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;

            var customerLabel = ModernDesign.CreateModernLabel(
                "👤 العميل:",
                ModernDesign.Fonts.BodyBold,
                ModernDesign.Colors.TextPrimary
            );
            customerLabel.Size = new Size(80, 30);
            customerLabel.Location = new Point(_customerPanel.Width - 100, 20);

            _customerCombo = new ComboBox
            {
                Font = ModernDesign.Fonts.Body,
                Size = new Size(300, ModernDesign.Sizes.InputHeight),
                Location = new Point(_customerPanel.Width - 420, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };

            var addCustomerButton = ModernDesign.CreateModernButton(
                "➕ إضافة عميل",
                ModernDesign.Colors.Secondary,
                Color.White,
                (s, e) => AddNewCustomer()
            );
            addCustomerButton.Size = new Size(120, ModernDesign.Sizes.ButtonHeight);
            addCustomerButton.Location = new Point(20, 20);

            _customerPanel.Controls.AddRange(new Control[] { customerLabel, _customerCombo, addCustomerButton });
            Controls.Add(_customerPanel);
        }

        private void CreateItemsSection(int yPosition)
        {
            _itemsPanel = ModernDesign.CreateModernPanel(ModernDesign.Colors.Surface);
            _itemsPanel.Size = new Size(Width - 40, 280);
            _itemsPanel.Location = new Point(20, yPosition);
            _itemsPanel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;

            var itemsLabel = ModernDesign.CreateModernLabel(
                "📦 المنتجات:",
                ModernDesign.Fonts.BodyBold,
                ModernDesign.Colors.TextPrimary
            );
            itemsLabel.Size = new Size(100, 30);
            itemsLabel.Location = new Point(_itemsPanel.Width - 120, 20);

            // حقل الباركود
            _barcodeText = ModernDesign.CreateModernTextBox("امسح الباركود أو أدخل كود المنتج");
            _barcodeText.Size = new Size(300, ModernDesign.Sizes.InputHeight);
            _barcodeText.Location = new Point(_itemsPanel.Width - 440, 25);
            _barcodeText.KeyPress += OnBarcodeKeyPress;

            var addItemButton = ModernDesign.CreateModernButton(
                "➕ إضافة",
                ModernDesign.Colors.Primary,
                Color.White,
                (s, e) => AddItem()
            );
            addItemButton.Size = new Size(100, ModernDesign.Sizes.ButtonHeight);
            addItemButton.Location = new Point(20, 20);

            // جدول المنتجات
            _itemsGrid = new DataGridView
            {
                Size = new Size(_itemsPanel.Width - 40, 200),
                Location = new Point(20, 60),
                BackgroundColor = ModernDesign.Colors.Surface,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                EnableHeadersVisualStyles = false,
                RowHeadersVisible = false,
                AllowUserToAddRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                Font = ModernDesign.Fonts.Body,
                RightToLeft = RightToLeft.Yes,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            SetupItemsGrid();

            _itemsPanel.Controls.AddRange(new Control[] { itemsLabel, _barcodeText, addItemButton, _itemsGrid });
            Controls.Add(_itemsPanel);
        }

        private void SetupItemsGrid()
        {
            _itemsGrid.Columns.Clear();

            var columns = new[]
            {
                new { Name = "Name", Header = "اسم المنتج", Width = 40 },
                new { Name = "Price", Header = "السعر", Width = 15 },
                new { Name = "Quantity", Header = "الكمية", Width = 15 },
                new { Name = "Total", Header = "الإجمالي", Width = 15 },
                new { Name = "Actions", Header = "إجراءات", Width = 15 }
            };

            foreach (var col in columns)
            {
                var column = new DataGridViewTextBoxColumn
                {
                    Name = col.Name,
                    HeaderText = col.Header,
                    FillWeight = col.Width,
                    ReadOnly = col.Name != "Quantity"
                };
                _itemsGrid.Columns.Add(column);
            }

            // تنسيق الرأس
            _itemsGrid.ColumnHeadersDefaultCellStyle.BackColor = ModernDesign.Colors.Primary;
            _itemsGrid.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            _itemsGrid.ColumnHeadersDefaultCellStyle.Font = ModernDesign.Fonts.BodyBold;
            _itemsGrid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _itemsGrid.ColumnHeadersHeight = 40;

            // تنسيق الصفوف
            _itemsGrid.DefaultCellStyle.BackColor = ModernDesign.Colors.Surface;
            _itemsGrid.DefaultCellStyle.ForeColor = ModernDesign.Colors.TextPrimary;
            _itemsGrid.DefaultCellStyle.SelectionBackColor = ModernDesign.Colors.Selected;
            _itemsGrid.DefaultCellStyle.SelectionForeColor = ModernDesign.Colors.TextPrimary;
            _itemsGrid.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _itemsGrid.RowTemplate.Height = 35;

            _itemsGrid.AlternatingRowsDefaultCellStyle.BackColor = ModernDesign.Colors.Hover;
        }

        private void CreateTotalsSection(int yPosition)
        {
            _totalsPanel = ModernDesign.CreateModernPanel(ModernDesign.Colors.Surface);
            _totalsPanel.Size = new Size(400, 120);
            _totalsPanel.Location = new Point(Width - 440, yPosition);
            _totalsPanel.Anchor = AnchorStyles.Top | AnchorStyles.Right;

            var totalsTitle = ModernDesign.CreateModernLabel(
                "💰 الإجماليات:",
                ModernDesign.Fonts.BodyBold,
                ModernDesign.Colors.TextPrimary
            );
            totalsTitle.Size = new Size(100, 25);
            totalsTitle.Location = new Point(_totalsPanel.Width - 120, 15);

            _subtotalLabel = CreateTotalLabel("المجموع الفرعي:", "0.00 ر.س", 45);
            _taxLabel = CreateTotalLabel("الضريبة (15%):", "0.00 ر.س", 70);
            _totalLabel = CreateTotalLabel("الإجمالي النهائي:", "0.00 ر.س", 95, true);

            _totalsPanel.Controls.AddRange(new Control[] { totalsTitle, _subtotalLabel, _taxLabel, _totalLabel });
            Controls.Add(_totalsPanel);
        }

        private Panel CreateTotalLabel(string text, string value, int y, bool isTotal = false)
        {
            var container = new Panel
            {
                Size = new Size(_totalsPanel.Width - 40, 20),
                Location = new Point(20, y),
                BackColor = Color.Transparent
            };

            var titleLabel = ModernDesign.CreateModernLabel(
                text,
                isTotal ? ModernDesign.Fonts.BodyBold : ModernDesign.Fonts.Body,
                isTotal ? ModernDesign.Colors.Primary : ModernDesign.Colors.TextPrimary
            );
            titleLabel.Size = new Size(container.Width / 2, 20);
            titleLabel.Location = new Point(container.Width / 2, 0);

            var valueLabel = ModernDesign.CreateModernLabel(
                value,
                isTotal ? ModernDesign.Fonts.BodyBold : ModernDesign.Fonts.Body,
                isTotal ? ModernDesign.Colors.Primary : ModernDesign.Colors.TextSecondary,
                ContentAlignment.MiddleLeft
            );
            valueLabel.Size = new Size(container.Width / 2, 20);
            valueLabel.Location = new Point(0, 0);

            container.Controls.AddRange(new Control[] { titleLabel, valueLabel });
            return container;
        }

        private void CreateActionsSection(int yPosition)
        {
            _actionsPanel = new Panel
            {
                Size = new Size(Width - 40, 60),
                Location = new Point(20, yPosition),
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            var saveButton = ModernDesign.CreateModernButton(
                "💾 حفظ الفاتورة",
                ModernDesign.Colors.Success,
                Color.White,
                (s, e) => SaveInvoice()
            );
            saveButton.Size = new Size(150, ModernDesign.Sizes.ButtonHeight);
            saveButton.Location = new Point(_actionsPanel.Width - 170, 10);

            var printButton = ModernDesign.CreateModernButton(
                "🖨️ طباعة",
                ModernDesign.Colors.Primary,
                Color.White,
                (s, e) => PrintInvoice()
            );
            printButton.Size = new Size(120, ModernDesign.Sizes.ButtonHeight);
            printButton.Location = new Point(_actionsPanel.Width - 310, 10);

            var clearButton = ModernDesign.CreateModernButton(
                "🗑️ مسح",
                ModernDesign.Colors.Error,
                Color.White,
                (s, e) => ClearInvoice()
            );
            clearButton.Size = new Size(100, ModernDesign.Sizes.ButtonHeight);
            clearButton.Location = new Point(20, 10);

            _actionsPanel.Controls.AddRange(new Control[] { saveButton, printButton, clearButton });
            Controls.Add(_actionsPanel);
        }

        private void LoadSampleData()
        {
            // تحميل عملاء تجريبيين
            _customerCombo.Items.AddRange(new[]
            {
                "عميل نقدي",
                "أحمد محمد علي",
                "فاطمة أحمد",
                "محمد عبدالله",
                "عائشة سالم"
            });
            _customerCombo.SelectedIndex = 0;

            // إضافة منتجات تجريبية
            AddSampleItems();
        }

        private void AddSampleItems()
        {
            var sampleItems = new[]
            {
                new { Name = "شامبو الأطفال", Price = 25.50m, Quantity = 2 },
                new { Name = "معجون أسنان", Price = 12.75m, Quantity = 1 },
                new { Name = "صابون طبيعي", Price = 8.00m, Quantity = 3 }
            };

            foreach (var item in sampleItems)
            {
                var total = item.Price * item.Quantity;
                _itemsGrid.Rows.Add(item.Name, $"{item.Price:F2} ر.س", item.Quantity, $"{total:F2} ر.س", "حذف");
            }

            UpdateTotals();
        }

        #endregion

        #region معالجة الأحداث

        private void OnBarcodeKeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                AddItem();
                e.Handled = true;
            }
        }

        private void AddNewCustomer()
        {
            MessageBox.Show("سيتم فتح نموذج إضافة عميل جديد", "إضافة عميل", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void AddItem()
        {
            var barcode = _barcodeText.Text.Trim();
            if (string.IsNullOrEmpty(barcode) || barcode == "امسح الباركود أو أدخل كود المنتج")
            {
                MessageBox.Show("يرجى إدخال كود المنتج أو مسح الباركود", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // محاكاة إضافة منتج
            _itemsGrid.Rows.Add($"منتج {barcode}", "15.00 ر.س", 1, "15.00 ر.س", "حذف");
            _barcodeText.Clear();
            UpdateTotals();
        }

        private void UpdateTotals()
        {
            decimal subtotal = 0;
            foreach (DataGridViewRow row in _itemsGrid.Rows)
            {
                if (row.Cells["Total"].Value != null)
                {
                    var totalText = row.Cells["Total"].Value.ToString().Replace(" ر.س", "");
                    if (decimal.TryParse(totalText, out decimal total))
                    {
                        subtotal += total;
                    }
                }
            }

            var tax = subtotal * 0.15m;
            var finalTotal = subtotal + tax;

            // تحديث التسميات
            UpdateTotalLabel(_subtotalLabel, $"{subtotal:F2} ر.س");
            UpdateTotalLabel(_taxLabel, $"{tax:F2} ر.س");
            UpdateTotalLabel(_totalLabel, $"{finalTotal:F2} ر.س");
        }

        private void UpdateTotalLabel(Panel container, string value)
        {
            if (container.Controls.Count > 1)
            {
                ((Label)container.Controls[1]).Text = value;
            }
        }

        private void SaveInvoice()
        {
            MessageBox.Show("تم حفظ الفاتورة بنجاح!", "حفظ الفاتورة", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void PrintInvoice()
        {
            MessageBox.Show("تم إرسال الفاتورة للطباعة!", "طباعة الفاتورة", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ClearInvoice()
        {
            var result = MessageBox.Show("هل تريد مسح جميع بيانات الفاتورة؟", "تأكيد المسح", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                _itemsGrid.Rows.Clear();
                _customerCombo.SelectedIndex = 0;
                _barcodeText.Clear();
                UpdateTotals();
            }
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);

            // إعادة ترتيب العناصر عند تغيير الحجم
            if (_headerPanel != null)
            {
                _headerPanel.Width = Width - 40;
            }

            if (_customerPanel != null)
            {
                _customerPanel.Width = Width - 40;
            }

            if (_itemsPanel != null)
            {
                _itemsPanel.Width = Width - 40;
                if (_itemsGrid != null)
                {
                    _itemsGrid.Width = _itemsPanel.Width - 40;
                }
            }

            if (_actionsPanel != null)
            {
                _actionsPanel.Width = Width - 40;
            }
        }

        #endregion
    }
}
