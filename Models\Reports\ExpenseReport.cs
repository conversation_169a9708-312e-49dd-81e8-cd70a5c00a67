using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace AredooPOS.Models.Reports
{
    /// <summary>
    /// نموذج تقرير المصاريف
    /// يحتوي على جميع البيانات المتعلقة بتقارير المصاريف والنفقات
    /// </summary>
    public class ExpenseReport
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم التقرير
        /// </summary>
        public int ReportID { get; set; }

        /// <summary>
        /// نوع التقرير
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ReportType { get; set; }

        /// <summary>
        /// تاريخ بداية التقرير
        /// </summary>
        [Required]
        public DateTime FromDate { get; set; }

        /// <summary>
        /// تاريخ نهاية التقرير
        /// </summary>
        [Required]
        public DateTime ToDate { get; set; }

        /// <summary>
        /// تاريخ إنشاء التقرير
        /// </summary>
        public DateTime GeneratedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// من أنشأ التقرير
        /// </summary>
        [Required]
        [StringLength(50)]
        public string GeneratedBy { get; set; }

        #endregion

        #region الإجماليات العامة

        /// <summary>
        /// إجمالي المصاريف
        /// </summary>
        public decimal TotalExpenses { get; set; }

        /// <summary>
        /// عدد المصاريف
        /// </summary>
        public int ExpenseCount { get; set; }

        /// <summary>
        /// متوسط المصروف
        /// </summary>
        public decimal AverageExpense { get; set; }

        /// <summary>
        /// أعلى مصروف
        /// </summary>
        public decimal HighestExpense { get; set; }

        /// <summary>
        /// أقل مصروف
        /// </summary>
        public decimal LowestExpense { get; set; }

        #endregion

        #region تصنيف المصاريف

        /// <summary>
        /// المصاريف التشغيلية
        /// </summary>
        public decimal OperationalExpenses { get; set; }

        /// <summary>
        /// المصاريف الإدارية
        /// </summary>
        public decimal AdministrativeExpenses { get; set; }

        /// <summary>
        /// مصاريف التسويق
        /// </summary>
        public decimal MarketingExpenses { get; set; }

        /// <summary>
        /// مصاريف الصيانة
        /// </summary>
        public decimal MaintenanceExpenses { get; set; }

        /// <summary>
        /// مصاريف المرافق
        /// </summary>
        public decimal UtilityExpenses { get; set; }

        /// <summary>
        /// مصاريف أخرى
        /// </summary>
        public decimal OtherExpenses { get; set; }

        #endregion

        #region طرق الدفع

        /// <summary>
        /// المصاريف النقدية
        /// </summary>
        public decimal CashExpenses { get; set; }

        /// <summary>
        /// مصاريف البطاقات
        /// </summary>
        public decimal CardExpenses { get; set; }

        /// <summary>
        /// مصاريف التحويل البنكي
        /// </summary>
        public decimal TransferExpenses { get; set; }

        /// <summary>
        /// مصاريف الشيكات
        /// </summary>
        public decimal CheckExpenses { get; set; }

        #endregion

        #region حالة الموافقة

        /// <summary>
        /// المصاريف المعتمدة
        /// </summary>
        public decimal ApprovedExpenses { get; set; }

        /// <summary>
        /// المصاريف المعلقة
        /// </summary>
        public decimal PendingExpenses { get; set; }

        /// <summary>
        /// المصاريف المرفوضة
        /// </summary>
        public decimal RejectedExpenses { get; set; }

        #endregion

        #region قوائم التفاصيل

        /// <summary>
        /// تفاصيل المصاريف حسب الفئة
        /// </summary>
        public List<ExpenseCategoryDetail> CategoryDetails { get; set; } = new List<ExpenseCategoryDetail>();

        /// <summary>
        /// تفاصيل المصاريف حسب المورد
        /// </summary>
        public List<SupplierExpenseDetail> SupplierDetails { get; set; } = new List<SupplierExpenseDetail>();

        /// <summary>
        /// تفاصيل المصاريف حسب المستخدم
        /// </summary>
        public List<UserExpenseDetail> UserDetails { get; set; } = new List<UserExpenseDetail>();

        /// <summary>
        /// تفاصيل المصاريف اليومية
        /// </summary>
        public List<DailyExpenseDetail> DailyDetails { get; set; } = new List<DailyExpenseDetail>();

        /// <summary>
        /// تفاصيل المصاريف الشهرية
        /// </summary>
        public List<MonthlyExpenseDetail> MonthlyDetails { get; set; } = new List<MonthlyExpenseDetail>();

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// فترة التقرير بالأيام
        /// </summary>
        public int ReportPeriodDays => (ToDate - FromDate).Days + 1;

        /// <summary>
        /// متوسط المصاريف اليومية
        /// </summary>
        public decimal DailyAverageExpenses => ReportPeriodDays > 0 ? TotalExpenses / ReportPeriodDays : 0;

        /// <summary>
        /// أكبر فئة مصاريف
        /// </summary>
        public ExpenseCategoryDetail TopExpenseCategory => CategoryDetails?.OrderByDescending(c => c.TotalAmount).FirstOrDefault();

        /// <summary>
        /// أكبر مورد مصاريف
        /// </summary>
        public SupplierExpenseDetail TopExpenseSupplier => SupplierDetails?.OrderByDescending(s => s.TotalAmount).FirstOrDefault();

        /// <summary>
        /// نسبة المصاريف المعتمدة
        /// </summary>
        public decimal ApprovalPercentage => TotalExpenses > 0 ? (ApprovedExpenses / TotalExpenses) * 100 : 0;

        /// <summary>
        /// نسبة المصاريف النقدية
        /// </summary>
        public decimal CashExpensePercentage => TotalExpenses > 0 ? (CashExpenses / TotalExpenses) * 100 : 0;

        #endregion

        #region الدوال المساعدة

        /// <summary>
        /// حساب الإجماليات من التفاصيل
        /// </summary>
        public void CalculateTotals()
        {
            if (CategoryDetails?.Any() == true)
            {
                TotalExpenses = CategoryDetails.Sum(c => c.TotalAmount);
                ExpenseCount = CategoryDetails.Sum(c => c.ExpenseCount);
                AverageExpense = ExpenseCount > 0 ? TotalExpenses / ExpenseCount : 0;
            }

            if (DailyDetails?.Any() == true)
            {
                HighestExpense = DailyDetails.Max(d => d.TotalAmount);
                LowestExpense = DailyDetails.Where(d => d.TotalAmount > 0).Min(d => d.TotalAmount);
            }

            // حساب المصاريف حسب الحالة
            ApprovedExpenses = CategoryDetails?.Sum(c => c.ApprovedAmount) ?? 0;
            PendingExpenses = CategoryDetails?.Sum(c => c.PendingAmount) ?? 0;
            RejectedExpenses = CategoryDetails?.Sum(c => c.RejectedAmount) ?? 0;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            if (FromDate > ToDate)
                return false;

            if (string.IsNullOrWhiteSpace(ReportType))
                return false;

            if (string.IsNullOrWhiteSpace(GeneratedBy))
                return false;

            return true;
        }

        /// <summary>
        /// الحصول على ملخص التقرير
        /// </summary>
        /// <returns>ملخص التقرير</returns>
        public string GetSummary()
        {
            return $"تقرير المصاريف من {FromDate:dd/MM/yyyy} إلى {ToDate:dd/MM/yyyy}\n" +
                   $"إجمالي المصاريف: {TotalExpenses:C}\n" +
                   $"عدد المصاريف: {ExpenseCount}\n" +
                   $"متوسط المصروف: {AverageExpense:C}";
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// تفاصيل مصاريف الفئة
    /// </summary>
    public class ExpenseCategoryDetail
    {
        public int CategoryID { get; set; }
        public string CategoryName { get; set; }
        public string CategoryCode { get; set; }
        public decimal TotalAmount { get; set; }
        public int ExpenseCount { get; set; }
        public decimal AverageAmount { get; set; }
        public decimal ApprovedAmount { get; set; }
        public decimal PendingAmount { get; set; }
        public decimal RejectedAmount { get; set; }
        public decimal Percentage { get; set; }
        public DateTime LastExpenseDate { get; set; }
    }

    /// <summary>
    /// تفاصيل مصاريف المورد
    /// </summary>
    public class SupplierExpenseDetail
    {
        public int SupplierID { get; set; }
        public string SupplierName { get; set; }
        public string SupplierCode { get; set; }
        public decimal TotalAmount { get; set; }
        public int ExpenseCount { get; set; }
        public decimal AverageAmount { get; set; }
        public DateTime LastExpenseDate { get; set; }
        public string PaymentTerms { get; set; }
        public decimal OutstandingAmount { get; set; }
    }

    /// <summary>
    /// تفاصيل مصاريف المستخدم
    /// </summary>
    public class UserExpenseDetail
    {
        public int UserID { get; set; }
        public string UserName { get; set; }
        public string FullName { get; set; }
        public decimal TotalAmount { get; set; }
        public int ExpenseCount { get; set; }
        public decimal AverageAmount { get; set; }
        public decimal ApprovedAmount { get; set; }
        public decimal PendingAmount { get; set; }
        public decimal RejectedAmount { get; set; }
        public DateTime LastExpenseDate { get; set; }
    }

    /// <summary>
    /// تفاصيل المصاريف اليومية
    /// </summary>
    public class DailyExpenseDetail
    {
        public DateTime ExpenseDate { get; set; }
        public decimal TotalAmount { get; set; }
        public int ExpenseCount { get; set; }
        public decimal AverageAmount { get; set; }
        public decimal ApprovedAmount { get; set; }
        public decimal PendingAmount { get; set; }
        public string DayName => ExpenseDate.ToString("dddd");
        public List<ExpenseCategoryDetail> Categories { get; set; } = new List<ExpenseCategoryDetail>();
    }

    /// <summary>
    /// تفاصيل المصاريف الشهرية
    /// </summary>
    public class MonthlyExpenseDetail
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; }
        public decimal TotalAmount { get; set; }
        public int ExpenseCount { get; set; }
        public decimal AverageAmount { get; set; }
        public decimal DailyAverage { get; set; }
        public int WorkingDays { get; set; }
        public decimal GrowthPercentage { get; set; }
    }

    #endregion

    #region التعدادات

    /// <summary>
    /// أنواع تقارير المصاريف
    /// </summary>
    public static class ExpenseReportTypes
    {
        public const string Daily = "Daily";
        public const string Weekly = "Weekly";
        public const string Monthly = "Monthly";
        public const string Yearly = "Yearly";
        public const string ByCategory = "ByCategory";
        public const string BySupplier = "BySupplier";
        public const string ByUser = "ByUser";
        public const string ByStatus = "ByStatus";
        public const string ByPaymentMethod = "ByPaymentMethod";
    }

    /// <summary>
    /// فئات المصاريف
    /// </summary>
    public static class ExpenseCategories
    {
        public const string Operational = "Operational";
        public const string Administrative = "Administrative";
        public const string Marketing = "Marketing";
        public const string Maintenance = "Maintenance";
        public const string Utilities = "Utilities";
        public const string Travel = "Travel";
        public const string Office = "Office";
        public const string Other = "Other";
    }

    /// <summary>
    /// حالات المصاريف
    /// </summary>
    public static class ExpenseStatus
    {
        public const string Pending = "Pending";
        public const string Approved = "Approved";
        public const string Rejected = "Rejected";
        public const string Paid = "Paid";
        public const string Cancelled = "Cancelled";
    }

    #endregion
}
