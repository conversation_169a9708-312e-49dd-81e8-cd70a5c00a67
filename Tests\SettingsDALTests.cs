using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using AredooPOS.DAL;
using AredooPOS.Models.Settings;
using Microsoft.Extensions.Logging;
using Moq;

namespace AredooPOS.Tests
{
    /// <summary>
    /// اختبارات وحدة طبقة الوصول للبيانات للإعدادات
    /// </summary>
    [TestClass]
    public class SettingsDALTests
    {
        #region المتغيرات والإعداد

        private SettingsDAL _settingsDAL;
        private Mock<ILogger<SettingsDAL>> _mockLogger;
        private string _testConnectionString;

        [TestInitialize]
        public void Setup()
        {
            _mockLogger = new Mock<ILogger<SettingsDAL>>();
            _testConnectionString = "Server=(localdb)\\MSSQLLocalDB;Database=AredooPOS_Test;Integrated Security=true;";
            _settingsDAL = new SettingsDAL(_testConnectionString, _mockLogger.Object);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _settingsDAL?.Dispose();
        }

        #endregion

        #region اختبارات الإعدادات العامة

        [TestMethod]
        public void GetSetting_ValidSettingName_ReturnsCorrectSetting()
        {
            // Arrange
            var settingName = "System.ApplicationName";

            // Act
            var result = _settingsDAL.GetSetting(settingName);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(settingName, result.SettingName);
        }

        [TestMethod]
        public void GetSetting_InvalidSettingName_ReturnsNull()
        {
            // Arrange
            var settingName = "NonExistent.Setting";

            // Act
            var result = _settingsDAL.GetSetting(settingName);

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public void SaveSetting_ValidSetting_ReturnsTrue()
        {
            // Arrange
            var setting = new SystemSettings
            {
                SettingName = "Test.Setting",
                SettingValue = "Test Value",
                Category = "Test",
                DataType = "String",
                IsRequired = false,
                IsEditable = true,
                UpdatedBy = "TestUser"
            };

            // Act
            var result = _settingsDAL.SaveSetting(setting);

            // Assert
            Assert.IsTrue(result);

            // Verify the setting was saved
            var savedSetting = _settingsDAL.GetSetting(setting.SettingName);
            Assert.IsNotNull(savedSetting);
            Assert.AreEqual(setting.SettingValue, savedSetting.SettingValue);
        }

        [TestMethod]
        public void SaveSettingValue_ValidData_ReturnsTrue()
        {
            // Arrange
            var settingName = "Test.Value.Setting";
            var settingValue = "Updated Test Value";
            var updatedBy = "TestUser";

            // Act
            var result = _settingsDAL.SaveSettingValue(settingName, settingValue, updatedBy);

            // Assert
            Assert.IsTrue(result);

            // Verify the value was updated
            var setting = _settingsDAL.GetSetting(settingName);
            Assert.IsNotNull(setting);
            Assert.AreEqual(settingValue, setting.SettingValue);
            Assert.AreEqual(updatedBy, setting.UpdatedBy);
        }

        [TestMethod]
        public void GetAllSettings_ReturnsNonEmptyList()
        {
            // Act
            var result = _settingsDAL.GetAllSettings();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Count > 0);
        }

        [TestMethod]
        public void GetSettingsByCategory_ValidCategory_ReturnsFilteredList()
        {
            // Arrange
            var category = "General";

            // Act
            var result = _settingsDAL.GetSettingsByCategory(category);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.All(s => s.Category == category));
        }

        [TestMethod]
        public void DeleteSetting_ExistingSetting_ReturnsTrue()
        {
            // Arrange
            var settingName = "Test.Delete.Setting";
            var setting = new SystemSettings
            {
                SettingName = settingName,
                SettingValue = "To Be Deleted",
                Category = "Test",
                DataType = "String",
                IsRequired = false,
                IsEditable = true,
                UpdatedBy = "TestUser"
            };

            // First save the setting
            _settingsDAL.SaveSetting(setting);

            // Act
            var result = _settingsDAL.DeleteSetting(settingName);

            // Assert
            Assert.IsTrue(result);

            // Verify the setting was deleted
            var deletedSetting = _settingsDAL.GetSetting(settingName);
            Assert.IsNull(deletedSetting);
        }

        #endregion

        #region اختبارات إعدادات الضريبة

        [TestMethod]
        public void GetTaxSettings_ReturnsValidTaxSettings()
        {
            // Act
            var result = _settingsDAL.GetTaxSettings();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.TaxSettingID > 0 || result.TaxSettingID == 0); // قد يكون 0 للإعدادات الافتراضية
        }

        [TestMethod]
        public void SaveTaxSettings_ValidSettings_ReturnsTrue()
        {
            // Arrange
            var taxSettings = new TaxSettings
            {
                IsTaxEnabled = true,
                DefaultTaxRate = 15.0m,
                TaxName = "ضريبة القيمة المضافة",
                TaxRegistrationNumber = "*********",
                TaxCalculationMethod = "Inclusive",
                ApplyToAllProducts = true,
                MinimumTaxableAmount = 0,
                TaxDecimalPlaces = 2,
                RoundingMethod = "Round"
            };

            // Act
            var result = _settingsDAL.SaveTaxSettings(taxSettings);

            // Assert
            Assert.IsTrue(result);

            // Verify the settings were saved
            var savedSettings = _settingsDAL.GetTaxSettings();
            Assert.AreEqual(taxSettings.DefaultTaxRate, savedSettings.DefaultTaxRate);
            Assert.AreEqual(taxSettings.TaxName, savedSettings.TaxName);
        }

        [TestMethod]
        public void GetTaxRates_ReturnsListOfTaxRates()
        {
            // Act
            var result = _settingsDAL.GetTaxRates();

            // Assert
            Assert.IsNotNull(result);
            // قد تكون القائمة فارغة في البداية
        }

        [TestMethod]
        public void SaveTaxRate_ValidTaxRate_ReturnsTrue()
        {
            // Arrange
            var taxRate = new TaxRate
            {
                Name = "ضريبة خاصة",
                Rate = 10.0m,
                ProductID = null,
                CategoryID = null,
                IsActive = true
            };

            // Act
            var result = _settingsDAL.SaveTaxRate(taxRate);

            // Assert
            Assert.IsTrue(result);
            Assert.IsTrue(taxRate.TaxRateID > 0);
        }

        #endregion

        #region اختبارات إعدادات العملة

        [TestMethod]
        public void GetCurrencySettings_ReturnsValidCurrencySettings()
        {
            // Act
            var result = _settingsDAL.GetCurrencySettings();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.CurrencySettingID > 0 || result.CurrencySettingID == 0);
        }

        [TestMethod]
        public void SaveCurrencySettings_ValidSettings_ReturnsTrue()
        {
            // Arrange
            var currencySettings = new CurrencySettings
            {
                DefaultCurrencyCode = "SAR",
                DecimalPlaces = 2,
                CurrencySymbol = "ر.س",
                SymbolPosition = "After",
                ThousandsSeparator = ",",
                DecimalSeparator = ".",
                SupportMultipleCurrencies = false,
                AutoUpdateExchangeRates = false,
                ExchangeRateSource = "Manual"
            };

            // Act
            var result = _settingsDAL.SaveCurrencySettings(currencySettings);

            // Assert
            Assert.IsTrue(result);

            // Verify the settings were saved
            var savedSettings = _settingsDAL.GetCurrencySettings();
            Assert.AreEqual(currencySettings.DefaultCurrencyCode, savedSettings.DefaultCurrencyCode);
            Assert.AreEqual(currencySettings.CurrencySymbol, savedSettings.CurrencySymbol);
        }

        [TestMethod]
        public void GetCurrencies_ReturnsListOfCurrencies()
        {
            // Act
            var result = _settingsDAL.GetCurrencies();

            // Assert
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public void SaveCurrency_ValidCurrency_ReturnsTrue()
        {
            // Arrange
            var currency = new Currency
            {
                Code = "USD",
                Name = "US Dollar",
                NameArabic = "دولار أمريكي",
                Symbol = "$",
                DecimalPlaces = 2,
                SymbolPosition = "Before",
                IsActive = true,
                DisplayOrder = 1
            };

            // Act
            var result = _settingsDAL.SaveCurrency(currency);

            // Assert
            Assert.IsTrue(result);
            Assert.IsTrue(currency.CurrencyID > 0);
        }

        #endregion

        #region اختبارات إعدادات المتجر

        [TestMethod]
        public void GetStoreSettings_ReturnsValidStoreSettings()
        {
            // Act
            var result = _settingsDAL.GetStoreSettings();

            // Assert
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public void SaveStoreSettings_ValidSettings_ReturnsTrue()
        {
            // Arrange
            var storeSettings = new StoreSettings
            {
                StoreName = "متجر الاختبار",
                StoreNameEnglish = "Test Store",
                StoreDescription = "متجر للاختبار",
                PrimaryPhone = "0*********",
                Email = "<EMAIL>",
                Address = "عنوان الاختبار",
                City = "الرياض",
                Country = "المملكة العربية السعودية"
            };

            // Act
            var result = _settingsDAL.SaveStoreSettings(storeSettings);

            // Assert
            Assert.IsTrue(result);

            // Verify the settings were saved
            var savedSettings = _settingsDAL.GetStoreSettings();
            Assert.AreEqual(storeSettings.StoreName, savedSettings.StoreName);
            Assert.AreEqual(storeSettings.Email, savedSettings.Email);
        }

        #endregion

        #region اختبارات إعدادات الطباعة

        [TestMethod]
        public void GetPrintSettings_ReturnsValidPrintSettings()
        {
            // Act
            var result = _settingsDAL.GetPrintSettings();

            // Assert
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public void SavePrintSettings_ValidSettings_ReturnsTrue()
        {
            // Arrange
            var printSettings = new PrintSettings
            {
                DefaultPrintSize = "A4",
                AutoPrint = false,
                DefaultCopies = 1,
                PaperWidth = 210,
                PaperHeight = 297,
                FontName = "Arial",
                DefaultFontSize = 12,
                PrintStoreLogo = true,
                PrintStoreInfo = true,
                PrintDateTime = true
            };

            // Act
            var result = _settingsDAL.SavePrintSettings(printSettings);

            // Assert
            Assert.IsTrue(result);

            // Verify the settings were saved
            var savedSettings = _settingsDAL.GetPrintSettings();
            Assert.AreEqual(printSettings.DefaultPrintSize, savedSettings.DefaultPrintSize);
            Assert.AreEqual(printSettings.FontName, savedSettings.FontName);
        }

        #endregion

        #region اختبارات العمليات المساعدة

        [TestMethod]
        public void InitializeDefaultSettings_ReturnsPositiveCount()
        {
            // Arrange
            var updatedBy = "TestUser";

            // Act
            var result = _settingsDAL.InitializeDefaultSettings(updatedBy);

            // Assert
            Assert.IsTrue(result >= 0);
        }

        [TestMethod]
        public void ExportSettings_ReturnsValidJsonString()
        {
            // Act
            var result = _settingsDAL.ExportSettings();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Length > 0);
            Assert.IsTrue(result.StartsWith("{") || result.StartsWith("["));
        }

        [TestMethod]
        public void ImportSettings_ValidJsonData_ReturnsTrue()
        {
            // Arrange
            var exportedData = _settingsDAL.ExportSettings();
            var updatedBy = "TestUser";

            // Act
            var result = _settingsDAL.ImportSettings(exportedData, updatedBy);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void SaveSetting_NullSetting_ThrowsArgumentNullException()
        {
            // Act
            _settingsDAL.SaveSetting(null);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void GetSetting_EmptySettingName_ThrowsArgumentException()
        {
            // Act
            _settingsDAL.GetSetting("");
        }

        #endregion
    }
}
