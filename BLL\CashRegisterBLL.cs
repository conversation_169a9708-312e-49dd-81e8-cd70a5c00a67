using System;
using System.Collections.Generic;
using System.Linq;
using AredooPOS.Models;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.BLL
{
    /// <summary>
    /// طبقة منطق الأعمال للصناديق
    /// تحتوي على جميع العمليات المتعلقة بإدارة الصناديق
    /// </summary>
    public class CashRegisterBLL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly CashRegisterDAL _cashRegisterDAL;
        private readonly CashSessionDAL _cashSessionDAL;
        private readonly ILogger<CashRegisterBLL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة منطق الأعمال للصناديق
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public CashRegisterBLL(string connectionString = null, ILogger<CashRegisterBLL> logger = null)
        {
            _cashRegisterDAL = new CashRegisterDAL(connectionString, null);
            _cashSessionDAL = new CashSessionDAL(connectionString, null);
            _logger = logger;
        }

        #endregion

        #region إدارة الصناديق

        /// <summary>
        /// إضافة صندوق جديد
        /// </summary>
        /// <param name="cashRegister">بيانات الصندوق</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>رقم الصندوق الجديد</returns>
        public int AddCashRegister(CashRegister cashRegister, string currentUser)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateCashRegister(cashRegister, true);

                // التحقق من عدم تكرار رمز الصندوق
                if (_cashRegisterDAL.RegisterCodeExists(cashRegister.RegisterCode))
                {
                    throw new InvalidOperationException($"رمز الصندوق '{cashRegister.RegisterCode}' موجود مسبقاً");
                }

                // تعيين معلومات النظام
                cashRegister.CreatedBy = currentUser;
                cashRegister.CreatedDate = DateTime.Now;
                cashRegister.CurrentBalance = cashRegister.OpeningBalance;

                // إضافة الصندوق
                var registerId = _cashRegisterDAL.AddCashRegister(cashRegister);

                _logger?.LogInformation($"تم إضافة صندوق جديد: {cashRegister.RegisterName} برقم {registerId}");
                return registerId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إضافة الصندوق {cashRegister?.RegisterName}");
                throw;
            }
        }

        /// <summary>
        /// تحديث بيانات الصندوق
        /// </summary>
        /// <param name="cashRegister">بيانات الصندوق المحدثة</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateCashRegister(CashRegister cashRegister, string currentUser)
        {
            try
            {
                // التحقق من وجود الصندوق
                var existingRegister = _cashRegisterDAL.GetCashRegisterById(cashRegister.CashRegisterID);
                if (existingRegister == null)
                {
                    throw new InvalidOperationException("الصندوق غير موجود");
                }

                // التحقق من صحة البيانات
                ValidateCashRegister(cashRegister, false);

                // التحقق من عدم تكرار رمز الصندوق
                if (_cashRegisterDAL.RegisterCodeExists(cashRegister.RegisterCode, cashRegister.CashRegisterID))
                {
                    throw new InvalidOperationException($"رمز الصندوق '{cashRegister.RegisterCode}' موجود مسبقاً");
                }

                // التحقق من عدم وجود جلسة مفتوحة عند تعطيل الصندوق
                if (!cashRegister.IsActive && existingRegister.IsActive)
                {
                    var openSession = _cashSessionDAL.GetOpenSessionForRegister(cashRegister.CashRegisterID);
                    if (openSession != null)
                    {
                        throw new InvalidOperationException("لا يمكن تعطيل الصندوق لوجود جلسة مفتوحة");
                    }
                }

                // تعيين معلومات التعديل
                cashRegister.ModifiedBy = currentUser;
                cashRegister.ModifiedDate = DateTime.Now;
                cashRegister.CreatedBy = existingRegister.CreatedBy;
                cashRegister.CreatedDate = existingRegister.CreatedDate;
                cashRegister.CurrentBalance = existingRegister.CurrentBalance;
                cashRegister.LastSessionID = existingRegister.LastSessionID;
                cashRegister.LastTransactionDate = existingRegister.LastTransactionDate;

                // تحديث الصندوق
                var success = _cashRegisterDAL.UpdateCashRegister(cashRegister);

                if (success)
                {
                    _logger?.LogInformation($"تم تحديث الصندوق: {cashRegister.RegisterName}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث الصندوق {cashRegister?.CashRegisterID}");
                throw;
            }
        }

        /// <summary>
        /// حذف صندوق
        /// </summary>
        /// <param name="cashRegisterID">رقم الصندوق</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public bool DeleteCashRegister(int cashRegisterID, string currentUser)
        {
            try
            {
                var cashRegister = _cashRegisterDAL.GetCashRegisterById(cashRegisterID);
                if (cashRegister == null)
                {
                    throw new InvalidOperationException("الصندوق غير موجود");
                }

                // التحقق من عدم وجود جلسة مفتوحة
                var openSession = _cashSessionDAL.GetOpenSessionForRegister(cashRegisterID);
                if (openSession != null)
                {
                    throw new InvalidOperationException("لا يمكن حذف الصندوق لوجود جلسة مفتوحة");
                }

                // التحقق من إمكانية الحذف (عدم وجود جلسات سابقة)
                var sessions = _cashSessionDAL.GetCashSessions(cashRegisterID);
                if (sessions.Any())
                {
                    throw new InvalidOperationException("لا يمكن حذف الصندوق لوجود جلسات سابقة");
                }

                var success = _cashRegisterDAL.DeleteCashRegister(cashRegisterID, currentUser);

                if (success)
                {
                    _logger?.LogInformation($"تم حذف الصندوق: {cashRegister.RegisterName}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حذف الصندوق {cashRegisterID}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على صندوق بالرقم
        /// </summary>
        /// <param name="cashRegisterID">رقم الصندوق</param>
        /// <returns>بيانات الصندوق</returns>
        public CashRegister GetCashRegisterById(int cashRegisterID)
        {
            try
            {
                return _cashRegisterDAL.GetCashRegisterById(cashRegisterID);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الصندوق {cashRegisterID}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على صندوق بالرمز
        /// </summary>
        /// <param name="registerCode">رمز الصندوق</param>
        /// <returns>بيانات الصندوق</returns>
        public CashRegister GetCashRegisterByCode(string registerCode)
        {
            try
            {
                return _cashRegisterDAL.GetCashRegisterByCode(registerCode);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الصندوق {registerCode}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع الصناديق
        /// </summary>
        /// <param name="includeInactive">تضمين الصناديق غير النشطة</param>
        /// <returns>قائمة الصناديق</returns>
        public List<CashRegister> GetAllCashRegisters(bool includeInactive = false)
        {
            try
            {
                return _cashRegisterDAL.GetAllCashRegisters(includeInactive);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على جميع الصناديق");
                throw;
            }
        }

        /// <summary>
        /// البحث في الصناديق
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="includeInactive">تضمين الصناديق غير النشطة</param>
        /// <returns>قائمة الصناديق المطابقة</returns>
        public List<CashRegister> SearchCashRegisters(string searchTerm, bool includeInactive = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return GetAllCashRegisters(includeInactive);
                }

                return _cashRegisterDAL.SearchCashRegisters(searchTerm, includeInactive);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في البحث في الصناديق");
                throw;
            }
        }

        #endregion

        #region إدارة الرصيد

        /// <summary>
        /// تحديث رصيد الصندوق
        /// </summary>
        /// <param name="cashRegisterID">رقم الصندوق</param>
        /// <param name="newBalance">الرصيد الجديد</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateCashRegisterBalance(int cashRegisterID, decimal newBalance, string currentUser)
        {
            try
            {
                var cashRegister = _cashRegisterDAL.GetCashRegisterById(cashRegisterID);
                if (cashRegister == null)
                {
                    throw new InvalidOperationException("الصندوق غير موجود");
                }

                // التحقق من صحة الرصيد الجديد
                if (newBalance < 0 && !cashRegister.AllowNegativeBalance)
                {
                    throw new InvalidOperationException("الصندوق لا يسمح بالرصيد السالب");
                }

                var success = _cashRegisterDAL.UpdateCashRegisterBalance(cashRegisterID, newBalance, currentUser);

                if (success)
                {
                    _logger?.LogInformation($"تم تحديث رصيد الصندوق {cashRegister.RegisterName} إلى {newBalance:C}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث رصيد الصندوق {cashRegisterID}");
                throw;
            }
        }

        /// <summary>
        /// التحقق من إمكانية إجراء معاملة
        /// </summary>
        /// <param name="cashRegisterID">رقم الصندوق</param>
        /// <param name="amount">مبلغ المعاملة</param>
        /// <param name="isWithdrawal">هل هي عملية سحب</param>
        /// <returns>نتيجة التحقق</returns>
        public TransactionValidationResult ValidateTransaction(int cashRegisterID, decimal amount, bool isWithdrawal = false)
        {
            try
            {
                var cashRegister = _cashRegisterDAL.GetCashRegisterById(cashRegisterID);
                if (cashRegister == null)
                {
                    return new TransactionValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "الصندوق غير موجود"
                    };
                }

                if (!cashRegister.IsActive)
                {
                    return new TransactionValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "الصندوق غير نشط"
                    };
                }

                if (amount <= 0)
                {
                    return new TransactionValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "مبلغ المعاملة يجب أن يكون أكبر من صفر"
                    };
                }

                if (isWithdrawal)
                {
                    var newBalance = cashRegister.CurrentBalance - amount;

                    if (newBalance < 0 && !cashRegister.AllowNegativeBalance)
                    {
                        return new TransactionValidationResult
                        {
                            IsValid = false,
                            ErrorMessage = "الرصيد غير كافي والصندوق لا يسمح بالرصيد السالب"
                        };
                    }

                    if (newBalance < cashRegister.MinCashLimit)
                    {
                        return new TransactionValidationResult
                        {
                            IsValid = false,
                            ErrorMessage = $"المعاملة ستؤدي إلى انخفاض الرصيد تحت الحد الأدنى ({cashRegister.MinCashLimit:C})"
                        };
                    }
                }
                else
                {
                    var newBalance = cashRegister.CurrentBalance + amount;

                    if (cashRegister.HasMaxLimit && newBalance > cashRegister.MaxCashLimit.Value)
                    {
                        return new TransactionValidationResult
                        {
                            IsValid = false,
                            ErrorMessage = $"المعاملة ستؤدي إلى تجاوز الحد الأقصى ({cashRegister.MaxCashLimit:C})"
                        };
                    }
                }

                return new TransactionValidationResult
                {
                    IsValid = true,
                    CashRegister = cashRegister
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في التحقق من صحة المعاملة للصندوق {cashRegisterID}");
                return new TransactionValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "حدث خطأ في التحقق من صحة المعاملة"
                };
            }
        }

        #endregion

        #region التنبيهات والمراقبة

        /// <summary>
        /// الحصول على الصناديق التي تحتاج تنبيه
        /// </summary>
        /// <returns>قائمة الصناديق التي تحتاج تنبيه</returns>
        public List<CashRegister> GetCashRegistersNeedingAlert()
        {
            try
            {
                return _cashRegisterDAL.GetCashRegistersNeedingAlert();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على الصناديق التي تحتاج تنبيه");
                throw;
            }
        }

        /// <summary>
        /// الحصول على ملخص حالة الصناديق
        /// </summary>
        /// <returns>ملخص حالة الصناديق</returns>
        public CashRegisterSummary GetCashRegisterSummary()
        {
            try
            {
                var allRegisters = _cashRegisterDAL.GetAllCashRegisters(true);
                var activeRegisters = allRegisters.Where(r => r.IsActive).ToList();

                return new CashRegisterSummary
                {
                    TotalRegisters = allRegisters.Count,
                    ActiveRegisters = activeRegisters.Count,
                    InactiveRegisters = allRegisters.Count - activeRegisters.Count,
                    TotalBalance = activeRegisters.Sum(r => r.CurrentBalance),
                    RegistersNeedingAlert = activeRegisters.Count(r => r.NeedsAlert),
                    RegistersWithNegativeBalance = activeRegisters.Count(r => r.HasNegativeBalance),
                    RegistersOverMaxLimit = activeRegisters.Count(r => r.IsOverMaxLimit),
                    RegistersBelowMinLimit = activeRegisters.Count(r => r.IsBelowMinLimit)
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على ملخص حالة الصناديق");
                throw;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// التحقق من صحة بيانات الصندوق
        /// </summary>
        /// <param name="cashRegister">بيانات الصندوق</param>
        /// <param name="isNew">هل الصندوق جديد</param>
        private void ValidateCashRegister(CashRegister cashRegister, bool isNew)
        {
            if (cashRegister == null)
                throw new ArgumentNullException(nameof(cashRegister));

            if (string.IsNullOrWhiteSpace(cashRegister.RegisterName))
                throw new ArgumentException("اسم الصندوق مطلوب");

            if (cashRegister.RegisterName.Length < 2)
                throw new ArgumentException("اسم الصندوق يجب أن يكون حرفين على الأقل");

            if (string.IsNullOrWhiteSpace(cashRegister.RegisterCode))
                throw new ArgumentException("رمز الصندوق مطلوب");

            if (cashRegister.RegisterCode.Length < 3)
                throw new ArgumentException("رمز الصندوق يجب أن يكون 3 أحرف على الأقل");

            if (cashRegister.OpeningBalance < 0)
                throw new ArgumentException("الرصيد الافتتاحي لا يمكن أن يكون سالب");

            if (cashRegister.MinCashLimit < 0)
                throw new ArgumentException("الحد الأدنى للنقدية لا يمكن أن يكون سالب");

            if (cashRegister.HasMaxLimit && cashRegister.MaxCashLimit.Value <= cashRegister.MinCashLimit)
                throw new ArgumentException("الحد الأقصى للنقدية يجب أن يكون أكبر من الحد الأدنى");
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// نتيجة التحقق من صحة المعاملة
    /// </summary>
    public class TransactionValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        public CashRegister CashRegister { get; set; }
    }

    /// <summary>
    /// ملخص حالة الصناديق
    /// </summary>
    public class CashRegisterSummary
    {
        public int TotalRegisters { get; set; }
        public int ActiveRegisters { get; set; }
        public int InactiveRegisters { get; set; }
        public decimal TotalBalance { get; set; }
        public int RegistersNeedingAlert { get; set; }
        public int RegistersWithNegativeBalance { get; set; }
        public int RegistersOverMaxLimit { get; set; }
        public int RegistersBelowMinLimit { get; set; }
    }

    #endregion
}
