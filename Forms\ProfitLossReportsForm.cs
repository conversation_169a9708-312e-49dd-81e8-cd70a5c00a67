using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AredooPOS.Models;
using AredooPOS.BLL;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// واجهة تقارير الأرباح والخسائر
    /// توفر تحليل شامل لربحية المنتجات والمبيعات
    /// </summary>
    public partial class ProfitLossReportsForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly ProductBLL _productBLL;
        private readonly StockBLL _stockBLL;
        private readonly CategoryDAL _categoryDAL;
        private readonly ILogger<ProfitLossReportsForm> _logger;

        // ألوان النظام
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SuccessColor = Color.FromArgb(46, 204, 113);
        private readonly Color WarningColor = Color.FromArgb(241, 196, 15);
        private readonly Color DangerColor = Color.FromArgb(231, 76, 60);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);

        // بيانات التقرير
        private List<ProductProfitAnalysis> _profitAnalysisData;
        private ProfitLossSummary _reportSummary;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ واجهة تقارير الأرباح والخسائر
        /// </summary>
        /// <param name="productBLL">طبقة منطق الأعمال للمنتجات</param>
        /// <param name="stockBLL">طبقة منطق الأعمال للمخزون</param>
        /// <param name="logger">مسجل الأحداث</param>
        public ProfitLossReportsForm(ProductBLL productBLL = null, StockBLL stockBLL = null, ILogger<ProfitLossReportsForm> logger = null)
        {
            _productBLL = productBLL ?? new ProductBLL();
            _stockBLL = stockBLL ?? new StockBLL();
            _categoryDAL = new CategoryDAL();
            _logger = logger;

            InitializeComponent();
            InitializeArabicUI();
            LoadCategories();
            SetupEventHandlers();
            LoadDefaultReport();
        }

        /// <summary>
        /// تهيئة الواجهة العربية
        /// </summary>
        private void InitializeArabicUI()
        {
            // إعدادات النموذج الأساسية
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "تقارير الأرباح والخسائر";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = LightGray;
            this.WindowState = FormWindowState.Maximized;

            // تطبيق الألوان والأنماط
            ApplyThemeColors();
            UpdateUITexts();
        }

        /// <summary>
        /// تطبيق ألوان النظام
        /// </summary>
        private void ApplyThemeColors()
        {
            // شريط العنوان
            pnlHeader.BackColor = PrimaryColor;
            lblTitle.ForeColor = Color.White;

            // لوحة الفلاتر
            pnlFilters.BackColor = Color.White;
            
            // أزرار العمليات
            btnGenerateReport.BackColor = PrimaryColor;
            btnGenerateReport.ForeColor = Color.White;
            btnGenerateReport.FlatStyle = FlatStyle.Flat;

            btnExportReport.BackColor = SuccessColor;
            btnExportReport.ForeColor = Color.White;
            btnExportReport.FlatStyle = FlatStyle.Flat;

            btnPrintReport.BackColor = Color.Gray;
            btnPrintReport.ForeColor = Color.White;
            btnPrintReport.FlatStyle = FlatStyle.Flat;

            // شبكة البيانات
            dgvProfitAnalysis.BackgroundColor = Color.White;
            dgvProfitAnalysis.GridColor = LightGray;
            dgvProfitAnalysis.DefaultCellStyle.BackColor = Color.White;
            dgvProfitAnalysis.DefaultCellStyle.ForeColor = Color.Black;
            dgvProfitAnalysis.DefaultCellStyle.SelectionBackColor = PrimaryColor;
            dgvProfitAnalysis.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvProfitAnalysis.ColumnHeadersDefaultCellStyle.BackColor = PrimaryColor;
            dgvProfitAnalysis.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvProfitAnalysis.EnableHeadersVisualStyles = false;

            // لوحة الملخص
            pnlSummary.BackColor = Color.White;
        }

        /// <summary>
        /// تحديث النصوص في الواجهة
        /// </summary>
        private void UpdateUITexts()
        {
            lblTitle.Text = "تقارير الأرباح والخسائر";
            
            // فلاتر التقرير
            grpFilters.Text = "فلاتر التقرير";
            lblFromDate.Text = "من تاريخ:";
            lblToDate.Text = "إلى تاريخ:";
            lblCategory.Text = "الفئة:";
            lblReportType.Text = "نوع التقرير:";
            
            // أزرار العمليات
            btnGenerateReport.Text = "إنشاء التقرير";
            btnExportReport.Text = "تصدير";
            btnPrintReport.Text = "طباعة";

            // أنواع التقارير
            cmbReportType.Items.Clear();
            cmbReportType.Items.AddRange(new string[]
            {
                "تحليل ربحية المنتجات",
                "أكثر المنتجات ربحاً",
                "أقل المنتجات ربحاً",
                "تحليل الفئات",
                "ملخص الأرباح والخسائر"
            });
            cmbReportType.SelectedIndex = 0;

            // ملخص التقرير
            grpSummary.Text = "ملخص التقرير";
            lblTotalRevenue.Text = "إجمالي الإيرادات: 0 ريال";
            lblTotalCost.Text = "إجمالي التكلفة: 0 ريال";
            lblTotalProfit.Text = "إجمالي الربح: 0 ريال";
            lblProfitMargin.Text = "نسبة الربح: 0%";

            // إعداد أعمدة الشبكة
            SetupDataGridColumns();
        }

        /// <summary>
        /// إعداد أعمدة شبكة البيانات
        /// </summary>
        private void SetupDataGridColumns()
        {
            dgvProfitAnalysis.Columns.Clear();
            dgvProfitAnalysis.AutoGenerateColumns = false;

            // عمود كود المنتج
            var colProductCode = new DataGridViewTextBoxColumn
            {
                Name = "ProductCode",
                HeaderText = "كود المنتج",
                DataPropertyName = "ProductCode",
                Width = 100,
                ReadOnly = true
            };
            dgvProfitAnalysis.Columns.Add(colProductCode);

            // عمود اسم المنتج
            var colProductName = new DataGridViewTextBoxColumn
            {
                Name = "ProductName",
                HeaderText = "اسم المنتج",
                DataPropertyName = "ProductName",
                Width = 200,
                ReadOnly = true
            };
            dgvProfitAnalysis.Columns.Add(colProductName);

            // عمود الفئة
            var colCategory = new DataGridViewTextBoxColumn
            {
                Name = "CategoryName",
                HeaderText = "الفئة",
                DataPropertyName = "CategoryName",
                Width = 120,
                ReadOnly = true
            };
            dgvProfitAnalysis.Columns.Add(colCategory);

            // عمود الكمية المباعة
            var colQuantitySold = new DataGridViewTextBoxColumn
            {
                Name = "QuantitySold",
                HeaderText = "الكمية المباعة",
                DataPropertyName = "QuantitySold",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N0" }
            };
            dgvProfitAnalysis.Columns.Add(colQuantitySold);

            // عمود سعر البيع
            var colUnitPrice = new DataGridViewTextBoxColumn
            {
                Name = "UnitPrice",
                HeaderText = "سعر البيع",
                DataPropertyName = "UnitPrice",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            };
            dgvProfitAnalysis.Columns.Add(colUnitPrice);

            // عمود سعر التكلفة
            var colCostPrice = new DataGridViewTextBoxColumn
            {
                Name = "CostPrice",
                HeaderText = "سعر التكلفة",
                DataPropertyName = "CostPrice",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            };
            dgvProfitAnalysis.Columns.Add(colCostPrice);

            // عمود إجمالي الإيرادات
            var colTotalRevenue = new DataGridViewTextBoxColumn
            {
                Name = "TotalRevenue",
                HeaderText = "إجمالي الإيرادات",
                DataPropertyName = "TotalRevenue",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            };
            dgvProfitAnalysis.Columns.Add(colTotalRevenue);

            // عمود إجمالي التكلفة
            var colTotalCost = new DataGridViewTextBoxColumn
            {
                Name = "TotalCost",
                HeaderText = "إجمالي التكلفة",
                DataPropertyName = "TotalCost",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            };
            dgvProfitAnalysis.Columns.Add(colTotalCost);

            // عمود إجمالي الربح
            var colTotalProfit = new DataGridViewTextBoxColumn
            {
                Name = "TotalProfit",
                HeaderText = "إجمالي الربح",
                DataPropertyName = "TotalProfit",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            };
            dgvProfitAnalysis.Columns.Add(colTotalProfit);

            // عمود نسبة الربح
            var colProfitMargin = new DataGridViewTextBoxColumn
            {
                Name = "ProfitMargin",
                HeaderText = "نسبة الربح (%)",
                DataPropertyName = "ProfitMargin",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
            };
            dgvProfitAnalysis.Columns.Add(colProfitMargin);

            // عمود الحالة
            var colStatus = new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                DataPropertyName = "Status",
                Width = 100,
                ReadOnly = true
            };
            dgvProfitAnalysis.Columns.Add(colStatus);
        }

        /// <summary>
        /// تحميل الفئات
        /// </summary>
        private void LoadCategories()
        {
            try
            {
                var categories = _categoryDAL.GetAllCategories(false);
                
                // إضافة خيار "جميع الفئات"
                var allCategoriesItem = new Category { CategoryID = 0, CategoryName = "جميع الفئات" };
                categories.Insert(0, allCategoriesItem);
                
                cmbCategory.DataSource = categories;
                cmbCategory.DisplayMember = "CategoryName";
                cmbCategory.ValueMember = "CategoryID";
                cmbCategory.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل الفئات");
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // أحداث الأزرار
            btnGenerateReport.Click += BtnGenerateReport_Click;
            btnExportReport.Click += BtnExportReport_Click;
            btnPrintReport.Click += BtnPrintReport_Click;

            // أحداث الشبكة
            dgvProfitAnalysis.CellFormatting += DgvProfitAnalysis_CellFormatting;

            // أحداث النموذج
            this.Load += ProfitLossReportsForm_Load;
        }

        /// <summary>
        /// تحميل التقرير الافتراضي
        /// </summary>
        private void LoadDefaultReport()
        {
            // تعيين التواريخ الافتراضية (آخر 30 يوم)
            dtpToDate.Value = DateTime.Now;
            dtpFromDate.Value = DateTime.Now.AddDays(-30);
            
            // إنشاء التقرير الافتراضي
            GenerateReport();
        }

        #endregion

        #region معالجات الأحداث

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void ProfitLossReportsForm_Load(object sender, EventArgs e)
        {
            _logger?.LogInformation("تم تحميل واجهة تقارير الأرباح والخسائر");
        }

        /// <summary>
        /// إنشاء التقرير
        /// </summary>
        private void BtnGenerateReport_Click(object sender, EventArgs e)
        {
            GenerateReport();
        }

        /// <summary>
        /// تصدير التقرير
        /// </summary>
        private void BtnExportReport_Click(object sender, EventArgs e)
        {
            // TODO: تنفيذ تصدير التقرير
            MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private void BtnPrintReport_Click(object sender, EventArgs e)
        {
            // TODO: تنفيذ طباعة التقرير
            MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// تنسيق خلايا الشبكة
        /// </summary>
        private void DgvProfitAnalysis_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvProfitAnalysis.Columns[e.ColumnIndex].Name == "Status")
            {
                var status = e.Value?.ToString();
                switch (status)
                {
                    case "مربح":
                        e.CellStyle.ForeColor = SuccessColor;
                        e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold);
                        break;
                    case "خاسر":
                        e.CellStyle.ForeColor = DangerColor;
                        e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold);
                        break;
                    case "متعادل":
                        e.CellStyle.ForeColor = WarningColor;
                        break;
                }
            }
            else if (dgvProfitAnalysis.Columns[e.ColumnIndex].Name == "TotalProfit")
            {
                if (e.Value != null && decimal.TryParse(e.Value.ToString(), out decimal profit))
                {
                    if (profit > 0)
                        e.CellStyle.ForeColor = SuccessColor;
                    else if (profit < 0)
                        e.CellStyle.ForeColor = DangerColor;
                    else
                        e.CellStyle.ForeColor = WarningColor;
                }
            }
        }

        #endregion

        #region إنشاء التقارير

        /// <summary>
        /// إنشاء التقرير
        /// </summary>
        private void GenerateReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);
                var categoryId = Convert.ToInt32(cmbCategory.SelectedValue);
                var reportType = cmbReportType.SelectedIndex;

                // الحصول على بيانات المنتجات
                var products = categoryId == 0 
                    ? _productBLL.GetAllProducts(false)
                    : _productBLL.SearchProducts("", categoryId, false);

                // تحليل الربحية
                _profitAnalysisData = AnalyzeProductProfitability(products, fromDate, toDate);

                // تطبيق فلتر نوع التقرير
                ApplyReportTypeFilter(reportType);

                // عرض البيانات
                dgvProfitAnalysis.DataSource = _profitAnalysisData;

                // حساب الملخص
                CalculateReportSummary();

                // تحديث واجهة الملخص
                UpdateSummaryUI();

                _logger?.LogInformation($"تم إنشاء تقرير الأرباح والخسائر من {fromDate:yyyy-MM-dd} إلى {toDate:yyyy-MM-dd}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنشاء التقرير");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحليل ربحية المنتجات
        /// </summary>
        /// <param name="products">قائمة المنتجات</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>تحليل الربحية</returns>
        private List<ProductProfitAnalysis> AnalyzeProductProfitability(List<Product> products, DateTime fromDate, DateTime toDate)
        {
            var analysisData = new List<ProductProfitAnalysis>();

            foreach (var product in products)
            {
                // TODO: الحصول على بيانات المبيعات الفعلية من قاعدة البيانات
                // هنا نستخدم بيانات تجريبية للعرض
                var quantitySold = GetQuantitySold(product.ProductID, fromDate, toDate);
                
                if (quantitySold > 0)
                {
                    var analysis = new ProductProfitAnalysis
                    {
                        ProductCode = product.ProductCode,
                        ProductName = product.ProductName,
                        CategoryName = product.CategoryName,
                        QuantitySold = quantitySold,
                        UnitPrice = product.UnitPrice,
                        CostPrice = product.CostPrice,
                        TotalRevenue = quantitySold * product.UnitPrice,
                        TotalCost = quantitySold * product.CostPrice,
                        TotalProfit = (quantitySold * product.UnitPrice) - (quantitySold * product.CostPrice),
                        ProfitMargin = product.CostPrice > 0 ? 
                            ((product.UnitPrice - product.CostPrice) / product.CostPrice) * 100 : 0
                    };

                    // تحديد حالة الربحية
                    if (analysis.TotalProfit > 0)
                        analysis.Status = "مربح";
                    else if (analysis.TotalProfit < 0)
                        analysis.Status = "خاسر";
                    else
                        analysis.Status = "متعادل";

                    analysisData.Add(analysis);
                }
            }

            return analysisData.OrderByDescending(a => a.TotalProfit).ToList();
        }

        /// <summary>
        /// الحصول على الكمية المباعة (بيانات تجريبية)
        /// </summary>
        /// <param name="productId">رقم المنتج</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>الكمية المباعة</returns>
        private decimal GetQuantitySold(int productId, DateTime fromDate, DateTime toDate)
        {
            // TODO: استبدال هذا بالاستعلام الفعلي من قاعدة البيانات
            var random = new Random(productId);
            return random.Next(0, 100);
        }

        /// <summary>
        /// تطبيق فلتر نوع التقرير
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        private void ApplyReportTypeFilter(int reportType)
        {
            switch (reportType)
            {
                case 1: // أكثر المنتجات ربحاً
                    _profitAnalysisData = _profitAnalysisData
                        .Where(a => a.TotalProfit > 0)
                        .OrderByDescending(a => a.TotalProfit)
                        .Take(20)
                        .ToList();
                    break;
                
                case 2: // أقل المنتجات ربحاً
                    _profitAnalysisData = _profitAnalysisData
                        .OrderBy(a => a.TotalProfit)
                        .Take(20)
                        .ToList();
                    break;
                
                case 3: // تحليل الفئات
                    _profitAnalysisData = _profitAnalysisData
                        .GroupBy(a => a.CategoryName)
                        .Select(g => new ProductProfitAnalysis
                        {
                            ProductCode = "",
                            ProductName = $"إجمالي فئة {g.Key}",
                            CategoryName = g.Key,
                            QuantitySold = g.Sum(a => a.QuantitySold),
                            UnitPrice = 0,
                            CostPrice = 0,
                            TotalRevenue = g.Sum(a => a.TotalRevenue),
                            TotalCost = g.Sum(a => a.TotalCost),
                            TotalProfit = g.Sum(a => a.TotalProfit),
                            ProfitMargin = g.Sum(a => a.TotalCost) > 0 ? 
                                (g.Sum(a => a.TotalProfit) / g.Sum(a => a.TotalCost)) * 100 : 0,
                            Status = g.Sum(a => a.TotalProfit) > 0 ? "مربح" : 
                                    g.Sum(a => a.TotalProfit) < 0 ? "خاسر" : "متعادل"
                        })
                        .OrderByDescending(a => a.TotalProfit)
                        .ToList();
                    break;
            }
        }

        /// <summary>
        /// حساب ملخص التقرير
        /// </summary>
        private void CalculateReportSummary()
        {
            if (_profitAnalysisData == null || !_profitAnalysisData.Any())
            {
                _reportSummary = new ProfitLossSummary();
                return;
            }

            _reportSummary = new ProfitLossSummary
            {
                TotalRevenue = _profitAnalysisData.Sum(a => a.TotalRevenue),
                TotalCost = _profitAnalysisData.Sum(a => a.TotalCost),
                TotalProfit = _profitAnalysisData.Sum(a => a.TotalProfit),
                TotalQuantitySold = _profitAnalysisData.Sum(a => a.QuantitySold),
                ProfitableProductsCount = _profitAnalysisData.Count(a => a.TotalProfit > 0),
                LossProductsCount = _profitAnalysisData.Count(a => a.TotalProfit < 0),
                BreakEvenProductsCount = _profitAnalysisData.Count(a => a.TotalProfit == 0)
            };

            _reportSummary.ProfitMargin = _reportSummary.TotalCost > 0 ? 
                (_reportSummary.TotalProfit / _reportSummary.TotalCost) * 100 : 0;
        }

        /// <summary>
        /// تحديث واجهة الملخص
        /// </summary>
        private void UpdateSummaryUI()
        {
            if (_reportSummary == null)
                return;

            lblTotalRevenue.Text = $"إجمالي الإيرادات: {_reportSummary.TotalRevenue:C}";
            lblTotalCost.Text = $"إجمالي التكلفة: {_reportSummary.TotalCost:C}";
            lblTotalProfit.Text = $"إجمالي الربح: {_reportSummary.TotalProfit:C}";
            lblProfitMargin.Text = $"نسبة الربح: {_reportSummary.ProfitMargin:N2}%";

            // تلوين الربح حسب القيمة
            if (_reportSummary.TotalProfit > 0)
                lblTotalProfit.ForeColor = SuccessColor;
            else if (_reportSummary.TotalProfit < 0)
                lblTotalProfit.ForeColor = DangerColor;
            else
                lblTotalProfit.ForeColor = WarningColor;
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// تحليل ربحية المنتج
    /// </summary>
    public class ProductProfitAnalysis
    {
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public string CategoryName { get; set; }
        public decimal QuantitySold { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal CostPrice { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCost { get; set; }
        public decimal TotalProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public string Status { get; set; }
    }

    /// <summary>
    /// ملخص الأرباح والخسائر
    /// </summary>
    public class ProfitLossSummary
    {
        public decimal TotalRevenue { get; set; }
        public decimal TotalCost { get; set; }
        public decimal TotalProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public decimal TotalQuantitySold { get; set; }
        public int ProfitableProductsCount { get; set; }
        public int LossProductsCount { get; set; }
        public int BreakEvenProductsCount { get; set; }
    }

    #endregion
}
