using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AridooPOS.Models;
using AridooPOS.BLL;
using AridooPOS.DAL;

namespace AridooPOS.UI
{
    public partial class InvoiceForm : Form
    {
        private readonly InvoiceBLL _invoiceBLL;
        private readonly ProductDAL _productDAL;
        private readonly CustomerDAL _customerDAL;
        private Invoice _currentInvoice;
        private List<InvoiceDetail> _invoiceDetails;

        public InvoiceForm()
        {
            InitializeComponent();
            InitializeArabicUI();
            InitializeData();
        }

        private void InitializeComponent()
        {
            this.panel1 = new Panel();
            this.lblTitle = new Label();
            this.panel2 = new Panel();
            this.lblInvoiceNumber = new Label();
            this.txtInvoiceNumber = new TextBox();
            this.lblInvoiceDate = new Label();
            this.dtpInvoiceDate = new DateTimePicker();
            this.lblCustomer = new Label();
            this.cmbCustomer = new ComboBox();
            this.btnAddCustomer = new Button();
            this.panel3 = new Panel();
            this.lblProduct = new Label();
            this.txtProductSearch = new TextBox();
            this.btnSearchProduct = new Button();
            this.lblQuantity = new Label();
            this.txtQuantity = new TextBox();
            this.lblUnitPrice = new Label();
            this.txtUnitPrice = new TextBox();
            this.lblDiscount = new Label();
            this.txtDiscount = new TextBox();
            this.btnAddProduct = new Button();
            this.dgvInvoiceDetails = new DataGridView();
            this.panel4 = new Panel();
            this.lblSubTotal = new Label();
            this.txtSubTotal = new TextBox();
            this.lblTotalDiscount = new Label();
            this.txtTotalDiscount = new TextBox();
            this.lblTax = new Label();
            this.txtTax = new TextBox();
            this.lblTotal = new Label();
            this.txtTotal = new TextBox();
            this.panel5 = new Panel();
            this.lblPaymentType = new Label();
            this.cmbPaymentType = new ComboBox();
            this.lblPaidAmount = new Label();
            this.txtPaidAmount = new TextBox();
            this.lblRemaining = new Label();
            this.txtRemaining = new TextBox();
            this.panel6 = new Panel();
            this.btnSave = new Button();
            this.btnPrint = new Button();
            this.btnCancel = new Button();

            this.panel1.SuspendLayout();
            this.panel2.SuspendLayout();
            this.panel3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvInvoiceDetails)).BeginInit();
            this.panel4.SuspendLayout();
            this.panel5.SuspendLayout();
            this.panel6.SuspendLayout();
            this.SuspendLayout();

            // panel1 - Header
            this.panel1.Controls.Add(this.lblTitle);
            this.panel1.Dock = DockStyle.Top;
            this.panel1.Location = new Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new Size(1000, 50);
            this.panel1.TabIndex = 0;
            this.panel1.BackColor = Color.FromArgb(52, 152, 219);

            // lblTitle
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Tahoma", 16F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(450, 15);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(100, 27);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "فاتورة جديدة";

            // panel2 - Invoice Info
            this.panel2.Controls.Add(this.lblInvoiceNumber);
            this.panel2.Controls.Add(this.txtInvoiceNumber);
            this.panel2.Controls.Add(this.lblInvoiceDate);
            this.panel2.Controls.Add(this.dtpInvoiceDate);
            this.panel2.Controls.Add(this.lblCustomer);
            this.panel2.Controls.Add(this.cmbCustomer);
            this.panel2.Controls.Add(this.btnAddCustomer);
            this.panel2.Dock = DockStyle.Top;
            this.panel2.Location = new Point(0, 50);
            this.panel2.Name = "panel2";
            this.panel2.Size = new Size(1000, 80);
            this.panel2.TabIndex = 1;
            this.panel2.BackColor = Color.FromArgb(236, 240, 241);

            // lblInvoiceNumber
            this.lblInvoiceNumber.AutoSize = true;
            this.lblInvoiceNumber.Location = new Point(900, 15);
            this.lblInvoiceNumber.Name = "lblInvoiceNumber";
            this.lblInvoiceNumber.Size = new Size(70, 13);
            this.lblInvoiceNumber.TabIndex = 0;
            this.lblInvoiceNumber.Text = "رقم الفاتورة:";

            // txtInvoiceNumber
            this.txtInvoiceNumber.Location = new Point(750, 12);
            this.txtInvoiceNumber.Name = "txtInvoiceNumber";
            this.txtInvoiceNumber.ReadOnly = true;
            this.txtInvoiceNumber.Size = new Size(140, 20);
            this.txtInvoiceNumber.TabIndex = 1;

            // lblInvoiceDate
            this.lblInvoiceDate.AutoSize = true;
            this.lblInvoiceDate.Location = new Point(650, 15);
            this.lblInvoiceDate.Name = "lblInvoiceDate";
            this.lblInvoiceDate.Size = new Size(70, 13);
            this.lblInvoiceDate.TabIndex = 2;
            this.lblInvoiceDate.Text = "تاريخ الفاتورة:";

            // dtpInvoiceDate
            this.dtpInvoiceDate.Location = new Point(500, 12);
            this.dtpInvoiceDate.Name = "dtpInvoiceDate";
            this.dtpInvoiceDate.Size = new Size(140, 20);
            this.dtpInvoiceDate.TabIndex = 3;

            // lblCustomer
            this.lblCustomer.AutoSize = true;
            this.lblCustomer.Location = new Point(900, 45);
            this.lblCustomer.Name = "lblCustomer";
            this.lblCustomer.Size = new Size(40, 13);
            this.lblCustomer.TabIndex = 4;
            this.lblCustomer.Text = "العميل:";

            // cmbCustomer
            this.cmbCustomer.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbCustomer.Location = new Point(600, 42);
            this.cmbCustomer.Name = "cmbCustomer";
            this.cmbCustomer.Size = new Size(290, 21);
            this.cmbCustomer.TabIndex = 5;

            // btnAddCustomer
            this.btnAddCustomer.Location = new Point(550, 41);
            this.btnAddCustomer.Name = "btnAddCustomer";
            this.btnAddCustomer.Size = new Size(40, 23);
            this.btnAddCustomer.TabIndex = 6;
            this.btnAddCustomer.Text = "...";
            this.btnAddCustomer.UseVisualStyleBackColor = true;

            // panel3 - Product Entry
            this.panel3.Controls.Add(this.lblProduct);
            this.panel3.Controls.Add(this.txtProductSearch);
            this.panel3.Controls.Add(this.btnSearchProduct);
            this.panel3.Controls.Add(this.lblQuantity);
            this.panel3.Controls.Add(this.txtQuantity);
            this.panel3.Controls.Add(this.lblUnitPrice);
            this.panel3.Controls.Add(this.txtUnitPrice);
            this.panel3.Controls.Add(this.lblDiscount);
            this.panel3.Controls.Add(this.txtDiscount);
            this.panel3.Controls.Add(this.btnAddProduct);
            this.panel3.Dock = DockStyle.Top;
            this.panel3.Location = new Point(0, 130);
            this.panel3.Name = "panel3";
            this.panel3.Size = new Size(1000, 60);
            this.panel3.TabIndex = 2;
            this.panel3.BackColor = Color.FromArgb(189, 195, 199);

            // lblProduct
            this.lblProduct.AutoSize = true;
            this.lblProduct.Location = new Point(950, 20);
            this.lblProduct.Name = "lblProduct";
            this.lblProduct.Size = new Size(40, 13);
            this.lblProduct.TabIndex = 0;
            this.lblProduct.Text = "المنتج:";

            // txtProductSearch
            this.txtProductSearch.Location = new Point(750, 17);
            this.txtProductSearch.Name = "txtProductSearch";
            this.txtProductSearch.Size = new Size(190, 20);
            this.txtProductSearch.TabIndex = 1;
            this.txtProductSearch.KeyDown += new KeyEventHandler(this.txtProductSearch_KeyDown);

            // btnSearchProduct
            this.btnSearchProduct.Location = new Point(700, 16);
            this.btnSearchProduct.Name = "btnSearchProduct";
            this.btnSearchProduct.Size = new Size(40, 23);
            this.btnSearchProduct.TabIndex = 2;
            this.btnSearchProduct.Text = "...";
            this.btnSearchProduct.UseVisualStyleBackColor = true;
            this.btnSearchProduct.Click += new EventHandler(this.btnSearchProduct_Click);

            // lblQuantity
            this.lblQuantity.AutoSize = true;
            this.lblQuantity.Location = new Point(650, 20);
            this.lblQuantity.Name = "lblQuantity";
            this.lblQuantity.Size = new Size(40, 13);
            this.lblQuantity.TabIndex = 3;
            this.lblQuantity.Text = "الكمية:";

            // txtQuantity
            this.txtQuantity.Location = new Point(580, 17);
            this.txtQuantity.Name = "txtQuantity";
            this.txtQuantity.Size = new Size(60, 20);
            this.txtQuantity.TabIndex = 4;
            this.txtQuantity.Text = "1";
            this.txtQuantity.TextAlign = HorizontalAlignment.Center;

            // lblUnitPrice
            this.lblUnitPrice.AutoSize = true;
            this.lblUnitPrice.Location = new Point(520, 20);
            this.lblUnitPrice.Name = "lblUnitPrice";
            this.lblUnitPrice.Size = new Size(50, 13);
            this.lblUnitPrice.TabIndex = 5;
            this.lblUnitPrice.Text = "السعر:";

            // txtUnitPrice
            this.txtUnitPrice.Location = new Point(430, 17);
            this.txtUnitPrice.Name = "txtUnitPrice";
            this.txtUnitPrice.Size = new Size(80, 20);
            this.txtUnitPrice.TabIndex = 6;
            this.txtUnitPrice.TextAlign = HorizontalAlignment.Center;

            // lblDiscount
            this.lblDiscount.AutoSize = true;
            this.lblDiscount.Location = new Point(380, 20);
            this.lblDiscount.Name = "lblDiscount";
            this.lblDiscount.Size = new Size(40, 13);
            this.lblDiscount.TabIndex = 7;
            this.lblDiscount.Text = "الخصم:";

            // txtDiscount
            this.txtDiscount.Location = new Point(310, 17);
            this.txtDiscount.Name = "txtDiscount";
            this.txtDiscount.Size = new Size(60, 20);
            this.txtDiscount.TabIndex = 8;
            this.txtDiscount.Text = "0";
            this.txtDiscount.TextAlign = HorizontalAlignment.Center;

            // btnAddProduct
            this.btnAddProduct.BackColor = Color.FromArgb(46, 204, 113);
            this.btnAddProduct.FlatStyle = FlatStyle.Flat;
            this.btnAddProduct.ForeColor = Color.White;
            this.btnAddProduct.Location = new Point(200, 15);
            this.btnAddProduct.Name = "btnAddProduct";
            this.btnAddProduct.Size = new Size(100, 25);
            this.btnAddProduct.TabIndex = 9;
            this.btnAddProduct.Text = "إضافة";
            this.btnAddProduct.UseVisualStyleBackColor = false;
            this.btnAddProduct.Click += new EventHandler(this.btnAddProduct_Click);

            // dgvInvoiceDetails
            this.dgvInvoiceDetails.AllowUserToAddRows = false;
            this.dgvInvoiceDetails.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvInvoiceDetails.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvInvoiceDetails.Dock = DockStyle.Fill;
            this.dgvInvoiceDetails.Location = new Point(0, 190);
            this.dgvInvoiceDetails.Name = "dgvInvoiceDetails";
            this.dgvInvoiceDetails.ReadOnly = true;
            this.dgvInvoiceDetails.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvInvoiceDetails.Size = new Size(1000, 300);
            this.dgvInvoiceDetails.TabIndex = 3;
            this.dgvInvoiceDetails.KeyDown += new KeyEventHandler(this.dgvInvoiceDetails_KeyDown);

            // MainForm
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1000, 700);
            this.Controls.Add(this.dgvInvoiceDetails);
            this.Controls.Add(this.panel3);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel1);
            this.Name = "InvoiceForm";
            this.Text = "فاتورة جديدة - أريدوو";
            this.WindowState = FormWindowState.Maximized;
            this.KeyPreview = true;

            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.panel3.ResumeLayout(false);
            this.panel3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvInvoiceDetails)).EndInit();
            this.ResumeLayout(false);
        }

        private void InitializeArabicUI()
        {
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void InitializeData()
        {
            _invoiceBLL = new InvoiceBLL();
            _productDAL = new ProductDAL();
            _customerDAL = new CustomerDAL();
            _invoiceDetails = new List<InvoiceDetail>();

            InitializeInvoice();
            LoadCustomers();
            SetupDataGridView();
        }

        private void InitializeInvoice()
        {
            _currentInvoice = new Invoice();
            txtInvoiceNumber.Text = "سيتم إنشاؤه تلقائياً";
            dtpInvoiceDate.Value = DateTime.Now;
        }

        private void LoadCustomers()
        {
            try
            {
                var customers = _customerDAL.GetAllActiveCustomers();
                cmbCustomer.DataSource = customers;
                cmbCustomer.DisplayMember = "CustomerName";
                cmbCustomer.ValueMember = "CustomerID";
                
                // تحديد العميل النقدي كافتراضي
                var cashCustomer = customers.FirstOrDefault(c => c.CustomerCode == "CASH001");
                if (cashCustomer != null)
                {
                    cmbCustomer.SelectedValue = cashCustomer.CustomerID;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetupDataGridView()
        {
            dgvInvoiceDetails.Columns.Clear();
            dgvInvoiceDetails.Columns.Add("ProductName", "اسم المنتج");
            dgvInvoiceDetails.Columns.Add("Quantity", "الكمية");
            dgvInvoiceDetails.Columns.Add("UnitPrice", "سعر الوحدة");
            dgvInvoiceDetails.Columns.Add("DiscountAmount", "الخصم");
            dgvInvoiceDetails.Columns.Add("LineTotal", "الإجمالي");

            dgvInvoiceDetails.Columns["Quantity"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvInvoiceDetails.Columns["UnitPrice"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvInvoiceDetails.Columns["DiscountAmount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvInvoiceDetails.Columns["LineTotal"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            dgvInvoiceDetails.Columns["UnitPrice"].DefaultCellStyle.Format = "N2";
            dgvInvoiceDetails.Columns["DiscountAmount"].DefaultCellStyle.Format = "N2";
            dgvInvoiceDetails.Columns["LineTotal"].DefaultCellStyle.Format = "N2";
        }

        private void txtProductSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                SearchAndSelectProduct();
            }
        }

        private void btnSearchProduct_Click(object sender, EventArgs e)
        {
            SearchAndSelectProduct();
        }

        private void SearchAndSelectProduct()
        {
            try
            {
                string searchText = txtProductSearch.Text.Trim();
                if (string.IsNullOrEmpty(searchText))
                {
                    MessageBox.Show("يرجى إدخال اسم المنتج أو الكود أو الباركود", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // البحث بالباركود أولاً
                Product product = _productDAL.GetProductByBarcode(searchText);

                // إذا لم يوجد، البحث بالكود
                if (product == null)
                    product = _productDAL.GetProductByCode(searchText);

                // إذا لم يوجد، البحث بالاسم
                if (product == null)
                {
                    var products = _productDAL.SearchProducts(searchText);
                    if (products.Count == 1)
                    {
                        product = products[0];
                    }
                    else if (products.Count > 1)
                    {
                        // عرض قائمة للاختيار
                        ProductSelectionForm selectionForm = new ProductSelectionForm(products);
                        if (selectionForm.ShowDialog() == DialogResult.OK)
                        {
                            product = selectionForm.SelectedProduct;
                        }
                    }
                }

                if (product != null)
                {
                    SelectProduct(product);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على المنتج", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث عن المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SelectProduct(Product product)
        {
            txtProductSearch.Text = product.ProductName;
            txtUnitPrice.Text = product.UnitPrice.ToString("N2");
            txtQuantity.Focus();
            txtQuantity.SelectAll();
        }

        private void btnAddProduct_Click(object sender, EventArgs e)
        {
            AddProductToInvoice();
        }

        private void AddProductToInvoice()
        {
            try
            {
                // التحقق من البيانات
                if (string.IsNullOrEmpty(txtProductSearch.Text.Trim()))
                {
                    MessageBox.Show("يرجى اختيار منتج", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!decimal.TryParse(txtQuantity.Text, out decimal quantity) || quantity <= 0)
                {
                    MessageBox.Show("يرجى إدخال كمية صحيحة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!decimal.TryParse(txtUnitPrice.Text, out decimal unitPrice) || unitPrice < 0)
                {
                    MessageBox.Show("يرجى إدخال سعر صحيح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!decimal.TryParse(txtDiscount.Text, out decimal discount) || discount < 0)
                {
                    discount = 0;
                }

                // البحث عن المنتج
                var product = _productDAL.GetProductByBarcode(txtProductSearch.Text.Trim()) ??
                             _productDAL.GetProductByCode(txtProductSearch.Text.Trim());

                if (product == null)
                {
                    var products = _productDAL.SearchProducts(txtProductSearch.Text.Trim());
                    if (products.Count > 0)
                        product = products[0];
                }

                if (product == null)
                {
                    MessageBox.Show("المنتج غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // التحقق من توفر الكمية
                if (!product.IsAvailable(quantity))
                {
                    MessageBox.Show($"الكمية المتوفرة: {product.StockQuantity}", "كمية غير كافية",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // إنشاء تفصيل الفاتورة
                var detail = new InvoiceDetail
                {
                    ProductID = product.ProductID,
                    ProductCode = product.ProductCode,
                    ProductName = product.ProductName,
                    Quantity = quantity,
                    UnitPrice = unitPrice,
                    DiscountAmount = discount,
                    TaxRate = product.TaxRate
                };

                detail.CalculateLineTotal();
                _invoiceDetails.Add(detail);

                // تحديث الشبكة
                RefreshDataGridView();

                // مسح الحقول
                ClearProductFields();

                // التركيز على البحث
                txtProductSearch.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshDataGridView()
        {
            dgvInvoiceDetails.Rows.Clear();

            foreach (var detail in _invoiceDetails)
            {
                dgvInvoiceDetails.Rows.Add(
                    detail.ProductName,
                    detail.Quantity,
                    detail.UnitPrice,
                    detail.DiscountAmount,
                    detail.LineTotal
                );
            }

            // حساب الإجماليات
            CalculateTotals();
        }

        private void CalculateTotals()
        {
            _currentInvoice.InvoiceDetails = _invoiceDetails;
            _currentInvoice.CalculateTotal();

            // عرض الإجماليات (سيتم إضافة الحقول في الجزء التالي)
        }

        private void ClearProductFields()
        {
            txtProductSearch.Clear();
            txtQuantity.Text = "1";
            txtUnitPrice.Clear();
            txtDiscount.Text = "0";
        }

        private void dgvInvoiceDetails_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete)
            {
                RemoveSelectedProduct();
            }
        }

        private void RemoveSelectedProduct()
        {
            if (dgvInvoiceDetails.SelectedRows.Count > 0)
            {
                int selectedIndex = dgvInvoiceDetails.SelectedRows[0].Index;
                if (selectedIndex >= 0 && selectedIndex < _invoiceDetails.Count)
                {
                    _invoiceDetails.RemoveAt(selectedIndex);
                    RefreshDataGridView();
                }
            }
        }

        #region Designer Variables
        private Panel panel1;
        private Label lblTitle;
        private Panel panel2;
        private Label lblInvoiceNumber;
        private TextBox txtInvoiceNumber;
        private Label lblInvoiceDate;
        private DateTimePicker dtpInvoiceDate;
        private Label lblCustomer;
        private ComboBox cmbCustomer;
        private Button btnAddCustomer;
        private Panel panel3;
        private Label lblProduct;
        private TextBox txtProductSearch;
        private Button btnSearchProduct;
        private Label lblQuantity;
        private TextBox txtQuantity;
        private Label lblUnitPrice;
        private TextBox txtUnitPrice;
        private Label lblDiscount;
        private TextBox txtDiscount;
        private Button btnAddProduct;
        private DataGridView dgvInvoiceDetails;
        private Panel panel4;
        private Label lblSubTotal;
        private TextBox txtSubTotal;
        private Label lblTotalDiscount;
        private TextBox txtTotalDiscount;
        private Label lblTax;
        private TextBox txtTax;
        private Label lblTotal;
        private TextBox txtTotal;
        private Panel panel5;
        private Label lblPaymentType;
        private ComboBox cmbPaymentType;
        private Label lblPaidAmount;
        private TextBox txtPaidAmount;
        private Label lblRemaining;
        private TextBox txtRemaining;
        private Panel panel6;
        private Button btnSave;
        private Button btnPrint;
        private Button btnCancel;
        #endregion
    }
}
