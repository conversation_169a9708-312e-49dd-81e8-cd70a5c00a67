-- =============================================
-- إجراء مخزن: sp_GetDebtReport
-- الوصف: الحصول على تقرير الديون والذمم المدينة
-- المؤلف: نظام أريدو POS
-- تاريخ الإنشاء: 2025-01-11
-- =============================================

CREATE PROCEDURE [dbo].[sp_GetDebtReport]
    @FromDate DATE,
    @ToDate DATE,
    @ReportType NVARCHAR(50) = 'Outstanding',
    @CustomerID INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(4000);
    
    BEGIN TRY
        -- الإجماليات العامة
        SELECT 
            ISNULL(SUM(i.TotalAmount), 0) AS TotalDebt,
            ISNULL(SUM(ISNULL(dp.TotalPaid, 0)), 0) AS TotalPaidAmount,
            ISNULL(SUM(i.TotalAmount - ISNULL(dp.TotalPaid, 0)), 0) AS TotalOutstandingAmount,
            COUNT(DISTINCT CASE WHEN i.TotalAmount - ISNULL(dp.TotalPaid, 0) > 0 THEN i.CustomerID END) AS DebtorCustomersCount,
            COUNT(CASE WHEN i.TotalAmount - ISNULL(dp.TotalPaid, 0) > 0 THEN 1 END) AS OutstandingInvoicesCount,
            
            -- تصنيف الديون حسب العمر
            ISNULL(SUM(CASE WHEN DATEDIFF(DAY, i.DueDate, GETDATE()) <= 30 AND i.TotalAmount - ISNULL(dp.TotalPaid, 0) > 0 THEN i.TotalAmount - ISNULL(dp.TotalPaid, 0) ELSE 0 END), 0) AS CurrentDebt,
            ISNULL(SUM(CASE WHEN DATEDIFF(DAY, i.DueDate, GETDATE()) BETWEEN 31 AND 60 AND i.TotalAmount - ISNULL(dp.TotalPaid, 0) > 0 THEN i.TotalAmount - ISNULL(dp.TotalPaid, 0) ELSE 0 END), 0) AS Debt30To60Days,
            ISNULL(SUM(CASE WHEN DATEDIFF(DAY, i.DueDate, GETDATE()) BETWEEN 61 AND 90 AND i.TotalAmount - ISNULL(dp.TotalPaid, 0) > 0 THEN i.TotalAmount - ISNULL(dp.TotalPaid, 0) ELSE 0 END), 0) AS Debt61To90Days,
            ISNULL(SUM(CASE WHEN DATEDIFF(DAY, i.DueDate, GETDATE()) BETWEEN 91 AND 120 AND i.TotalAmount - ISNULL(dp.TotalPaid, 0) > 0 THEN i.TotalAmount - ISNULL(dp.TotalPaid, 0) ELSE 0 END), 0) AS Debt91To120Days,
            ISNULL(SUM(CASE WHEN DATEDIFF(DAY, i.DueDate, GETDATE()) > 120 AND i.TotalAmount - ISNULL(dp.TotalPaid, 0) > 0 THEN i.TotalAmount - ISNULL(dp.TotalPaid, 0) ELSE 0 END), 0) AS DebtOver120Days
            
        FROM Invoices i
        LEFT JOIN (
            SELECT 
                InvoiceID,
                SUM(Amount) AS TotalPaid
            FROM DebtPayments
            GROUP BY InvoiceID
        ) dp ON i.InvoiceID = dp.InvoiceID
        WHERE i.InvoiceDate BETWEEN @FromDate AND @ToDate
            AND i.PaymentMethod = 'Credit'
            AND i.IsVoided = 0
            AND (@CustomerID IS NULL OR i.CustomerID = @CustomerID);
        
        -- تفاصيل الديون حسب العميل
        SELECT 
            c.CustomerID,
            c.CustomerName,
            c.CustomerPhone,
            ISNULL(SUM(i.TotalAmount), 0) AS TotalDebt,
            ISNULL(SUM(ISNULL(dp.TotalPaid, 0)), 0) AS PaidAmount,
            ISNULL(SUM(i.TotalAmount - ISNULL(dp.TotalPaid, 0)), 0) AS OutstandingAmount,
            COUNT(i.InvoiceID) AS InvoiceCount,
            MAX(i.InvoiceDate) AS LastInvoiceDate,
            MAX(dp.LastPaymentDate) AS LastPaymentDate,
            c.CreditStatus,
            c.CreditLimit,
            CASE 
                WHEN SUM(i.TotalAmount - ISNULL(dp.TotalPaid, 0)) > c.CreditLimit THEN 'Exceeded'
                WHEN SUM(i.TotalAmount - ISNULL(dp.TotalPaid, 0)) > c.CreditLimit * 0.8 THEN 'Warning'
                ELSE 'Normal'
            END AS CreditStatusCalculated
        FROM Customers c
        LEFT JOIN Invoices i ON c.CustomerID = i.CustomerID
            AND i.InvoiceDate BETWEEN @FromDate AND @ToDate
            AND i.PaymentMethod = 'Credit'
            AND i.IsVoided = 0
        LEFT JOIN (
            SELECT 
                InvoiceID,
                SUM(Amount) AS TotalPaid,
                MAX(PaymentDate) AS LastPaymentDate
            FROM DebtPayments
            GROUP BY InvoiceID
        ) dp ON i.InvoiceID = dp.InvoiceID
        WHERE (@CustomerID IS NULL OR c.CustomerID = @CustomerID)
        GROUP BY c.CustomerID, c.CustomerName, c.CustomerPhone, c.CreditStatus, c.CreditLimit
        HAVING SUM(i.TotalAmount - ISNULL(dp.TotalPaid, 0)) > 0
        ORDER BY OutstandingAmount DESC;
        
        -- تفاصيل الديون حسب الفاتورة
        SELECT 
            i.InvoiceID,
            i.InvoiceNumber,
            i.CustomerID,
            c.CustomerName,
            i.InvoiceDate,
            i.DueDate,
            i.TotalAmount AS InvoiceAmount,
            ISNULL(dp.TotalPaid, 0) AS PaidAmount,
            i.TotalAmount - ISNULL(dp.TotalPaid, 0) AS OutstandingAmount,
            DATEDIFF(DAY, i.DueDate, GETDATE()) AS DaysOutstanding,
            CASE 
                WHEN i.TotalAmount - ISNULL(dp.TotalPaid, 0) <= 0 THEN 'Paid'
                WHEN DATEDIFF(DAY, i.DueDate, GETDATE()) <= 0 THEN 'Current'
                WHEN DATEDIFF(DAY, i.DueDate, GETDATE()) <= 30 THEN 'Overdue 1-30'
                WHEN DATEDIFF(DAY, i.DueDate, GETDATE()) <= 60 THEN 'Overdue 31-60'
                WHEN DATEDIFF(DAY, i.DueDate, GETDATE()) <= 90 THEN 'Overdue 61-90'
                ELSE 'Overdue 90+'
            END AS Status
        FROM Invoices i
        LEFT JOIN Customers c ON i.CustomerID = c.CustomerID
        LEFT JOIN (
            SELECT 
                InvoiceID,
                SUM(Amount) AS TotalPaid
            FROM DebtPayments
            GROUP BY InvoiceID
        ) dp ON i.InvoiceID = dp.InvoiceID
        WHERE i.InvoiceDate BETWEEN @FromDate AND @ToDate
            AND i.PaymentMethod = 'Credit'
            AND i.IsVoided = 0
            AND (@CustomerID IS NULL OR i.CustomerID = @CustomerID)
            AND i.TotalAmount - ISNULL(dp.TotalPaid, 0) > 0
        ORDER BY DaysOutstanding DESC, OutstandingAmount DESC;
        
        -- تفاصيل المدفوعات
        SELECT 
            dp.PaymentID,
            dp.CustomerID,
            c.CustomerName,
            dp.PaymentDate,
            dp.PaymentAmount,
            dp.PaymentMethod,
            dp.ReferenceNumber,
            dp.Notes
        FROM DebtPayments dp
        LEFT JOIN Customers c ON dp.CustomerID = c.CustomerID
        WHERE dp.PaymentDate BETWEEN @FromDate AND @ToDate
            AND (@CustomerID IS NULL OR dp.CustomerID = @CustomerID)
        ORDER BY dp.PaymentDate DESC;
        
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END

GO

-- =============================================
-- إجراء مخزن: sp_GetInstallmentReport
-- الوصف: الحصول على تقرير الأقساط
-- =============================================

CREATE PROCEDURE [dbo].[sp_GetInstallmentReport]
    @FromDate DATE,
    @ToDate DATE,
    @ReportType NVARCHAR(50) = 'Due',
    @CustomerID INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(4000);
    
    BEGIN TRY
        -- الإجماليات العامة
        SELECT 
            ISNULL(SUM(ins.InstallmentAmount), 0) AS TotalInstallmentValue,
            ISNULL(SUM(CASE WHEN ins.Status = 'Paid' THEN ins.InstallmentAmount ELSE 0 END), 0) AS TotalPaidAmount,
            ISNULL(SUM(CASE WHEN ins.Status != 'Paid' THEN ins.InstallmentAmount ELSE 0 END), 0) AS TotalOutstandingAmount,
            COUNT(ins.InstallmentID) AS TotalInstallments,
            COUNT(CASE WHEN ins.Status = 'Paid' THEN 1 END) AS PaidInstallments,
            COUNT(CASE WHEN ins.Status != 'Paid' THEN 1 END) AS OutstandingInstallments,
            COUNT(CASE WHEN ins.Status != 'Paid' AND ins.DueDate < GETDATE() THEN 1 END) AS OverdueInstallments,
            ISNULL(SUM(CASE WHEN ins.Status != 'Paid' AND ins.DueDate = CAST(GETDATE() AS DATE) THEN ins.InstallmentAmount ELSE 0 END), 0) AS TodayDueAmount,
            ISNULL(SUM(CASE WHEN ins.Status != 'Paid' AND ins.DueDate BETWEEN GETDATE() AND DATEADD(DAY, 7, GETDATE()) THEN ins.InstallmentAmount ELSE 0 END), 0) AS WeekDueAmount,
            ISNULL(SUM(CASE WHEN ins.Status != 'Paid' AND ins.DueDate BETWEEN GETDATE() AND DATEADD(DAY, 30, GETDATE()) THEN ins.InstallmentAmount ELSE 0 END), 0) AS MonthDueAmount,
            ISNULL(SUM(CASE WHEN ins.Status != 'Paid' AND ins.DueDate < GETDATE() THEN ins.InstallmentAmount ELSE 0 END), 0) AS OverdueInstallmentAmount
        FROM Installments ins
        LEFT JOIN InstallmentContracts ic ON ins.ContractID = ic.ContractID
        WHERE ins.DueDate BETWEEN @FromDate AND @ToDate
            AND (@CustomerID IS NULL OR ic.CustomerID = @CustomerID);
        
        -- تفاصيل العملاء
        SELECT 
            c.CustomerID,
            c.CustomerName,
            c.CustomerPhone,
            ISNULL(SUM(ins.InstallmentAmount), 0) AS TotalInstallmentValue,
            ISNULL(SUM(CASE WHEN ins.Status = 'Paid' THEN ins.InstallmentAmount ELSE 0 END), 0) AS PaidAmount,
            ISNULL(SUM(CASE WHEN ins.Status != 'Paid' THEN ins.InstallmentAmount ELSE 0 END), 0) AS OutstandingAmount,
            COUNT(ins.InstallmentID) AS TotalInstallments,
            COUNT(CASE WHEN ins.Status = 'Paid' THEN 1 END) AS PaidInstallments,
            COUNT(CASE WHEN ins.Status != 'Paid' THEN 1 END) AS OutstandingInstallments,
            COUNT(CASE WHEN ins.Status != 'Paid' AND ins.DueDate < GETDATE() THEN 1 END) AS OverdueInstallments,
            MAX(CASE WHEN ins.Status = 'Paid' THEN ins.PaidDate END) AS LastPaymentDate,
            MIN(CASE WHEN ins.Status != 'Paid' THEN ins.DueDate END) AS NextDueDate,
            MIN(CASE WHEN ins.Status != 'Paid' THEN ins.InstallmentAmount END) AS NextDueAmount,
            ISNULL(MAX(CASE WHEN ins.Status != 'Paid' AND ins.DueDate < GETDATE() THEN DATEDIFF(DAY, ins.DueDate, GETDATE()) END), 0) AS DaysOverdue,
            CASE 
                WHEN COUNT(CASE WHEN ins.Status != 'Paid' AND ins.DueDate < GETDATE() THEN 1 END) > 3 THEN 'High'
                WHEN COUNT(CASE WHEN ins.Status != 'Paid' AND ins.DueDate < GETDATE() THEN 1 END) > 1 THEN 'Medium'
                ELSE 'Low'
            END AS RiskLevel
        FROM Customers c
        LEFT JOIN InstallmentContracts ic ON c.CustomerID = ic.CustomerID
        LEFT JOIN Installments ins ON ic.ContractID = ins.ContractID
            AND ins.DueDate BETWEEN @FromDate AND @ToDate
        WHERE (@CustomerID IS NULL OR c.CustomerID = @CustomerID)
        GROUP BY c.CustomerID, c.CustomerName, c.CustomerPhone
        HAVING COUNT(ins.InstallmentID) > 0
        ORDER BY OutstandingAmount DESC;
        
        -- تفاصيل الأقساط
        SELECT 
            ins.InstallmentID,
            ins.ContractID,
            ic.ContractNumber,
            ic.CustomerID,
            c.CustomerName,
            ins.InstallmentNumber,
            ins.DueDate,
            ins.InstallmentAmount,
            ins.Status,
            ins.PaidDate,
            ins.PaidAmount,
            ins.LateFee,
            CASE 
                WHEN ins.Status = 'Paid' THEN 0
                WHEN ins.DueDate >= GETDATE() THEN 0
                ELSE DATEDIFF(DAY, ins.DueDate, GETDATE())
            END AS DaysOverdue,
            CASE 
                WHEN ins.Status = 'Paid' THEN 'Paid'
                WHEN ins.DueDate > GETDATE() THEN 'Future'
                WHEN ins.DueDate = CAST(GETDATE() AS DATE) THEN 'Due Today'
                WHEN ins.DueDate < GETDATE() THEN 'Overdue'
            END AS StatusDescription
        FROM Installments ins
        LEFT JOIN InstallmentContracts ic ON ins.ContractID = ic.ContractID
        LEFT JOIN Customers c ON ic.CustomerID = c.CustomerID
        WHERE ins.DueDate BETWEEN @FromDate AND @ToDate
            AND (@CustomerID IS NULL OR ic.CustomerID = @CustomerID)
        ORDER BY ins.DueDate, c.CustomerName;
        
        -- تفاصيل العقود
        SELECT 
            ic.ContractID,
            ic.ContractNumber,
            ic.CustomerID,
            c.CustomerName,
            ic.ContractDate,
            ic.TotalAmount,
            ic.DownPayment,
            ic.InstallmentAmount,
            ic.NumberOfInstallments,
            ic.InterestRate,
            ic.Status AS ContractStatus,
            COUNT(ins.InstallmentID) AS TotalInstallments,
            COUNT(CASE WHEN ins.Status = 'Paid' THEN 1 END) AS PaidInstallments,
            ISNULL(SUM(CASE WHEN ins.Status = 'Paid' THEN ins.InstallmentAmount ELSE 0 END), 0) AS TotalPaid,
            ISNULL(SUM(CASE WHEN ins.Status != 'Paid' THEN ins.InstallmentAmount ELSE 0 END), 0) AS RemainingAmount
        FROM InstallmentContracts ic
        LEFT JOIN Customers c ON ic.CustomerID = c.CustomerID
        LEFT JOIN Installments ins ON ic.ContractID = ins.ContractID
        WHERE ic.ContractDate BETWEEN @FromDate AND @ToDate
            AND (@CustomerID IS NULL OR ic.CustomerID = @CustomerID)
        GROUP BY ic.ContractID, ic.ContractNumber, ic.CustomerID, c.CustomerName, ic.ContractDate, 
                 ic.TotalAmount, ic.DownPayment, ic.InstallmentAmount, ic.NumberOfInstallments, 
                 ic.InterestRate, ic.Status
        ORDER BY ic.ContractDate DESC;
        
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END

GO

-- =============================================
-- إجراء مخزن: sp_GetCashFlowReport
-- الوصف: الحصول على تقرير حركة الصندوق
-- =============================================

CREATE PROCEDURE [dbo].[sp_GetCashFlowReport]
    @FromDate DATE,
    @ToDate DATE,
    @CashRegisterID INT = NULL,
    @ReportType NVARCHAR(50) = 'Daily'
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(4000);
    
    BEGIN TRY
        -- الإجماليات العامة
        SELECT 
            -- الأرصدة
            ISNULL((SELECT TOP 1 ClosingBalance FROM CashRegisterSessions WHERE SessionDate < @FromDate AND (@CashRegisterID IS NULL OR CashRegisterID = @CashRegisterID) ORDER BY SessionDate DESC), 0) AS OpeningBalance,
            ISNULL((SELECT TOP 1 ClosingBalance FROM CashRegisterSessions WHERE SessionDate <= @ToDate AND (@CashRegisterID IS NULL OR CashRegisterID = @CashRegisterID) ORDER BY SessionDate DESC), 0) AS ClosingBalance,
            
            -- التدفقات الداخلة
            ISNULL(SUM(CASE WHEN ct.TransactionType = 'Sale' AND ct.PaymentMethod = 'Cash' THEN ct.Amount ELSE 0 END), 0) AS CashSales,
            ISNULL(SUM(CASE WHEN ct.TransactionType = 'DebtCollection' THEN ct.Amount ELSE 0 END), 0) AS DebtCollections,
            ISNULL(SUM(CASE WHEN ct.TransactionType = 'InstallmentCollection' THEN ct.Amount ELSE 0 END), 0) AS InstallmentCollections,
            ISNULL(SUM(CASE WHEN ct.TransactionType = 'Deposit' THEN ct.Amount ELSE 0 END), 0) AS CashDeposits,
            ISNULL(SUM(CASE WHEN ct.TransactionType = 'OtherIncome' THEN ct.Amount ELSE 0 END), 0) AS OtherIncome,
            
            -- التدفقات الخارجة
            ISNULL(SUM(CASE WHEN ct.TransactionType = 'Expense' THEN ct.Amount ELSE 0 END), 0) AS CashExpenses,
            ISNULL(SUM(CASE WHEN ct.TransactionType = 'Withdrawal' THEN ct.Amount ELSE 0 END), 0) AS CashWithdrawals,
            ISNULL(SUM(CASE WHEN ct.TransactionType = 'SupplierPayment' THEN ct.Amount ELSE 0 END), 0) AS SupplierPayments,
            ISNULL(SUM(CASE WHEN ct.TransactionType = 'Return' AND ct.PaymentMethod = 'Cash' THEN ct.Amount ELSE 0 END), 0) AS CashReturns,
            ISNULL(SUM(CASE WHEN ct.TransactionType = 'OtherExpense' THEN ct.Amount ELSE 0 END), 0) AS OtherExpenses,
            
            -- تفاصيل طرق الدفع
            ISNULL(SUM(CASE WHEN ct.PaymentMethod = 'Cash' THEN ct.Amount ELSE 0 END), 0) AS CashTransactions,
            ISNULL(SUM(CASE WHEN ct.PaymentMethod = 'Card' THEN ct.Amount ELSE 0 END), 0) AS CardTransactions,
            ISNULL(SUM(CASE WHEN ct.PaymentMethod = 'Transfer' THEN ct.Amount ELSE 0 END), 0) AS TransferTransactions,
            ISNULL(SUM(CASE WHEN ct.PaymentMethod = 'Check' THEN ct.Amount ELSE 0 END), 0) AS CheckTransactions,
            
            -- معلومات الصندوق
            cr.CashRegisterName
            
        FROM CashTransactions ct
        LEFT JOIN CashRegisters cr ON ct.CashRegisterID = cr.CashRegisterID
        WHERE ct.TransactionDate BETWEEN @FromDate AND @ToDate
            AND (@CashRegisterID IS NULL OR ct.CashRegisterID = @CashRegisterID)
            AND ct.IsVoided = 0;
        
        -- تفاصيل المعاملات
        SELECT 
            ct.TransactionID,
            ct.TransactionNumber,
            ct.TransactionDate,
            ct.TransactionType,
            ct.Amount,
            ct.PaymentMethod,
            ct.Description,
            ct.Category,
            u.UserName,
            ct.ReferenceNumber,
            ct.IsVoided
        FROM CashTransactions ct
        LEFT JOIN Users u ON ct.CreatedBy = u.UserID
        WHERE ct.TransactionDate BETWEEN @FromDate AND @ToDate
            AND (@CashRegisterID IS NULL OR ct.CashRegisterID = @CashRegisterID)
        ORDER BY ct.TransactionDate DESC;
        
        -- التفاصيل اليومية
        WITH DateRange AS (
            SELECT @FromDate AS ReportDate
            UNION ALL
            SELECT DATEADD(DAY, 1, ReportDate)
            FROM DateRange
            WHERE ReportDate < @ToDate
        )
        SELECT 
            dr.ReportDate AS FlowDate,
            ISNULL((SELECT TOP 1 OpeningBalance FROM CashRegisterSessions WHERE CAST(SessionDate AS DATE) = dr.ReportDate AND (@CashRegisterID IS NULL OR CashRegisterID = @CashRegisterID)), 0) AS OpeningBalance,
            ISNULL(SUM(CASE WHEN ct.TransactionType IN ('Sale', 'DebtCollection', 'InstallmentCollection', 'Deposit', 'OtherIncome') THEN ct.Amount ELSE 0 END), 0) AS CashInflows,
            ISNULL(SUM(CASE WHEN ct.TransactionType IN ('Expense', 'Withdrawal', 'SupplierPayment', 'Return', 'OtherExpense') THEN ct.Amount ELSE 0 END), 0) AS CashOutflows,
            ISNULL(SUM(CASE WHEN ct.TransactionType IN ('Sale', 'DebtCollection', 'InstallmentCollection', 'Deposit', 'OtherIncome') THEN ct.Amount ELSE 0 END), 0) - 
            ISNULL(SUM(CASE WHEN ct.TransactionType IN ('Expense', 'Withdrawal', 'SupplierPayment', 'Return', 'OtherExpense') THEN ct.Amount ELSE 0 END), 0) AS NetCashFlow,
            ISNULL((SELECT TOP 1 ClosingBalance FROM CashRegisterSessions WHERE CAST(SessionDate AS DATE) = dr.ReportDate AND (@CashRegisterID IS NULL OR CashRegisterID = @CashRegisterID)), 0) AS ClosingBalance,
            COUNT(ct.TransactionID) AS TransactionCount,
            COUNT(DISTINCT crs.SessionID) AS SessionCount
        FROM DateRange dr
        LEFT JOIN CashTransactions ct ON CAST(ct.TransactionDate AS DATE) = dr.ReportDate
            AND (@CashRegisterID IS NULL OR ct.CashRegisterID = @CashRegisterID)
            AND ct.IsVoided = 0
        LEFT JOIN CashRegisterSessions crs ON CAST(crs.SessionDate AS DATE) = dr.ReportDate
            AND (@CashRegisterID IS NULL OR crs.CashRegisterID = @CashRegisterID)
        GROUP BY dr.ReportDate
        ORDER BY dr.ReportDate
        OPTION (MAXRECURSION 366);
        
        -- تفاصيل الجلسات
        SELECT 
            crs.SessionID,
            crs.SessionDate,
            crs.OpenTime,
            crs.CloseTime,
            crs.OpeningBalance,
            crs.ClosingBalance,
            ISNULL(SUM(CASE WHEN ct.TransactionType = 'Sale' THEN ct.Amount ELSE 0 END), 0) AS TotalSales,
            ISNULL(SUM(CASE WHEN ct.TransactionType = 'Expense' THEN ct.Amount ELSE 0 END), 0) AS TotalExpenses,
            crs.ClosingBalance - crs.OpeningBalance AS NetFlow,
            COUNT(ct.TransactionID) AS TransactionCount,
            u.UserName,
            crs.Status
        FROM CashRegisterSessions crs
        LEFT JOIN CashTransactions ct ON crs.SessionID = ct.SessionID
        LEFT JOIN Users u ON crs.UserID = u.UserID
        WHERE crs.SessionDate BETWEEN @FromDate AND @ToDate
            AND (@CashRegisterID IS NULL OR crs.CashRegisterID = @CashRegisterID)
        GROUP BY crs.SessionID, crs.SessionDate, crs.OpenTime, crs.CloseTime, 
                 crs.OpeningBalance, crs.ClosingBalance, u.UserName, crs.Status
        ORDER BY crs.SessionDate DESC;
        
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END

GO
