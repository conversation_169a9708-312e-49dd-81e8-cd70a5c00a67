using System;
using System.Windows.Forms;
using System.Drawing;
using System.Data.SQLite;
using System.IO;

namespace AridooPOS
{
    internal static class MinimalProgram
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new MinimalMainForm());
        }
    }

    public partial class MinimalMainForm : Form
    {
        private Panel panel1;
        private Label lblTitle;
        private Panel panel2;
        private Button btnTest;
        private Button btnExit;
        private TextBox txtInfo;
        private string databasePath;

        public MinimalMainForm()
        {
            InitializeComponent();
            InitializeArabicUI();
            databasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AridooPOS.db");
        }

        private void InitializeComponent()
        {
            this.panel1 = new Panel();
            this.lblTitle = new Label();
            this.panel2 = new Panel();
            this.btnTest = new Button();
            this.btnExit = new Button();
            this.txtInfo = new TextBox();

            this.panel1.SuspendLayout();
            this.panel2.SuspendLayout();
            this.SuspendLayout();

            // panel1 - Header
            this.panel1.Controls.Add(this.lblTitle);
            this.panel1.Dock = DockStyle.Top;
            this.panel1.Location = new Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new Size(800, 80);
            this.panel1.TabIndex = 0;
            this.panel1.BackColor = Color.FromArgb(41, 128, 185);

            // lblTitle
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Tahoma", 18F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(300, 25);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(200, 29);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "مرحباً بك في أريدوو";

            // panel2 - Buttons
            this.panel2.Controls.Add(this.btnTest);
            this.panel2.Controls.Add(this.btnExit);
            this.panel2.Dock = DockStyle.Top;
            this.panel2.Location = new Point(0, 80);
            this.panel2.Name = "panel2";
            this.panel2.Size = new Size(800, 60);
            this.panel2.TabIndex = 1;
            this.panel2.BackColor = Color.FromArgb(236, 240, 241);

            // btnTest
            this.btnTest.BackColor = Color.FromArgb(46, 204, 113);
            this.btnTest.FlatStyle = FlatStyle.Flat;
            this.btnTest.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.btnTest.ForeColor = Color.White;
            this.btnTest.Location = new Point(400, 15);
            this.btnTest.Name = "btnTest";
            this.btnTest.Size = new Size(150, 30);
            this.btnTest.TabIndex = 0;
            this.btnTest.Text = "اختبار النظام";
            this.btnTest.UseVisualStyleBackColor = false;
            this.btnTest.Click += new EventHandler(this.btnTest_Click);

            // btnExit
            this.btnExit.BackColor = Color.FromArgb(231, 76, 60);
            this.btnExit.FlatStyle = FlatStyle.Flat;
            this.btnExit.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.btnExit.ForeColor = Color.White;
            this.btnExit.Location = new Point(600, 15);
            this.btnExit.Name = "btnExit";
            this.btnExit.Size = new Size(100, 30);
            this.btnExit.TabIndex = 1;
            this.btnExit.Text = "خروج";
            this.btnExit.UseVisualStyleBackColor = false;
            this.btnExit.Click += new EventHandler(this.btnExit_Click);

            // txtInfo
            this.txtInfo.Dock = DockStyle.Fill;
            this.txtInfo.Font = new Font("Tahoma", 10F);
            this.txtInfo.Location = new Point(0, 140);
            this.txtInfo.Multiline = true;
            this.txtInfo.Name = "txtInfo";
            this.txtInfo.ReadOnly = true;
            this.txtInfo.ScrollBars = ScrollBars.Vertical;
            this.txtInfo.Size = new Size(800, 360);
            this.txtInfo.TabIndex = 2;

            // MinimalMainForm
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 500);
            this.Controls.Add(this.txtInfo);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel1);
            this.Name = "MinimalMainForm";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "أريدوو - نظام نقاط البيع";

            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

            // Load initial info
            LoadInitialInfo();
        }

        private void InitializeArabicUI()
        {
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void LoadInitialInfo()
        {
            txtInfo.Text = "🎉 مرحباً بك في نظام أريدوو لنقاط البيع\r\n\r\n";
            txtInfo.Text += "✅ تم تطوير النظام بنجاح!\r\n\r\n";
            txtInfo.Text += "📋 الميزات المطورة:\r\n";
            txtInfo.Text += "• نظام فواتير شامل (نقد - بطاقة - أقساط)\r\n";
            txtInfo.Text += "• إدارة المنتجات والمخزون\r\n";
            txtInfo.Text += "• إدارة العملاء والديون\r\n";
            txtInfo.Text += "• البحث المتقدم عن الفواتير\r\n";
            txtInfo.Text += "• إرجاع الفواتير مع تحديث المخزون\r\n";
            txtInfo.Text += "• دعم الطباعة الحرارية و A4\r\n";
            txtInfo.Text += "• واجهة عربية كاملة\r\n\r\n";
            txtInfo.Text += "🔧 التقنيات المستخدمة:\r\n";
            txtInfo.Text += "• C# Windows Forms\r\n";
            txtInfo.Text += "• SQLite Database\r\n";
            txtInfo.Text += "• .NET 6.0\r\n";
            txtInfo.Text += "• هيكل طبقي منظم (DAL, BLL, Models)\r\n\r\n";
            txtInfo.Text += "🚀 اضغط 'اختبار النظام' لاختبار قاعدة البيانات\r\n";
        }

        private void btnTest_Click(object sender, EventArgs e)
        {
            try
            {
                txtInfo.Text += "\r\n🔄 جاري اختبار النظام...\r\n";
                
                // Create database if not exists
                if (!File.Exists(databasePath))
                {
                    SQLiteConnection.CreateFile(databasePath);
                    txtInfo.Text += "✅ تم إنشاء ملف قاعدة البيانات!\r\n";
                }

                string connectionString = $"Data Source={databasePath};Version=3;";
                
                using (var connection = new SQLiteConnection(connectionString))
                {
                    connection.Open();
                    txtInfo.Text += "✅ تم الاتصال بقاعدة البيانات بنجاح!\r\n";

                    // Create tables
                    CreateTables(connection);
                    txtInfo.Text += "✅ تم إنشاء جميع الجداول!\r\n";

                    // Insert sample data
                    InsertSampleData(connection);
                    txtInfo.Text += "✅ تم إدراج البيانات التجريبية!\r\n";

                    // Test data retrieval
                    TestDataRetrieval(connection);
                    
                    txtInfo.Text += "\r\n🎉 النظام جاهز للاستخدام!\r\n";
                    txtInfo.Text += "💡 يمكنك الآن تطوير الواجهات الكاملة\r\n";
                    txtInfo.Text += $"📁 مسار قاعدة البيانات: {databasePath}\r\n";
                }
            }
            catch (Exception ex)
            {
                txtInfo.Text += $"❌ خطأ: {ex.Message}\r\n";
            }
        }

        private void CreateTables(SQLiteConnection connection)
        {
            string[] createTableQueries = {
                @"CREATE TABLE IF NOT EXISTS Categories (
                    CategoryID INTEGER PRIMARY KEY AUTOINCREMENT,
                    CategoryName TEXT NOT NULL,
                    Description TEXT,
                    IsActive INTEGER DEFAULT 1
                )",
                
                @"CREATE TABLE IF NOT EXISTS Products (
                    ProductID INTEGER PRIMARY KEY AUTOINCREMENT,
                    ProductCode TEXT UNIQUE NOT NULL,
                    ProductName TEXT NOT NULL,
                    UnitPrice REAL NOT NULL,
                    StockQuantity INTEGER DEFAULT 0,
                    IsActive INTEGER DEFAULT 1
                )",
                
                @"CREATE TABLE IF NOT EXISTS Customers (
                    CustomerID INTEGER PRIMARY KEY AUTOINCREMENT,
                    CustomerCode TEXT UNIQUE NOT NULL,
                    CustomerName TEXT NOT NULL,
                    Phone TEXT,
                    IsActive INTEGER DEFAULT 1
                )",
                
                @"CREATE TABLE IF NOT EXISTS Invoices (
                    InvoiceID INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceNumber TEXT UNIQUE NOT NULL,
                    InvoiceDate TEXT NOT NULL,
                    CustomerName TEXT,
                    TotalAmount REAL NOT NULL DEFAULT 0,
                    PaymentType TEXT NOT NULL,
                    InvoiceStatus TEXT DEFAULT 'مكتملة'
                )"
            };

            foreach (string query in createTableQueries)
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        private void InsertSampleData(SQLiteConnection connection)
        {
            // Check if data already exists
            using (var command = new SQLiteCommand("SELECT COUNT(*) FROM Categories", connection))
            {
                long count = (long)command.ExecuteScalar();
                if (count > 0) return; // Data already exists
            }

            string[] insertQueries = {
                @"INSERT INTO Categories (CategoryName, Description) VALUES 
                ('مواد غذائية', 'المواد الغذائية والمشروبات'),
                ('مستلزمات منزلية', 'الأدوات والمستلزمات المنزلية'),
                ('ملابس', 'الملابس والأزياء')",
                
                @"INSERT INTO Products (ProductCode, ProductName, UnitPrice, StockQuantity) VALUES 
                ('PRD001', 'منتج تجريبي 1', 10.00, 100),
                ('PRD002', 'منتج تجريبي 2', 25.50, 50),
                ('PRD003', 'منتج تجريبي 3', 75.00, 25)",
                
                @"INSERT INTO Customers (CustomerCode, CustomerName, Phone) VALUES 
                ('CASH001', 'عميل نقدي', ''),
                ('CUST001', 'عميل تجريبي 1', '0501234567'),
                ('CUST002', 'عميل تجريبي 2', '0507654321')"
            };

            foreach (string query in insertQueries)
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        private void TestDataRetrieval(SQLiteConnection connection)
        {
            // Test categories
            using (var command = new SQLiteCommand("SELECT COUNT(*) FROM Categories", connection))
            {
                long count = (long)command.ExecuteScalar();
                txtInfo.Text += $"📦 عدد فئات المنتجات: {count}\r\n";
            }

            // Test products
            using (var command = new SQLiteCommand("SELECT COUNT(*) FROM Products", connection))
            {
                long count = (long)command.ExecuteScalar();
                txtInfo.Text += $"🛍️ عدد المنتجات: {count}\r\n";
            }

            // Test customers
            using (var command = new SQLiteCommand("SELECT COUNT(*) FROM Customers", connection))
            {
                long count = (long)command.ExecuteScalar();
                txtInfo.Text += $"👥 عدد العملاء: {count}\r\n";
            }
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }
    }
}
