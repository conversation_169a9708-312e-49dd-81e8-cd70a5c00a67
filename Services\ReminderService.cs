using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;
using AredooPOS.Models;
using AredooPOS.BLL;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Services
{
    /// <summary>
    /// خدمة التذكيرات التلقائية
    /// تدير إرسال التذكيرات للعملاء بخصوص الديون المستحقة والمتأخرة
    /// </summary>
    public class ReminderService : IDisposable
    {
        #region المتغيرات والخصائص الخاصة

        private readonly DebtBLL _debtBLL;
        private readonly CustomerBLL _customerBLL;
        private readonly SMSService _smsService;
        private readonly EmailService _emailService;
        private readonly ILogger<ReminderService> _logger;

        // مؤقت التذكيرات
        private readonly Timer _reminderTimer;
        private bool _isRunning = false;
        private bool _disposed = false;

        // إعدادات التذكيرات
        public ReminderSettings Settings { get; set; }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ خدمة التذكيرات
        /// </summary>
        /// <param name="debtBLL">طبقة منطق الأعمال للديون</param>
        /// <param name="customerBLL">طبقة منطق الأعمال للعملاء</param>
        /// <param name="smsService">خدمة الرسائل النصية</param>
        /// <param name="emailService">خدمة البريد الإلكتروني</param>
        /// <param name="logger">مسجل الأحداث</param>
        public ReminderService(
            DebtBLL debtBLL,
            CustomerBLL customerBLL,
            SMSService smsService,
            EmailService emailService,
            ILogger<ReminderService> logger = null)
        {
            _debtBLL = debtBLL ?? throw new ArgumentNullException(nameof(debtBLL));
            _customerBLL = customerBLL ?? throw new ArgumentNullException(nameof(customerBLL));
            _smsService = smsService ?? throw new ArgumentNullException(nameof(smsService));
            _emailService = emailService ?? throw new ArgumentNullException(nameof(emailService));
            _logger = logger;

            // تهيئة الإعدادات الافتراضية
            Settings = new ReminderSettings();

            // إعداد المؤقت (كل ساعة)
            _reminderTimer = new Timer(TimeSpan.FromHours(1).TotalMilliseconds);
            _reminderTimer.Elapsed += OnReminderTimerElapsed;
            _reminderTimer.AutoReset = true;

            _logger?.LogInformation("تم تهيئة خدمة التذكيرات التلقائية");
        }

        #endregion

        #region إدارة الخدمة

        /// <summary>
        /// بدء خدمة التذكيرات
        /// </summary>
        public void Start()
        {
            if (_isRunning)
            {
                _logger?.LogWarning("خدمة التذكيرات تعمل بالفعل");
                return;
            }

            try
            {
                _reminderTimer.Start();
                _isRunning = true;

                _logger?.LogInformation("تم بدء خدمة التذكيرات التلقائية");

                // تشغيل فوري للتحقق من التذكيرات
                _ = Task.Run(ProcessReminders);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في بدء خدمة التذكيرات");
                throw;
            }
        }

        /// <summary>
        /// إيقاف خدمة التذكيرات
        /// </summary>
        public void Stop()
        {
            if (!_isRunning)
            {
                _logger?.LogWarning("خدمة التذكيرات متوقفة بالفعل");
                return;
            }

            try
            {
                _reminderTimer.Stop();
                _isRunning = false;

                _logger?.LogInformation("تم إيقاف خدمة التذكيرات التلقائية");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إيقاف خدمة التذكيرات");
                throw;
            }
        }

        /// <summary>
        /// حدث انتهاء مؤقت التذكيرات
        /// </summary>
        private async void OnReminderTimerElapsed(object sender, ElapsedEventArgs e)
        {
            await ProcessReminders();
        }

        #endregion

        #region معالجة التذكيرات

        /// <summary>
        /// معالجة التذكيرات
        /// </summary>
        public async Task ProcessReminders()
        {
            if (!Settings.EnableAutomaticReminders)
            {
                return;
            }

            try
            {
                _logger?.LogInformation("بدء معالجة التذكيرات التلقائية");

                // الحصول على الديون التي تحتاج تذكيرات
                var debtsNeedingReminders = GetDebtsNeedingReminders();

                if (!debtsNeedingReminders.Any())
                {
                    _logger?.LogInformation("لا توجد ديون تحتاج تذكيرات");
                    return;
                }

                var processedCount = 0;
                var successCount = 0;

                foreach (var debt in debtsNeedingReminders)
                {
                    try
                    {
                        var reminderType = DetermineReminderType(debt);
                        var success = await SendReminder(debt, reminderType);

                        if (success)
                        {
                            successCount++;
                            UpdateDebtReminderCount(debt);
                        }

                        processedCount++;

                        // تأخير قصير بين التذكيرات لتجنب الإرهاق
                        await Task.Delay(Settings.DelayBetweenReminders);
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "خطأ في إرسال تذكير للدين {DebtNumber}", debt.DebtNumber);
                    }
                }

                _logger?.LogInformation("تم معالجة {ProcessedCount} تذكير، نجح منها {SuccessCount}",
                    processedCount, successCount);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة التذكيرات");
            }
        }

        /// <summary>
        /// الحصول على الديون التي تحتاج تذكيرات
        /// </summary>
        /// <returns>قائمة الديون</returns>
        private List<Debt> GetDebtsNeedingReminders()
        {
            var criteria = new DebtSearchCriteria
            {
                OnlyOutstanding = true,
                FromDate = DateTime.Now.AddDays(-Settings.MaxReminderAge),
                ToDate = DateTime.Now
            };

            var allDebts = _debtBLL.SearchDebts(criteria);
            var debtsNeedingReminders = new List<Debt>();

            foreach (var debt in allDebts)
            {
                // التحقق من العميل
                var customer = _customerBLL.GetCustomerById(debt.CustomerID);
                if (customer == null || !customer.IsActive || !customer.SendReminders)
                {
                    continue;
                }

                // التحقق من الحاجة للتذكير
                if (ShouldSendReminder(debt))
                {
                    debtsNeedingReminders.Add(debt);
                }
            }

            return debtsNeedingReminders;
        }

        /// <summary>
        /// التحقق من الحاجة لإرسال تذكير
        /// </summary>
        /// <param name="debt">الدين</param>
        /// <returns>true إذا كان يحتاج تذكير</returns>
        private bool ShouldSendReminder(Debt debt)
        {
            // التحقق من الحد الأقصى لعدد التذكيرات
            if (debt.ReminderCount >= Settings.MaxRemindersPerDebt)
            {
                return false;
            }

            // التحقق من الحد الأدنى للمبلغ
            if (debt.RemainingAmount < Settings.MinAmountForReminder)
            {
                return false;
            }

            // تحديد نوع التذكير المطلوب
            var reminderType = DetermineReminderType(debt);

            // التحقق من التوقيت المناسب للتذكير
            return IsTimeForReminder(debt, reminderType);
        }

        /// <summary>
        /// تحديد نوع التذكير
        /// </summary>
        /// <param name="debt">الدين</param>
        /// <returns>نوع التذكير</returns>
        private string DetermineReminderType(Debt debt)
        {
            if (!debt.DueDate.HasValue)
            {
                return ReminderTypes.Friendly;
            }

            var overdueDays = debt.OverdueDays;

            if (overdueDays <= 0)
            {
                // قبل الاستحقاق
                var daysUntilDue = (debt.DueDate.Value - DateTime.Now).Days;
                if (daysUntilDue <= Settings.DaysBeforeDueForReminder)
                {
                    return ReminderTypes.Friendly;
                }
            }
            else if (overdueDays <= Settings.DaysForFirstWarning)
            {
                return ReminderTypes.FirstWarning;
            }
            else if (overdueDays <= Settings.DaysForFinalWarning)
            {
                return ReminderTypes.FinalWarning;
            }
            else
            {
                return ReminderTypes.LegalNotice;
            }

            return null; // لا يحتاج تذكير الآن
        }

        /// <summary>
        /// التحقق من التوقيت المناسب للتذكير
        /// </summary>
        /// <param name="debt">الدين</param>
        /// <param name="reminderType">نوع التذكير</param>
        /// <returns>true إذا كان الوقت مناسب</returns>
        private bool IsTimeForReminder(Debt debt, string reminderType)
        {
            if (string.IsNullOrEmpty(reminderType))
            {
                return false;
            }

            // الحصول على آخر تذكير
            var lastReminder = GetLastReminder(debt.DebtID);

            if (lastReminder == null)
            {
                return true; // أول تذكير
            }

            // التحقق من الفترة بين التذكيرات
            var daysSinceLastReminder = (DateTime.Now - lastReminder.ReminderDate).Days;
            var requiredInterval = GetReminderInterval(reminderType);

            return daysSinceLastReminder >= requiredInterval;
        }

        /// <summary>
        /// الحصول على فترة التذكير المطلوبة
        /// </summary>
        /// <param name="reminderType">نوع التذكير</param>
        /// <returns>عدد الأيام</returns>
        private int GetReminderInterval(string reminderType)
        {
            return reminderType switch
            {
                ReminderTypes.Friendly => Settings.FriendlyReminderInterval,
                ReminderTypes.FirstWarning => Settings.FirstWarningInterval,
                ReminderTypes.FinalWarning => Settings.FinalWarningInterval,
                ReminderTypes.LegalNotice => Settings.LegalNoticeInterval,
                _ => Settings.DefaultReminderInterval
            };
        }

        /// <summary>
        /// الحصول على آخر تذكير للدين
        /// </summary>
        /// <param name="debtId">رقم الدين</param>
        /// <returns>آخر تذكير</returns>
        private DebtReminder GetLastReminder(int debtId)
        {
            // هذه الطريقة تحتاج إلى تنفيذ في DebtReminderDAL
            // سيتم إضافتها لاحقاً
            return null;
        }

        #endregion

        #region إرسال التذكيرات

        /// <summary>
        /// إرسال تذكير
        /// </summary>
        /// <param name="debt">الدين</param>
        /// <param name="reminderType">نوع التذكير</param>
        /// <returns>true إذا تم الإرسال بنجاح</returns>
        private async Task<bool> SendReminder(Debt debt, string reminderType)
        {
            try
            {
                // الحصول على معلومات العميل
                var customer = _customerBLL.GetCustomerById(debt.CustomerID);
                if (customer == null)
                {
                    return false;
                }

                // إنشاء التذكير
                var reminder = CreateReminder(debt, customer, reminderType);

                // تحديد طريقة الإرسال
                var deliveryMethod = DetermineDeliveryMethod(customer, reminderType);
                reminder.DeliveryMethod = deliveryMethod;

                // إرسال التذكير
                var success = await SendReminderByMethod(reminder, customer);

                if (success)
                {
                    // حفظ التذكير في قاعدة البيانات
                    SaveReminder(reminder);

                    _logger?.LogInformation("تم إرسال تذكير {ReminderType} للعميل {CustomerName} بخصوص الدين {DebtNumber}",
                        reminderType, customer.CustomerName, debt.DebtNumber);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إرسال التذكير للدين {DebtNumber}", debt.DebtNumber);
                return false;
            }
        }

        /// <summary>
        /// إنشاء كائن التذكير
        /// </summary>
        /// <param name="debt">الدين</param>
        /// <param name="customer">العميل</param>
        /// <param name="reminderType">نوع التذكير</param>
        /// <returns>كائن التذكير</returns>
        private DebtReminder CreateReminder(Debt debt, Customer customer, string reminderType)
        {
            var reminder = new DebtReminder
            {
                DebtID = debt.DebtID,
                ReminderNumber = GenerateReminderNumber(),
                ReminderDate = DateTime.Now,
                DueDate = debt.DueDate,
                ReminderType = reminderType,
                DebtAmount = debt.OriginalAmount,
                RemainingAmount = debt.RemainingAmount,
                OverdueDays = debt.OverdueDays,
                Subject = GenerateReminderSubject(reminderType, debt),
                Message = GenerateReminderMessage(reminderType, debt, customer),
                ReminderStatus = ReminderStatuses.Pending,
                SentBy = "النظام التلقائي",
                CreatedDate = DateTime.Now
            };

            return reminder;
        }

        /// <summary>
        /// تحديد طريقة الإرسال
        /// </summary>
        /// <param name="customer">العميل</param>
        /// <param name="reminderType">نوع التذكير</param>
        /// <returns>طريقة الإرسال</returns>
        private string DetermineDeliveryMethod(Customer customer, string reminderType)
        {
            // إعطاء أولوية للطريقة المفضلة للعميل
            if (!string.IsNullOrEmpty(customer.PreferredReminderMethod))
            {
                return customer.PreferredReminderMethod;
            }

            // اختيار الطريقة حسب نوع التذكير
            return reminderType switch
            {
                ReminderTypes.Friendly => DeliveryMethods.SMS,
                ReminderTypes.FirstWarning => DeliveryMethods.SMS,
                ReminderTypes.FinalWarning => DeliveryMethods.Call,
                ReminderTypes.LegalNotice => DeliveryMethods.Letter,
                _ => DeliveryMethods.SMS
            };
        }

        /// <summary>
        /// إرسال التذكير حسب الطريقة
        /// </summary>
        /// <param name="reminder">التذكير</param>
        /// <param name="customer">العميل</param>
        /// <returns>true إذا تم الإرسال بنجاح</returns>
        private async Task<bool> SendReminderByMethod(DebtReminder reminder, Customer customer)
        {
            switch (reminder.DeliveryMethod)
            {
                case DeliveryMethods.SMS:
                    return await SendSMSReminder(reminder, customer);

                case DeliveryMethods.Email:
                    return await SendEmailReminder(reminder, customer);

                case DeliveryMethods.WhatsApp:
                    return await SendWhatsAppReminder(reminder, customer);

                case DeliveryMethods.Call:
                    // تسجيل مطلوب اتصال هاتفي
                    reminder.ReminderStatus = ReminderStatuses.Pending;
                    reminder.DeliveryStatusMessage = "مطلوب اتصال هاتفي";
                    return true;

                case DeliveryMethods.Letter:
                    // تسجيل مطلوب خطاب رسمي
                    reminder.ReminderStatus = ReminderStatuses.Pending;
                    reminder.DeliveryStatusMessage = "مطلوب خطاب رسمي";
                    return true;

                default:
                    return false;
            }
        }

        /// <summary>
        /// إرسال تذكير عبر الرسائل النصية
        /// </summary>
        /// <param name="reminder">التذكير</param>
        /// <param name="customer">العميل</param>
        /// <returns>true إذا تم الإرسال بنجاح</returns>
        private async Task<bool> SendSMSReminder(DebtReminder reminder, Customer customer)
        {
            if (string.IsNullOrEmpty(customer.Phone))
            {
                reminder.DeliveryStatusMessage = "رقم الهاتف غير متوفر";
                return false;
            }

            try
            {
                var result = await _smsService.SendSMS(customer.Phone, reminder.Message);

                if (result.Success)
                {
                    reminder.ReminderStatus = ReminderStatuses.Sent;
                    reminder.ExternalMessageID = result.MessageId;
                    reminder.DeliveryCost = result.Cost;
                    reminder.DeliveryAddress = customer.Phone;
                }
                else
                {
                    reminder.ReminderStatus = ReminderStatuses.Failed;
                    reminder.DeliveryStatusMessage = result.ErrorMessage;
                }

                return result.Success;
            }
            catch (Exception ex)
            {
                reminder.ReminderStatus = ReminderStatuses.Failed;
                reminder.DeliveryStatusMessage = ex.Message;
                return false;
            }
        }

        /// <summary>
        /// إرسال تذكير عبر البريد الإلكتروني
        /// </summary>
        /// <param name="reminder">التذكير</param>
        /// <param name="customer">العميل</param>
        /// <returns>true إذا تم الإرسال بنجاح</returns>
        private async Task<bool> SendEmailReminder(DebtReminder reminder, Customer customer)
        {
            if (string.IsNullOrEmpty(customer.Email))
            {
                reminder.DeliveryStatusMessage = "البريد الإلكتروني غير متوفر";
                return false;
            }

            try
            {
                var result = await _emailService.SendEmail(
                    customer.Email,
                    reminder.Subject,
                    reminder.Message);

                if (result.Success)
                {
                    reminder.ReminderStatus = ReminderStatuses.Sent;
                    reminder.ExternalMessageID = result.MessageId;
                    reminder.DeliveryAddress = customer.Email;
                }
                else
                {
                    reminder.ReminderStatus = ReminderStatuses.Failed;
                    reminder.DeliveryStatusMessage = result.ErrorMessage;
                }

                return result.Success;
            }
            catch (Exception ex)
            {
                reminder.ReminderStatus = ReminderStatuses.Failed;
                reminder.DeliveryStatusMessage = ex.Message;
                return false;
            }
        }

        /// <summary>
        /// إرسال تذكير عبر الواتساب
        /// </summary>
        /// <param name="reminder">التذكير</param>
        /// <param name="customer">العميل</param>
        /// <returns>true إذا تم الإرسال بنجاح</returns>
        private async Task<bool> SendWhatsAppReminder(DebtReminder reminder, Customer customer)
        {
            // تنفيذ إرسال الواتساب (يحتاج إلى API خاص)
            // مؤقتاً سيتم التعامل معه كرسالة نصية
            return await SendSMSReminder(reminder, customer);
        }

        #endregion

        #region توليد المحتوى

        /// <summary>
        /// توليد موضوع التذكير
        /// </summary>
        /// <param name="reminderType">نوع التذكير</param>
        /// <param name="debt">الدين</param>
        /// <returns>موضوع التذكير</returns>
        private string GenerateReminderSubject(string reminderType, Debt debt)
        {
            return reminderType switch
            {
                ReminderTypes.Friendly => $"تذكير ودي - استحقاق دين رقم {debt.DebtNumber}",
                ReminderTypes.FirstWarning => $"إنذار أول - تأخر دين رقم {debt.DebtNumber}",
                ReminderTypes.FinalWarning => $"إنذار أخير - دين متأخر رقم {debt.DebtNumber}",
                ReminderTypes.LegalNotice => $"إشعار قانوني - دين رقم {debt.DebtNumber}",
                _ => $"تذكير - دين رقم {debt.DebtNumber}"
            };
        }

        /// <summary>
        /// توليد نص التذكير
        /// </summary>
        /// <param name="reminderType">نوع التذكير</param>
        /// <param name="debt">الدين</param>
        /// <param name="customer">العميل</param>
        /// <returns>نص التذكير</returns>
        private string GenerateReminderMessage(string reminderType, Debt debt, Customer customer)
        {
            var companyName = Settings.CompanyName;
            var customerName = customer.CustomerName;
            var debtNumber = debt.DebtNumber;
            var amount = debt.RemainingAmount;
            var dueDate = debt.DueDate?.ToString("yyyy/MM/dd") ?? "غير محدد";
            var overdueDays = debt.OverdueDays;

            return reminderType switch
            {
                ReminderTypes.Friendly => GenerateFriendlyMessage(companyName, customerName, debtNumber, amount, dueDate),
                ReminderTypes.FirstWarning => GenerateFirstWarningMessage(companyName, customerName, debtNumber, amount, dueDate, overdueDays),
                ReminderTypes.FinalWarning => GenerateFinalWarningMessage(companyName, customerName, debtNumber, amount, dueDate, overdueDays),
                ReminderTypes.LegalNotice => GenerateLegalNoticeMessage(companyName, customerName, debtNumber, amount, dueDate, overdueDays),
                _ => GenerateDefaultMessage(companyName, customerName, debtNumber, amount, dueDate)
            };
        }

        /// <summary>
        /// توليد رسالة تذكير ودية
        /// </summary>
        private string GenerateFriendlyMessage(string companyName, string customerName, string debtNumber, decimal amount, string dueDate)
        {
            return $@"عزيزي/عزيزتي {customerName}،

نود تذكيركم بأن لديكم دين مستحق بمبلغ {amount:C} (رقم الدين: {debtNumber}) بتاريخ استحقاق {dueDate}.

نرجو منكم التكرم بسداد المبلغ في أقرب وقت ممكن.

شكراً لتعاونكم.

{companyName}";
        }

        /// <summary>
        /// توليد رسالة إنذار أول
        /// </summary>
        private string GenerateFirstWarningMessage(string companyName, string customerName, string debtNumber, decimal amount, string dueDate, int overdueDays)
        {
            return $@"عزيزي/عزيزتي {customerName}،

نود إعلامكم بأن الدين رقم {debtNumber} بمبلغ {amount:C} قد تأخر عن موعد الاستحقاق ({dueDate}) بـ {overdueDays} يوم.

نرجو منكم المبادرة بالسداد فوراً لتجنب أي إجراءات إضافية.

للاستفسار يرجى الاتصال بنا.

{companyName}";
        }

        /// <summary>
        /// توليد رسالة إنذار أخير
        /// </summary>
        private string GenerateFinalWarningMessage(string companyName, string customerName, string debtNumber, decimal amount, string dueDate, int overdueDays)
        {
            return $@"عزيزي/عزيزتي {customerName}،

هذا إنذار أخير بخصوص الدين رقم {debtNumber} بمبلغ {amount:C} المتأخر منذ {overdueDays} يوم عن تاريخ الاستحقاق ({dueDate}).

في حالة عدم السداد خلال 7 أيام من تاريخ هذا الإشعار، سنضطر لاتخاذ الإجراءات القانونية اللازمة.

نرجو المبادرة بالسداد فوراً.

{companyName}";
        }

        /// <summary>
        /// توليد رسالة إشعار قانوني
        /// </summary>
        private string GenerateLegalNoticeMessage(string companyName, string customerName, string debtNumber, decimal amount, string dueDate, int overdueDays)
        {
            return $@"السيد/السيدة {customerName}،

بناءً على عدم استجابتكم للإنذارات السابقة، نحيطكم علماً بأن الدين رقم {debtNumber} بمبلغ {amount:C} لا يزال مستحقاً منذ {overdueDays} يوم.

سيتم اتخاذ الإجراءات القانونية المناسبة في حالة عدم السداد خلال 3 أيام من تاريخ هذا الإشعار.

{companyName}
القسم القانوني";
        }

        /// <summary>
        /// توليد رسالة افتراضية
        /// </summary>
        private string GenerateDefaultMessage(string companyName, string customerName, string debtNumber, decimal amount, string dueDate)
        {
            return $@"عزيزي/عزيزتي {customerName}،

لديكم دين مستحق رقم {debtNumber} بمبلغ {amount:C} بتاريخ استحقاق {dueDate}.

نرجو منكم السداد في أقرب وقت ممكن.

{companyName}";
        }

        /// <summary>
        /// توليد رقم تذكير
        /// </summary>
        /// <returns>رقم التذكير</returns>
        private string GenerateReminderNumber()
        {
            var prefix = "REM";
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            return $"{prefix}{timestamp}";
        }

        #endregion

        #region عمليات قاعدة البيانات

        /// <summary>
        /// حفظ التذكير في قاعدة البيانات
        /// </summary>
        /// <param name="reminder">التذكير</param>
        private void SaveReminder(DebtReminder reminder)
        {
            try
            {
                // هذه الطريقة تحتاج إلى تنفيذ في DebtReminderDAL
                // سيتم إضافتها لاحقاً
                _logger?.LogInformation("تم حفظ التذكير رقم {ReminderNumber}", reminder.ReminderNumber);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ التذكير رقم {ReminderNumber}", reminder.ReminderNumber);
            }
        }

        /// <summary>
        /// تحديث عداد التذكيرات للدين
        /// </summary>
        /// <param name="debt">الدين</param>
        private void UpdateDebtReminderCount(Debt debt)
        {
            try
            {
                debt.ReminderCount++;
                debt.ModifiedDate = DateTime.Now;
                _debtBLL.UpdateDebt(debt);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحديث عداد التذكيرات للدين {DebtNumber}", debt.DebtNumber);
            }
        }

        #endregion

        #region إدارة الذاكرة

        /// <summary>
        /// تحرير الموارد
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// تحرير الموارد
        /// </summary>
        /// <param name="disposing">هل يتم التحرير</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                Stop();
                _reminderTimer?.Dispose();
                _disposed = true;

                _logger?.LogInformation("تم تحرير موارد خدمة التذكيرات");
            }
        }

        #endregion
    }

    #region إعدادات التذكيرات

    /// <summary>
    /// إعدادات خدمة التذكيرات
    /// </summary>
    public class ReminderSettings
    {
        /// <summary>
        /// تفعيل التذكيرات التلقائية
        /// </summary>
        public bool EnableAutomaticReminders { get; set; } = true;

        /// <summary>
        /// اسم الشركة
        /// </summary>
        public string CompanyName { get; set; } = "شركة أريدو";

        /// <summary>
        /// الحد الأدنى للمبلغ لإرسال التذكير
        /// </summary>
        public decimal MinAmountForReminder { get; set; } = 10;

        /// <summary>
        /// الحد الأقصى لعدد التذكيرات لكل دين
        /// </summary>
        public int MaxRemindersPerDebt { get; set; } = 5;

        /// <summary>
        /// الحد الأقصى لعمر الدين للتذكير (بالأيام)
        /// </summary>
        public int MaxReminderAge { get; set; } = 365;

        /// <summary>
        /// عدد الأيام قبل الاستحقاق لإرسال التذكير
        /// </summary>
        public int DaysBeforeDueForReminder { get; set; } = 3;

        /// <summary>
        /// عدد الأيام للإنذار الأول
        /// </summary>
        public int DaysForFirstWarning { get; set; } = 7;

        /// <summary>
        /// عدد الأيام للإنذار الأخير
        /// </summary>
        public int DaysForFinalWarning { get; set; } = 30;

        /// <summary>
        /// فترة التذكير الودي (بالأيام)
        /// </summary>
        public int FriendlyReminderInterval { get; set; } = 3;

        /// <summary>
        /// فترة الإنذار الأول (بالأيام)
        /// </summary>
        public int FirstWarningInterval { get; set; } = 7;

        /// <summary>
        /// فترة الإنذار الأخير (بالأيام)
        /// </summary>
        public int FinalWarningInterval { get; set; } = 14;

        /// <summary>
        /// فترة الإشعار القانوني (بالأيام)
        /// </summary>
        public int LegalNoticeInterval { get; set; } = 30;

        /// <summary>
        /// فترة التذكير الافتراضية (بالأيام)
        /// </summary>
        public int DefaultReminderInterval { get; set; } = 7;

        /// <summary>
        /// التأخير بين التذكيرات (بالميلي ثانية)
        /// </summary>
        public int DelayBetweenReminders { get; set; } = 1000;
    }

    #endregion

    #region خدمات الإرسال

    /// <summary>
    /// نتيجة إرسال الرسالة
    /// </summary>
    public class SendResult
    {
        public bool Success { get; set; }
        public string MessageId { get; set; }
        public string ErrorMessage { get; set; }
        public decimal Cost { get; set; }
    }

    /// <summary>
    /// خدمة الرسائل النصية
    /// </summary>
    public class SMSService
    {
        public async Task<SendResult> SendSMS(string phoneNumber, string message)
        {
            // تنفيذ إرسال الرسائل النصية
            // يحتاج إلى تكامل مع مزود خدمة SMS
            await Task.Delay(100); // محاكاة الإرسال

            return new SendResult
            {
                Success = true,
                MessageId = Guid.NewGuid().ToString(),
                Cost = 0.1m
            };
        }
    }

    /// <summary>
    /// خدمة البريد الإلكتروني
    /// </summary>
    public class EmailService
    {
        public async Task<SendResult> SendEmail(string email, string subject, string body)
        {
            // تنفيذ إرسال البريد الإلكتروني
            // يحتاج إلى تكامل مع مزود خدمة Email
            await Task.Delay(100); // محاكاة الإرسال

            return new SendResult
            {
                Success = true,
                MessageId = Guid.NewGuid().ToString(),
                Cost = 0
            };
        }
    }

    #endregion
}