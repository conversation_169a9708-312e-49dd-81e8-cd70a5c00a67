using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using AredooPOS.BLL;
using AredooPOS.Models.Settings;
using Microsoft.Extensions.Logging;
using Moq;

namespace AredooPOS.Tests
{
    /// <summary>
    /// اختبارات وحدة طبقة منطق الأعمال للإعدادات
    /// </summary>
    [TestClass]
    public class SettingsBLLTests
    {
        #region المتغيرات والإعداد

        private SettingsBLL _settingsBLL;
        private Mock<ILogger<SettingsBLL>> _mockLogger;
        private string _testConnectionString;

        [TestInitialize]
        public void Setup()
        {
            _mockLogger = new Mock<ILogger<SettingsBLL>>();
            _testConnectionString = "Server=(localdb)\\MSSQLLocalDB;Database=AredooPOS_Test;Integrated Security=true;";
            _settingsBLL = new SettingsBLL(_testConnectionString, _mockLogger.Object);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _settingsBLL?.ClearCache();
        }

        #endregion

        #region اختبارات الإعدادات العامة

        [TestMethod]
        public void GetSettingValue_ExistingSetting_ReturnsCorrectValue()
        {
            // Arrange
            var settingName = "System.ApplicationName";
            var expectedValue = "أريدو POS";

            // First save a test setting
            _settingsBLL.SaveSettingValue(settingName, expectedValue, "TestUser");

            // Act
            var result = _settingsBLL.GetSettingValue(settingName);

            // Assert
            Assert.AreEqual(expectedValue, result);
        }

        [TestMethod]
        public void GetSettingValue_NonExistentSetting_ReturnsDefaultValue()
        {
            // Arrange
            var settingName = "NonExistent.Setting";
            var defaultValue = "Default Value";

            // Act
            var result = _settingsBLL.GetSettingValue(settingName, defaultValue);

            // Assert
            Assert.AreEqual(defaultValue, result);
        }

        [TestMethod]
        public void GetSettingValueAsInt_ValidIntegerSetting_ReturnsCorrectValue()
        {
            // Arrange
            var settingName = "Test.Integer.Setting";
            var expectedValue = 42;

            // First save a test setting
            _settingsBLL.SaveSettingValue(settingName, expectedValue.ToString(), "TestUser");

            // Act
            var result = _settingsBLL.GetSettingValueAsInt(settingName);

            // Assert
            Assert.AreEqual(expectedValue, result);
        }

        [TestMethod]
        public void GetSettingValueAsInt_InvalidIntegerSetting_ReturnsDefaultValue()
        {
            // Arrange
            var settingName = "Test.Invalid.Integer";
            var defaultValue = 100;

            // First save an invalid integer setting
            _settingsBLL.SaveSettingValue(settingName, "not_a_number", "TestUser");

            // Act
            var result = _settingsBLL.GetSettingValueAsInt(settingName, defaultValue);

            // Assert
            Assert.AreEqual(defaultValue, result);
        }

        [TestMethod]
        public void GetSettingValueAsDecimal_ValidDecimalSetting_ReturnsCorrectValue()
        {
            // Arrange
            var settingName = "Test.Decimal.Setting";
            var expectedValue = 15.75m;

            // First save a test setting
            _settingsBLL.SaveSettingValue(settingName, expectedValue.ToString(), "TestUser");

            // Act
            var result = _settingsBLL.GetSettingValueAsDecimal(settingName);

            // Assert
            Assert.AreEqual(expectedValue, result);
        }

        [TestMethod]
        public void GetSettingValueAsBool_ValidBooleanSetting_ReturnsCorrectValue()
        {
            // Arrange
            var settingName = "Test.Boolean.Setting";
            var expectedValue = true;

            // First save a test setting
            _settingsBLL.SaveSettingValue(settingName, expectedValue.ToString(), "TestUser");

            // Act
            var result = _settingsBLL.GetSettingValueAsBool(settingName);

            // Assert
            Assert.AreEqual(expectedValue, result);
        }

        [TestMethod]
        public void SaveSettingValue_ValidData_ReturnsTrue()
        {
            // Arrange
            var settingName = "Test.Save.Setting";
            var settingValue = "Test Value";
            var updatedBy = "TestUser";

            // Act
            var result = _settingsBLL.SaveSettingValue(settingName, settingValue, updatedBy);

            // Assert
            Assert.IsTrue(result);

            // Verify the value was saved
            var savedValue = _settingsBLL.GetSettingValue(settingName);
            Assert.AreEqual(settingValue, savedValue);
        }

        [TestMethod]
        public void SaveMultipleSettings_ValidData_ReturnsCorrectCount()
        {
            // Arrange
            var settings = new Dictionary<string, string>
            {
                { "Test.Multiple.Setting1", "Value1" },
                { "Test.Multiple.Setting2", "Value2" },
                { "Test.Multiple.Setting3", "Value3" }
            };
            var updatedBy = "TestUser";

            // Act
            var result = _settingsBLL.SaveMultipleSettings(settings, updatedBy);

            // Assert
            Assert.AreEqual(settings.Count, result);

            // Verify all values were saved
            foreach (var setting in settings)
            {
                var savedValue = _settingsBLL.GetSettingValue(setting.Key);
                Assert.AreEqual(setting.Value, savedValue);
            }
        }

        [TestMethod]
        public void ClearCache_ClearsSettingsCache()
        {
            // Arrange
            var settingName = "Test.Cache.Setting";
            var settingValue = "Cached Value";

            // Save a setting to populate cache
            _settingsBLL.SaveSettingValue(settingName, settingValue, "TestUser");
            var cachedValue = _settingsBLL.GetSettingValue(settingName);

            // Act
            _settingsBLL.ClearCache();

            // Assert
            // The cache should be cleared, but the value should still be retrievable from database
            var valueAfterClear = _settingsBLL.GetSettingValue(settingName);
            Assert.AreEqual(settingValue, valueAfterClear);
        }

        #endregion

        #region اختبارات إعدادات الضريبة

        [TestMethod]
        public void GetTaxSettings_ReturnsValidTaxSettings()
        {
            // Act
            var result = _settingsBLL.GetTaxSettings();

            // Assert
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public void SaveTaxSettings_ValidSettings_ReturnsTrue()
        {
            // Arrange
            var taxSettings = new TaxSettings
            {
                IsTaxEnabled = true,
                DefaultTaxRate = 15.0m,
                TaxName = "ضريبة القيمة المضافة",
                TaxCalculationMethod = "Inclusive",
                ApplyToAllProducts = true,
                MinimumTaxableAmount = 0,
                TaxDecimalPlaces = 2,
                RoundingMethod = "Round"
            };

            // Act
            var result = _settingsBLL.SaveTaxSettings(taxSettings);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void CalculateTax_ValidAmount_ReturnsCorrectTax()
        {
            // Arrange
            var amount = 100.0m;
            var expectedTaxRate = 15.0m;

            // First set up tax settings
            var taxSettings = new TaxSettings
            {
                IsTaxEnabled = true,
                DefaultTaxRate = expectedTaxRate,
                TaxCalculationMethod = "Exclusive"
            };
            _settingsBLL.SaveTaxSettings(taxSettings);

            // Act
            var result = _settingsBLL.CalculateTax(amount);

            // Assert
            var expectedTax = amount * (expectedTaxRate / 100);
            Assert.AreEqual(expectedTax, result);
        }

        [TestMethod]
        public void CalculateTax_TaxDisabled_ReturnsZero()
        {
            // Arrange
            var amount = 100.0m;

            // First set up tax settings with tax disabled
            var taxSettings = new TaxSettings
            {
                IsTaxEnabled = false,
                DefaultTaxRate = 15.0m
            };
            _settingsBLL.SaveTaxSettings(taxSettings);

            // Act
            var result = _settingsBLL.CalculateTax(amount);

            // Assert
            Assert.AreEqual(0, result);
        }

        #endregion

        #region اختبارات إعدادات العملة

        [TestMethod]
        public void GetCurrencySettings_ReturnsValidCurrencySettings()
        {
            // Act
            var result = _settingsBLL.GetCurrencySettings();

            // Assert
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public void SaveCurrencySettings_ValidSettings_ReturnsTrue()
        {
            // Arrange
            var currencySettings = new CurrencySettings
            {
                DefaultCurrencyCode = "SAR",
                DecimalPlaces = 2,
                CurrencySymbol = "ر.س",
                SymbolPosition = "After",
                ThousandsSeparator = ",",
                DecimalSeparator = ".",
                SupportMultipleCurrencies = false
            };

            // Act
            var result = _settingsBLL.SaveCurrencySettings(currencySettings);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void FormatCurrency_ValidAmount_ReturnsFormattedString()
        {
            // Arrange
            var amount = 1234.56m;

            // First set up currency settings
            var currencySettings = new CurrencySettings
            {
                DefaultCurrencyCode = "SAR",
                DecimalPlaces = 2,
                CurrencySymbol = "ر.س",
                SymbolPosition = "After",
                ThousandsSeparator = ",",
                DecimalSeparator = "."
            };
            _settingsBLL.SaveCurrencySettings(currencySettings);

            // Act
            var result = _settingsBLL.FormatCurrency(amount);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Contains("1,234.56") || result.Contains("1234.56"));
        }

        #endregion

        #region اختبارات إعدادات المتجر

        [TestMethod]
        public void GetStoreSettings_ReturnsValidStoreSettings()
        {
            // Act
            var result = _settingsBLL.GetStoreSettings();

            // Assert
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public void SaveStoreSettings_ValidSettings_ReturnsTrue()
        {
            // Arrange
            var storeSettings = new StoreSettings
            {
                StoreName = "متجر الاختبار",
                StoreNameEnglish = "Test Store",
                PrimaryPhone = "0123456789",
                Email = "<EMAIL>",
                Address = "عنوان الاختبار",
                City = "الرياض"
            };

            // Act
            var result = _settingsBLL.SaveStoreSettings(storeSettings);

            // Assert
            Assert.IsTrue(result);
        }

        #endregion

        #region اختبارات إعدادات الطباعة

        [TestMethod]
        public void GetPrintSettings_ReturnsValidPrintSettings()
        {
            // Act
            var result = _settingsBLL.GetPrintSettings();

            // Assert
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public void SavePrintSettings_ValidSettings_ReturnsTrue()
        {
            // Arrange
            var printSettings = new PrintSettings
            {
                DefaultPrintSize = "A4",
                AutoPrint = false,
                DefaultCopies = 1,
                FontName = "Arial",
                DefaultFontSize = 12,
                PrintStoreLogo = true,
                PrintStoreInfo = true
            };

            // Act
            var result = _settingsBLL.SavePrintSettings(printSettings);

            // Assert
            Assert.IsTrue(result);
        }

        #endregion

        #region اختبارات العمليات المتقدمة

        [TestMethod]
        public void InitializeDefaultSettings_ReturnsTrue()
        {
            // Arrange
            var updatedBy = "TestUser";

            // Act
            var result = _settingsBLL.InitializeDefaultSettings(updatedBy);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void ValidateEssentialSettings_WithValidSettings_ReturnsTrue()
        {
            // Arrange
            // Set up essential settings
            var storeSettings = new StoreSettings { StoreName = "Test Store" };
            var currencySettings = new CurrencySettings { DefaultCurrencyCode = "SAR" };
            var printSettings = new PrintSettings { DefaultPrintSize = "A4" };

            _settingsBLL.SaveStoreSettings(storeSettings);
            _settingsBLL.SaveCurrencySettings(currencySettings);
            _settingsBLL.SavePrintSettings(printSettings);

            // Act
            var result = _settingsBLL.ValidateEssentialSettings();

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void GetSettingsSummary_ReturnsValidSummary()
        {
            // Act
            var result = _settingsBLL.GetSettingsSummary();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.StoreName);
            Assert.IsNotNull(result.DefaultCurrency);
        }

        [TestMethod]
        public void ExportAllSettings_ReturnsValidJsonString()
        {
            // Act
            var result = _settingsBLL.ExportAllSettings();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Length > 0);
        }

        [TestMethod]
        public void ImportSettings_ValidJsonData_ReturnsTrue()
        {
            // Arrange
            var exportedData = _settingsBLL.ExportAllSettings();
            var updatedBy = "TestUser";

            // Act
            var result = _settingsBLL.ImportSettings(exportedData, updatedBy);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void ResetSettingsToDefault_ValidCategory_ReturnsTrue()
        {
            // Arrange
            var category = "General";
            var updatedBy = "TestUser";

            // Act
            var result = _settingsBLL.ResetSettingsToDefault(category, updatedBy);

            // Assert
            Assert.IsTrue(result);
        }

        #endregion

        #region اختبارات العمليات المساعدة

        [TestMethod]
        public void SettingExists_ExistingSetting_ReturnsTrue()
        {
            // Arrange
            var settingName = "Test.Exists.Setting";
            _settingsBLL.SaveSettingValue(settingName, "Test Value", "TestUser");

            // Act
            var result = _settingsBLL.SettingExists(settingName);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void SettingExists_NonExistentSetting_ReturnsFalse()
        {
            // Arrange
            var settingName = "NonExistent.Setting";

            // Act
            var result = _settingsBLL.SettingExists(settingName);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void GetSettingsByCategory_ValidCategory_ReturnsFilteredList()
        {
            // Arrange
            var category = "General";

            // Act
            var result = _settingsBLL.GetSettingsByCategory(category);

            // Assert
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public void SearchSettings_ValidSearchTerm_ReturnsMatchingSettings()
        {
            // Arrange
            var searchTerm = "System";

            // Act
            var result = _settingsBLL.SearchSettings(searchTerm);

            // Assert
            Assert.IsNotNull(result);
        }

        [TestMethod]
        public void GetSettingsStatistics_ReturnsValidStatistics()
        {
            // Act
            var result = _settingsBLL.GetSettingsStatistics();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.TotalSettings >= 0);
            Assert.IsNotNull(result.SettingsByCategory);
        }

        #endregion
    }
}
