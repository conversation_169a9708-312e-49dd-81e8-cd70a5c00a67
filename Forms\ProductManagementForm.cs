using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using AredooPOS.Models;
using AredooPOS.BLL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// واجهة إدارة المنتجات
    /// توفر إدارة شاملة للمنتجات والمخزون
    /// </summary>
    public partial class ProductManagementForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly ProductBLL _productBLL;
        private readonly StockBLL _stockBLL;
        private readonly ILogger<ProductManagementForm> _logger;

        // ألوان النظام
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SuccessColor = Color.FromArgb(46, 204, 113);
        private readonly Color WarningColor = Color.FromArgb(241, 196, 15);
        private readonly Color DangerColor = Color.FromArgb(231, 76, 60);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);

        // المستخدم الحالي
        private string _currentUser = "النظام";

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ واجهة إدارة المنتجات
        /// </summary>
        /// <param name="productBLL">طبقة منطق الأعمال للمنتجات</param>
        /// <param name="stockBLL">طبقة منطق الأعمال للمخزون</param>
        /// <param name="logger">مسجل الأحداث</param>
        public ProductManagementForm(ProductBLL productBLL = null, StockBLL stockBLL = null, ILogger<ProductManagementForm> logger = null)
        {
            _productBLL = productBLL ?? new ProductBLL();
            _stockBLL = stockBLL ?? new StockBLL();
            _logger = logger;

            InitializeComponent();
            InitializeArabicUI();
            LoadProducts();
            SetupEventHandlers();
        }

        /// <summary>
        /// تهيئة الواجهة العربية
        /// </summary>
        private void InitializeArabicUI()
        {
            // إعدادات النموذج الأساسية
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "إدارة المنتجات والمخزون";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = LightGray;
            this.WindowState = FormWindowState.Maximized;

            // تطبيق الألوان والأنماط
            ApplyThemeColors();
            UpdateUITexts();
        }

        /// <summary>
        /// تطبيق ألوان النظام
        /// </summary>
        private void ApplyThemeColors()
        {
            // شريط العنوان
            pnlHeader.BackColor = PrimaryColor;
            lblTitle.ForeColor = Color.White;

            // شريط الأدوات
            toolStrip.BackColor = Color.White;
            
            // أزرار العمليات
            btnAdd.BackColor = SuccessColor;
            btnAdd.ForeColor = Color.White;
            btnAdd.FlatStyle = FlatStyle.Flat;

            btnEdit.BackColor = PrimaryColor;
            btnEdit.ForeColor = Color.White;
            btnEdit.FlatStyle = FlatStyle.Flat;

            btnDelete.BackColor = DangerColor;
            btnDelete.ForeColor = Color.White;
            btnDelete.FlatStyle = FlatStyle.Flat;

            btnRefresh.BackColor = Color.Gray;
            btnRefresh.ForeColor = Color.White;
            btnRefresh.FlatStyle = FlatStyle.Flat;

            // شبكة البيانات
            dgvProducts.BackgroundColor = Color.White;
            dgvProducts.GridColor = LightGray;
            dgvProducts.DefaultCellStyle.BackColor = Color.White;
            dgvProducts.DefaultCellStyle.ForeColor = Color.Black;
            dgvProducts.DefaultCellStyle.SelectionBackColor = PrimaryColor;
            dgvProducts.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvProducts.ColumnHeadersDefaultCellStyle.BackColor = PrimaryColor;
            dgvProducts.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvProducts.EnableHeadersVisualStyles = false;

            // لوحة البحث
            pnlSearch.BackColor = Color.White;
            txtSearch.BackColor = Color.White;
            cmbCategory.BackColor = Color.White;
            cmbStockStatus.BackColor = Color.White;
        }

        /// <summary>
        /// تحديث النصوص في الواجهة
        /// </summary>
        private void UpdateUITexts()
        {
            lblTitle.Text = "إدارة المنتجات والمخزون";
            
            // أزرار العمليات
            btnAdd.Text = "إضافة منتج";
            btnEdit.Text = "تعديل";
            btnDelete.Text = "حذف";
            btnRefresh.Text = "تحديث";
            btnExport.Text = "تصدير";
            btnImport.Text = "استيراد";
            btnStockReport.Text = "تقرير المخزون";
            btnLowStock.Text = "منخفض المخزون";

            // لوحة البحث
            lblSearch.Text = "البحث:";
            lblCategory.Text = "الفئة:";
            lblStockStatus.Text = "حالة المخزون:";
            txtSearch.PlaceholderText = "ابحث بالكود أو الاسم أو الباركود...";

            // خيارات حالة المخزون
            cmbStockStatus.Items.Clear();
            cmbStockStatus.Items.AddRange(new string[]
            {
                "الكل",
                "متوفر",
                "منخفض",
                "نافد",
                "زائد عن الحد"
            });
            cmbStockStatus.SelectedIndex = 0;

            // أعمدة الشبكة
            SetupDataGridColumns();
        }

        /// <summary>
        /// إعداد أعمدة شبكة البيانات
        /// </summary>
        private void SetupDataGridColumns()
        {
            dgvProducts.Columns.Clear();
            dgvProducts.AutoGenerateColumns = false;

            // عمود الكود
            var colCode = new DataGridViewTextBoxColumn
            {
                Name = "ProductCode",
                HeaderText = "كود المنتج",
                DataPropertyName = "ProductCode",
                Width = 100,
                ReadOnly = true
            };
            dgvProducts.Columns.Add(colCode);

            // عمود الاسم
            var colName = new DataGridViewTextBoxColumn
            {
                Name = "ProductName",
                HeaderText = "اسم المنتج",
                DataPropertyName = "ProductName",
                Width = 200,
                ReadOnly = true
            };
            dgvProducts.Columns.Add(colName);

            // عمود الفئة
            var colCategory = new DataGridViewTextBoxColumn
            {
                Name = "CategoryName",
                HeaderText = "الفئة",
                DataPropertyName = "CategoryName",
                Width = 120,
                ReadOnly = true
            };
            dgvProducts.Columns.Add(colCategory);

            // عمود الباركود
            var colBarcode = new DataGridViewTextBoxColumn
            {
                Name = "Barcode",
                HeaderText = "الباركود",
                DataPropertyName = "Barcode",
                Width = 120,
                ReadOnly = true
            };
            dgvProducts.Columns.Add(colBarcode);

            // عمود سعر التكلفة
            var colCostPrice = new DataGridViewTextBoxColumn
            {
                Name = "CostPrice",
                HeaderText = "سعر التكلفة",
                DataPropertyName = "CostPrice",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            };
            dgvProducts.Columns.Add(colCostPrice);

            // عمود سعر البيع
            var colUnitPrice = new DataGridViewTextBoxColumn
            {
                Name = "UnitPrice",
                HeaderText = "سعر البيع",
                DataPropertyName = "UnitPrice",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            };
            dgvProducts.Columns.Add(colUnitPrice);

            // عمود الكمية
            var colStock = new DataGridViewTextBoxColumn
            {
                Name = "StockQuantity",
                HeaderText = "الكمية",
                DataPropertyName = "StockQuantity",
                Width = 80,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N0" }
            };
            dgvProducts.Columns.Add(colStock);

            // عمود الوحدة
            var colUnit = new DataGridViewTextBoxColumn
            {
                Name = "Unit",
                HeaderText = "الوحدة",
                DataPropertyName = "Unit",
                Width = 80,
                ReadOnly = true
            };
            dgvProducts.Columns.Add(colUnit);

            // عمود الحد الأدنى
            var colMinStock = new DataGridViewTextBoxColumn
            {
                Name = "MinStockLevel",
                HeaderText = "الحد الأدنى",
                DataPropertyName = "MinStockLevel",
                Width = 80,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "N0" }
            };
            dgvProducts.Columns.Add(colMinStock);

            // عمود قيمة المخزون
            var colStockValue = new DataGridViewTextBoxColumn
            {
                Name = "StockValue",
                HeaderText = "قيمة المخزون",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            };
            dgvProducts.Columns.Add(colStockValue);

            // عمود الحالة
            var colStatus = new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                Width = 100,
                ReadOnly = true
            };
            dgvProducts.Columns.Add(colStatus);

            // عمود النشاط
            var colActive = new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                HeaderText = "نشط",
                DataPropertyName = "IsActive",
                Width = 60,
                ReadOnly = true
            };
            dgvProducts.Columns.Add(colActive);
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // أحداث الأزرار
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnRefresh.Click += BtnRefresh_Click;
            btnExport.Click += BtnExport_Click;
            btnImport.Click += BtnImport_Click;
            btnStockReport.Click += BtnStockReport_Click;
            btnLowStock.Click += BtnLowStock_Click;

            // أحداث البحث
            txtSearch.TextChanged += TxtSearch_TextChanged;
            cmbCategory.SelectedIndexChanged += CmbCategory_SelectedIndexChanged;
            cmbStockStatus.SelectedIndexChanged += CmbStockStatus_SelectedIndexChanged;

            // أحداث الشبكة
            dgvProducts.SelectionChanged += DgvProducts_SelectionChanged;
            dgvProducts.CellDoubleClick += DgvProducts_CellDoubleClick;
            dgvProducts.CellFormatting += DgvProducts_CellFormatting;

            // أحداث النموذج
            this.Load += ProductManagementForm_Load;
            this.KeyDown += ProductManagementForm_KeyDown;
        }

        #endregion

        #region تحميل البيانات

        /// <summary>
        /// تحميل المنتجات
        /// </summary>
        private void LoadProducts()
        {
            try
            {
                var products = _productBLL.GetAllProducts(false);
                
                // إضافة البيانات المحسوبة
                var productData = products.Select(p => new
                {
                    p.ProductID,
                    p.ProductCode,
                    p.ProductName,
                    p.CategoryName,
                    p.Barcode,
                    p.CostPrice,
                    p.UnitPrice,
                    p.StockQuantity,
                    p.Unit,
                    p.MinStockLevel,
                    StockValue = p.GetStockValue(),
                    Status = GetStockStatus(p),
                    p.IsActive
                }).ToList();

                dgvProducts.DataSource = productData;
                
                // تحديث إحصائيات المخزون
                UpdateStockStatistics(products);
                
                _logger?.LogInformation($"تم تحميل {products.Count} منتج");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل المنتجات");
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث إحصائيات المخزون
        /// </summary>
        /// <param name="products">قائمة المنتجات</param>
        private void UpdateStockStatistics(System.Collections.Generic.List<Product> products)
        {
            try
            {
                var summary = _stockBLL.GetStockSummary();
                
                lblTotalProducts.Text = $"إجمالي المنتجات: {summary.TotalProducts}";
                lblTotalValue.Text = $"قيمة المخزون: {summary.TotalStockValue:C}";
                lblLowStock.Text = $"منخفض المخزون: {summary.LowStockCount}";
                lblOutOfStock.Text = $"نافد المخزون: {summary.OutOfStockCount}";
                
                // تلوين التنبيهات
                lblLowStock.ForeColor = summary.LowStockCount > 0 ? WarningColor : Color.Black;
                lblOutOfStock.ForeColor = summary.OutOfStockCount > 0 ? DangerColor : Color.Black;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحديث إحصائيات المخزون");
            }
        }

        /// <summary>
        /// الحصول على حالة المخزون
        /// </summary>
        /// <param name="product">المنتج</param>
        /// <returns>حالة المخزون</returns>
        private string GetStockStatus(Product product)
        {
            if (!product.TrackStock)
                return "غير متتبع";
            
            if (product.IsOutOfStock())
                return "نافد";
            
            if (product.IsLowStock())
                return "منخفض";
            
            if (product.MaxStockLevel > 0 && product.StockQuantity > product.MaxStockLevel)
                return "زائد";
            
            return "متوفر";
        }

        #endregion

        #region معالجات الأحداث

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void ProductManagementForm_Load(object sender, EventArgs e)
        {
            _logger?.LogInformation("تم تحميل واجهة إدارة المنتجات");
        }

        /// <summary>
        /// حدث الضغط على مفاتيح لوحة المفاتيح
        /// </summary>
        private void ProductManagementForm_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.F1:
                    BtnAdd_Click(sender, e);
                    break;
                case Keys.F2:
                    BtnEdit_Click(sender, e);
                    break;
                case Keys.F3:
                    txtSearch.Focus();
                    break;
                case Keys.F5:
                    BtnRefresh_Click(sender, e);
                    break;
                case Keys.Delete:
                    if (e.Control)
                        BtnDelete_Click(sender, e);
                    break;
            }
        }

        /// <summary>
        /// إضافة منتج جديد
        /// </summary>
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new AddEditProductForm(_productBLL, null, _currentUser);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    LoadProducts();
                    _logger?.LogInformation("تم إضافة منتج جديد");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إضافة منتج جديد");
                MessageBox.Show($"خطأ في إضافة منتج جديد: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تعديل منتج
        /// </summary>
        private void BtnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvProducts.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار منتج للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var productId = Convert.ToInt32(dgvProducts.SelectedRows[0].Cells["ProductID"].Value);
                var product = _productBLL.GetProductById(productId);
                
                if (product == null)
                {
                    MessageBox.Show("المنتج غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                var editForm = new AddEditProductForm(_productBLL, product, _currentUser);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadProducts();
                    _logger?.LogInformation($"تم تعديل المنتج {product.ProductCode}");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تعديل المنتج");
                MessageBox.Show($"خطأ في تعديل المنتج: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حذف منتج
        /// </summary>
        private void BtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvProducts.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار منتج للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var productCode = dgvProducts.SelectedRows[0].Cells["ProductCode"].Value.ToString();
                var productName = dgvProducts.SelectedRows[0].Cells["ProductName"].Value.ToString();
                
                var result = MessageBox.Show(
                    $"هل تريد حذف المنتج '{productCode} - {productName}'؟\n\nملاحظة: إذا كان المنتج مرتبط بمعاملات سيتم إلغاء تفعيله بدلاً من حذفه.",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var productId = Convert.ToInt32(dgvProducts.SelectedRows[0].Cells["ProductID"].Value);
                    var success = _productBLL.DeleteProduct(productId, _currentUser);
                    
                    if (success)
                    {
                        LoadProducts();
                        MessageBox.Show("تم حذف المنتج بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        _logger?.LogInformation($"تم حذف المنتج {productCode}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حذف المنتج");
                MessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadProducts();
        }

        /// <summary>
        /// تصدير البيانات
        /// </summary>
        private void BtnExport_Click(object sender, EventArgs e)
        {
            // TODO: تنفيذ تصدير البيانات
            MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// استيراد البيانات
        /// </summary>
        private void BtnImport_Click(object sender, EventArgs e)
        {
            // TODO: تنفيذ استيراد البيانات
            MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// تقرير المخزون
        /// </summary>
        private void BtnStockReport_Click(object sender, EventArgs e)
        {
            // TODO: فتح تقرير المخزون
            MessageBox.Show("سيتم تنفيذ هذه الميزة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// عرض المنتجات منخفضة المخزون
        /// </summary>
        private void BtnLowStock_Click(object sender, EventArgs e)
        {
            try
            {
                var lowStockProducts = _productBLL.GetLowStockProducts();
                
                if (lowStockProducts.Count == 0)
                {
                    MessageBox.Show("لا توجد منتجات منخفضة المخزون", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // عرض المنتجات منخفضة المخزون فقط
                var productData = lowStockProducts.Select(p => new
                {
                    p.ProductID,
                    p.ProductCode,
                    p.ProductName,
                    p.CategoryName,
                    p.Barcode,
                    p.CostPrice,
                    p.UnitPrice,
                    p.StockQuantity,
                    p.Unit,
                    p.MinStockLevel,
                    StockValue = p.GetStockValue(),
                    Status = GetStockStatus(p),
                    p.IsActive
                }).ToList();

                dgvProducts.DataSource = productData;
                
                MessageBox.Show($"تم العثور على {lowStockProducts.Count} منتج منخفض المخزون", 
                    "منتجات منخفضة المخزون", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في عرض المنتجات منخفضة المخزون");
                MessageBox.Show($"خطأ في عرض المنتجات منخفضة المخزون: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// البحث في المنتجات
        /// </summary>
        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            PerformSearch();
        }

        /// <summary>
        /// تغيير الفئة
        /// </summary>
        private void CmbCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            PerformSearch();
        }

        /// <summary>
        /// تغيير حالة المخزون
        /// </summary>
        private void CmbStockStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            PerformSearch();
        }

        /// <summary>
        /// تنفيذ البحث
        /// </summary>
        private void PerformSearch()
        {
            try
            {
                var searchTerm = txtSearch.Text.Trim();
                var products = _productBLL.SearchProducts(searchTerm);

                // تطبيق فلتر حالة المخزون
                if (cmbStockStatus.SelectedIndex > 0)
                {
                    var statusFilter = cmbStockStatus.SelectedItem.ToString();
                    products = products.Where(p => GetStockStatus(p) == statusFilter).ToList();
                }

                var productData = products.Select(p => new
                {
                    p.ProductID,
                    p.ProductCode,
                    p.ProductName,
                    p.CategoryName,
                    p.Barcode,
                    p.CostPrice,
                    p.UnitPrice,
                    p.StockQuantity,
                    p.Unit,
                    p.MinStockLevel,
                    StockValue = p.GetStockValue(),
                    Status = GetStockStatus(p),
                    p.IsActive
                }).ToList();

                dgvProducts.DataSource = productData;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في البحث");
            }
        }

        /// <summary>
        /// تغيير التحديد في الشبكة
        /// </summary>
        private void DgvProducts_SelectionChanged(object sender, EventArgs e)
        {
            var hasSelection = dgvProducts.SelectedRows.Count > 0;
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;
        }

        /// <summary>
        /// النقر المزدوج على الشبكة
        /// </summary>
        private void DgvProducts_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEdit_Click(sender, e);
            }
        }

        /// <summary>
        /// تنسيق خلايا الشبكة
        /// </summary>
        private void DgvProducts_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvProducts.Columns[e.ColumnIndex].Name == "Status")
            {
                var status = e.Value?.ToString();
                switch (status)
                {
                    case "نافد":
                        e.CellStyle.ForeColor = DangerColor;
                        e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold);
                        break;
                    case "منخفض":
                        e.CellStyle.ForeColor = WarningColor;
                        e.CellStyle.Font = new Font(e.CellStyle.Font, FontStyle.Bold);
                        break;
                    case "زائد":
                        e.CellStyle.ForeColor = Color.Orange;
                        break;
                    case "متوفر":
                        e.CellStyle.ForeColor = SuccessColor;
                        break;
                }
            }
        }

        #endregion
    }
}
