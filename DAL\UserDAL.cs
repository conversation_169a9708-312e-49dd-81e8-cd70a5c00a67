using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using AredooPOS.Models;
using Microsoft.Extensions.Logging;

namespace AredooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات للمستخدمين
    /// تحتوي على جميع العمليات المتعلقة بقاعدة البيانات للمستخدمين
    /// </summary>
    public class UserDAL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly string _connectionString;
        private readonly ILogger<UserDAL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة الوصول للبيانات للمستخدمين
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public UserDAL(string connectionString = null, ILogger<UserDAL> logger = null)
        {
            _connectionString = connectionString ?? DatabaseConfig.GetConnectionString();
            _logger = logger;
        }

        #endregion

        #region العمليات الأساسية

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        /// <returns>رقم المستخدم الجديد</returns>
        public int AddUser(User user)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_AddUser", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                // إضافة المعاملات
                command.Parameters.AddWithValue("@Username", user.Username);
                command.Parameters.AddWithValue("@PasswordHash", user.PasswordHash);
                command.Parameters.AddWithValue("@PasswordSalt", user.PasswordSalt ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Email", user.Email ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@FirstName", user.FirstName);
                command.Parameters.AddWithValue("@LastName", user.LastName);
                command.Parameters.AddWithValue("@PhoneNumber", user.PhoneNumber ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@DateOfBirth", user.DateOfBirth ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Gender", user.Gender ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Address", user.Address ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@EmployeeNumber", user.EmployeeNumber ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@JobTitle", user.JobTitle ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Department", user.Department ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@HireDate", user.HireDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Salary", user.Salary ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@RoleID", user.RoleID);
                command.Parameters.AddWithValue("@IsActive", user.IsActive);
                command.Parameters.AddWithValue("@IsLocked", user.IsLocked);
                command.Parameters.AddWithValue("@CanAccessSystem", user.CanAccessSystem);
                command.Parameters.AddWithValue("@CanManageUsers", user.CanManageUsers);
                command.Parameters.AddWithValue("@CanViewReports", user.CanViewReports);
                command.Parameters.AddWithValue("@CanManageProducts", user.CanManageProducts);
                command.Parameters.AddWithValue("@CanProcessSales", user.CanProcessSales);
                command.Parameters.AddWithValue("@PasswordExpiryDate", user.PasswordExpiryDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@MustChangePassword", user.MustChangePassword);
                command.Parameters.AddWithValue("@ProfileImagePath", user.ProfileImagePath ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Notes", user.Notes ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@UserSettings", user.UserSettings ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@CreatedBy", user.CreatedBy);

                // معامل الإخراج
                var outputParam = new SqlParameter("@NewUserID", SqlDbType.Int)
                {
                    Direction = ParameterDirection.Output
                };
                command.Parameters.Add(outputParam);

                connection.Open();
                command.ExecuteNonQuery();

                var userId = Convert.ToInt32(outputParam.Value);
                _logger?.LogInformation($"تم إضافة مستخدم جديد برقم {userId}");
                return userId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إضافة المستخدم");
                throw;
            }
        }

        /// <summary>
        /// تحديث بيانات المستخدم
        /// </summary>
        /// <param name="user">بيانات المستخدم المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateUser(User user)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_UpdateUser", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                // إضافة المعاملات
                command.Parameters.AddWithValue("@UserID", user.UserID);
                command.Parameters.AddWithValue("@Username", user.Username);
                command.Parameters.AddWithValue("@Email", user.Email ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@FirstName", user.FirstName);
                command.Parameters.AddWithValue("@LastName", user.LastName);
                command.Parameters.AddWithValue("@PhoneNumber", user.PhoneNumber ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@DateOfBirth", user.DateOfBirth ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Gender", user.Gender ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Address", user.Address ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@EmployeeNumber", user.EmployeeNumber ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@JobTitle", user.JobTitle ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Department", user.Department ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@HireDate", user.HireDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Salary", user.Salary ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@RoleID", user.RoleID);
                command.Parameters.AddWithValue("@IsActive", user.IsActive);
                command.Parameters.AddWithValue("@IsLocked", user.IsLocked);
                command.Parameters.AddWithValue("@LockReason", user.LockReason ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@CanAccessSystem", user.CanAccessSystem);
                command.Parameters.AddWithValue("@CanManageUsers", user.CanManageUsers);
                command.Parameters.AddWithValue("@CanViewReports", user.CanViewReports);
                command.Parameters.AddWithValue("@CanManageProducts", user.CanManageProducts);
                command.Parameters.AddWithValue("@CanProcessSales", user.CanProcessSales);
                command.Parameters.AddWithValue("@ProfileImagePath", user.ProfileImagePath ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Notes", user.Notes ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@UserSettings", user.UserSettings ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ModifiedBy", user.ModifiedBy);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم تحديث المستخدم {user.UserID}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث المستخدم {user.UserID}");
                throw;
            }
        }

        /// <summary>
        /// حذف مستخدم
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="deletedBy">من قام بالحذف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public bool DeleteUser(int userId, string deletedBy)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_DeleteUser", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@UserID", userId);
                command.Parameters.AddWithValue("@DeletedBy", deletedBy);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم حذف المستخدم {userId}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حذف المستخدم {userId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على مستخدم بالرقم
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <returns>بيانات المستخدم</returns>
        public User GetUserById(int userId)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetUserById", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@UserID", userId);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapUserFromReader(reader);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على المستخدم {userId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على مستخدم باسم المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>بيانات المستخدم</returns>
        public User GetUserByUsername(string username)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetUserByUsername", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@Username", username);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapUserFromReader(reader);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على المستخدم {username}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على مستخدم بالبريد الإلكتروني
        /// </summary>
        /// <param name="email">البريد الإلكتروني</param>
        /// <returns>بيانات المستخدم</returns>
        public User GetUserByEmail(string email)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetUserByEmail", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@Email", email);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapUserFromReader(reader);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على المستخدم بالبريد {email}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع المستخدمين
        /// </summary>
        /// <param name="includeInactive">تضمين المستخدمين غير النشطين</param>
        /// <returns>قائمة المستخدمين</returns>
        public List<User> GetAllUsers(bool includeInactive = false)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetAllUsers", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@IncludeInactive", includeInactive);

                connection.Open();
                using var reader = command.ExecuteReader();

                var users = new List<User>();
                while (reader.Read())
                {
                    users.Add(MapUserFromReader(reader));
                }

                return users;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على جميع المستخدمين");
                throw;
            }
        }

        #endregion

        #region البحث والفلترة

        /// <summary>
        /// البحث في المستخدمين
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="roleId">رقم الدور (اختياري)</param>
        /// <param name="includeInactive">تضمين المستخدمين غير النشطين</param>
        /// <returns>قائمة المستخدمين المطابقة</returns>
        public List<User> SearchUsers(string searchTerm, int? roleId = null, bool includeInactive = false)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_SearchUsers", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@SearchTerm", searchTerm ?? string.Empty);
                command.Parameters.AddWithValue("@RoleID", roleId ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@IncludeInactive", includeInactive);

                connection.Open();
                using var reader = command.ExecuteReader();

                var users = new List<User>();
                while (reader.Read())
                {
                    users.Add(MapUserFromReader(reader));
                }

                return users;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في البحث في المستخدمين");
                throw;
            }
        }

        /// <summary>
        /// الحصول على المستخدمين حسب الدور
        /// </summary>
        /// <param name="roleId">رقم الدور</param>
        /// <param name="includeInactive">تضمين المستخدمين غير النشطين</param>
        /// <returns>قائمة المستخدمين</returns>
        public List<User> GetUsersByRole(int roleId, bool includeInactive = false)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetUsersByRole", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@RoleID", roleId);
                command.Parameters.AddWithValue("@IncludeInactive", includeInactive);

                connection.Open();
                using var reader = command.ExecuteReader();

                var users = new List<User>();
                while (reader.Read())
                {
                    users.Add(MapUserFromReader(reader));
                }

                return users;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على المستخدمين للدور {roleId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على المستخدمين المقفلين
        /// </summary>
        /// <returns>قائمة المستخدمين المقفلين</returns>
        public List<User> GetLockedUsers()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetLockedUsers", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                connection.Open();
                using var reader = command.ExecuteReader();

                var users = new List<User>();
                while (reader.Read())
                {
                    users.Add(MapUserFromReader(reader));
                }

                return users;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على المستخدمين المقفلين");
                throw;
            }
        }

        #endregion

        #region عمليات كلمة المرور

        /// <summary>
        /// تحديث كلمة المرور
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="newPasswordHash">كلمة المرور الجديدة المشفرة</param>
        /// <param name="newSalt">الملح الجديد</param>
        /// <param name="expiryDate">تاريخ انتهاء الصلاحية</param>
        /// <param name="modifiedBy">من قام بالتعديل</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdatePassword(int userId, string newPasswordHash, string newSalt, DateTime? expiryDate, string modifiedBy)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_UpdateUserPassword", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@UserID", userId);
                command.Parameters.AddWithValue("@PasswordHash", newPasswordHash);
                command.Parameters.AddWithValue("@PasswordSalt", newSalt ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PasswordExpiryDate", expiryDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@MustChangePassword", false);
                command.Parameters.AddWithValue("@ModifiedBy", modifiedBy);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم تحديث كلمة مرور المستخدم {userId}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث كلمة مرور المستخدم {userId}");
                throw;
            }
        }

        /// <summary>
        /// إعادة تعيين كلمة المرور
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="tempPasswordHash">كلمة المرور المؤقتة المشفرة</param>
        /// <param name="tempSalt">الملح المؤقت</param>
        /// <param name="resetBy">من قام بإعادة التعيين</param>
        /// <returns>true إذا تم إعادة التعيين بنجاح</returns>
        public bool ResetPassword(int userId, string tempPasswordHash, string tempSalt, string resetBy)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_ResetUserPassword", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@UserID", userId);
                command.Parameters.AddWithValue("@TempPasswordHash", tempPasswordHash);
                command.Parameters.AddWithValue("@TempSalt", tempSalt ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@MustChangePassword", true);
                command.Parameters.AddWithValue("@ResetBy", resetBy);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم إعادة تعيين كلمة مرور المستخدم {userId}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إعادة تعيين كلمة مرور المستخدم {userId}");
                throw;
            }
        }

        #endregion

        #region عمليات الأمان

        /// <summary>
        /// قفل حساب المستخدم
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="reason">سبب القفل</param>
        /// <param name="lockedBy">من قام بالقفل</param>
        /// <returns>true إذا تم القفل بنجاح</returns>
        public bool LockUser(int userId, string reason, string lockedBy)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_LockUser", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@UserID", userId);
                command.Parameters.AddWithValue("@LockReason", reason);
                command.Parameters.AddWithValue("@LockedBy", lockedBy);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم قفل المستخدم {userId}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في قفل المستخدم {userId}");
                throw;
            }
        }

        /// <summary>
        /// إلغاء قفل حساب المستخدم
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="unlockedBy">من قام بإلغاء القفل</param>
        /// <returns>true إذا تم إلغاء القفل بنجاح</returns>
        public bool UnlockUser(int userId, string unlockedBy)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_UnlockUser", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@UserID", userId);
                command.Parameters.AddWithValue("@UnlockedBy", unlockedBy);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم إلغاء قفل المستخدم {userId}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إلغاء قفل المستخدم {userId}");
                throw;
            }
        }

        /// <summary>
        /// تسجيل محاولة دخول فاشلة
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="ipAddress">عنوان IP</param>
        /// <returns>true إذا تم التسجيل بنجاح</returns>
        public bool RecordFailedLogin(int userId, string ipAddress)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_RecordFailedLogin", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@UserID", userId);
                command.Parameters.AddWithValue("@IPAddress", ipAddress ?? (object)DBNull.Value);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تسجيل محاولة دخول فاشلة للمستخدم {userId}");
                throw;
            }
        }

        /// <summary>
        /// تسجيل دخول ناجح
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="ipAddress">عنوان IP</param>
        /// <returns>true إذا تم التسجيل بنجاح</returns>
        public bool RecordSuccessfulLogin(int userId, string ipAddress)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_RecordSuccessfulLogin", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@UserID", userId);
                command.Parameters.AddWithValue("@IPAddress", ipAddress ?? (object)DBNull.Value);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تسجيل دخول ناجح للمستخدم {userId}");
                throw;
            }
        }

        #endregion

        #region التحقق من الوجود

        /// <summary>
        /// التحقق من وجود اسم المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="excludeUserId">استثناء مستخدم معين (للتحديث)</param>
        /// <returns>true إذا كان اسم المستخدم موجود</returns>
        public bool UsernameExists(string username, int? excludeUserId = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_CheckUsernameExists", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@Username", username);
                command.Parameters.AddWithValue("@ExcludeUserID", excludeUserId ?? (object)DBNull.Value);

                connection.Open();
                var result = command.ExecuteScalar();

                return Convert.ToBoolean(result);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في التحقق من وجود اسم المستخدم {username}");
                throw;
            }
        }

        /// <summary>
        /// التحقق من وجود البريد الإلكتروني
        /// </summary>
        /// <param name="email">البريد الإلكتروني</param>
        /// <param name="excludeUserId">استثناء مستخدم معين (للتحديث)</param>
        /// <returns>true إذا كان البريد الإلكتروني موجود</returns>
        public bool EmailExists(string email, int? excludeUserId = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_CheckEmailExists", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@Email", email);
                command.Parameters.AddWithValue("@ExcludeUserID", excludeUserId ?? (object)DBNull.Value);

                connection.Open();
                var result = command.ExecuteScalar();

                return Convert.ToBoolean(result);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في التحقق من وجود البريد الإلكتروني {email}");
                throw;
            }
        }

        #endregion

        #region الإحصائيات

        /// <summary>
        /// الحصول على عدد المستخدمين
        /// </summary>
        /// <param name="activeOnly">المستخدمين النشطين فقط</param>
        /// <returns>عدد المستخدمين</returns>
        public int GetUsersCount(bool activeOnly = true)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetUsersCount", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@ActiveOnly", activeOnly);

                connection.Open();
                var result = command.ExecuteScalar();

                return Convert.ToInt32(result);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على عدد المستخدمين");
                throw;
            }
        }

        /// <summary>
        /// الحصول على إحصائيات المستخدمين
        /// </summary>
        /// <returns>إحصائيات المستخدمين</returns>
        public UserStatistics GetUserStatistics()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetUserStatistics", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return new UserStatistics
                    {
                        TotalUsers = reader.GetInt32("TotalUsers"),
                        ActiveUsers = reader.GetInt32("ActiveUsers"),
                        InactiveUsers = reader.GetInt32("InactiveUsers"),
                        LockedUsers = reader.GetInt32("LockedUsers"),
                        OnlineUsers = reader.GetInt32("OnlineUsers"),
                        UsersWithExpiredPasswords = reader.GetInt32("UsersWithExpiredPasswords")
                    };
                }

                return new UserStatistics();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إحصائيات المستخدمين");
                throw;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// تحويل بيانات القارئ إلى كائن مستخدم
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن المستخدم</returns>
        private User MapUserFromReader(SqlDataReader reader)
        {
            return new User
            {
                UserID = reader.GetInt32("UserID"),
                Username = reader.GetString("Username"),
                PasswordHash = reader.GetString("PasswordHash"),
                PasswordSalt = reader.IsDBNull("PasswordSalt") ? null : reader.GetString("PasswordSalt"),
                Email = reader.IsDBNull("Email") ? null : reader.GetString("Email"),
                FirstName = reader.GetString("FirstName"),
                LastName = reader.GetString("LastName"),
                PhoneNumber = reader.IsDBNull("PhoneNumber") ? null : reader.GetString("PhoneNumber"),
                DateOfBirth = reader.IsDBNull("DateOfBirth") ? null : reader.GetDateTime("DateOfBirth"),
                Gender = reader.IsDBNull("Gender") ? null : reader.GetString("Gender"),
                Address = reader.IsDBNull("Address") ? null : reader.GetString("Address"),
                EmployeeNumber = reader.IsDBNull("EmployeeNumber") ? null : reader.GetString("EmployeeNumber"),
                JobTitle = reader.IsDBNull("JobTitle") ? null : reader.GetString("JobTitle"),
                Department = reader.IsDBNull("Department") ? null : reader.GetString("Department"),
                HireDate = reader.IsDBNull("HireDate") ? null : reader.GetDateTime("HireDate"),
                Salary = reader.IsDBNull("Salary") ? null : reader.GetDecimal("Salary"),
                RoleID = reader.GetInt32("RoleID"),
                RoleName = reader.IsDBNull("RoleName") ? null : reader.GetString("RoleName"),
                PermissionLevel = reader.GetInt32("PermissionLevel"),
                IsSystemAdmin = reader.GetBoolean("IsSystemAdmin"),
                IsActive = reader.GetBoolean("IsActive"),
                IsLocked = reader.GetBoolean("IsLocked"),
                LockedDate = reader.IsDBNull("LockedDate") ? null : reader.GetDateTime("LockedDate"),
                LockReason = reader.IsDBNull("LockReason") ? null : reader.GetString("LockReason"),
                FailedLoginAttempts = reader.GetInt32("FailedLoginAttempts"),
                LastFailedLoginDate = reader.IsDBNull("LastFailedLoginDate") ? null : reader.GetDateTime("LastFailedLoginDate"),
                LastLoginDate = reader.IsDBNull("LastLoginDate") ? null : reader.GetDateTime("LastLoginDate"),
                LastLoginIP = reader.IsDBNull("LastLoginIP") ? null : reader.GetString("LastLoginIP"),
                CanAccessSystem = reader.GetBoolean("CanAccessSystem"),
                CanManageUsers = reader.GetBoolean("CanManageUsers"),
                CanViewReports = reader.GetBoolean("CanViewReports"),
                CanManageProducts = reader.GetBoolean("CanManageProducts"),
                CanProcessSales = reader.GetBoolean("CanProcessSales"),
                PasswordExpiryDate = reader.IsDBNull("PasswordExpiryDate") ? null : reader.GetDateTime("PasswordExpiryDate"),
                MustChangePassword = reader.GetBoolean("MustChangePassword"),
                ProfileImagePath = reader.IsDBNull("ProfileImagePath") ? null : reader.GetString("ProfileImagePath"),
                Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes"),
                UserSettings = reader.IsDBNull("UserSettings") ? null : reader.GetString("UserSettings"),
                CreatedBy = reader.IsDBNull("CreatedBy") ? null : reader.GetString("CreatedBy"),
                CreatedDate = reader.GetDateTime("CreatedDate"),
                ModifiedBy = reader.IsDBNull("ModifiedBy") ? null : reader.GetString("ModifiedBy"),
                ModifiedDate = reader.GetDateTime("ModifiedDate")
            };
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// إحصائيات المستخدمين
    /// </summary>
    public class UserStatistics
    {
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int InactiveUsers { get; set; }
        public int LockedUsers { get; set; }
        public int OnlineUsers { get; set; }
        public int UsersWithExpiredPasswords { get; set; }
    }

    #endregion
}