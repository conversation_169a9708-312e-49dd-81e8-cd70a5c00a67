using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using AredooPOS.Models;
using Microsoft.Extensions.Logging;

namespace AredooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات لفئات المنتجات
    /// تحتوي على جميع العمليات المتعلقة بفئات المنتجات في قاعدة البيانات
    /// </summary>
    public class CategoryDAL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly string _connectionString;
        private readonly ILogger<CategoryDAL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة الوصول للبيانات لفئات المنتجات
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public CategoryDAL(string connectionString = null, ILogger<CategoryDAL> logger = null)
        {
            _connectionString = connectionString ?? DatabaseConnection.ConnectionString;
            _logger = logger;
        }

        #endregion

        #region العمليات الأساسية (CRUD)

        /// <summary>
        /// إضافة فئة جديدة
        /// </summary>
        /// <param name="category">بيانات الفئة</param>
        /// <returns>رقم الفئة الجديدة</returns>
        public int AddCategory(Category category)
        {
            try
            {
                var query = @"
                    INSERT INTO Categories (
                        CategoryCode, CategoryName, CategoryNameEn, Description, ParentCategoryID,
                        Level, CategoryPath, SortOrder, ImagePath, Icon, Color, IsActive, IsVisible,
                        AllowProducts, DefaultProfitMargin, DefaultTaxRate, DefaultUnit, Notes,
                        Keywords, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate
                    ) VALUES (
                        @CategoryCode, @CategoryName, @CategoryNameEn, @Description, @ParentCategoryID,
                        @Level, @CategoryPath, @SortOrder, @ImagePath, @Icon, @Color, @IsActive, @IsVisible,
                        @AllowProducts, @DefaultProfitMargin, @DefaultTaxRate, @DefaultUnit, @Notes,
                        @Keywords, @CreatedBy, @CreatedDate, @ModifiedBy, @ModifiedDate
                    );
                    SELECT last_insert_rowid();";

                var parameters = GetCategoryParameters(category);
                var result = DatabaseConnection.ExecuteScalar(query, parameters);
                var categoryId = Convert.ToInt32(result);

                _logger?.LogInformation($"تم إضافة فئة جديدة برقم {categoryId}");
                return categoryId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إضافة الفئة {category.CategoryCode}");
                throw new Exception($"خطأ في إضافة الفئة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث بيانات فئة
        /// </summary>
        /// <param name="category">بيانات الفئة المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateCategory(Category category)
        {
            try
            {
                var query = @"
                    UPDATE Categories SET
                        CategoryCode = @CategoryCode,
                        CategoryName = @CategoryName,
                        CategoryNameEn = @CategoryNameEn,
                        Description = @Description,
                        ParentCategoryID = @ParentCategoryID,
                        Level = @Level,
                        CategoryPath = @CategoryPath,
                        SortOrder = @SortOrder,
                        ImagePath = @ImagePath,
                        Icon = @Icon,
                        Color = @Color,
                        IsActive = @IsActive,
                        IsVisible = @IsVisible,
                        AllowProducts = @AllowProducts,
                        DefaultProfitMargin = @DefaultProfitMargin,
                        DefaultTaxRate = @DefaultTaxRate,
                        DefaultUnit = @DefaultUnit,
                        Notes = @Notes,
                        Keywords = @Keywords,
                        ModifiedBy = @ModifiedBy,
                        ModifiedDate = @ModifiedDate
                    WHERE CategoryID = @CategoryID";

                var parameters = GetCategoryParameters(category);
                Array.Resize(ref parameters, parameters.Length + 1);
                parameters[parameters.Length - 1] = new SqlParameter("@CategoryID", category.CategoryID);

                var rowsAffected = DatabaseConnection.ExecuteNonQuery(query, parameters);
                var success = rowsAffected > 0;

                if (success)
                    _logger?.LogInformation($"تم تحديث الفئة {category.CategoryCode}");

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث الفئة {category.CategoryCode}");
                throw new Exception($"خطأ في تحديث الفئة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حذف فئة
        /// </summary>
        /// <param name="categoryId">رقم الفئة</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public bool DeleteCategory(int categoryId)
        {
            try
            {
                // التحقق من وجود منتجات أو فئات فرعية
                var checkQuery = @"
                    SELECT COUNT(*) FROM Products WHERE CategoryID = @CategoryID
                    UNION ALL
                    SELECT COUNT(*) FROM Categories WHERE ParentCategoryID = @CategoryID";

                var checkParams = new SqlParameter[] { new SqlParameter("@CategoryID", categoryId) };
                var checkResult = DatabaseConnection.ExecuteQuery(checkQuery, checkParams);

                var hasItems = false;
                foreach (DataRow row in checkResult.Rows)
                {
                    if (Convert.ToInt32(row[0]) > 0)
                    {
                        hasItems = true;
                        break;
                    }
                }

                if (hasItems)
                {
                    // إذا كان هناك منتجات أو فئات فرعية، قم بإلغاء تفعيل الفئة
                    var deactivateQuery = "UPDATE Categories SET IsActive = 0, ModifiedDate = @ModifiedDate WHERE CategoryID = @CategoryID";
                    var deactivateParams = new SqlParameter[]
                    {
                        new SqlParameter("@CategoryID", categoryId),
                        new SqlParameter("@ModifiedDate", DateTime.Now)
                    };

                    var rowsAffected = DatabaseConnection.ExecuteNonQuery(deactivateQuery, deactivateParams);
                    var success = rowsAffected > 0;

                    if (success)
                        _logger?.LogInformation($"تم إلغاء تفعيل الفئة {categoryId}");

                    return success;
                }
                else
                {
                    // حذف الفئة نهائياً
                    var deleteQuery = "DELETE FROM Categories WHERE CategoryID = @CategoryID";
                    var deleteParams = new SqlParameter[] { new SqlParameter("@CategoryID", categoryId) };

                    var rowsAffected = DatabaseConnection.ExecuteNonQuery(deleteQuery, deleteParams);
                    var success = rowsAffected > 0;

                    if (success)
                        _logger?.LogInformation($"تم حذف الفئة {categoryId}");

                    return success;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حذف الفئة {categoryId}");
                throw new Exception($"خطأ في حذف الفئة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على فئة بالرقم
        /// </summary>
        /// <param name="categoryId">رقم الفئة</param>
        /// <returns>بيانات الفئة أو null</returns>
        public Category GetCategoryById(int categoryId)
        {
            try
            {
                var query = @"
                    SELECT c.*, pc.CategoryName as ParentCategoryName
                    FROM Categories c
                    LEFT JOIN Categories pc ON c.ParentCategoryID = pc.CategoryID
                    WHERE c.CategoryID = @CategoryID";

                var parameters = new SqlParameter[] { new SqlParameter("@CategoryID", categoryId) };
                var result = DatabaseConnection.ExecuteQuery(query, parameters);

                if (result.Rows.Count > 0)
                {
                    return MapDataRowToCategory(result.Rows[0]);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الفئة {categoryId}");
                throw new Exception($"خطأ في الحصول على الفئة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على فئة بالكود
        /// </summary>
        /// <param name="categoryCode">كود الفئة</param>
        /// <returns>بيانات الفئة أو null</returns>
        public Category GetCategoryByCode(string categoryCode)
        {
            try
            {
                var query = @"
                    SELECT c.*, pc.CategoryName as ParentCategoryName
                    FROM Categories c
                    LEFT JOIN Categories pc ON c.ParentCategoryID = pc.CategoryID
                    WHERE c.CategoryCode = @CategoryCode";

                var parameters = new SqlParameter[] { new SqlParameter("@CategoryCode", categoryCode) };
                var result = DatabaseConnection.ExecuteQuery(query, parameters);

                if (result.Rows.Count > 0)
                {
                    return MapDataRowToCategory(result.Rows[0]);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الفئة بالكود {categoryCode}");
                throw new Exception($"خطأ في الحصول على الفئة بالكود: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على جميع الفئات
        /// </summary>
        /// <param name="includeInactive">تضمين الفئات غير النشطة</param>
        /// <returns>قائمة الفئات</returns>
        public List<Category> GetAllCategories(bool includeInactive = false)
        {
            try
            {
                var query = @"
                    SELECT c.*, pc.CategoryName as ParentCategoryName
                    FROM Categories c
                    LEFT JOIN Categories pc ON c.ParentCategoryID = pc.CategoryID";

                if (!includeInactive)
                    query += " WHERE c.IsActive = 1";

                query += " ORDER BY c.Level, c.SortOrder, c.CategoryName";

                var result = DatabaseConnection.ExecuteQuery(query);
                var categories = new List<Category>();

                foreach (DataRow row in result.Rows)
                {
                    categories.Add(MapDataRowToCategory(row));
                }

                return categories;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على جميع الفئات");
                throw new Exception($"خطأ في الحصول على جميع الفئات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على الفئات الرئيسية
        /// </summary>
        /// <param name="includeInactive">تضمين الفئات غير النشطة</param>
        /// <returns>قائمة الفئات الرئيسية</returns>
        public List<Category> GetRootCategories(bool includeInactive = false)
        {
            try
            {
                var query = @"
                    SELECT c.*, pc.CategoryName as ParentCategoryName
                    FROM Categories c
                    LEFT JOIN Categories pc ON c.ParentCategoryID = pc.CategoryID
                    WHERE c.ParentCategoryID IS NULL";

                if (!includeInactive)
                    query += " AND c.IsActive = 1";

                query += " ORDER BY c.SortOrder, c.CategoryName";

                var result = DatabaseConnection.ExecuteQuery(query);
                var categories = new List<Category>();

                foreach (DataRow row in result.Rows)
                {
                    categories.Add(MapDataRowToCategory(row));
                }

                return categories;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على الفئات الرئيسية");
                throw new Exception($"خطأ في الحصول على الفئات الرئيسية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على الفئات الفرعية
        /// </summary>
        /// <param name="parentCategoryId">رقم الفئة الأب</param>
        /// <param name="includeInactive">تضمين الفئات غير النشطة</param>
        /// <returns>قائمة الفئات الفرعية</returns>
        public List<Category> GetSubCategories(int parentCategoryId, bool includeInactive = false)
        {
            try
            {
                var query = @"
                    SELECT c.*, pc.CategoryName as ParentCategoryName
                    FROM Categories c
                    LEFT JOIN Categories pc ON c.ParentCategoryID = pc.CategoryID
                    WHERE c.ParentCategoryID = @ParentCategoryID";

                if (!includeInactive)
                    query += " AND c.IsActive = 1";

                query += " ORDER BY c.SortOrder, c.CategoryName";

                var parameters = new SqlParameter[] { new SqlParameter("@ParentCategoryID", parentCategoryId) };
                var result = DatabaseConnection.ExecuteQuery(query, parameters);
                var categories = new List<Category>();

                foreach (DataRow row in result.Rows)
                {
                    categories.Add(MapDataRowToCategory(row));
                }

                return categories;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الفئات الفرعية للفئة {parentCategoryId}");
                throw new Exception($"خطأ في الحصول على الفئات الفرعية: {ex.Message}", ex);
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// إنشاء معاملات الفئة
        /// </summary>
        /// <param name="category">بيانات الفئة</param>
        /// <returns>مصفوفة المعاملات</returns>
        private SqlParameter[] GetCategoryParameters(Category category)
        {
            return new SqlParameter[]
            {
                new SqlParameter("@CategoryCode", category.CategoryCode),
                new SqlParameter("@CategoryName", category.CategoryName),
                new SqlParameter("@CategoryNameEn", category.CategoryNameEn ?? (object)DBNull.Value),
                new SqlParameter("@Description", category.Description ?? (object)DBNull.Value),
                new SqlParameter("@ParentCategoryID", category.ParentCategoryID ?? (object)DBNull.Value),
                new SqlParameter("@Level", category.Level),
                new SqlParameter("@CategoryPath", category.CategoryPath ?? (object)DBNull.Value),
                new SqlParameter("@SortOrder", category.SortOrder),
                new SqlParameter("@ImagePath", category.ImagePath ?? (object)DBNull.Value),
                new SqlParameter("@Icon", category.Icon ?? (object)DBNull.Value),
                new SqlParameter("@Color", category.Color ?? (object)DBNull.Value),
                new SqlParameter("@IsActive", category.IsActive),
                new SqlParameter("@IsVisible", category.IsVisible),
                new SqlParameter("@AllowProducts", category.AllowProducts),
                new SqlParameter("@DefaultProfitMargin", category.DefaultProfitMargin),
                new SqlParameter("@DefaultTaxRate", category.DefaultTaxRate),
                new SqlParameter("@DefaultUnit", category.DefaultUnit),
                new SqlParameter("@Notes", category.Notes ?? (object)DBNull.Value),
                new SqlParameter("@Keywords", category.Keywords ?? (object)DBNull.Value),
                new SqlParameter("@CreatedBy", category.CreatedBy ?? (object)DBNull.Value),
                new SqlParameter("@CreatedDate", category.CreatedDate),
                new SqlParameter("@ModifiedBy", category.ModifiedBy ?? (object)DBNull.Value),
                new SqlParameter("@ModifiedDate", category.ModifiedDate)
            };
        }

        /// <summary>
        /// تحويل صف البيانات إلى كائن فئة
        /// </summary>
        /// <param name="row">صف البيانات</param>
        /// <returns>كائن الفئة</returns>
        private Category MapDataRowToCategory(DataRow row)
        {
            return new Category
            {
                CategoryID = Convert.ToInt32(row["CategoryID"]),
                CategoryCode = row["CategoryCode"].ToString(),
                CategoryName = row["CategoryName"].ToString(),
                CategoryNameEn = row.IsNull("CategoryNameEn") ? null : row["CategoryNameEn"].ToString(),
                Description = row.IsNull("Description") ? null : row["Description"].ToString(),
                ParentCategoryID = row.IsNull("ParentCategoryID") ? null : Convert.ToInt32(row["ParentCategoryID"]),
                ParentCategoryName = row.IsNull("ParentCategoryName") ? null : row["ParentCategoryName"].ToString(),
                Level = Convert.ToInt32(row["Level"]),
                CategoryPath = row.IsNull("CategoryPath") ? null : row["CategoryPath"].ToString(),
                SortOrder = Convert.ToInt32(row["SortOrder"]),
                ImagePath = row.IsNull("ImagePath") ? null : row["ImagePath"].ToString(),
                Icon = row.IsNull("Icon") ? null : row["Icon"].ToString(),
                Color = row.IsNull("Color") ? null : row["Color"].ToString(),
                IsActive = Convert.ToBoolean(row["IsActive"]),
                IsVisible = Convert.ToBoolean(row["IsVisible"]),
                AllowProducts = Convert.ToBoolean(row["AllowProducts"]),
                DefaultProfitMargin = Convert.ToDecimal(row["DefaultProfitMargin"]),
                DefaultTaxRate = Convert.ToDecimal(row["DefaultTaxRate"]),
                DefaultUnit = row["DefaultUnit"].ToString(),
                Notes = row.IsNull("Notes") ? null : row["Notes"].ToString(),
                Keywords = row.IsNull("Keywords") ? null : row["Keywords"].ToString(),
                CreatedBy = row.IsNull("CreatedBy") ? null : row["CreatedBy"].ToString(),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                ModifiedBy = row.IsNull("ModifiedBy") ? null : row["ModifiedBy"].ToString(),
                ModifiedDate = Convert.ToDateTime(row["ModifiedDate"])
            };
        }

        #endregion
    }
}
