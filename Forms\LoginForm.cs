using System;
using System.Drawing;
using System.Windows.Forms;
using AredooPOS.BLL;
using AredooPOS.Models;
using AredooPOS.Services;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// واجهة تسجيل الدخول
    /// توفر واجهة آمنة وسهلة الاستخدام لتسجيل دخول المستخدمين
    /// </summary>
    public partial class LoginForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly AuthenticationBLL _authBLL;
        private readonly ILogger<LoginForm> _logger;

        // ألوان النظام
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SuccessColor = Color.FromArgb(46, 204, 113);
        private readonly Color DangerColor = Color.FromArgb(231, 76, 60);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);
        private readonly Color DarkGray = Color.FromArgb(52, 73, 94);

        // نتيجة تسجيل الدخول
        public LoginResult LoginResult { get; private set; }
        public bool IsLoginSuccessful => LoginResult?.IsSuccessful == true;

        // عدد محاولات تسجيل الدخول الفاشلة
        private int _failedAttempts = 0;
        private const int MaxFailedAttempts = 3;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ واجهة تسجيل الدخول
        /// </summary>
        /// <param name="logger">مسجل الأحداث</param>
        public LoginForm(ILogger<LoginForm> logger = null)
        {
            _authBLL = new AuthenticationBLL();
            _logger = logger;

            InitializeComponent();
            InitializeArabicUI();
            SetupEventHandlers();
        }

        /// <summary>
        /// تهيئة الواجهة العربية
        /// </summary>
        private void InitializeArabicUI()
        {
            // إعدادات النموذج الأساسية
            this.Font = new Font("Tahoma", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "تسجيل الدخول - نظام أريدو لنقاط البيع";
            this.Size = new Size(450, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = LightGray;

            // تطبيق الألوان والأنماط
            ApplyThemeColors();
            UpdateUITexts();
        }

        /// <summary>
        /// تطبيق ألوان النظام
        /// </summary>
        private void ApplyThemeColors()
        {
            // لوحة الرأس
            pnlHeader.BackColor = PrimaryColor;
            lblTitle.ForeColor = Color.White;
            lblSubtitle.ForeColor = Color.White;

            // لوحة تسجيل الدخول
            pnlLogin.BackColor = Color.White;
            pnlLogin.BorderStyle = BorderStyle.None;

            // حقول الإدخال
            txtUsername.BorderStyle = BorderStyle.FixedSingle;
            txtPassword.BorderStyle = BorderStyle.FixedSingle;
            txtUsername.BackColor = Color.White;
            txtPassword.BackColor = Color.White;

            // أزرار العمليات
            btnLogin.BackColor = PrimaryColor;
            btnLogin.ForeColor = Color.White;
            btnLogin.FlatStyle = FlatStyle.Flat;
            btnLogin.FlatAppearance.BorderSize = 0;

            btnCancel.BackColor = Color.Gray;
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.FlatAppearance.BorderSize = 0;

            btnForgotPassword.BackColor = Color.Transparent;
            btnForgotPassword.ForeColor = PrimaryColor;
            btnForgotPassword.FlatStyle = FlatStyle.Flat;
            btnForgotPassword.FlatAppearance.BorderSize = 0;

            // رسائل الحالة
            lblMessage.ForeColor = DangerColor;
            lblMessage.Visible = false;

            // شريط التقدم
            progressBar.Visible = false;
            progressBar.Style = ProgressBarStyle.Marquee;
        }

        /// <summary>
        /// تحديث النصوص في الواجهة
        /// </summary>
        private void UpdateUITexts()
        {
            lblTitle.Text = "نظام أريدو لنقاط البيع";
            lblSubtitle.Text = "نظام إدارة شامل للمبيعات والمخزون";
            
            lblUsername.Text = "اسم المستخدم:";
            lblPassword.Text = "كلمة المرور:";
            
            btnLogin.Text = "تسجيل الدخول";
            btnCancel.Text = "إلغاء";
            btnForgotPassword.Text = "نسيت كلمة المرور؟";
            
            chkRememberMe.Text = "تذكرني";
            chkShowPassword.Text = "إظهار كلمة المرور";
            
            lblVersion.Text = $"الإصدار {Application.ProductVersion}";
            lblCopyright.Text = "© 2024 جميع الحقوق محفوظة";
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // أحداث الأزرار
            btnLogin.Click += BtnLogin_Click;
            btnCancel.Click += BtnCancel_Click;
            btnForgotPassword.Click += BtnForgotPassword_Click;

            // أحداث حقول الإدخال
            txtUsername.KeyPress += TxtInput_KeyPress;
            txtPassword.KeyPress += TxtInput_KeyPress;
            txtUsername.TextChanged += TxtInput_TextChanged;
            txtPassword.TextChanged += TxtInput_TextChanged;

            // أحداث خانات الاختيار
            chkShowPassword.CheckedChanged += ChkShowPassword_CheckedChanged;

            // أحداث النموذج
            this.Load += LoginForm_Load;
            this.Shown += LoginForm_Shown;
        }

        #endregion

        #region معالجات الأحداث

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void LoginForm_Load(object sender, EventArgs e)
        {
            // تحميل اسم المستخدم المحفوظ
            LoadRememberedUsername();
            
            // تعطيل زر تسجيل الدخول في البداية
            UpdateLoginButtonState();
            
            _logger?.LogInformation("تم تحميل واجهة تسجيل الدخول");
        }

        /// <summary>
        /// حدث إظهار النموذج
        /// </summary>
        private void LoginForm_Shown(object sender, EventArgs e)
        {
            // التركيز على حقل اسم المستخدم
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
                txtUsername.Focus();
            else
                txtPassword.Focus();
        }

        /// <summary>
        /// تسجيل الدخول
        /// </summary>
        private async void BtnLogin_Click(object sender, EventArgs e)
        {
            await PerformLogin();
        }

        /// <summary>
        /// إلغاء تسجيل الدخول
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// نسيت كلمة المرور
        /// </summary>
        private void BtnForgotPassword_Click(object sender, EventArgs e)
        {
            // TODO: تنفيذ واجهة استعادة كلمة المرور
            MessageBox.Show("يرجى الاتصال بمدير النظام لاستعادة كلمة المرور", "استعادة كلمة المرور", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// ضغط مفتاح في حقول الإدخال
        /// </summary>
        private async void TxtInput_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true;
                
                if (sender == txtUsername && string.IsNullOrWhiteSpace(txtUsername.Text))
                    return;
                
                if (sender == txtUsername)
                    txtPassword.Focus();
                else if (sender == txtPassword)
                    await PerformLogin();
            }
        }

        /// <summary>
        /// تغيير نص حقول الإدخال
        /// </summary>
        private void TxtInput_TextChanged(object sender, EventArgs e)
        {
            UpdateLoginButtonState();
            HideMessage();
        }

        /// <summary>
        /// تغيير حالة إظهار كلمة المرور
        /// </summary>
        private void ChkShowPassword_CheckedChanged(object sender, EventArgs e)
        {
            txtPassword.UseSystemPasswordChar = !chkShowPassword.Checked;
        }

        #endregion

        #region عمليات تسجيل الدخول

        /// <summary>
        /// تنفيذ عملية تسجيل الدخول
        /// </summary>
        private async System.Threading.Tasks.Task PerformLogin()
        {
            try
            {
                // التحقق من صحة المدخلات
                if (!ValidateInput())
                    return;

                // إظهار حالة التحميل
                SetLoadingState(true);

                var username = txtUsername.Text.Trim();
                var password = txtPassword.Text;
                var ipAddress = GetClientIPAddress();
                var userAgent = GetUserAgent();
                var deviceName = Environment.MachineName;

                // محاولة تسجيل الدخول
                LoginResult = _authBLL.Login(username, password, ipAddress, userAgent, deviceName);

                if (LoginResult.IsSuccessful)
                {
                    // حفظ اسم المستخدم إذا كان مطلوباً
                    if (chkRememberMe.Checked)
                        SaveRememberedUsername(username);
                    else
                        ClearRememberedUsername();

                    // التحقق من ضرورة تغيير كلمة المرور
                    if (LoginResult.MustChangePassword)
                    {
                        ShowMessage("يجب تغيير كلمة المرور قبل المتابعة", MessageType.Warning);
                        
                        // TODO: فتح واجهة تغيير كلمة المرور
                        // var changePasswordForm = new ChangePasswordForm(LoginResult.User);
                        // if (changePasswordForm.ShowDialog() != DialogResult.OK)
                        // {
                        //     LoginResult = null;
                        //     return;
                        // }
                    }

                    ShowMessage("تم تسجيل الدخول بنجاح", MessageType.Success);
                    
                    // إغلاق النموذج بنجاح
                    await System.Threading.Tasks.Task.Delay(1000); // إظهار رسالة النجاح لثانية واحدة
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    // تسجيل محاولة فاشلة
                    _failedAttempts++;
                    
                    ShowMessage(LoginResult.ErrorMessage, MessageType.Error);
                    
                    // قفل النموذج بعد عدد معين من المحاولات الفاشلة
                    if (_failedAttempts >= MaxFailedAttempts)
                    {
                        ShowMessage($"تم تجاوز الحد الأقصى للمحاولات ({MaxFailedAttempts}). سيتم إغلاق النظام.", MessageType.Error);
                        await System.Threading.Tasks.Task.Delay(3000);
                        Application.Exit();
                    }
                    
                    // مسح كلمة المرور
                    txtPassword.Clear();
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في عملية تسجيل الدخول");
                ShowMessage("حدث خطأ في النظام، يرجى المحاولة مرة أخرى", MessageType.Error);
            }
            finally
            {
                SetLoadingState(false);
            }
        }

        /// <summary>
        /// التحقق من صحة المدخلات
        /// </summary>
        /// <returns>true إذا كانت المدخلات صحيحة</returns>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                ShowMessage("يرجى إدخال اسم المستخدم", MessageType.Warning);
                txtUsername.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                ShowMessage("يرجى إدخال كلمة المرور", MessageType.Warning);
                txtPassword.Focus();
                return false;
            }

            if (txtUsername.Text.Trim().Length < 3)
            {
                ShowMessage("اسم المستخدم يجب أن يكون 3 أحرف على الأقل", MessageType.Warning);
                txtUsername.Focus();
                return false;
            }

            return true;
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// تحديث حالة زر تسجيل الدخول
        /// </summary>
        private void UpdateLoginButtonState()
        {
            btnLogin.Enabled = !string.IsNullOrWhiteSpace(txtUsername.Text) && 
                              !string.IsNullOrWhiteSpace(txtPassword.Text);
        }

        /// <summary>
        /// تعيين حالة التحميل
        /// </summary>
        /// <param name="isLoading">هل في حالة تحميل</param>
        private void SetLoadingState(bool isLoading)
        {
            btnLogin.Enabled = !isLoading;
            btnCancel.Enabled = !isLoading;
            txtUsername.Enabled = !isLoading;
            txtPassword.Enabled = !isLoading;
            progressBar.Visible = isLoading;
            
            if (isLoading)
            {
                btnLogin.Text = "جاري تسجيل الدخول...";
                this.Cursor = Cursors.WaitCursor;
            }
            else
            {
                btnLogin.Text = "تسجيل الدخول";
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// إظهار رسالة للمستخدم
        /// </summary>
        /// <param name="message">نص الرسالة</param>
        /// <param name="type">نوع الرسالة</param>
        private void ShowMessage(string message, MessageType type)
        {
            lblMessage.Text = message;
            lblMessage.ForeColor = type switch
            {
                MessageType.Success => SuccessColor,
                MessageType.Warning => Color.Orange,
                MessageType.Error => DangerColor,
                _ => DarkGray
            };
            lblMessage.Visible = true;
        }

        /// <summary>
        /// إخفاء الرسالة
        /// </summary>
        private void HideMessage()
        {
            lblMessage.Visible = false;
        }

        /// <summary>
        /// الحصول على عنوان IP للعميل
        /// </summary>
        /// <returns>عنوان IP</returns>
        private string GetClientIPAddress()
        {
            try
            {
                var hostName = System.Net.Dns.GetHostName();
                var addresses = System.Net.Dns.GetHostAddresses(hostName);
                
                foreach (var address in addresses)
                {
                    if (address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                        return address.ToString();
                }
                
                return "127.0.0.1";
            }
            catch
            {
                return "127.0.0.1";
            }
        }

        /// <summary>
        /// الحصول على معلومات المتصفح/التطبيق
        /// </summary>
        /// <returns>معلومات المتصفح</returns>
        private string GetUserAgent()
        {
            return $"AredooPOS Desktop Application {Application.ProductVersion} on {Environment.OSVersion}";
        }

        /// <summary>
        /// تحميل اسم المستخدم المحفوظ
        /// </summary>
        private void LoadRememberedUsername()
        {
            try
            {
                var rememberedUsername = Properties.Settings.Default.RememberedUsername;
                if (!string.IsNullOrWhiteSpace(rememberedUsername))
                {
                    txtUsername.Text = rememberedUsername;
                    chkRememberMe.Checked = true;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل اسم المستخدم المحفوظ");
            }
        }

        /// <summary>
        /// حفظ اسم المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        private void SaveRememberedUsername(string username)
        {
            try
            {
                Properties.Settings.Default.RememberedUsername = username;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ اسم المستخدم");
            }
        }

        /// <summary>
        /// مسح اسم المستخدم المحفوظ
        /// </summary>
        private void ClearRememberedUsername()
        {
            try
            {
                Properties.Settings.Default.RememberedUsername = string.Empty;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في مسح اسم المستخدم المحفوظ");
            }
        }

        #endregion
    }

    #region التعدادات المساعدة

    /// <summary>
    /// أنواع الرسائل
    /// </summary>
    public enum MessageType
    {
        Info,
        Success,
        Warning,
        Error
    }

    #endregion
}
