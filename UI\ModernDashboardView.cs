using System;
using System.Drawing;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// لوحة المعلومات الحديثة
    /// </summary>
    public class ModernDashboardView : UserControl
    {
        #region المتغيرات

        private Panel _statsPanel;
        private Panel _actionsPanel;
        private Panel _alertsPanel;

        #endregion

        #region البناء والتهيئة

        public ModernDashboardView()
        {
            InitializeComponent();
            CreateContent();
        }

        private void InitializeComponent()
        {
            BackColor = ModernDesign.Colors.Background;
            Dock = DockStyle.Fill;
            RightToLeft = RightToLeft.Yes;
            AutoScroll = true;
        }

        private void CreateContent()
        {
            var yPosition = ModernDesign.Spacing.Large;

            // عنوان اللوحة
            var titleLabel = ModernDesign.CreateModernLabel(
                "📊 لوحة المعلومات",
                ModernDesign.Fonts.Title,
                ModernDesign.Colors.TextPrimary
            );
            titleLabel.Size = new Size(Width - 40, 40);
            titleLabel.Location = new Point(20, yPosition);
            Controls.Add(titleLabel);
            yPosition += 60;

            // بطاقات الإحصائيات
            CreateStatsCards(yPosition);
            yPosition += 140;

            // الإجراءات السريعة
            CreateQuickActions(yPosition);
            yPosition += 200;

            // التنبيهات
            CreateAlertsSection(yPosition);
        }

        private void CreateStatsCards(int yPosition)
        {
            _statsPanel = new Panel
            {
                Size = new Size(Width - 40, 120),
                Location = new Point(20, yPosition),
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            var cardWidth = (_statsPanel.Width - 60) / 4;
            var cardSpacing = 20;

            var stats = new[]
            {
                new { Title = "مبيعات اليوم", Value = "15,750 ر.س", Icon = "💰", Color = ModernDesign.Colors.Success },
                new { Title = "عدد الفواتير", Value = "47", Icon = "📄", Color = ModernDesign.Colors.Primary },
                new { Title = "العملاء الجدد", Value = "12", Icon = "👥", Color = ModernDesign.Colors.Info },
                new { Title = "المنتجات", Value = "234", Icon = "📦", Color = ModernDesign.Colors.Warning }
            };

            for (int i = 0; i < stats.Length; i++)
            {
                var stat = stats[i];
                var card = CreateStatCard(stat.Title, stat.Value, stat.Icon, stat.Color);
                card.Size = new Size(cardWidth, 100);
                card.Location = new Point(i * (cardWidth + cardSpacing), 10);
                _statsPanel.Controls.Add(card);
            }

            Controls.Add(_statsPanel);
        }

        private Panel CreateStatCard(string title, string value, string icon, Color accentColor)
        {
            var card = ModernDesign.CreateModernPanel(ModernDesign.Colors.Surface);
            card.Padding = new Padding(ModernDesign.Spacing.Medium);

            // الأيقونة
            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", 24),
                ForeColor = accentColor,
                Size = new Size(40, 40),
                Location = new Point(card.Width - 50, 10),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // القيمة
            var valueLabel = ModernDesign.CreateModernLabel(
                value,
                ModernDesign.Fonts.Heading,
                ModernDesign.Colors.TextPrimary
            );
            valueLabel.Size = new Size(card.Width - 60, 30);
            valueLabel.Location = new Point(10, 15);

            // العنوان
            var titleLabel = ModernDesign.CreateModernLabel(
                title,
                ModernDesign.Fonts.Caption,
                ModernDesign.Colors.TextSecondary
            );
            titleLabel.Size = new Size(card.Width - 60, 20);
            titleLabel.Location = new Point(10, 50);

            card.Controls.AddRange(new Control[] { iconLabel, valueLabel, titleLabel });
            return card;
        }

        private void CreateQuickActions(int yPosition)
        {
            var actionsTitle = ModernDesign.CreateModernLabel(
                "⚡ الإجراءات السريعة",
                ModernDesign.Fonts.Subheading,
                ModernDesign.Colors.TextPrimary
            );
            actionsTitle.Size = new Size(Width - 40, 30);
            actionsTitle.Location = new Point(20, yPosition);
            Controls.Add(actionsTitle);

            _actionsPanel = new Panel
            {
                Size = new Size(Width - 40, 150),
                Location = new Point(20, yPosition + 40),
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            var buttonWidth = (_actionsPanel.Width - 60) / 3;
            var buttonSpacing = 30;

            var actions = new[]
            {
                new { Text = "💰 إنشاء فاتورة جديدة", Color = ModernDesign.Colors.Primary },
                new { Text = "👤 إضافة عميل جديد", Color = ModernDesign.Colors.Success },
                new { Text = "📦 إضافة منتج جديد", Color = ModernDesign.Colors.Info }
            };

            for (int i = 0; i < actions.Length; i++)
            {
                var action = actions[i];
                var button = ModernDesign.CreateModernButton(
                    action.Text,
                    action.Color,
                    Color.White,
                    (s, e) => HandleQuickAction(action.Text)
                );
                button.Size = new Size(buttonWidth, 60);
                button.Location = new Point(i * (buttonWidth + buttonSpacing), 20);
                _actionsPanel.Controls.Add(button);
            }

            Controls.Add(_actionsPanel);
        }

        private void CreateAlertsSection(int yPosition)
        {
            var alertsTitle = ModernDesign.CreateModernLabel(
                "🔔 التنبيهات والإشعارات",
                ModernDesign.Fonts.Subheading,
                ModernDesign.Colors.TextPrimary
            );
            alertsTitle.Size = new Size(Width - 40, 30);
            alertsTitle.Location = new Point(20, yPosition);
            Controls.Add(alertsTitle);

            _alertsPanel = ModernDesign.CreateModernPanel(ModernDesign.Colors.Surface);
            _alertsPanel.Size = new Size(Width - 40, 200);
            _alertsPanel.Location = new Point(20, yPosition + 40);
            _alertsPanel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;

            var alerts = new[]
            {
                "⚠️ منتج 'شامبو الأطفال' وصل للحد الأدنى (5 قطع متبقية)",
                "💳 قسط العميل 'أحمد محمد' مستحق اليوم (500 ر.س)",
                "📦 وصول شحنة جديدة من المنتجات (50 صنف)",
                "💰 تم تحقيق هدف المبيعات اليومي (15,000 ر.س)",
                "🔔 تذكير: إغلاق الصندوق في 6:00 مساءً"
            };

            var alertY = 20;
            foreach (var alert in alerts)
            {
                var alertLabel = ModernDesign.CreateModernLabel(
                    alert,
                    ModernDesign.Fonts.Body,
                    ModernDesign.Colors.TextPrimary
                );
                alertLabel.Size = new Size(_alertsPanel.Width - 40, 25);
                alertLabel.Location = new Point(20, alertY);
                _alertsPanel.Controls.Add(alertLabel);
                alertY += 30;
            }

            Controls.Add(_alertsPanel);
        }

        #endregion

        #region معالجة الأحداث

        private void HandleQuickAction(string actionText)
        {
            if (actionText.Contains("فاتورة"))
            {
                MessageBox.Show("سيتم فتح نموذج إنشاء فاتورة جديدة", "إنشاء فاتورة", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else if (actionText.Contains("عميل"))
            {
                MessageBox.Show("سيتم فتح نموذج إضافة عميل جديد", "إضافة عميل", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else if (actionText.Contains("منتج"))
            {
                MessageBox.Show("سيتم فتح نموذج إضافة منتج جديد", "إضافة منتج", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            
            // إعادة ترتيب العناصر عند تغيير الحجم
            if (_statsPanel != null)
            {
                _statsPanel.Width = Width - 40;
                var cardWidth = (_statsPanel.Width - 60) / 4;
                var cardSpacing = 20;
                
                for (int i = 0; i < _statsPanel.Controls.Count; i++)
                {
                    var card = _statsPanel.Controls[i];
                    card.Size = new Size(cardWidth, 100);
                    card.Location = new Point(i * (cardWidth + cardSpacing), 10);
                }
            }

            if (_actionsPanel != null)
            {
                _actionsPanel.Width = Width - 40;
                var buttonWidth = (_actionsPanel.Width - 60) / 3;
                var buttonSpacing = 30;
                
                for (int i = 0; i < _actionsPanel.Controls.Count; i++)
                {
                    var button = _actionsPanel.Controls[i];
                    button.Size = new Size(buttonWidth, 60);
                    button.Location = new Point(i * (buttonWidth + buttonSpacing), 20);
                }
            }

            if (_alertsPanel != null)
            {
                _alertsPanel.Width = Width - 40;
            }
        }

        #endregion
    }
}
