<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net472</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <AssemblyTitle>أريدو الكاشير - نظام نقاط البيع الحديث</AssemblyTitle>
    <AssemblyDescription>نظام كاشير عربي حديث مع تصميم Fluent وMaterial Design، متوافق مع Windows 7+</AssemblyDescription>
    <AssemblyCompany>أريدو للتقنية</AssemblyCompany>
    <AssemblyProduct>أريدو الكاشير</AssemblyProduct>
    <AssemblyCopyright>© 2024 أريدو للتقنية. جميع الحقوق محفوظة.</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <StartupObject>AredooCashier.AredooCashierProgram</StartupObject>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <Deterministic>true</Deterministic>
    <LangVersion>latest</LangVersion>
    <Nullable>disable</Nullable>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>

  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>false</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <!-- مراجع النظام -->
  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing.Design" />
  </ItemGroup>

  <!-- ملفات الكود -->
  <ItemGroup>
    <!-- البرنامج الرئيسي -->
    <Compile Include="AredooCashierProgram.cs" />
    <Compile Include="AredooCashierApp.cs" />
    
    <!-- نظام التصميم الحديث -->
    <Compile Include="UI\ModernDesignSystem.cs" />
    
    <!-- المكونات الأساسية -->
    <Compile Include="UI\ModernTopBar.cs" />
    <Compile Include="UI\ModernSidebar.cs" />
    <Compile Include="UI\ModernDashboard.cs" />
    <Compile Include="UI\ModernInvoiceForm.cs" />
  </ItemGroup>

  <!-- الموارد والأصول -->
  <ItemGroup>
    <Content Include="Assets\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <!-- المجلدات -->
  <ItemGroup>
    <Folder Include="Assets\" />
    <Folder Include="Resources\" />
    <Folder Include="UI\" />
    <Folder Include="Data\" />
    <Folder Include="Reports\" />
    <Folder Include="Receipts\" />
  </ItemGroup>

  <!-- إنشاء المجلدات المطلوبة -->
  <Target Name="CreateDirectories" BeforeTargets="Build">
    <MakeDir Directories="$(OutputPath)Assets" Condition="!Exists('$(OutputPath)Assets')" />
    <MakeDir Directories="$(OutputPath)Resources" Condition="!Exists('$(OutputPath)Resources')" />
    <MakeDir Directories="$(OutputPath)Data" Condition="!Exists('$(OutputPath)Data')" />
    <MakeDir Directories="$(OutputPath)Reports" Condition="!Exists('$(OutputPath)Reports')" />
    <MakeDir Directories="$(OutputPath)Receipts" Condition="!Exists('$(OutputPath)Receipts')" />
  </Target>

  <!-- إجراءات ما بعد البناء -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="echo 🎉 تم بناء تطبيق أريدو الكاشير الحديث بنجاح!" />
    <Exec Command="echo 📁 الملف التنفيذي: $(OutputPath)$(AssemblyName).exe" />
    <Exec Command="echo 🎨 تصميم حديث مع دعم كامل للغة العربية" />
    <Exec Command="echo 💻 متوافق مع Windows 7+ وجميع الإصدارات" />
    <Exec Command="echo 🚀 جاهز للاستخدام في نقاط البيع" />
  </Target>

  <!-- معلومات إضافية للنشر -->
  <PropertyGroup>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <ProductName>أريدو الكاشير - نظام نقاط البيع الحديث</ProductName>
    <PublisherName>أريدو للتقنية</PublisherName>
    <SuiteName>أريدو لحلول نقاط البيع</SuiteName>
  </PropertyGroup>

</Project>
