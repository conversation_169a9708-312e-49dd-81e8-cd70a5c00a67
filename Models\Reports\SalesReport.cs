using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace AredooPOS.Models.Reports
{
    /// <summary>
    /// نموذج تقرير المبيعات
    /// يحتوي على جميع البيانات المتعلقة بتقارير المبيعات اليومية والشهرية وحسب المنتج
    /// </summary>
    public class SalesReport
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم التقرير
        /// </summary>
        public int ReportID { get; set; }

        /// <summary>
        /// نوع التقرير
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ReportType { get; set; }

        /// <summary>
        /// تاريخ بداية التقرير
        /// </summary>
        [Required]
        public DateTime FromDate { get; set; }

        /// <summary>
        /// تاريخ نهاية التقرير
        /// </summary>
        [Required]
        public DateTime ToDate { get; set; }

        /// <summary>
        /// تاريخ إنشاء التقرير
        /// </summary>
        public DateTime GeneratedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// من أنشأ التقرير
        /// </summary>
        [Required]
        [StringLength(50)]
        public string GeneratedBy { get; set; }

        #endregion

        #region الإجماليات العامة

        /// <summary>
        /// إجمالي المبيعات
        /// </summary>
        public decimal TotalSales { get; set; }

        /// <summary>
        /// إجمالي الكمية المباعة
        /// </summary>
        public decimal TotalQuantitySold { get; set; }

        /// <summary>
        /// إجمالي عدد الفواتير
        /// </summary>
        public int TotalInvoices { get; set; }

        /// <summary>
        /// إجمالي عدد الأصناف المباعة
        /// </summary>
        public int TotalItemsSold { get; set; }

        /// <summary>
        /// متوسط قيمة الفاتورة
        /// </summary>
        public decimal AverageInvoiceValue { get; set; }

        /// <summary>
        /// إجمالي الخصومات
        /// </summary>
        public decimal TotalDiscounts { get; set; }

        /// <summary>
        /// إجمالي الضرائب
        /// </summary>
        public decimal TotalTaxes { get; set; }

        /// <summary>
        /// صافي المبيعات (بعد الخصم والضريبة)
        /// </summary>
        public decimal NetSales { get; set; }

        #endregion

        #region تفاصيل طرق الدفع

        /// <summary>
        /// المبيعات النقدية
        /// </summary>
        public decimal CashSales { get; set; }

        /// <summary>
        /// مبيعات البطاقات
        /// </summary>
        public decimal CardSales { get; set; }

        /// <summary>
        /// مبيعات التحويل البنكي
        /// </summary>
        public decimal TransferSales { get; set; }

        /// <summary>
        /// المبيعات الآجلة
        /// </summary>
        public decimal CreditSales { get; set; }

        /// <summary>
        /// مبيعات الأقساط
        /// </summary>
        public decimal InstallmentSales { get; set; }

        #endregion

        #region تفاصيل المرتجعات

        /// <summary>
        /// إجمالي المرتجعات
        /// </summary>
        public decimal TotalReturns { get; set; }

        /// <summary>
        /// عدد فواتير المرتجعات
        /// </summary>
        public int ReturnInvoicesCount { get; set; }

        /// <summary>
        /// كمية المرتجعات
        /// </summary>
        public decimal ReturnQuantity { get; set; }

        /// <summary>
        /// نسبة المرتجعات
        /// </summary>
        public decimal ReturnPercentage => TotalSales > 0 ? (TotalReturns / TotalSales) * 100 : 0;

        #endregion

        #region قوائم التفاصيل

        /// <summary>
        /// تفاصيل المبيعات حسب المنتج
        /// </summary>
        public List<ProductSalesDetail> ProductSalesDetails { get; set; } = new List<ProductSalesDetail>();

        /// <summary>
        /// تفاصيل المبيعات حسب الفئة
        /// </summary>
        public List<CategorySalesDetail> CategorySalesDetails { get; set; } = new List<CategorySalesDetail>();

        /// <summary>
        /// تفاصيل المبيعات حسب العميل
        /// </summary>
        public List<CustomerSalesDetail> CustomerSalesDetails { get; set; } = new List<CustomerSalesDetail>();

        /// <summary>
        /// تفاصيل المبيعات حسب المستخدم
        /// </summary>
        public List<UserSalesDetail> UserSalesDetails { get; set; } = new List<UserSalesDetail>();

        /// <summary>
        /// تفاصيل المبيعات اليومية
        /// </summary>
        public List<DailySalesDetail> DailySalesDetails { get; set; } = new List<DailySalesDetail>();

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// فترة التقرير بالأيام
        /// </summary>
        public int ReportPeriodDays => (ToDate - FromDate).Days + 1;

        /// <summary>
        /// متوسط المبيعات اليومية
        /// </summary>
        public decimal DailyAverageSales => ReportPeriodDays > 0 ? TotalSales / ReportPeriodDays : 0;

        /// <summary>
        /// متوسط عدد الفواتير اليومية
        /// </summary>
        public decimal DailyAverageInvoices => ReportPeriodDays > 0 ? (decimal)TotalInvoices / ReportPeriodDays : 0;

        /// <summary>
        /// نسبة الخصومات
        /// </summary>
        public decimal DiscountPercentage => TotalSales > 0 ? (TotalDiscounts / TotalSales) * 100 : 0;

        /// <summary>
        /// نسبة الضرائب
        /// </summary>
        public decimal TaxPercentage => TotalSales > 0 ? (TotalTaxes / TotalSales) * 100 : 0;

        /// <summary>
        /// أفضل منتج مبيعاً
        /// </summary>
        public ProductSalesDetail TopSellingProduct => ProductSalesDetails?.OrderByDescending(p => p.TotalSales).FirstOrDefault();

        /// <summary>
        /// أفضل فئة مبيعاً
        /// </summary>
        public CategorySalesDetail TopSellingCategory => CategorySalesDetails?.OrderByDescending(c => c.TotalSales).FirstOrDefault();

        /// <summary>
        /// أفضل عميل
        /// </summary>
        public CustomerSalesDetail TopCustomer => CustomerSalesDetails?.OrderByDescending(c => c.TotalSales).FirstOrDefault();

        /// <summary>
        /// أفضل مستخدم مبيعاً
        /// </summary>
        public UserSalesDetail TopSalesUser => UserSalesDetails?.OrderByDescending(u => u.TotalSales).FirstOrDefault();

        #endregion

        #region الدوال المساعدة

        /// <summary>
        /// حساب الإجماليات من التفاصيل
        /// </summary>
        public void CalculateTotals()
        {
            if (ProductSalesDetails?.Any() == true)
            {
                TotalSales = ProductSalesDetails.Sum(p => p.TotalSales);
                TotalQuantitySold = ProductSalesDetails.Sum(p => p.QuantitySold);
                TotalDiscounts = ProductSalesDetails.Sum(p => p.TotalDiscount);
                TotalTaxes = ProductSalesDetails.Sum(p => p.TotalTax);
                NetSales = TotalSales - TotalDiscounts + TotalTaxes;
            }

            if (DailySalesDetails?.Any() == true)
            {
                TotalInvoices = DailySalesDetails.Sum(d => d.InvoiceCount);
                AverageInvoiceValue = TotalInvoices > 0 ? TotalSales / TotalInvoices : 0;
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            if (FromDate > ToDate)
                return false;

            if (string.IsNullOrWhiteSpace(ReportType))
                return false;

            if (string.IsNullOrWhiteSpace(GeneratedBy))
                return false;

            return true;
        }

        /// <summary>
        /// الحصول على ملخص التقرير
        /// </summary>
        /// <returns>ملخص التقرير</returns>
        public string GetSummary()
        {
            return $"تقرير المبيعات من {FromDate:dd/MM/yyyy} إلى {ToDate:dd/MM/yyyy}\n" +
                   $"إجمالي المبيعات: {TotalSales:C}\n" +
                   $"عدد الفواتير: {TotalInvoices}\n" +
                   $"متوسط الفاتورة: {AverageInvoiceValue:C}";
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// تفاصيل مبيعات المنتج
    /// </summary>
    public class ProductSalesDetail
    {
        public int ProductID { get; set; }
        public string ProductName { get; set; }
        public string ProductCode { get; set; }
        public string CategoryName { get; set; }
        public decimal QuantitySold { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalCost { get; set; }
        public decimal TotalProfit { get; set; }
        public decimal TotalDiscount { get; set; }
        public decimal TotalTax { get; set; }
        public int InvoiceCount { get; set; }
        public decimal ProfitMargin => TotalSales > 0 ? (TotalProfit / TotalSales) * 100 : 0;
    }

    /// <summary>
    /// تفاصيل مبيعات الفئة
    /// </summary>
    public class CategorySalesDetail
    {
        public int CategoryID { get; set; }
        public string CategoryName { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalQuantity { get; set; }
        public decimal TotalProfit { get; set; }
        public int ProductCount { get; set; }
        public int InvoiceCount { get; set; }
        public decimal AveragePrice { get; set; }
    }

    /// <summary>
    /// تفاصيل مبيعات العميل
    /// </summary>
    public class CustomerSalesDetail
    {
        public int CustomerID { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalQuantity { get; set; }
        public int InvoiceCount { get; set; }
        public decimal AverageInvoiceValue { get; set; }
        public DateTime LastPurchaseDate { get; set; }
        public decimal TotalDiscount { get; set; }
    }

    /// <summary>
    /// تفاصيل مبيعات المستخدم
    /// </summary>
    public class UserSalesDetail
    {
        public int UserID { get; set; }
        public string UserName { get; set; }
        public string FullName { get; set; }
        public decimal TotalSales { get; set; }
        public int InvoiceCount { get; set; }
        public decimal AverageInvoiceValue { get; set; }
        public decimal TotalQuantity { get; set; }
        public decimal TotalDiscount { get; set; }
        public TimeSpan TotalWorkingHours { get; set; }
    }

    /// <summary>
    /// تفاصيل المبيعات اليومية
    /// </summary>
    public class DailySalesDetail
    {
        public DateTime SalesDate { get; set; }
        public decimal TotalSales { get; set; }
        public int InvoiceCount { get; set; }
        public decimal TotalQuantity { get; set; }
        public decimal AverageInvoiceValue { get; set; }
        public decimal TotalDiscount { get; set; }
        public decimal TotalTax { get; set; }
        public decimal TotalReturns { get; set; }
        public int ReturnCount { get; set; }
        public string DayName => SalesDate.ToString("dddd");
    }

    #endregion

    #region التعدادات

    /// <summary>
    /// أنواع تقارير المبيعات
    /// </summary>
    public static class SalesReportTypes
    {
        public const string Daily = "Daily";
        public const string Weekly = "Weekly";
        public const string Monthly = "Monthly";
        public const string Yearly = "Yearly";
        public const string Custom = "Custom";
        public const string ByProduct = "ByProduct";
        public const string ByCategory = "ByCategory";
        public const string ByCustomer = "ByCustomer";
        public const string ByUser = "ByUser";
    }

    #endregion
}
