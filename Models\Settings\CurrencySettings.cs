using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Linq;

namespace AredooPOS.Models.Settings
{
    /// <summary>
    /// نموذج إعدادات العملة
    /// يحتوي على جميع الإعدادات المتعلقة بالعملات وأسعار الصرف
    /// </summary>
    public class CurrencySettings
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم إعداد العملة
        /// </summary>
        public int CurrencySettingID { get; set; }

        /// <summary>
        /// العملة الافتراضية
        /// </summary>
        [Required]
        [StringLength(3)]
        public string DefaultCurrencyCode { get; set; } = "IQD";

        /// <summary>
        /// عدد الأرقام العشرية
        /// </summary>
        [Range(0, 4)]
        public int DecimalPlaces { get; set; } = 0;

        /// <summary>
        /// رمز العملة
        /// </summary>
        [Required]
        [StringLength(10)]
        public string CurrencySymbol { get; set; } = "د.ع";

        /// <summary>
        /// موضع رمز العملة
        /// </summary>
        [StringLength(10)]
        public string SymbolPosition { get; set; } = "After"; // Before, After

        /// <summary>
        /// فاصل الآلاف
        /// </summary>
        [StringLength(1)]
        public string ThousandsSeparator { get; set; } = ",";

        /// <summary>
        /// فاصل العشرية
        /// </summary>
        [StringLength(1)]
        public string DecimalSeparator { get; set; } = ".";

        /// <summary>
        /// هل تدعم عملات متعددة
        /// </summary>
        public bool SupportMultipleCurrencies { get; set; } = true;

        /// <summary>
        /// تحديث أسعار الصرف تلقائياً
        /// </summary>
        public bool AutoUpdateExchangeRates { get; set; } = false;

        /// <summary>
        /// مصدر أسعار الصرف
        /// </summary>
        [StringLength(100)]
        public string ExchangeRateSource { get; set; } = "Manual";

        /// <summary>
        /// تاريخ آخر تحديث لأسعار الصرف
        /// </summary>
        public DateTime? LastExchangeRateUpdate { get; set; }

        #endregion

        #region قوائم العملات

        /// <summary>
        /// قائمة العملات المدعومة
        /// </summary>
        public List<Currency> SupportedCurrencies { get; set; } = new List<Currency>();

        /// <summary>
        /// قائمة أسعار الصرف
        /// </summary>
        public List<ExchangeRate> ExchangeRates { get; set; } = new List<ExchangeRate>();

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// العملة الافتراضية
        /// </summary>
        public Currency DefaultCurrency => SupportedCurrencies.FirstOrDefault(c => c.Code == DefaultCurrencyCode);

        /// <summary>
        /// تنسيق العملة
        /// </summary>
        public string CurrencyFormat
        {
            get
            {
                var format = "N" + DecimalPlaces;
                return SymbolPosition == "Before" ? $"{CurrencySymbol} {{0:{format}}}" : $"{{0:{format}}} {CurrencySymbol}";
            }
        }

        #endregion

        #region الدوال المساعدة

        /// <summary>
        /// تنسيق المبلغ بالعملة الافتراضية
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ منسق</returns>
        public string FormatAmount(decimal amount)
        {
            return FormatAmount(amount, DefaultCurrencyCode);
        }

        /// <summary>
        /// تنسيق المبلغ بعملة محددة
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="currencyCode">رمز العملة</param>
        /// <returns>المبلغ منسق</returns>
        public string FormatAmount(decimal amount, string currencyCode)
        {
            var currency = SupportedCurrencies.FirstOrDefault(c => c.Code == currencyCode) ?? DefaultCurrency;
            if (currency == null)
                return amount.ToString("N" + DecimalPlaces);

            var roundedAmount = Math.Round(amount, currency.DecimalPlaces);
            var formattedNumber = roundedAmount.ToString("N" + currency.DecimalPlaces, GetCultureInfo());

            return currency.SymbolPosition == "Before" 
                ? $"{currency.Symbol} {formattedNumber}" 
                : $"{formattedNumber} {currency.Symbol}";
        }

        /// <summary>
        /// تحويل العملة
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="fromCurrency">من عملة</param>
        /// <param name="toCurrency">إلى عملة</param>
        /// <returns>المبلغ محول</returns>
        public decimal ConvertCurrency(decimal amount, string fromCurrency, string toCurrency)
        {
            if (fromCurrency == toCurrency)
                return amount;

            // تحويل إلى العملة الأساسية أولاً
            var baseAmount = ConvertToBaseCurrency(amount, fromCurrency);
            
            // ثم تحويل إلى العملة المطلوبة
            return ConvertFromBaseCurrency(baseAmount, toCurrency);
        }

        /// <summary>
        /// تحويل إلى العملة الأساسية
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="fromCurrency">من عملة</param>
        /// <returns>المبلغ بالعملة الأساسية</returns>
        private decimal ConvertToBaseCurrency(decimal amount, string fromCurrency)
        {
            if (fromCurrency == DefaultCurrencyCode)
                return amount;

            var exchangeRate = GetExchangeRate(fromCurrency, DefaultCurrencyCode);
            return amount * exchangeRate;
        }

        /// <summary>
        /// تحويل من العملة الأساسية
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="toCurrency">إلى عملة</param>
        /// <returns>المبلغ بالعملة المطلوبة</returns>
        private decimal ConvertFromBaseCurrency(decimal amount, string toCurrency)
        {
            if (toCurrency == DefaultCurrencyCode)
                return amount;

            var exchangeRate = GetExchangeRate(DefaultCurrencyCode, toCurrency);
            return amount * exchangeRate;
        }

        /// <summary>
        /// الحصول على سعر الصرف
        /// </summary>
        /// <param name="fromCurrency">من عملة</param>
        /// <param name="toCurrency">إلى عملة</param>
        /// <returns>سعر الصرف</returns>
        public decimal GetExchangeRate(string fromCurrency, string toCurrency)
        {
            if (fromCurrency == toCurrency)
                return 1;

            var rate = ExchangeRates.FirstOrDefault(r => 
                r.FromCurrency == fromCurrency && r.ToCurrency == toCurrency && r.IsActive);

            if (rate != null)
                return rate.Rate;

            // البحث عن السعر العكسي
            var reverseRate = ExchangeRates.FirstOrDefault(r => 
                r.FromCurrency == toCurrency && r.ToCurrency == fromCurrency && r.IsActive);

            if (reverseRate != null && reverseRate.Rate != 0)
                return 1 / reverseRate.Rate;

            return 1; // افتراضي
        }

        /// <summary>
        /// تحديث سعر الصرف
        /// </summary>
        /// <param name="fromCurrency">من عملة</param>
        /// <param name="toCurrency">إلى عملة</param>
        /// <param name="rate">السعر الجديد</param>
        /// <param name="updatedBy">من قام بالتحديث</param>
        public void UpdateExchangeRate(string fromCurrency, string toCurrency, decimal rate, string updatedBy)
        {
            var existingRate = ExchangeRates.FirstOrDefault(r => 
                r.FromCurrency == fromCurrency && r.ToCurrency == toCurrency);

            if (existingRate != null)
            {
                existingRate.Rate = rate;
                existingRate.LastUpdated = DateTime.Now;
                existingRate.UpdatedBy = updatedBy;
            }
            else
            {
                ExchangeRates.Add(new ExchangeRate
                {
                    FromCurrency = fromCurrency,
                    ToCurrency = toCurrency,
                    Rate = rate,
                    IsActive = true,
                    LastUpdated = DateTime.Now,
                    UpdatedBy = updatedBy
                });
            }

            LastExchangeRateUpdate = DateTime.Now;
        }

        /// <summary>
        /// الحصول على معلومات الثقافة
        /// </summary>
        /// <returns>معلومات الثقافة</returns>
        private CultureInfo GetCultureInfo()
        {
            var culture = new CultureInfo("ar-IQ");
            culture.NumberFormat.NumberDecimalDigits = DecimalPlaces;
            culture.NumberFormat.NumberGroupSeparator = ThousandsSeparator;
            culture.NumberFormat.NumberDecimalSeparator = DecimalSeparator;
            return culture;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            if (string.IsNullOrWhiteSpace(DefaultCurrencyCode) || DefaultCurrencyCode.Length != 3)
                return false;

            if (string.IsNullOrWhiteSpace(CurrencySymbol))
                return false;

            if (DecimalPlaces < 0 || DecimalPlaces > 4)
                return false;

            if (!new[] { "Before", "After" }.Contains(SymbolPosition))
                return false;

            return true;
        }

        #endregion
    }

    /// <summary>
    /// نموذج العملة
    /// </summary>
    public class Currency
    {
        /// <summary>
        /// رقم العملة
        /// </summary>
        public int CurrencyID { get; set; }

        /// <summary>
        /// رمز العملة (ISO 4217)
        /// </summary>
        [Required]
        [StringLength(3)]
        public string Code { get; set; }

        /// <summary>
        /// اسم العملة
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// اسم العملة بالعربية
        /// </summary>
        [StringLength(100)]
        public string NameArabic { get; set; }

        /// <summary>
        /// رمز العملة
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Symbol { get; set; }

        /// <summary>
        /// عدد الأرقام العشرية
        /// </summary>
        [Range(0, 4)]
        public int DecimalPlaces { get; set; }

        /// <summary>
        /// موضع الرمز
        /// </summary>
        [StringLength(10)]
        public string SymbolPosition { get; set; } = "After";

        /// <summary>
        /// هل العملة نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// الاسم المعروض
        /// </summary>
        public string DisplayName => !string.IsNullOrWhiteSpace(NameArabic) ? NameArabic : Name;

        /// <summary>
        /// النص الكامل للعملة
        /// </summary>
        public string FullText => $"{Code} - {DisplayName} ({Symbol})";
    }

    /// <summary>
    /// نموذج سعر الصرف
    /// </summary>
    public class ExchangeRate
    {
        /// <summary>
        /// رقم سعر الصرف
        /// </summary>
        public int ExchangeRateID { get; set; }

        /// <summary>
        /// من عملة
        /// </summary>
        [Required]
        [StringLength(3)]
        public string FromCurrency { get; set; }

        /// <summary>
        /// إلى عملة
        /// </summary>
        [Required]
        [StringLength(3)]
        public string ToCurrency { get; set; }

        /// <summary>
        /// السعر
        /// </summary>
        [Range(0.0001, 999999)]
        public decimal Rate { get; set; }

        /// <summary>
        /// تاريخ السعر
        /// </summary>
        public DateTime RateDate { get; set; } = DateTime.Now;

        /// <summary>
        /// هل السعر نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// مصدر السعر
        /// </summary>
        [StringLength(100)]
        public string Source { get; set; } = "Manual";

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// من قام بآخر تحديث
        /// </summary>
        [StringLength(50)]
        public string UpdatedBy { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; }

        /// <summary>
        /// النص الوصفي لسعر الصرف
        /// </summary>
        public string Description => $"1 {FromCurrency} = {Rate:N4} {ToCurrency}";
    }

    /// <summary>
    /// العملات المدعومة افتراضياً
    /// </summary>
    public static class SupportedCurrencies
    {
        public static readonly List<Currency> DefaultCurrencies = new List<Currency>
        {
            new Currency
            {
                Code = "IQD",
                Name = "Iraqi Dinar",
                NameArabic = "دينار عراقي",
                Symbol = "د.ع",
                DecimalPlaces = 0,
                SymbolPosition = "After",
                DisplayOrder = 1
            },
            new Currency
            {
                Code = "USD",
                Name = "US Dollar",
                NameArabic = "دولار أمريكي",
                Symbol = "$",
                DecimalPlaces = 2,
                SymbolPosition = "Before",
                DisplayOrder = 2
            },
            new Currency
            {
                Code = "EUR",
                Name = "Euro",
                NameArabic = "يورو",
                Symbol = "€",
                DecimalPlaces = 2,
                SymbolPosition = "Before",
                DisplayOrder = 3
            },
            new Currency
            {
                Code = "GBP",
                Name = "British Pound",
                NameArabic = "جنيه إسترليني",
                Symbol = "£",
                DecimalPlaces = 2,
                SymbolPosition = "Before",
                DisplayOrder = 4
            },
            new Currency
            {
                Code = "SAR",
                Name = "Saudi Riyal",
                NameArabic = "ريال سعودي",
                Symbol = "ر.س",
                DecimalPlaces = 2,
                SymbolPosition = "After",
                DisplayOrder = 5
            },
            new Currency
            {
                Code = "AED",
                Name = "UAE Dirham",
                NameArabic = "درهم إماراتي",
                Symbol = "د.إ",
                DecimalPlaces = 2,
                SymbolPosition = "After",
                DisplayOrder = 6
            },
            new Currency
            {
                Code = "KWD",
                Name = "Kuwaiti Dinar",
                NameArabic = "دينار كويتي",
                Symbol = "د.ك",
                DecimalPlaces = 3,
                SymbolPosition = "After",
                DisplayOrder = 7
            },
            new Currency
            {
                Code = "QAR",
                Name = "Qatari Riyal",
                NameArabic = "ريال قطري",
                Symbol = "ر.ق",
                DecimalPlaces = 2,
                SymbolPosition = "After",
                DisplayOrder = 8
            },
            new Currency
            {
                Code = "BHD",
                Name = "Bahraini Dinar",
                NameArabic = "دينار بحريني",
                Symbol = "د.ب",
                DecimalPlaces = 3,
                SymbolPosition = "After",
                DisplayOrder = 9
            },
            new Currency
            {
                Code = "OMR",
                Name = "Omani Rial",
                NameArabic = "ريال عماني",
                Symbol = "ر.ع",
                DecimalPlaces = 3,
                SymbolPosition = "After",
                DisplayOrder = 10
            }
        };
    }
}
