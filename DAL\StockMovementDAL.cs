using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using AredooPOS.Models;
using Microsoft.Extensions.Logging;

namespace AredooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات لحركات المخزون
    /// تحتوي على جميع العمليات المتعلقة بحركات المخزون في قاعدة البيانات
    /// </summary>
    public class StockMovementDAL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly string _connectionString;
        private readonly ILogger<StockMovementDAL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة الوصول للبيانات لحركات المخزون
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public StockMovementDAL(string connectionString = null, ILogger<StockMovementDAL> logger = null)
        {
            _connectionString = connectionString ?? DatabaseConnection.ConnectionString;
            _logger = logger;
        }

        #endregion

        #region العمليات الأساسية (CRUD)

        /// <summary>
        /// إضافة حركة مخزون جديدة
        /// </summary>
        /// <param name="movement">بيانات حركة المخزون</param>
        /// <returns>رقم حركة المخزون الجديدة</returns>
        public int AddStockMovement(StockMovement movement)
        {
            try
            {
                var query = @"
                    INSERT INTO StockMovements (
                        ProductID, ProductCode, ProductName, ReferenceNumber, MovementDate, MovementType,
                        Quantity, Unit, StockBefore, StockAfter, UnitCost, Currency, Reason,
                        DocumentNumber, DocumentType, SupplierID, SupplierName, CustomerID, CustomerName,
                        BatchNumber, ExpiryDate, WarehouseLocation, Notes, Status, ConfirmedDate,
                        ConfirmedBy, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate
                    ) VALUES (
                        @ProductID, @ProductCode, @ProductName, @ReferenceNumber, @MovementDate, @MovementType,
                        @Quantity, @Unit, @StockBefore, @StockAfter, @UnitCost, @Currency, @Reason,
                        @DocumentNumber, @DocumentType, @SupplierID, @SupplierName, @CustomerID, @CustomerName,
                        @BatchNumber, @ExpiryDate, @WarehouseLocation, @Notes, @Status, @ConfirmedDate,
                        @ConfirmedBy, @CreatedBy, @CreatedDate, @ModifiedBy, @ModifiedDate
                    );
                    SELECT last_insert_rowid();";

                var parameters = GetStockMovementParameters(movement);
                var result = DatabaseConnection.ExecuteScalar(query, parameters);
                var movementId = Convert.ToInt32(result);

                _logger?.LogInformation($"تم إضافة حركة مخزون جديدة برقم {movementId}");
                return movementId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إضافة حركة المخزون {movement.ReferenceNumber}");
                throw new Exception($"خطأ في إضافة حركة المخزون: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث بيانات حركة مخزون
        /// </summary>
        /// <param name="movement">بيانات حركة المخزون المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateStockMovement(StockMovement movement)
        {
            try
            {
                var query = @"
                    UPDATE StockMovements SET
                        ProductID = @ProductID,
                        ProductCode = @ProductCode,
                        ProductName = @ProductName,
                        ReferenceNumber = @ReferenceNumber,
                        MovementDate = @MovementDate,
                        MovementType = @MovementType,
                        Quantity = @Quantity,
                        Unit = @Unit,
                        StockBefore = @StockBefore,
                        StockAfter = @StockAfter,
                        UnitCost = @UnitCost,
                        Currency = @Currency,
                        Reason = @Reason,
                        DocumentNumber = @DocumentNumber,
                        DocumentType = @DocumentType,
                        SupplierID = @SupplierID,
                        SupplierName = @SupplierName,
                        CustomerID = @CustomerID,
                        CustomerName = @CustomerName,
                        BatchNumber = @BatchNumber,
                        ExpiryDate = @ExpiryDate,
                        WarehouseLocation = @WarehouseLocation,
                        Notes = @Notes,
                        Status = @Status,
                        ConfirmedDate = @ConfirmedDate,
                        ConfirmedBy = @ConfirmedBy,
                        ModifiedBy = @ModifiedBy,
                        ModifiedDate = @ModifiedDate
                    WHERE StockMovementID = @StockMovementID";

                var parameters = GetStockMovementParameters(movement);
                Array.Resize(ref parameters, parameters.Length + 1);
                parameters[parameters.Length - 1] = new SqlParameter("@StockMovementID", movement.StockMovementID);

                var rowsAffected = DatabaseConnection.ExecuteNonQuery(query, parameters);
                var success = rowsAffected > 0;

                if (success)
                    _logger?.LogInformation($"تم تحديث حركة المخزون {movement.ReferenceNumber}");

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث حركة المخزون {movement.ReferenceNumber}");
                throw new Exception($"خطأ في تحديث حركة المخزون: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حذف حركة مخزون
        /// </summary>
        /// <param name="movementId">رقم حركة المخزون</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public bool DeleteStockMovement(int movementId)
        {
            try
            {
                var deleteQuery = "DELETE FROM StockMovements WHERE StockMovementID = @StockMovementID";
                var deleteParams = new SqlParameter[] { new SqlParameter("@StockMovementID", movementId) };

                var rowsAffected = DatabaseConnection.ExecuteNonQuery(deleteQuery, deleteParams);
                var success = rowsAffected > 0;

                if (success)
                    _logger?.LogInformation($"تم حذف حركة المخزون {movementId}");

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حذف حركة المخزون {movementId}");
                throw new Exception($"خطأ في حذف حركة المخزون: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على حركة مخزون بالرقم
        /// </summary>
        /// <param name="movementId">رقم حركة المخزون</param>
        /// <returns>بيانات حركة المخزون أو null</returns>
        public StockMovement GetStockMovementById(int movementId)
        {
            try
            {
                var query = @"
                    SELECT sm.*, p.ProductName, p.ProductCode
                    FROM StockMovements sm
                    LEFT JOIN Products p ON sm.ProductID = p.ProductID
                    WHERE sm.StockMovementID = @StockMovementID";

                var parameters = new SqlParameter[] { new SqlParameter("@StockMovementID", movementId) };
                var result = DatabaseConnection.ExecuteQuery(query, parameters);

                if (result.Rows.Count > 0)
                {
                    return MapDataRowToStockMovement(result.Rows[0]);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على حركة المخزون {movementId}");
                throw new Exception($"خطأ في الحصول على حركة المخزون: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على حركات المخزون لمنتج معين
        /// </summary>
        /// <param name="productId">رقم المنتج</param>
        /// <param name="fromDate">من تاريخ (اختياري)</param>
        /// <param name="toDate">إلى تاريخ (اختياري)</param>
        /// <returns>قائمة حركات المخزون</returns>
        public List<StockMovement> GetStockMovementsByProduct(int productId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = @"
                    SELECT sm.*, p.ProductName, p.ProductCode
                    FROM StockMovements sm
                    LEFT JOIN Products p ON sm.ProductID = p.ProductID
                    WHERE sm.ProductID = @ProductID";

                var parameters = new List<SqlParameter>
                {
                    new SqlParameter("@ProductID", productId)
                };

                if (fromDate.HasValue)
                {
                    query += " AND sm.MovementDate >= @FromDate";
                    parameters.Add(new SqlParameter("@FromDate", fromDate.Value));
                }

                if (toDate.HasValue)
                {
                    query += " AND sm.MovementDate <= @ToDate";
                    parameters.Add(new SqlParameter("@ToDate", toDate.Value));
                }

                query += " ORDER BY sm.MovementDate DESC, sm.StockMovementID DESC";

                var result = DatabaseConnection.ExecuteQuery(query, parameters.ToArray());
                var movements = new List<StockMovement>();

                foreach (DataRow row in result.Rows)
                {
                    movements.Add(MapDataRowToStockMovement(row));
                }

                return movements;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على حركات المخزون للمنتج {productId}");
                throw new Exception($"خطأ في الحصول على حركات المخزون للمنتج: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على حركات المخزون حسب النوع
        /// </summary>
        /// <param name="movementType">نوع الحركة</param>
        /// <param name="fromDate">من تاريخ (اختياري)</param>
        /// <param name="toDate">إلى تاريخ (اختياري)</param>
        /// <returns>قائمة حركات المخزون</returns>
        public List<StockMovement> GetStockMovementsByType(string movementType, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = @"
                    SELECT sm.*, p.ProductName, p.ProductCode
                    FROM StockMovements sm
                    LEFT JOIN Products p ON sm.ProductID = p.ProductID
                    WHERE sm.MovementType = @MovementType";

                var parameters = new List<SqlParameter>
                {
                    new SqlParameter("@MovementType", movementType)
                };

                if (fromDate.HasValue)
                {
                    query += " AND sm.MovementDate >= @FromDate";
                    parameters.Add(new SqlParameter("@FromDate", fromDate.Value));
                }

                if (toDate.HasValue)
                {
                    query += " AND sm.MovementDate <= @ToDate";
                    parameters.Add(new SqlParameter("@ToDate", toDate.Value));
                }

                query += " ORDER BY sm.MovementDate DESC, sm.StockMovementID DESC";

                var result = DatabaseConnection.ExecuteQuery(query, parameters.ToArray());
                var movements = new List<StockMovement>();

                foreach (DataRow row in result.Rows)
                {
                    movements.Add(MapDataRowToStockMovement(row));
                }

                return movements;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على حركات المخزون من النوع {movementType}");
                throw new Exception($"خطأ في الحصول على حركات المخزون حسب النوع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على جميع حركات المخزون
        /// </summary>
        /// <param name="fromDate">من تاريخ (اختياري)</param>
        /// <param name="toDate">إلى تاريخ (اختياري)</param>
        /// <param name="limit">الحد الأقصى للنتائج (اختياري)</param>
        /// <returns>قائمة حركات المخزون</returns>
        public List<StockMovement> GetAllStockMovements(DateTime? fromDate = null, DateTime? toDate = null, int? limit = null)
        {
            try
            {
                var query = @"
                    SELECT sm.*, p.ProductName, p.ProductCode
                    FROM StockMovements sm
                    LEFT JOIN Products p ON sm.ProductID = p.ProductID
                    WHERE 1=1";

                var parameters = new List<SqlParameter>();

                if (fromDate.HasValue)
                {
                    query += " AND sm.MovementDate >= @FromDate";
                    parameters.Add(new SqlParameter("@FromDate", fromDate.Value));
                }

                if (toDate.HasValue)
                {
                    query += " AND sm.MovementDate <= @ToDate";
                    parameters.Add(new SqlParameter("@ToDate", toDate.Value));
                }

                query += " ORDER BY sm.MovementDate DESC, sm.StockMovementID DESC";

                if (limit.HasValue)
                {
                    query += $" LIMIT {limit.Value}";
                }

                var result = DatabaseConnection.ExecuteQuery(query, parameters.ToArray());
                var movements = new List<StockMovement>();

                foreach (DataRow row in result.Rows)
                {
                    movements.Add(MapDataRowToStockMovement(row));
                }

                return movements;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على جميع حركات المخزون");
                throw new Exception($"خطأ في الحصول على جميع حركات المخزون: {ex.Message}", ex);
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// إنشاء معاملات حركة المخزون
        /// </summary>
        /// <param name="movement">بيانات حركة المخزون</param>
        /// <returns>مصفوفة المعاملات</returns>
        private SqlParameter[] GetStockMovementParameters(StockMovement movement)
        {
            return new SqlParameter[]
            {
                new SqlParameter("@ProductID", movement.ProductID),
                new SqlParameter("@ProductCode", movement.ProductCode ?? (object)DBNull.Value),
                new SqlParameter("@ProductName", movement.ProductName ?? (object)DBNull.Value),
                new SqlParameter("@ReferenceNumber", movement.ReferenceNumber ?? (object)DBNull.Value),
                new SqlParameter("@MovementDate", movement.MovementDate),
                new SqlParameter("@MovementType", movement.MovementType),
                new SqlParameter("@Quantity", movement.Quantity),
                new SqlParameter("@Unit", movement.Unit),
                new SqlParameter("@StockBefore", movement.StockBefore),
                new SqlParameter("@StockAfter", movement.StockAfter),
                new SqlParameter("@UnitCost", movement.UnitCost),
                new SqlParameter("@Currency", movement.Currency),
                new SqlParameter("@Reason", movement.Reason),
                new SqlParameter("@DocumentNumber", movement.DocumentNumber ?? (object)DBNull.Value),
                new SqlParameter("@DocumentType", movement.DocumentType ?? (object)DBNull.Value),
                new SqlParameter("@SupplierID", movement.SupplierID ?? (object)DBNull.Value),
                new SqlParameter("@SupplierName", movement.SupplierName ?? (object)DBNull.Value),
                new SqlParameter("@CustomerID", movement.CustomerID ?? (object)DBNull.Value),
                new SqlParameter("@CustomerName", movement.CustomerName ?? (object)DBNull.Value),
                new SqlParameter("@BatchNumber", movement.BatchNumber ?? (object)DBNull.Value),
                new SqlParameter("@ExpiryDate", movement.ExpiryDate ?? (object)DBNull.Value),
                new SqlParameter("@WarehouseLocation", movement.WarehouseLocation ?? (object)DBNull.Value),
                new SqlParameter("@Notes", movement.Notes ?? (object)DBNull.Value),
                new SqlParameter("@Status", movement.Status),
                new SqlParameter("@ConfirmedDate", movement.ConfirmedDate ?? (object)DBNull.Value),
                new SqlParameter("@ConfirmedBy", movement.ConfirmedBy ?? (object)DBNull.Value),
                new SqlParameter("@CreatedBy", movement.CreatedBy ?? (object)DBNull.Value),
                new SqlParameter("@CreatedDate", movement.CreatedDate),
                new SqlParameter("@ModifiedBy", movement.ModifiedBy ?? (object)DBNull.Value),
                new SqlParameter("@ModifiedDate", movement.ModifiedDate)
            };
        }

        /// <summary>
        /// تحويل صف البيانات إلى كائن حركة مخزون
        /// </summary>
        /// <param name="row">صف البيانات</param>
        /// <returns>كائن حركة المخزون</returns>
        private StockMovement MapDataRowToStockMovement(DataRow row)
        {
            return new StockMovement
            {
                StockMovementID = Convert.ToInt32(row["StockMovementID"]),
                ProductID = Convert.ToInt32(row["ProductID"]),
                ProductCode = row.IsNull("ProductCode") ? null : row["ProductCode"].ToString(),
                ProductName = row.IsNull("ProductName") ? null : row["ProductName"].ToString(),
                ReferenceNumber = row.IsNull("ReferenceNumber") ? null : row["ReferenceNumber"].ToString(),
                MovementDate = Convert.ToDateTime(row["MovementDate"]),
                MovementType = row["MovementType"].ToString(),
                Quantity = Convert.ToDecimal(row["Quantity"]),
                Unit = row["Unit"].ToString(),
                StockBefore = Convert.ToDecimal(row["StockBefore"]),
                StockAfter = Convert.ToDecimal(row["StockAfter"]),
                UnitCost = Convert.ToDecimal(row["UnitCost"]),
                Currency = row["Currency"].ToString(),
                Reason = row["Reason"].ToString(),
                DocumentNumber = row.IsNull("DocumentNumber") ? null : row["DocumentNumber"].ToString(),
                DocumentType = row.IsNull("DocumentType") ? null : row["DocumentType"].ToString(),
                SupplierID = row.IsNull("SupplierID") ? null : Convert.ToInt32(row["SupplierID"]),
                SupplierName = row.IsNull("SupplierName") ? null : row["SupplierName"].ToString(),
                CustomerID = row.IsNull("CustomerID") ? null : Convert.ToInt32(row["CustomerID"]),
                CustomerName = row.IsNull("CustomerName") ? null : row["CustomerName"].ToString(),
                BatchNumber = row.IsNull("BatchNumber") ? null : row["BatchNumber"].ToString(),
                ExpiryDate = row.IsNull("ExpiryDate") ? null : Convert.ToDateTime(row["ExpiryDate"]),
                WarehouseLocation = row.IsNull("WarehouseLocation") ? null : row["WarehouseLocation"].ToString(),
                Notes = row.IsNull("Notes") ? null : row["Notes"].ToString(),
                Status = row["Status"].ToString(),
                ConfirmedDate = row.IsNull("ConfirmedDate") ? null : Convert.ToDateTime(row["ConfirmedDate"]),
                ConfirmedBy = row.IsNull("ConfirmedBy") ? null : row["ConfirmedBy"].ToString(),
                CreatedBy = row.IsNull("CreatedBy") ? null : row["CreatedBy"].ToString(),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                ModifiedBy = row.IsNull("ModifiedBy") ? null : row["ModifiedBy"].ToString(),
                ModifiedDate = Convert.ToDateTime(row["ModifiedDate"])
            };
        }

        #endregion
    }
}
