using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace AredooPOS.Models
{
    /// <summary>
    /// نموذج الديون - يمثل دين على عميل معين
    /// يحتوي على جميع معلومات الدين وحالة السداد
    /// </summary>
    public class Debt
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم الدين في قاعدة البيانات (مفتاح أساسي)
        /// </summary>
        public int DebtID { get; set; }

        /// <summary>
        /// رقم الدين المعروض للمستخدم
        /// </summary>
        [Required(ErrorMessage = "رقم الدين مطلوب")]
        [StringLength(20, ErrorMessage = "رقم الدين لا يجب أن يتجاوز 20 حرف")]
        public string DebtNumber { get; set; }

        /// <summary>
        /// نوع الدين (فاتورة، دين يدوي، قسط متأخر)
        /// </summary>
        [Required(ErrorMessage = "نوع الدين مطلوب")]
        [StringLength(20, ErrorMessage = "نوع الدين لا يجب أن يتجاوز 20 حرف")]
        public string DebtType { get; set; }

        /// <summary>
        /// تاريخ إنشاء الدين
        /// </summary>
        [Required(ErrorMessage = "تاريخ الدين مطلوب")]
        public DateTime DebtDate { get; set; }

        /// <summary>
        /// تاريخ استحقاق الدين
        /// </summary>
        public DateTime? DueDate { get; set; }

        #endregion

        #region معلومات العميل

        /// <summary>
        /// رقم العميل في قاعدة البيانات (مفتاح خارجي)
        /// </summary>
        [Required(ErrorMessage = "رقم العميل مطلوب")]
        public int CustomerID { get; set; }

        /// <summary>
        /// اسم العميل
        /// </summary>
        [Required(ErrorMessage = "اسم العميل مطلوب")]
        [StringLength(100, ErrorMessage = "اسم العميل لا يجب أن يتجاوز 100 حرف")]
        public string CustomerName { get; set; }

        /// <summary>
        /// رقم هاتف العميل
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الهاتف لا يجب أن يتجاوز 20 حرف")]
        public string CustomerPhone { get; set; }

        #endregion

        #region معلومات المرجع

        /// <summary>
        /// رقم الفاتورة المرتبطة (إن وجدت)
        /// </summary>
        public int? InvoiceID { get; set; }

        /// <summary>
        /// رقم الفاتورة المعروض
        /// </summary>
        [StringLength(20, ErrorMessage = "رقم الفاتورة لا يجب أن يتجاوز 20 حرف")]
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// رقم القسط المرتبط (إن وجد)
        /// </summary>
        public int? InstallmentID { get; set; }

        /// <summary>
        /// مرجع خارجي (رقم شيك، سند، إلخ)
        /// </summary>
        [StringLength(50, ErrorMessage = "المرجع الخارجي لا يجب أن يتجاوز 50 حرف")]
        public string ExternalReference { get; set; }

        #endregion

        #region المبالغ المالية

        /// <summary>
        /// المبلغ الأصلي للدين
        /// </summary>
        [Required(ErrorMessage = "مبلغ الدين مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "مبلغ الدين يجب أن يكون أكبر من صفر")]
        public decimal OriginalAmount { get; set; }

        /// <summary>
        /// المبلغ المدفوع من الدين
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "المبلغ المدفوع يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal PaidAmount { get; set; }

        /// <summary>
        /// المبلغ المتبقي من الدين
        /// </summary>
        public decimal RemainingAmount { get; set; }

        /// <summary>
        /// رسوم التأخير (إن وجدت)
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "رسوم التأخير يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal LateFees { get; set; }

        /// <summary>
        /// إجمالي المبلغ المستحق (الأصلي + رسوم التأخير)
        /// </summary>
        public decimal TotalDueAmount { get; set; }

        #endregion

        #region حالة الدين

        /// <summary>
        /// حالة الدين (مستحق، مدفوع جزئياً، مدفوع كاملاً، متأخر، ملغي)
        /// </summary>
        [Required(ErrorMessage = "حالة الدين مطلوبة")]
        [StringLength(20, ErrorMessage = "حالة الدين لا يجب أن تتجاوز 20 حرف")]
        public string DebtStatus { get; set; }

        /// <summary>
        /// أولوية التحصيل (عادي، مهم، عاجل)
        /// </summary>
        [StringLength(20, ErrorMessage = "أولوية التحصيل لا يجب أن تتجاوز 20 حرف")]
        public string Priority { get; set; }

        /// <summary>
        /// عدد أيام التأخير
        /// </summary>
        public int OverdueDays { get; set; }

        /// <summary>
        /// تاريخ آخر تذكير
        /// </summary>
        public DateTime? LastReminderDate { get; set; }

        /// <summary>
        /// عدد التذكيرات المرسلة
        /// </summary>
        public int ReminderCount { get; set; }

        #endregion

        #region الملاحظات والمعلومات الإضافية

        /// <summary>
        /// وصف الدين أو سبب إنشائه
        /// </summary>
        [StringLength(200, ErrorMessage = "وصف الدين لا يجب أن يتجاوز 200 حرف")]
        public string Description { get; set; }

        /// <summary>
        /// ملاحظات عامة
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 500 حرف")]
        public string Notes { get; set; }

        /// <summary>
        /// ملاحظات داخلية (لا تظهر للعميل)
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات الداخلية لا يجب أن تتجاوز 500 حرف")]
        public string InternalNotes { get; set; }

        #endregion

        #region معلومات المستخدم والنظام

        /// <summary>
        /// منشئ الدين
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المنشئ لا يجب أن يتجاوز 50 حرف")]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ إنشاء السجل
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// آخر من عدل السجل
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المعدل لا يجب أن يتجاوز 50 حرف")]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime ModifiedDate { get; set; }

        #endregion

        #region القوائم المرتبطة

        /// <summary>
        /// قائمة مدفوعات الدين
        /// </summary>
        public List<DebtPayment> DebtPayments { get; set; }

        /// <summary>
        /// قائمة تذكيرات الدين
        /// </summary>
        public List<DebtReminder> DebtReminders { get; set; }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ الدين مع التهيئة الافتراضية
        /// </summary>
        public Debt()
        {
            // تهيئة القوائم
            DebtPayments = new List<DebtPayment>();
            DebtReminders = new List<DebtReminder>();

            // تهيئة التواريخ
            DebtDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;

            // تهيئة القيم الافتراضية
            DebtType = DebtTypes.Manual;
            DebtStatus = DebtStatuses.Outstanding;
            Priority = DebtPriorities.Normal;
            PaidAmount = 0;
            LateFees = 0;
            OverdueDays = 0;
            ReminderCount = 0;
        }

        #endregion

        #region العمليات الحسابية

        /// <summary>
        /// حساب المبلغ المتبقي والإجمالي المستحق
        /// </summary>
        public void CalculateAmounts()
        {
            // حساب المبلغ المتبقي
            RemainingAmount = OriginalAmount - PaidAmount;

            // حساب إجمالي المبلغ المستحق (مع رسوم التأخير)
            TotalDueAmount = OriginalAmount + LateFees;

            // تحديث حالة الدين
            UpdateDebtStatus();

            // حساب أيام التأخير
            CalculateOverdueDays();

            // تحديث تاريخ التعديل
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// تحديث حالة الدين بناءً على المبالغ
        /// </summary>
        private void UpdateDebtStatus()
        {
            if (RemainingAmount <= 0)
            {
                DebtStatus = DebtStatuses.FullyPaid;
            }
            else if (PaidAmount > 0)
            {
                DebtStatus = DebtStatuses.PartiallyPaid;
            }
            else if (OverdueDays > 0)
            {
                DebtStatus = DebtStatuses.Overdue;
            }
            else
            {
                DebtStatus = DebtStatuses.Outstanding;
            }
        }

        /// <summary>
        /// حساب عدد أيام التأخير
        /// </summary>
        private void CalculateOverdueDays()
        {
            if (DueDate.HasValue && DateTime.Now > DueDate.Value)
            {
                OverdueDays = (DateTime.Now - DueDate.Value).Days;
            }
            else
            {
                OverdueDays = 0;
            }
        }

        /// <summary>
        /// حساب رسوم التأخير بناءً على عدد الأيام
        /// </summary>
        /// <param name="dailyFeeRate">معدل الرسوم اليومية (نسبة مئوية)</param>
        public void CalculateLateFees(decimal dailyFeeRate = 0.1m)
        {
            if (OverdueDays > 0 && RemainingAmount > 0)
            {
                LateFees = RemainingAmount * (dailyFeeRate / 100) * OverdueDays;
            }
            else
            {
                LateFees = 0;
            }
        }

        /// <summary>
        /// إضافة دفعة جديدة للدين
        /// </summary>
        /// <param name="amount">مبلغ الدفعة</param>
        /// <param name="paymentMethod">طريقة الدفع</param>
        /// <param name="notes">ملاحظات</param>
        /// <returns>كائن الدفعة الجديد</returns>
        public DebtPayment AddPayment(decimal amount, string paymentMethod, string notes = "")
        {
            var payment = new DebtPayment
            {
                DebtID = this.DebtID,
                Amount = amount,
                PaymentMethod = paymentMethod,
                PaymentDate = DateTime.Now,
                Notes = notes,
                CreatedBy = this.ModifiedBy,
                CreatedDate = DateTime.Now
            };

            DebtPayments.Add(payment);
            PaidAmount += amount;
            CalculateAmounts();

            return payment;
        }

        #endregion

        #region عمليات التحقق والتصديق

        /// <summary>
        /// التحقق من صحة بيانات الدين
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(DebtNumber) &&
                   CustomerID > 0 &&
                   !string.IsNullOrWhiteSpace(CustomerName) &&
                   OriginalAmount > 0 &&
                   PaidAmount >= 0 &&
                   PaidAmount <= OriginalAmount + LateFees;
        }

        /// <summary>
        /// التحقق من إمكانية تعديل الدين
        /// </summary>
        /// <returns>true إذا كان بإمكان التعديل</returns>
        public bool CanEdit()
        {
            return DebtStatus != DebtStatuses.FullyPaid &&
                   DebtStatus != DebtStatuses.Cancelled;
        }

        /// <summary>
        /// التحقق من إمكانية إلغاء الدين
        /// </summary>
        /// <returns>true إذا كان بإمكان الإلغاء</returns>
        public bool CanCancel()
        {
            return PaidAmount == 0 &&
                   DebtStatus != DebtStatuses.FullyPaid &&
                   DebtStatus != DebtStatuses.Cancelled;
        }

        /// <summary>
        /// التحقق من كون الدين متأخراً
        /// </summary>
        /// <returns>true إذا كان الدين متأخراً</returns>
        public bool IsOverdue()
        {
            return OverdueDays > 0 && RemainingAmount > 0;
        }

        /// <summary>
        /// التحقق من كون الدين مدفوعاً بالكامل
        /// </summary>
        /// <returns>true إذا كان الدين مدفوعاً بالكامل</returns>
        public bool IsFullyPaid()
        {
            return RemainingAmount <= 0;
        }

        #endregion

        #region عمليات النسخ والتحويل

        /// <summary>
        /// تحويل الدين إلى نص وصفي
        /// </summary>
        /// <returns>وصف نصي للدين</returns>
        public override string ToString()
        {
            return $"دين رقم {DebtNumber} - {CustomerName} - المبلغ: {OriginalAmount:C} - المتبقي: {RemainingAmount:C}";
        }

        #endregion
    }

    #region الثوابت والتعدادات

    /// <summary>
    /// أنواع الديون
    /// </summary>
    public static class DebtTypes
    {
        public const string Invoice = "فاتورة";
        public const string Manual = "دين يدوي";
        public const string Installment = "قسط";
        public const string LateFee = "رسوم تأخير";
        public const string Other = "أخرى";
    }

    /// <summary>
    /// حالات الديون
    /// </summary>
    public static class DebtStatuses
    {
        public const string Outstanding = "مستحق";
        public const string PartiallyPaid = "مدفوع جزئياً";
        public const string FullyPaid = "مدفوع كاملاً";
        public const string Overdue = "متأخر";
        public const string Cancelled = "ملغي";
    }

    /// <summary>
    /// أولويات التحصيل
    /// </summary>
    public static class DebtPriorities
    {
        public const string Low = "منخفض";
        public const string Normal = "عادي";
        public const string High = "مهم";
        public const string Urgent = "عاجل";
    }

    #endregion
}
