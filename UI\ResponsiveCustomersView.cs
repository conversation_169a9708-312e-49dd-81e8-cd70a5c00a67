using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// واجهة إدارة العملاء المتجاوبة مع جداول وبحث متكيف
    /// </summary>
    public class ResponsiveCustomersView : UserControl
    {
        #region المتغيرات

        private Panel _headerContainer;
        private Panel _searchContainer;
        private Panel _customersContainer;
        private Panel _actionsContainer;

        // عناصر البحث المتجاوبة
        private TextBox _searchTextBox;
        private ComboBox _searchTypeComboBox;
        private Button _searchButton;
        private Button _clearSearchButton;

        // جدول العملاء المتجاوب
        private DataGridView _customersGrid;

        // الأزرار المتجاوبة
        private Button _addCustomerButton;
        private Button _editCustomerButton;
        private Button _deleteCustomerButton;
        private Button _viewHistoryButton;
        private Button _exportButton;

        // البيانات
        private List<ResponsiveCustomer> _customers;
        private List<ResponsiveCustomer> _filteredCustomers;

        #endregion

        #region الأحداث

        public event EventHandler<ResponsiveCustomerEventArgs> CustomerSelected;
        public event EventHandler<ResponsiveCustomerEventArgs> CustomerAdded;
        public event EventHandler<ResponsiveCustomerEventArgs> CustomerEdited;
        public event EventHandler<ResponsiveCustomerEventArgs> CustomerDeleted;

        #endregion

        #region البناء والتهيئة

        public ResponsiveCustomersView()
        {
            _customers = new List<ResponsiveCustomer>();
            _filteredCustomers = new List<ResponsiveCustomer>();
            InitializeComponent();
            SetupResponsiveDesign();
            SetupEvents();
            LoadSampleData();
        }

        private void InitializeComponent()
        {
            // إعدادات الواجهة المتجاوبة
            BackColor = ResponsiveDesignSystem.Colors.Background;
            Dock = DockStyle.Fill;
            AutoScroll = true;
            Padding = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetLargeSpacing());

            CreateResponsiveContainers();
            CreateHeaderSection();
            CreateSearchSection();
            CreateCustomersGrid();
            CreateActionsSection();
        }

        private void CreateResponsiveContainers()
        {
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetLargeSpacing();
            var sectionHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight() * 2;

            // حاوي الرأس
            _headerContainer = ResponsiveLayoutSystem.CreateResponsiveRow();
            _headerContainer.Height = sectionHeight;

            // حاوي البحث
            _searchContainer = ResponsiveLayoutSystem.CreateResponsiveRow();
            _searchContainer.Height = sectionHeight;
            _searchContainer.Location = new Point(0, sectionHeight + spacing);

            // حاوي جدول العملاء
            _customersContainer = ResponsiveLayoutSystem.CreateResponsiveRow();
            _customersContainer.Height = Height - (sectionHeight * 3) - (spacing * 3);
            _customersContainer.Location = new Point(0, (sectionHeight * 2) + (spacing * 2));

            // حاوي الأزرار
            _actionsContainer = ResponsiveLayoutSystem.CreateResponsiveRow();
            _actionsContainer.Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetLargeButtonHeight() + spacing;
            _actionsContainer.Dock = DockStyle.Bottom;

            Controls.AddRange(new Control[] { _headerContainer, _searchContainer, _customersContainer, _actionsContainer });
        }

        private void CreateHeaderSection()
        {
            var headerCard = new Panel
            {
                BackColor = ResponsiveDesignSystem.Colors.Surface,
                Dock = DockStyle.Fill,
                Padding = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing())
            };

            var titleLabel = new Label
            {
                Text = "👥 إدارة العملاء",
                Font = ResponsiveDesignSystem.Fonts.GetHeading3(),
                ForeColor = ResponsiveDesignSystem.Colors.Primary,
                Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight(),
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            var statsLabel = new Label
            {
                Text = "إجمالي العملاء: 0 | العملاء النشطون: 0 | إجمالي المبيعات: 0.00 ر.س",
                Font = ResponsiveDesignSystem.Fonts.GetBody(),
                ForeColor = ResponsiveDesignSystem.Colors.TextSecondary,
                Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetInputHeight(),
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            headerCard.Controls.AddRange(new Control[] { statsLabel, titleLabel });
            headerCard.Paint += (s, e) => ResponsiveDesignSystem.DrawResponsiveCard(e.Graphics, headerCard.ClientRectangle, ResponsiveDesignSystem.Colors.Surface);

            _headerContainer.Controls.Add(headerCard);
        }

        private void CreateSearchSection()
        {
            var searchCard = new Panel
            {
                BackColor = ResponsiveDesignSystem.Colors.Surface,
                Dock = DockStyle.Fill,
                Padding = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing())
            };

            var searchLabel = new Label
            {
                Text = "🔍 البحث والتصفية",
                Font = ResponsiveDesignSystem.Fonts.GetHeading5(),
                ForeColor = ResponsiveDesignSystem.Colors.TextPrimary,
                Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight(),
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            searchCard.Controls.Add(searchLabel);
            searchCard.Paint += (s, e) => ResponsiveDesignSystem.DrawResponsiveCard(e.Graphics, searchCard.ClientRectangle, ResponsiveDesignSystem.Colors.Surface);

            _searchContainer.Controls.Add(searchCard);
        }

        private void CreateCustomersGrid()
        {
            var customersCard = new Panel
            {
                BackColor = ResponsiveDesignSystem.Colors.Surface,
                Dock = DockStyle.Fill,
                Padding = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing())
            };

            var customersLabel = new Label
            {
                Text = "📋 قائمة العملاء",
                Font = ResponsiveDesignSystem.Fonts.GetHeading5(),
                ForeColor = ResponsiveDesignSystem.Colors.TextPrimary,
                Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight(),
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            _customersGrid = new DataGridView
            {
                BackgroundColor = ResponsiveDesignSystem.Colors.Surface,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                EnableHeadersVisualStyles = false,
                RowHeadersVisible = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                Font = ResponsiveDesignSystem.Fonts.GetBody(),
                RightToLeft = RightToLeft.Yes,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                Dock = DockStyle.Fill
            };

            SetupCustomersGrid();

            customersCard.Controls.AddRange(new Control[] { _customersGrid, customersLabel });
            customersCard.Paint += (s, e) => ResponsiveDesignSystem.DrawResponsiveCard(e.Graphics, customersCard.ClientRectangle, ResponsiveDesignSystem.Colors.Surface);

            _customersContainer.Controls.Add(customersCard);
        }

        private void SetupCustomersGrid()
        {
            _customersGrid.Columns.Clear();

            var columns = new[]
            {
                new { Name = "Id", Header = "رقم العميل", Width = 15 },
                new { Name = "Name", Header = "اسم العميل", Width = 25 },
                new { Name = "Phone", Header = "رقم الهاتف", Width = 20 },
                new { Name = "Email", Header = "البريد الإلكتروني", Width = 25 },
                new { Name = "TotalPurchases", Header = "إجمالي المشتريات", Width = 15 }
            };

            foreach (var col in columns)
            {
                var column = new DataGridViewTextBoxColumn
                {
                    Name = col.Name,
                    HeaderText = col.Header,
                    FillWeight = col.Width
                };
                _customersGrid.Columns.Add(column);
            }

            // تنسيق الرأس المتجاوب
            _customersGrid.ColumnHeadersDefaultCellStyle.BackColor = ResponsiveDesignSystem.Colors.Primary;
            _customersGrid.ColumnHeadersDefaultCellStyle.ForeColor = ResponsiveDesignSystem.Colors.TextOnPrimary;
            _customersGrid.ColumnHeadersDefaultCellStyle.Font = ResponsiveDesignSystem.Fonts.GetBody();
            _customersGrid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _customersGrid.ColumnHeadersHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight();

            // تنسيق الصفوف المتجاوب
            _customersGrid.DefaultCellStyle.BackColor = ResponsiveDesignSystem.Colors.Surface;
            _customersGrid.DefaultCellStyle.ForeColor = ResponsiveDesignSystem.Colors.TextPrimary;
            _customersGrid.DefaultCellStyle.SelectionBackColor = ResponsiveDesignSystem.Colors.PrimaryLight;
            _customersGrid.DefaultCellStyle.SelectionForeColor = ResponsiveDesignSystem.Colors.TextPrimary;
            _customersGrid.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _customersGrid.RowTemplate.Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetInputHeight();

            // تنسيق الصفوف المتناوبة
            _customersGrid.AlternatingRowsDefaultCellStyle.BackColor = ResponsiveDesignSystem.Colors.SurfaceVariant;
        }

        private void CreateActionsSection()
        {
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();
            var buttonHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetLargeButtonHeight();

            _addCustomerButton = CreateActionButton("➕ إضافة عميل", ResponsiveDesignSystem.Colors.Success, buttonHeight);
            _editCustomerButton = CreateActionButton("✏️ تعديل", ResponsiveDesignSystem.Colors.Primary, buttonHeight);
            _deleteCustomerButton = CreateActionButton("🗑️ حذف", ResponsiveDesignSystem.Colors.Error, buttonHeight);

            _actionsContainer.Controls.AddRange(new Control[] { _addCustomerButton, _editCustomerButton, _deleteCustomerButton });
        }

        private Button CreateActionButton(string text, Color color, int height)
        {
            var button = new Button
            {
                Text = text,
                Font = ResponsiveDesignSystem.Fonts.GetButton(),
                FlatStyle = FlatStyle.Flat,
                BackColor = color,
                ForeColor = ResponsiveDesignSystem.Colors.TextOnPrimary,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes,
                Height = height
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ResponsiveDesignSystem.LightenColor(color, 0.1f);
            button.FlatAppearance.MouseDownBackColor = ResponsiveDesignSystem.DarkenColor(color, 0.1f);

            return button;
        }

        private void SetupResponsiveDesign()
        {
            RightToLeft = RightToLeft.Yes;
            Resize += OnResize;
        }

        private void SetupEvents()
        {
            _addCustomerButton.Click += (s, e) => MessageBox.Show("إضافة عميل جديد - قيد التطوير", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
            _editCustomerButton.Click += (s, e) => MessageBox.Show("تعديل العميل - قيد التطوير", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
            _deleteCustomerButton.Click += (s, e) => MessageBox.Show("حذف العميل - قيد التطوير", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void LoadSampleData()
        {
            _customers.AddRange(new[]
            {
                new ResponsiveCustomer { Id = "C001", Name = "أحمد محمد علي", Phone = "0501234567", Email = "<EMAIL>", TotalPurchases = 15750.50m },
                new ResponsiveCustomer { Id = "C002", Name = "فاطمة أحمد", Phone = "0507654321", Email = "<EMAIL>", TotalPurchases = 8920.75m },
                new ResponsiveCustomer { Id = "C003", Name = "محمد عبدالله", Phone = "0551234567", Email = "<EMAIL>", TotalPurchases = 22340.25m }
            });

            _filteredCustomers = new List<ResponsiveCustomer>(_customers);
            RefreshCustomersGrid();
        }

        private void RefreshCustomersGrid()
        {
            _customersGrid.Rows.Clear();

            foreach (var customer in _filteredCustomers)
            {
                var row = new DataGridViewRow();
                row.CreateCells(_customersGrid);
                row.Cells[0].Value = customer.Id;
                row.Cells[1].Value = customer.Name;
                row.Cells[2].Value = customer.Phone;
                row.Cells[3].Value = customer.Email;
                row.Cells[4].Value = $"{customer.TotalPurchases:N2} ر.س";
                row.Tag = customer;
                _customersGrid.Rows.Add(row);
            }
        }

        private void OnResize(object sender, EventArgs e)
        {
            ResponsiveLayoutSystem.HandleResize(this.FindForm());
        }

        #endregion
    }

    #region الكلاسات المساعدة

    public class ResponsiveCustomer
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public decimal TotalPurchases { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;
    }

    public class ResponsiveCustomerEventArgs : EventArgs
    {
        public ResponsiveCustomer Customer { get; }

        public ResponsiveCustomerEventArgs(ResponsiveCustomer customer)
        {
            Customer = customer;
        }
    }

    #endregion
}
