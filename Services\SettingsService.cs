using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AredooPOS.Models.Settings;
using AredooPOS.BLL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Services
{
    /// <summary>
    /// خدمة الإعدادات
    /// توفر واجهة موحدة لإدارة جميع أنواع الإعدادات في النظام
    /// </summary>
    public class SettingsService
    {
        #region المتغيرات والخصائص الخاصة

        private readonly SettingsBLL _settingsBLL;
        private readonly ILogger<SettingsService> _logger;

        // إعدادات مؤقتة للجلسة الحالية
        private readonly Dictionary<string, object> _sessionSettings;

        #endregion

        #region الأحداث

        /// <summary>
        /// حدث عند تغيير إعداد
        /// </summary>
        public event EventHandler<SettingChangedEventArgs> SettingChanged;

        /// <summary>
        /// حدث عند حفظ الإعدادات
        /// </summary>
        public event EventHandler<SettingsSavedEventArgs> SettingsSaved;

        /// <summary>
        /// حدث عند فشل حفظ الإعدادات
        /// </summary>
        public event EventHandler<SettingsSaveFailedEventArgs> SettingsSaveFailed;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ خدمة الإعدادات
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public SettingsService(string connectionString = null, ILogger<SettingsService> logger = null)
        {
            _settingsBLL = new SettingsBLL(connectionString, logger);
            _logger = logger;
            _sessionSettings = new Dictionary<string, object>();

            _logger?.LogInformation("تم تهيئة خدمة الإعدادات");
        }

        #endregion

        #region إدارة الإعدادات العامة

        /// <summary>
        /// الحصول على قيمة إعداد
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>قيمة الإعداد</returns>
        public async Task<string> GetSettingValueAsync(string settingName, string defaultValue = null)
        {
            try
            {
                return await Task.Run(() => _settingsBLL.GetSettingValue(settingName, defaultValue));
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على قيمة الإعداد {settingName}");
                return defaultValue;
            }
        }

        /// <summary>
        /// الحصول على قيمة إعداد كرقم صحيح
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>قيمة الإعداد</returns>
        public async Task<int> GetSettingValueAsIntAsync(string settingName, int defaultValue = 0)
        {
            try
            {
                return await Task.Run(() => _settingsBLL.GetSettingValueAsInt(settingName, defaultValue));
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على قيمة الإعداد {settingName} كرقم صحيح");
                return defaultValue;
            }
        }

        /// <summary>
        /// الحصول على قيمة إعداد كرقم عشري
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>قيمة الإعداد</returns>
        public async Task<decimal> GetSettingValueAsDecimalAsync(string settingName, decimal defaultValue = 0)
        {
            try
            {
                return await Task.Run(() => _settingsBLL.GetSettingValueAsDecimal(settingName, defaultValue));
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على قيمة الإعداد {settingName} كرقم عشري");
                return defaultValue;
            }
        }

        /// <summary>
        /// الحصول على قيمة إعداد كقيمة منطقية
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>قيمة الإعداد</returns>
        public async Task<bool> GetSettingValueAsBoolAsync(string settingName, bool defaultValue = false)
        {
            try
            {
                return await Task.Run(() => _settingsBLL.GetSettingValueAsBool(settingName, defaultValue));
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على قيمة الإعداد {settingName} كقيمة منطقية");
                return defaultValue;
            }
        }

        /// <summary>
        /// حفظ قيمة إعداد
        /// </summary>
        /// <param name="settingName">اسم الإعداد</param>
        /// <param name="settingValue">قيمة الإعداد</param>
        /// <param name="updatedBy">من قام بالتحديث</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public async Task<bool> SaveSettingValueAsync(string settingName, string settingValue, string updatedBy)
        {
            try
            {
                var oldValue = await GetSettingValueAsync(settingName);
                var result = await Task.Run(() => _settingsBLL.SaveSettingValue(settingName, settingValue, updatedBy));

                if (result)
                {
                    // إثارة حدث تغيير الإعداد
                    SettingChanged?.Invoke(this, new SettingChangedEventArgs
                    {
                        SettingName = settingName,
                        OldValue = oldValue,
                        NewValue = settingValue,
                        UpdatedBy = updatedBy,
                        UpdatedAt = DateTime.Now
                    });

                    _logger?.LogInformation($"تم حفظ الإعداد {settingName} بالقيمة {settingValue}");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حفظ الإعداد {settingName}");
                
                // إثارة حدث فشل الحفظ
                SettingsSaveFailed?.Invoke(this, new SettingsSaveFailedEventArgs
                {
                    SettingName = settingName,
                    ErrorMessage = ex.Message,
                    FailedAt = DateTime.Now
                });

                return false;
            }
        }

        /// <summary>
        /// حفظ عدة إعدادات
        /// </summary>
        /// <param name="settings">قاموس الإعدادات</param>
        /// <param name="updatedBy">من قام بالتحديث</param>
        /// <returns>عدد الإعدادات المحفوظة</returns>
        public async Task<int> SaveMultipleSettingsAsync(Dictionary<string, string> settings, string updatedBy)
        {
            try
            {
                var savedCount = await Task.Run(() => _settingsBLL.SaveMultipleSettings(settings, updatedBy));

                if (savedCount > 0)
                {
                    // إثارة حدث حفظ الإعدادات
                    SettingsSaved?.Invoke(this, new SettingsSavedEventArgs
                    {
                        SavedCount = savedCount,
                        TotalCount = settings.Count,
                        UpdatedBy = updatedBy,
                        SavedAt = DateTime.Now
                    });

                    _logger?.LogInformation($"تم حفظ {savedCount} من أصل {settings.Count} إعداد");
                }

                return savedCount;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ عدة إعدادات");
                
                SettingsSaveFailed?.Invoke(this, new SettingsSaveFailedEventArgs
                {
                    SettingName = "Multiple Settings",
                    ErrorMessage = ex.Message,
                    FailedAt = DateTime.Now
                });

                return 0;
            }
        }

        #endregion

        #region إدارة الإعدادات المتخصصة

        /// <summary>
        /// الحصول على إعدادات الضريبة
        /// </summary>
        /// <returns>إعدادات الضريبة</returns>
        public async Task<TaxSettings> GetTaxSettingsAsync()
        {
            try
            {
                return await Task.Run(() => _settingsBLL.GetTaxSettings());
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إعدادات الضريبة");
                return new TaxSettings();
            }
        }

        /// <summary>
        /// حفظ إعدادات الضريبة
        /// </summary>
        /// <param name="taxSettings">إعدادات الضريبة</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public async Task<bool> SaveTaxSettingsAsync(TaxSettings taxSettings)
        {
            try
            {
                var result = await Task.Run(() => _settingsBLL.SaveTaxSettings(taxSettings));

                if (result)
                {
                    SettingsSaved?.Invoke(this, new SettingsSavedEventArgs
                    {
                        SavedCount = 1,
                        TotalCount = 1,
                        UpdatedBy = "System",
                        SavedAt = DateTime.Now,
                        SettingType = "Tax"
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ إعدادات الضريبة");
                return false;
            }
        }

        /// <summary>
        /// الحصول على إعدادات العملة
        /// </summary>
        /// <returns>إعدادات العملة</returns>
        public async Task<CurrencySettings> GetCurrencySettingsAsync()
        {
            try
            {
                return await Task.Run(() => _settingsBLL.GetCurrencySettings());
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إعدادات العملة");
                return new CurrencySettings();
            }
        }

        /// <summary>
        /// حفظ إعدادات العملة
        /// </summary>
        /// <param name="currencySettings">إعدادات العملة</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public async Task<bool> SaveCurrencySettingsAsync(CurrencySettings currencySettings)
        {
            try
            {
                var result = await Task.Run(() => _settingsBLL.SaveCurrencySettings(currencySettings));

                if (result)
                {
                    SettingsSaved?.Invoke(this, new SettingsSavedEventArgs
                    {
                        SavedCount = 1,
                        TotalCount = 1,
                        UpdatedBy = "System",
                        SavedAt = DateTime.Now,
                        SettingType = "Currency"
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ إعدادات العملة");
                return false;
            }
        }

        /// <summary>
        /// الحصول على إعدادات المتجر
        /// </summary>
        /// <returns>إعدادات المتجر</returns>
        public async Task<StoreSettings> GetStoreSettingsAsync()
        {
            try
            {
                return await Task.Run(() => _settingsBLL.GetStoreSettings());
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إعدادات المتجر");
                return new StoreSettings();
            }
        }

        /// <summary>
        /// حفظ إعدادات المتجر
        /// </summary>
        /// <param name="storeSettings">إعدادات المتجر</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public async Task<bool> SaveStoreSettingsAsync(StoreSettings storeSettings)
        {
            try
            {
                var result = await Task.Run(() => _settingsBLL.SaveStoreSettings(storeSettings));

                if (result)
                {
                    SettingsSaved?.Invoke(this, new SettingsSavedEventArgs
                    {
                        SavedCount = 1,
                        TotalCount = 1,
                        UpdatedBy = "System",
                        SavedAt = DateTime.Now,
                        SettingType = "Store"
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ إعدادات المتجر");
                return false;
            }
        }

        /// <summary>
        /// الحصول على إعدادات الطباعة
        /// </summary>
        /// <returns>إعدادات الطباعة</returns>
        public async Task<PrintSettings> GetPrintSettingsAsync()
        {
            try
            {
                return await Task.Run(() => _settingsBLL.GetPrintSettings());
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إعدادات الطباعة");
                return new PrintSettings();
            }
        }

        /// <summary>
        /// حفظ إعدادات الطباعة
        /// </summary>
        /// <param name="printSettings">إعدادات الطباعة</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public async Task<bool> SavePrintSettingsAsync(PrintSettings printSettings)
        {
            try
            {
                var result = await Task.Run(() => _settingsBLL.SavePrintSettings(printSettings));

                if (result)
                {
                    SettingsSaved?.Invoke(this, new SettingsSavedEventArgs
                    {
                        SavedCount = 1,
                        TotalCount = 1,
                        UpdatedBy = "System",
                        SavedAt = DateTime.Now,
                        SettingType = "Print"
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ إعدادات الطباعة");
                return false;
            }
        }

        #endregion

        #region العمليات المتقدمة

        /// <summary>
        /// تهيئة الإعدادات الافتراضية
        /// </summary>
        /// <param name="updatedBy">من قام بالتهيئة</param>
        /// <returns>true إذا تم التهيئة بنجاح</returns>
        public async Task<bool> InitializeDefaultSettingsAsync(string updatedBy)
        {
            try
            {
                var result = await Task.Run(() => _settingsBLL.InitializeDefaultSettings(updatedBy));

                if (result)
                {
                    _logger?.LogInformation("تم تهيئة الإعدادات الافتراضية بنجاح");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تهيئة الإعدادات الافتراضية");
                return false;
            }
        }

        /// <summary>
        /// التحقق من اكتمال الإعدادات الأساسية
        /// </summary>
        /// <returns>true إذا كانت الإعدادات مكتملة</returns>
        public async Task<bool> ValidateEssentialSettingsAsync()
        {
            try
            {
                return await Task.Run(() => _settingsBLL.ValidateEssentialSettings());
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في التحقق من الإعدادات الأساسية");
                return false;
            }
        }

        /// <summary>
        /// الحصول على ملخص الإعدادات
        /// </summary>
        /// <returns>ملخص الإعدادات</returns>
        public async Task<SettingsSummary> GetSettingsSummaryAsync()
        {
            try
            {
                return await Task.Run(() => _settingsBLL.GetSettingsSummary());
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على ملخص الإعدادات");
                return new SettingsSummary();
            }
        }

        /// <summary>
        /// تصدير جميع الإعدادات
        /// </summary>
        /// <returns>البيانات المصدرة</returns>
        public async Task<string> ExportAllSettingsAsync()
        {
            try
            {
                return await Task.Run(() => _settingsBLL.ExportAllSettings());
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تصدير الإعدادات");
                throw;
            }
        }

        /// <summary>
        /// استيراد الإعدادات
        /// </summary>
        /// <param name="jsonData">البيانات المستوردة</param>
        /// <param name="updatedBy">من قام بالاستيراد</param>
        /// <returns>true إذا تم الاستيراد بنجاح</returns>
        public async Task<bool> ImportSettingsAsync(string jsonData, string updatedBy)
        {
            try
            {
                var result = await Task.Run(() => _settingsBLL.ImportSettings(jsonData, updatedBy));

                if (result)
                {
                    SettingsSaved?.Invoke(this, new SettingsSavedEventArgs
                    {
                        SavedCount = 1,
                        TotalCount = 1,
                        UpdatedBy = updatedBy,
                        SavedAt = DateTime.Now,
                        SettingType = "Import"
                    });

                    _logger?.LogInformation("تم استيراد الإعدادات بنجاح");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في استيراد الإعدادات");
                return false;
            }
        }

        /// <summary>
        /// إعادة تعيين الإعدادات إلى القيم الافتراضية
        /// </summary>
        /// <param name="category">الفئة (اختياري)</param>
        /// <param name="updatedBy">من قام بالإعادة</param>
        /// <returns>true إذا تم الإعادة بنجاح</returns>
        public async Task<bool> ResetSettingsToDefaultAsync(string category, string updatedBy)
        {
            try
            {
                var result = await Task.Run(() => _settingsBLL.ResetSettingsToDefault(category, updatedBy));

                if (result)
                {
                    SettingsSaved?.Invoke(this, new SettingsSavedEventArgs
                    {
                        SavedCount = 1,
                        TotalCount = 1,
                        UpdatedBy = updatedBy,
                        SavedAt = DateTime.Now,
                        SettingType = "Reset"
                    });

                    _logger?.LogInformation($"تم إعادة تعيين إعدادات الفئة {category} إلى القيم الافتراضية");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إعادة تعيين الإعدادات");
                return false;
            }
        }

        #endregion

        #region إدارة الإعدادات المؤقتة

        /// <summary>
        /// تعيين إعداد مؤقت للجلسة
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <param name="value">القيمة</param>
        public void SetSessionSetting(string key, object value)
        {
            _sessionSettings[key] = value;
        }

        /// <summary>
        /// الحصول على إعداد مؤقت من الجلسة
        /// </summary>
        /// <typeparam name="T">نوع البيانات</typeparam>
        /// <param name="key">المفتاح</param>
        /// <param name="defaultValue">القيمة الافتراضية</param>
        /// <returns>القيمة</returns>
        public T GetSessionSetting<T>(string key, T defaultValue = default)
        {
            if (_sessionSettings.TryGetValue(key, out var value) && value is T)
            {
                return (T)value;
            }
            return defaultValue;
        }

        /// <summary>
        /// إزالة إعداد مؤقت من الجلسة
        /// </summary>
        /// <param name="key">المفتاح</param>
        /// <returns>true إذا تم الحذف</returns>
        public bool RemoveSessionSetting(string key)
        {
            return _sessionSettings.Remove(key);
        }

        /// <summary>
        /// مسح جميع الإعدادات المؤقتة
        /// </summary>
        public void ClearSessionSettings()
        {
            _sessionSettings.Clear();
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// حساب الضريبة
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="productID">رقم المنتج (اختياري)</param>
        /// <param name="categoryID">رقم الفئة (اختياري)</param>
        /// <returns>مبلغ الضريبة</returns>
        public async Task<decimal> CalculateTaxAsync(decimal amount, int? productID = null, int? categoryID = null)
        {
            try
            {
                return await Task.Run(() => _settingsBLL.CalculateTax(amount, productID, categoryID));
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حساب الضريبة");
                return 0;
            }
        }

        /// <summary>
        /// تنسيق المبلغ بالعملة
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ منسق</returns>
        public async Task<string> FormatCurrencyAsync(decimal amount)
        {
            try
            {
                return await Task.Run(() => _settingsBLL.FormatCurrency(amount));
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تنسيق العملة");
                return amount.ToString("N2");
            }
        }

        /// <summary>
        /// تحويل العملة
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="fromCurrency">من عملة</param>
        /// <param name="toCurrency">إلى عملة</param>
        /// <returns>المبلغ محول</returns>
        public async Task<decimal> ConvertCurrencyAsync(decimal amount, string fromCurrency, string toCurrency)
        {
            try
            {
                return await Task.Run(() => _settingsBLL.ConvertCurrency(amount, fromCurrency, toCurrency));
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحويل العملة");
                return amount;
            }
        }

        /// <summary>
        /// مسح كاش الإعدادات
        /// </summary>
        public void ClearSettingsCache()
        {
            _settingsBLL.ClearCache();
        }

        #endregion
    }

    #region نماذج الأحداث

    /// <summary>
    /// بيانات حدث تغيير الإعداد
    /// </summary>
    public class SettingChangedEventArgs : EventArgs
    {
        public string SettingName { get; set; }
        public string OldValue { get; set; }
        public string NewValue { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// بيانات حدث حفظ الإعدادات
    /// </summary>
    public class SettingsSavedEventArgs : EventArgs
    {
        public int SavedCount { get; set; }
        public int TotalCount { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime SavedAt { get; set; }
        public string SettingType { get; set; }
    }

    /// <summary>
    /// بيانات حدث فشل حفظ الإعدادات
    /// </summary>
    public class SettingsSaveFailedEventArgs : EventArgs
    {
        public string SettingName { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime FailedAt { get; set; }
    }

    #endregion
}
