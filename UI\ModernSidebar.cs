using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// الشريط الجانبي الحديث لتطبيق أريدو الكاشير
    /// </summary>
    public class ModernSidebar : UserControl
    {
        #region المتغيرات

        private bool _isCollapsed = false;
        private List<SidebarItem> _menuItems;
        private SidebarItem _selectedItem;
        private Panel _headerPanel;
        private Panel _menuPanel;
        private Panel _footerPanel;
        private Button _collapseButton;

        #endregion

        #region الخصائص

        /// <summary>
        /// حالة الطي
        /// </summary>
        public bool IsCollapsed
        {
            get => _isCollapsed;
            set
            {
                _isCollapsed = value;
                UpdateLayout();
            }
        }

        /// <summary>
        /// العنصر المحدد
        /// </summary>
        public SidebarItem SelectedItem
        {
            get => _selectedItem;
            set
            {
                if (_selectedItem != value)
                {
                    _selectedItem = value;
                    UpdateSelection();
                    ItemSelected?.Invoke(this, new SidebarItemEventArgs(value));
                }
            }
        }

        #endregion

        #region الأحداث

        /// <summary>
        /// حدث تحديد عنصر من القائمة
        /// </summary>
        public event EventHandler<SidebarItemEventArgs> ItemSelected;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ الشريط الجانبي
        /// </summary>
        public ModernSidebar()
        {
            InitializeMenuItems();
            InitializeComponent();
            SetupDesign();
        }

        /// <summary>
        /// تهيئة عناصر القائمة
        /// </summary>
        private void InitializeMenuItems()
        {
            _menuItems = new List<SidebarItem>
            {
                new SidebarItem("dashboard", "لوحة المعلومات", "📊", ModernDesignSystem.Colors.Primary),
                new SidebarItem("sales", "المبيعات", "💰", ModernDesignSystem.Colors.Success),
                new SidebarItem("invoices", "الفواتير", "📄", ModernDesignSystem.Colors.Info),
                new SidebarItem("customers", "العملاء", "👥", ModernDesignSystem.Colors.Secondary),
                new SidebarItem("products", "المنتجات", "📦", Color.FromArgb(255, 140, 0)),
                new SidebarItem("installments", "الأقساط", "📅", Color.FromArgb(138, 43, 226)),
                new SidebarItem("debts", "الديون", "💳", ModernDesignSystem.Colors.Warning),
                new SidebarItem("reports", "التقارير", "📈", Color.FromArgb(220, 20, 60)),
                new SidebarItem("settings", "الإعدادات", "⚙️", ModernDesignSystem.Colors.TextSecondary)
            };

            // تحديد العنصر الأول كمحدد افتراضياً
            _selectedItem = _menuItems.FirstOrDefault();
        }

        /// <summary>
        /// تهيئة المكونات
        /// </summary>
        private void InitializeComponent()
        {
            // إعدادات الشريط الجانبي
            Size = new Size(ModernDesignSystem.Dimensions.SidebarWidth, 600);
            BackColor = ModernDesignSystem.Colors.Surface;
            Dock = DockStyle.Left;
            Font = ModernDesignSystem.Fonts.Body;

            // لوحة الرأس
            _headerPanel = new Panel
            {
                Size = new Size(Width, 80),
                Location = new Point(0, 0),
                BackColor = Color.Transparent,
                Dock = DockStyle.Top
            };

            // لوحة القائمة
            _menuPanel = new Panel
            {
                BackColor = Color.Transparent,
                Dock = DockStyle.Fill,
                AutoScroll = true
            };

            // لوحة التذييل
            _footerPanel = new Panel
            {
                Size = new Size(Width, 60),
                BackColor = Color.Transparent,
                Dock = DockStyle.Bottom
            };

            // زر الطي/التوسيع
            _collapseButton = new Button
            {
                Text = "☰",
                Font = new Font("Segoe UI Emoji", 16),
                Size = new Size(40, 40),
                Location = new Point(Width - 50, 20),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent,
                ForeColor = ModernDesignSystem.Colors.TextSecondary,
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            _collapseButton.FlatAppearance.BorderSize = 0;
            _collapseButton.FlatAppearance.MouseOverBackColor = ModernDesignSystem.Colors.Hover;
            _collapseButton.Click += OnCollapseButtonClick;

            // إضافة العناصر
            _headerPanel.Controls.Add(_collapseButton);
            Controls.AddRange(new Control[] { _menuPanel, _headerPanel, _footerPanel });

            // إنشاء عناصر القائمة
            CreateMenuItems();
        }

        /// <summary>
        /// إنشاء عناصر القائمة
        /// </summary>
        private void CreateMenuItems()
        {
            _menuPanel.Controls.Clear();
            
            var yPosition = 10;
            foreach (var item in _menuItems)
            {
                var menuButton = CreateMenuButton(item, yPosition);
                _menuPanel.Controls.Add(menuButton);
                yPosition += 60;
            }
        }

        /// <summary>
        /// إنشاء زر القائمة
        /// </summary>
        private Control CreateMenuButton(SidebarItem item, int yPosition)
        {
            var button = new Panel
            {
                Size = new Size(Width - 20, 50),
                Location = new Point(10, yPosition),
                BackColor = Color.Transparent,
                Cursor = Cursors.Hand,
                Tag = item
            };

            // أيقونة العنصر
            var iconLabel = new Label
            {
                Text = item.Icon,
                Font = new Font("Segoe UI Emoji", 20),
                ForeColor = item.Color,
                Size = new Size(40, 40),
                Location = new Point(_isCollapsed ? 10 : Width - 60, 5),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            // نص العنصر
            var textLabel = new Label
            {
                Text = item.Title,
                Font = ModernDesignSystem.Fonts.BodyBold,
                ForeColor = ModernDesignSystem.Colors.TextPrimary,
                Size = new Size(Width - 80, 40),
                Location = new Point(10, 5),
                TextAlign = ContentAlignment.MiddleRight,
                BackColor = Color.Transparent,
                RightToLeft = RightToLeft.Yes,
                Visible = !_isCollapsed
            };

            // إضافة الأحداث
            button.Click += (s, e) => OnMenuItemClick(item);
            button.MouseEnter += (s, e) => OnMenuItemHover(button, true);
            button.MouseLeave += (s, e) => OnMenuItemHover(button, false);
            button.Paint += (s, e) => OnMenuItemPaint(button, e, item);

            // إضافة العناصر للزر
            button.Controls.AddRange(new Control[] { iconLabel, textLabel });

            return button;
        }

        /// <summary>
        /// إعداد التصميم
        /// </summary>
        private void SetupDesign()
        {
            // تطبيق التخطيط العربي
            RightToLeft = RightToLeft.Yes;

            // إضافة حدود
            Paint += OnPaint;
        }

        #endregion

        #region الأحداث

        /// <summary>
        /// النقر على زر الطي
        /// </summary>
        private void OnCollapseButtonClick(object sender, EventArgs e)
        {
            IsCollapsed = !IsCollapsed;
        }

        /// <summary>
        /// النقر على عنصر القائمة
        /// </summary>
        private void OnMenuItemClick(SidebarItem item)
        {
            SelectedItem = item;
        }

        /// <summary>
        /// تمرير الماوس على عنصر القائمة
        /// </summary>
        private void OnMenuItemHover(Panel button, bool isHovered)
        {
            var item = (SidebarItem)button.Tag;
            if (item != _selectedItem)
            {
                button.BackColor = isHovered ? 
                    ModernDesignSystem.Colors.Hover : 
                    Color.Transparent;
            }
        }

        /// <summary>
        /// رسم عنصر القائمة
        /// </summary>
        private void OnMenuItemPaint(Panel button, PaintEventArgs e, SidebarItem item)
        {
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // رسم الخلفية للعنصر المحدد
            if (item == _selectedItem)
            {
                var selectedBounds = new Rectangle(0, 0, button.Width, button.Height);
                using (var selectedBrush = new SolidBrush(Color.FromArgb(30, item.Color)))
                using (var selectedPath = ModernDesignSystem.CreateRoundedRectangle(selectedBounds, 8))
                {
                    g.FillPath(selectedBrush, selectedPath);
                }

                // رسم خط التحديد
                using (var selectedPen = new Pen(item.Color, 3))
                {
                    g.DrawLine(selectedPen, button.Width - 3, 10, button.Width - 3, button.Height - 10);
                }
            }
        }

        /// <summary>
        /// رسم الشريط الجانبي
        /// </summary>
        private void OnPaint(object sender, PaintEventArgs e)
        {
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // رسم الخلفية
            using (var backgroundBrush = new SolidBrush(ModernDesignSystem.Colors.Surface))
            {
                g.FillRectangle(backgroundBrush, ClientRectangle);
            }

            // رسم الحد الأيمن
            using (var borderPen = new Pen(ModernDesignSystem.Colors.Border, 1))
            {
                g.DrawLine(borderPen, Width - 1, 0, Width - 1, Height);
            }

            // رسم عنوان الشريط الجانبي (إذا لم يكن مطوياً)
            if (!_isCollapsed)
            {
                var titleRect = new Rectangle(20, 20, Width - 70, 40);
                using (var titleBrush = new SolidBrush(ModernDesignSystem.Colors.TextPrimary))
                {
                    var titleFormat = new StringFormat
                    {
                        Alignment = StringAlignment.Far,
                        LineAlignment = StringAlignment.Center,
                        FormatFlags = StringFormatFlags.DirectionRightToLeft
                    };
                    g.DrawString("القائمة الرئيسية", ModernDesignSystem.Fonts.Heading4, titleBrush, titleRect, titleFormat);
                }
            }
        }

        #endregion

        #region التحديثات

        /// <summary>
        /// تحديث التخطيط
        /// </summary>
        private void UpdateLayout()
        {
            var newWidth = _isCollapsed ? 
                ModernDesignSystem.Dimensions.SidebarCollapsedWidth : 
                ModernDesignSystem.Dimensions.SidebarWidth;

            Width = newWidth;
            _collapseButton.Text = _isCollapsed ? "☰" : "✖";
            _collapseButton.Location = new Point(Width - 50, 20);

            // إعادة إنشاء عناصر القائمة
            CreateMenuItems();
            Invalidate();
        }

        /// <summary>
        /// تحديث التحديد
        /// </summary>
        private void UpdateSelection()
        {
            // إعادة رسم جميع العناصر
            foreach (Control control in _menuPanel.Controls)
            {
                control.Invalidate();
            }
        }

        #endregion
    }

    #region الكلاسات المساعدة

    /// <summary>
    /// عنصر الشريط الجانبي
    /// </summary>
    public class SidebarItem
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string Icon { get; set; }
        public Color Color { get; set; }

        public SidebarItem(string id, string title, string icon, Color color)
        {
            Id = id;
            Title = title;
            Icon = icon;
            Color = color;
        }
    }

    /// <summary>
    /// معطيات حدث تحديد عنصر الشريط الجانبي
    /// </summary>
    public class SidebarItemEventArgs : EventArgs
    {
        public SidebarItem Item { get; }

        public SidebarItemEventArgs(SidebarItem item)
        {
            Item = item;
        }
    }

    #endregion
}
