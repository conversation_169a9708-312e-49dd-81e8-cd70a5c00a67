using System;
using System.Drawing;
using System.Windows.Forms;
using System.Globalization;
using System.Threading;
using AredooCashier.UI;

namespace AredooCashier
{
    /// <summary>
    /// التطبيق الرئيسي لكاشير أريدو الحديث
    /// </summary>
    public partial class AredooCashierApp : Form
    {
        #region المتغيرات

        private ModernTopBar _topBar;
        private ModernSidebar _sidebar;
        private Panel _contentPanel;
        private UserControl _currentView;

        // الواجهات
        private ModernDashboard _dashboardView;
        private ModernInvoiceForm _invoiceView;
        private UserControl _customersView;
        private UserControl _productsView;
        private UserControl _reportsView;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ التطبيق الرئيسي
        /// </summary>
        public AredooCashierApp()
        {
            InitializeComponent();
            SetupArabicCulture();
            SetupModernDesign();
            SetupEvents();
            LoadDashboard();
        }

        /// <summary>
        /// تهيئة المكونات
        /// </summary>
        private void InitializeComponent()
        {
            // إعدادات النموذج الرئيسي
            Text = "أريدو الكاشير - نظام نقاط البيع الحديث";
            Size = new Size(1400, 900);
            StartPosition = FormStartPosition.CenterScreen;
            WindowState = FormWindowState.Maximized;
            MinimumSize = new Size(1024, 768);
            BackColor = ModernDesignSystem.Colors.Background;
            Font = ModernDesignSystem.Fonts.Body;
            Icon = SystemIcons.Application;

            // الشريط العلوي
            _topBar = new ModernTopBar
            {
                UserName = "أحمد محمد الكاشير",
                UserRole = "كاشير رئيسي",
                StoreName = "متجر الأمل التجاري",
                IsOnline = true
            };

            // الشريط الجانبي
            _sidebar = new ModernSidebar();

            // لوحة المحتوى
            _contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = ModernDesignSystem.Colors.Background,
                Padding = new Padding(0)
            };

            // ترتيب العناصر
            Controls.Add(_contentPanel);
            Controls.Add(_sidebar);
            Controls.Add(_topBar);
        }

        /// <summary>
        /// إعداد الثقافة العربية
        /// </summary>
        private void SetupArabicCulture()
        {
            try
            {
                var arabicCulture = new CultureInfo("ar-SA");
                Thread.CurrentThread.CurrentCulture = arabicCulture;
                Thread.CurrentThread.CurrentUICulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentCulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentUICulture = arabicCulture;

                // تطبيق التخطيط العربي
                RightToLeft = RightToLeft.Yes;
                RightToLeftLayout = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"تحذير: فشل في تعيين الثقافة العربية - {ex.Message}", 
                    "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// إعداد التصميم الحديث
        /// </summary>
        private void SetupModernDesign()
        {
            // تطبيق التصميم الحديث على النموذج
            SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
            
            // إنشاء الواجهات
            CreateViews();
        }

        /// <summary>
        /// إنشاء الواجهات
        /// </summary>
        private void CreateViews()
        {
            // لوحة المعلومات
            _dashboardView = new ModernDashboard();
            _dashboardView.NewInvoiceClicked += (s, e) => LoadInvoiceView();
            _dashboardView.AddCustomerClicked += (s, e) => LoadCustomersView();
            _dashboardView.AddProductClicked += (s, e) => LoadProductsView();
            _dashboardView.OpenDrawerClicked += (s, e) => OpenCashDrawer();

            // نموذج الفاتورة
            _invoiceView = new ModernInvoiceForm();
            _invoiceView.InvoiceSaved += OnInvoiceSaved;
            _invoiceView.InvoicePrinted += OnInvoicePrinted;
            _invoiceView.InvoiceCleared += OnInvoiceCleared;

            // الواجهات الأخرى (مبسطة)
            _customersView = CreatePlaceholderView("👥 إدارة العملاء", "هنا سيتم عرض قائمة العملاء وإدارة بياناتهم");
            _productsView = CreatePlaceholderView("📦 إدارة المنتجات", "هنا سيتم عرض قائمة المنتجات وإدارة المخزون");
            _reportsView = CreatePlaceholderView("📊 التقارير والإحصائيات", "هنا سيتم عرض التقارير المختلفة والرسوم البيانية");
        }

        /// <summary>
        /// إنشاء واجهة مؤقتة
        /// </summary>
        private UserControl CreatePlaceholderView(string title, string description)
        {
            var view = new UserControl
            {
                Dock = DockStyle.Fill,
                BackColor = ModernDesignSystem.Colors.Background,
                RightToLeft = RightToLeft.Yes
            };

            var card = new Panel
            {
                Size = new Size(600, 300),
                BackColor = ModernDesignSystem.Colors.Surface,
                Anchor = AnchorStyles.None
            };
            card.Location = new Point((view.Width - card.Width) / 2, (view.Height - card.Height) / 2);

            var titleLabel = new Label
            {
                Text = title,
                Font = ModernDesignSystem.Fonts.Heading2,
                ForeColor = ModernDesignSystem.Colors.Primary,
                Size = new Size(card.Width - 40, 50),
                Location = new Point(20, 30),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            var descLabel = new Label
            {
                Text = description,
                Font = ModernDesignSystem.Fonts.Body,
                ForeColor = ModernDesignSystem.Colors.TextSecondary,
                Size = new Size(card.Width - 40, 100),
                Location = new Point(20, 100),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            var comingSoonLabel = new Label
            {
                Text = "🚧 قريباً - قيد التطوير",
                Font = ModernDesignSystem.Fonts.Heading4,
                ForeColor = ModernDesignSystem.Colors.Warning,
                Size = new Size(card.Width - 40, 40),
                Location = new Point(20, 220),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            card.Controls.AddRange(new Control[] { titleLabel, descLabel, comingSoonLabel });
            card.Paint += (s, e) => ModernDesignSystem.DrawModernCard(e.Graphics, card.ClientRectangle, ModernDesignSystem.Colors.Surface);

            view.Controls.Add(card);
            return view;
        }

        /// <summary>
        /// إعداد الأحداث
        /// </summary>
        private void SetupEvents()
        {
            // أحداث الشريط العلوي
            _topBar.SettingsClicked += OnSettingsClicked;
            _topBar.LogoutClicked += OnLogoutClicked;

            // أحداث الشريط الجانبي
            _sidebar.ItemSelected += OnSidebarItemSelected;

            // أحداث النموذج
            Load += OnFormLoad;
            FormClosing += OnFormClosing;
        }

        #endregion

        #region التنقل بين الواجهات

        /// <summary>
        /// تحميل واجهة في لوحة المحتوى
        /// </summary>
        private void LoadView(UserControl view)
        {
            if (_currentView != null)
            {
                _contentPanel.Controls.Remove(_currentView);
            }

            _currentView = view;
            _currentView.Dock = DockStyle.Fill;
            _contentPanel.Controls.Add(_currentView);
            _currentView.BringToFront();
        }

        /// <summary>
        /// تحميل لوحة المعلومات
        /// </summary>
        private void LoadDashboard()
        {
            LoadView(_dashboardView);
        }

        /// <summary>
        /// تحميل نموذج الفاتورة
        /// </summary>
        private void LoadInvoiceView()
        {
            LoadView(_invoiceView);
        }

        /// <summary>
        /// تحميل واجهة العملاء
        /// </summary>
        private void LoadCustomersView()
        {
            LoadView(_customersView);
        }

        /// <summary>
        /// تحميل واجهة المنتجات
        /// </summary>
        private void LoadProductsView()
        {
            LoadView(_productsView);
        }

        /// <summary>
        /// تحميل واجهة التقارير
        /// </summary>
        private void LoadReportsView()
        {
            LoadView(_reportsView);
        }

        #endregion

        #region معالجات الأحداث

        /// <summary>
        /// تحديد عنصر من الشريط الجانبي
        /// </summary>
        private void OnSidebarItemSelected(object sender, SidebarItemEventArgs e)
        {
            switch (e.Item.Id)
            {
                case "dashboard":
                    LoadDashboard();
                    break;
                case "sales":
                case "invoices":
                    LoadInvoiceView();
                    break;
                case "customers":
                    LoadCustomersView();
                    break;
                case "products":
                    LoadProductsView();
                    break;
                case "reports":
                    LoadReportsView();
                    break;
                case "installments":
                case "debts":
                case "settings":
                    ShowComingSoon(e.Item.Title);
                    break;
            }
        }

        /// <summary>
        /// النقر على زر الإعدادات
        /// </summary>
        private void OnSettingsClicked(object sender, EventArgs e)
        {
            ShowComingSoon("الإعدادات");
        }

        /// <summary>
        /// النقر على زر تسجيل الخروج
        /// </summary>
        private void OnLogoutClicked(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد تسجيل الخروج من النظام؟",
                "تأكيد تسجيل الخروج",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        /// <summary>
        /// حفظ الفاتورة
        /// </summary>
        private void OnInvoiceSaved(object sender, EventArgs e)
        {
            MessageBox.Show("تم حفظ الفاتورة بنجاح!", "نجح الحفظ", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// طباعة الفاتورة
        /// </summary>
        private void OnInvoicePrinted(object sender, EventArgs e)
        {
            MessageBox.Show("تم إرسال الفاتورة للطباعة!", "نجحت الطباعة", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// مسح الفاتورة
        /// </summary>
        private void OnInvoiceCleared(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد مسح جميع بيانات الفاتورة؟",
                "تأكيد المسح",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                MessageBox.Show("تم مسح الفاتورة!", "تم المسح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        /// <summary>
        /// فتح درج النقود
        /// </summary>
        private void OpenCashDrawer()
        {
            MessageBox.Show("تم فتح درج النقود!", "درج النقود", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// عرض رسالة قريباً
        /// </summary>
        private void ShowComingSoon(string feature)
        {
            MessageBox.Show($"ميزة '{feature}' قيد التطوير وستكون متاحة قريباً!", 
                "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// تحميل النموذج
        /// </summary>
        private void OnFormLoad(object sender, EventArgs e)
        {
            // رسالة ترحيب
            MessageBox.Show(
                "🎉 مرحباً بك في نظام أريدو الكاشير الحديث!\n\n" +
                "✨ تم تطوير هذا النظام خصيصاً للتجار العرب\n" +
                "🎨 تصميم حديث مستوحى من Microsoft Fluent Design\n" +
                "🌐 دعم كامل للغة العربية مع RTL\n" +
                "📱 واجهة سهلة الاستخدام ومناسبة للشاشات اللمسية\n\n" +
                "🚀 ابدأ رحلتك في إدارة نقاط البيع بكفاءة عالية!",
                "مرحباً بك في أريدو",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        /// <summary>
        /// إغلاق النموذج
        /// </summary>
        private void OnFormClosing(object sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد إغلاق نظام أريدو الكاشير؟",
                "تأكيد الإغلاق",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.No)
            {
                e.Cancel = true;
            }
        }

        #endregion
    }
}
