# نظام أريدوو للكاشير - ملف .gitignore
# Aredoo POS System - .gitignore file

# ===================================================================
# ملفات Visual Studio والبناء
# Visual Studio and Build Files
# ===================================================================

# User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# User-specific files (MonoDevelop/Xamarin Studio)
*.userprefs

# Mono auto generated files
mono_crash.*

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Visual Studio 2015/2017 cache/options directory
.vs/

# Uncomment if you have tasks that create the project's static files in wwwroot
#wwwroot/

# Visual Studio 2017 auto generated files
Generated\ Files/

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NUnit
*.VisualState.xml
TestResult.xml
nunit-*.xml

# Build Results of an ATL Project
[Dd]ebugPS/
[Rr]eleasePS/
dlldata.c

# Benchmark Results
BenchmarkDotNet.Artifacts/

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

# ASP.NET Scaffolding
ScaffoldingReadMe.txt

# StyleCop
StyleCopReport.xml

# Files built by Visual Studio
*_i.c
*_p.c
*_h.h
*.ilk
*.meta
*.obj
*.iobj
*.pch
*.pdb
*.ipdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*_wpftmp.csproj
*.log
*.tlog
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Chutzpah Test files
_Chutzpah*

# Visual C++ cache files
ipch/
*.aps
*.ncb
*.opendb
*.opensdf
*.sdf
*.cachefile
*.VC.db
*.VC.VC.opendb

# Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

# Visual Studio Trace Files
*.e2e

# TFS 2012 Local Workspace
$tf/

# Guidance Automation Toolkit
*.gpState

# ReSharper is a .NET coding add-in
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# TeamCity is a build add-in
_TeamCity*

# DotCover is a Code Coverage Tool
*.dotCover

# AxoCover is a Code Coverage Tool
.axoCover/*
!.axoCover/settings.json

# Coverlet is a free, cross platform Code Coverage Tool
coverage*.json
coverage*.xml
coverage*.info

# Visual Studio code coverage results
*.coverage
*.coveragexml

# NCrunch
_NCrunch_*
.*crunch*.local.xml
nCrunchTemp_*

# MightyMoose
*.mm.*
AutoTest.Net/

# Web workbench (sass)
.sass-cache/

# Installshield output folder
[Ee]xpress/

# DocProject is a documentation generator add-in
DocProject/buildhelp/
DocProject/Help/*.HxT
DocProject/Help/*.HxC
DocProject/Help/*.hhc
DocProject/Help/*.hhk
DocProject/Help/*.hhp
DocProject/Help/Html2
DocProject/Help/html

# Click-Once directory
publish/

# Publish Web Output
*.[Pp]ublish.xml
*.azurePubxml
# Note: Comment the next line if you want to checkin your web deploy settings,
# but database connection strings (with potential passwords) will be unencrypted
*.pubxml
*.publishproj

# Microsoft Azure Web App publish settings. Comment the next line if you want to
# checkin your Azure Web App publish settings, but sensitive information contained
# in these scripts will be unencrypted
PublishScripts/

# NuGet Packages
*.nupkg
# NuGet Symbol Packages
*.snupkg
# The packages folder can be ignored because of Package Restore
**/[Pp]ackages/*
# except build/, which is used as an MSBuild target.
!**/[Pp]ackages/build/
# Uncomment if necessary however generally it will be regenerated when needed
#!**/[Pp]ackages/repositories.config
# NuGet v3's project.json files produces more ignorable files
*.nuget.props
*.nuget.targets

# ===================================================================
# ملفات خاصة بنظام أريدوو
# Aredoo POS Specific Files
# ===================================================================

# ملفات البيانات المحلية
# Local Data Files
Data/
LocalData/
*.mdf
*.ldf

# ملفات النسخ الاحتياطي
# Backup Files
Backup/
*.bak
*.backup

# ملفات السجلات
# Log Files
Logs/
*.log
ErrorLog_*.txt

# ملفات مؤقتة
# Temporary Files
Temp/
*.tmp
*.temp

# ملفات الصور المرفوعة
# Uploaded Images
Images/Products/*
Images/Customers/*
!Images/Products/.gitkeep
!Images/Customers/.gitkeep

# ملفات التقارير المُصدرة
# Exported Reports
Reports/Generated/
*.pdf
*.xlsx
*.csv
!Reports/Templates/

# ملفات الإعدادات المحلية
# Local Configuration Files
appsettings.local.json
appsettings.production.json
ConnectionStrings.config

# ملفات الترخيص المحلية
# Local License Files
license.key
activation.dat

# ملفات قاعدة البيانات المحلية
# Local Database Files
*.db
*.sqlite
*.sqlite3

# ===================================================================
# ملفات النظام والمحررات
# System and Editor Files
# ===================================================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# JetBrains IDEs
.idea/
*.sln.iml

# VS Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Local History for Visual Studio Code
.history/

# Windows Installer files from build outputs
*.cab
*.msi
*.msm
*.msp

# ===================================================================
# ملفات الأمان والحساسة
# Security and Sensitive Files
# ===================================================================

# مفاتيح API وكلمات المرور
# API Keys and Passwords
*.key
*.pem
*.p12
*.pfx
secrets.json
passwords.txt

# ملفات الاتصال بقاعدة البيانات
# Database Connection Files
connectionstrings.config
database.config

# ملفات التشفير
# Encryption Files
encryption.key
salt.dat

# ===================================================================
# ملفات التوثيق والمساعدة
# Documentation and Help Files
# ===================================================================

# ملفات التوثيق المُولدة
# Generated Documentation
docs/generated/
help/generated/

# ملفات المساعدة المؤقتة
# Temporary Help Files
*.chm
*.hlp

# ===================================================================
# ملفات أخرى
# Other Files
# ===================================================================

# Node.js (إذا كان هناك أدوات Node.js)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python (إذا كان هناك سكريبتات Python)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# ===================================================================
# ملفات يجب الاحتفاظ بها
# Files to Keep
# ===================================================================

# الاحتفاظ بملفات القوالب
!Templates/
!Resources/Templates/

# الاحتفاظ بملفات قاعدة البيانات الأساسية
!Database/AredooPOS_Complete.sql
!Database/InitialData_And_Indexes.sql
!Database/StoredProcedures.sql

# الاحتفاظ بملفات الموارد الأساسية
!Resources/Icons/
!Resources/Images/Default/

# الاحتفاظ بملفات التوثيق الأساسية
!README.md
!CHANGELOG.md
!LICENSE.txt

# ===================================================================
# ملاحظات
# Notes
# ===================================================================

# هذا الملف يستثني الملفات التي لا يجب تتبعها في Git
# This file excludes files that should not be tracked in Git

# للإضافة الإجبارية لملف مستثنى، استخدم:
# To force add an excluded file, use:
# git add -f filename

# لعرض الملفات المستثناة، استخدم:
# To see ignored files, use:
# git status --ignored
