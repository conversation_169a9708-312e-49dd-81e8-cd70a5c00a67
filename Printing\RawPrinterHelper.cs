using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;

namespace AredooPOS.Printing
{
    /// <summary>
    /// مساعد الطباعة المباشرة
    /// يدعم إرسال البيانات الخام للطابعة مباشرة
    /// متوافق مع Windows 7+
    /// </summary>
    public static class RawPrinterHelper
    {
        #region Windows API

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        private class DOCINFOA
        {
            [MarshalAs(UnmanagedType.LPStr)]
            public string pDocName;
            [MarshalAs(UnmanagedType.LPStr)]
            public string pOutputFile;
            [MarshalAs(UnmanagedType.LPStr)]
            public string pDataType;
        }

        [DllImport("winspool.Drv", EntryPoint = "OpenPrinterA", SetLastError = true, CharSet = CharSet.Ansi, ExactSpelling = true, CallingConvention = CallingConvention.StdCall)]
        private static extern bool OpenPrinter([MarshalAs(UnmanagedType.LPStr)] string szPrinter, out IntPtr hPrinter, IntPtr pd);

        [DllImport("winspool.Drv", EntryPoint = "ClosePrinter", SetLastError = true, ExactSpelling = true, CallingConvention = CallingConvention.StdCall)]
        private static extern bool ClosePrinter(IntPtr hPrinter);

        [DllImport("winspool.Drv", EntryPoint = "StartDocPrinterA", SetLastError = true, CharSet = CharSet.Ansi, ExactSpelling = true, CallingConvention = CallingConvention.StdCall)]
        private static extern bool StartDocPrinter(IntPtr hPrinter, int level, [In, MarshalAs(UnmanagedType.LPStruct)] DOCINFOA di);

        [DllImport("winspool.Drv", EntryPoint = "EndDocPrinter", SetLastError = true, ExactSpelling = true, CallingConvention = CallingConvention.StdCall)]
        private static extern bool EndDocPrinter(IntPtr hPrinter);

        [DllImport("winspool.Drv", EntryPoint = "StartPagePrinter", SetLastError = true, ExactSpelling = true, CallingConvention = CallingConvention.StdCall)]
        private static extern bool StartPagePrinter(IntPtr hPrinter);

        [DllImport("winspool.Drv", EntryPoint = "EndPagePrinter", SetLastError = true, ExactSpelling = true, CallingConvention = CallingConvention.StdCall)]
        private static extern bool EndPagePrinter(IntPtr hPrinter);

        [DllImport("winspool.Drv", EntryPoint = "WritePrinter", SetLastError = true, ExactSpelling = true, CallingConvention = CallingConvention.StdCall)]
        private static extern bool WritePrinter(IntPtr hPrinter, IntPtr pBytes, int dwCount, out int dwWritten);

        #endregion

        #region الطباعة المباشرة

        /// <summary>
        /// إرسال نص خام للطابعة
        /// </summary>
        /// <param name="printerName">اسم الطابعة</param>
        /// <param name="text">النص</param>
        /// <returns>true إذا تم الإرسال بنجاح</returns>
        public static bool SendStringToPrinter(string printerName, string text)
        {
            try
            {
                var bytes = Encoding.UTF8.GetBytes(text);
                return SendBytesToPrinter(printerName, bytes);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إرسال النص للطابعة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إرسال بايتات خام للطابعة
        /// </summary>
        /// <param name="printerName">اسم الطابعة</param>
        /// <param name="bytes">البايتات</param>
        /// <returns>true إذا تم الإرسال بنجاح</returns>
        public static bool SendBytesToPrinter(string printerName, byte[] bytes)
        {
            IntPtr hPrinter = IntPtr.Zero;
            bool success = false;

            try
            {
                // فتح الطابعة
                if (!OpenPrinter(printerName.Normalize(), out hPrinter, IntPtr.Zero))
                {
                    return false;
                }

                // إعداد معلومات المستند
                var docInfo = new DOCINFOA
                {
                    pDocName = "AredooPOS Print Job",
                    pOutputFile = null,
                    pDataType = "RAW"
                };

                // بدء المستند
                if (!StartDocPrinter(hPrinter, 1, docInfo))
                {
                    return false;
                }

                // بدء الصفحة
                if (!StartPagePrinter(hPrinter))
                {
                    EndDocPrinter(hPrinter);
                    return false;
                }

                // إرسال البيانات
                success = SendBytesToPrinterInternal(hPrinter, bytes);

                // إنهاء الصفحة
                EndPagePrinter(hPrinter);

                // إنهاء المستند
                EndDocPrinter(hPrinter);

                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إرسال البايتات للطابعة: {ex.Message}");
                return false;
            }
            finally
            {
                // إغلاق الطابعة
                if (hPrinter != IntPtr.Zero)
                {
                    ClosePrinter(hPrinter);
                }
            }
        }

        /// <summary>
        /// إرسال البايتات الداخلي
        /// </summary>
        /// <param name="hPrinter">مؤشر الطابعة</param>
        /// <param name="bytes">البايتات</param>
        /// <returns>true إذا تم الإرسال بنجاح</returns>
        private static bool SendBytesToPrinterInternal(IntPtr hPrinter, byte[] bytes)
        {
            try
            {
                IntPtr pBytes = Marshal.AllocHGlobal(bytes.Length);
                try
                {
                    Marshal.Copy(bytes, 0, pBytes, bytes.Length);
                    return WritePrinter(hPrinter, pBytes, bytes.Length, out int bytesWritten) && bytesWritten == bytes.Length;
                }
                finally
                {
                    Marshal.FreeHGlobal(pBytes);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الإرسال الداخلي: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إرسال ملف للطابعة
        /// </summary>
        /// <param name="printerName">اسم الطابعة</param>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>true إذا تم الإرسال بنجاح</returns>
        public static bool SendFileToPrinter(string printerName, string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return false;
                }

                var bytes = File.ReadAllBytes(filePath);
                return SendBytesToPrinter(printerName, bytes);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إرسال الملف للطابعة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إرسال أوامر ESC/POS للطابعة
        /// </summary>
        /// <param name="printerName">اسم الطابعة</param>
        /// <param name="commands">الأوامر</param>
        /// <returns>true إذا تم الإرسال بنجاح</returns>
        public static bool SendESCPOSCommands(string printerName, byte[] commands)
        {
            return SendBytesToPrinter(printerName, commands);
        }

        #endregion

        #region دوال مساعدة

        /// <summary>
        /// فحص وجود الطابعة
        /// </summary>
        /// <param name="printerName">اسم الطابعة</param>
        /// <returns>true إذا كانت الطابعة موجودة</returns>
        public static bool PrinterExists(string printerName)
        {
            try
            {
                IntPtr hPrinter = IntPtr.Zero;
                bool exists = OpenPrinter(printerName.Normalize(), out hPrinter, IntPtr.Zero);
                
                if (exists && hPrinter != IntPtr.Zero)
                {
                    ClosePrinter(hPrinter);
                }
                
                return exists;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على قائمة الطابعات المثبتة
        /// </summary>
        /// <returns>قائمة أسماء الطابعات</returns>
        public static string[] GetInstalledPrinters()
        {
            try
            {
                return System.Drawing.Printing.PrinterSettings.InstalledPrinters.Cast<string>().ToArray();
            }
            catch
            {
                return new string[0];
            }
        }

        /// <summary>
        /// الحصول على الطابعة الافتراضية
        /// </summary>
        /// <returns>اسم الطابعة الافتراضية</returns>
        public static string GetDefaultPrinter()
        {
            try
            {
                var printerSettings = new System.Drawing.Printing.PrinterSettings();
                return printerSettings.PrinterName;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// فحص حالة الطابعة
        /// </summary>
        /// <param name="printerName">اسم الطابعة</param>
        /// <returns>حالة الطابعة</returns>
        public static PrinterStatus GetPrinterStatus(string printerName)
        {
            try
            {
                var printerSettings = new System.Drawing.Printing.PrinterSettings
                {
                    PrinterName = printerName
                };

                if (!printerSettings.IsValid)
                {
                    return PrinterStatus.NotFound;
                }

                // فحص الاتصال
                if (!PrinterExists(printerName))
                {
                    return PrinterStatus.Offline;
                }

                return PrinterStatus.Ready;
            }
            catch
            {
                return PrinterStatus.Error;
            }
        }

        /// <summary>
        /// طباعة نص بسيط
        /// </summary>
        /// <param name="printerName">اسم الطابعة</param>
        /// <param name="text">النص</param>
        /// <param name="encoding">الترميز</param>
        /// <returns>true إذا تم الطباعة بنجاح</returns>
        public static bool PrintSimpleText(string printerName, string text, Encoding encoding = null)
        {
            try
            {
                encoding = encoding ?? Encoding.UTF8;
                var bytes = encoding.GetBytes(text);
                return SendBytesToPrinter(printerName, bytes);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في طباعة النص البسيط: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// طباعة نص مع أوامر ESC/POS
        /// </summary>
        /// <param name="printerName">اسم الطابعة</param>
        /// <param name="text">النص</param>
        /// <param name="initCommands">أوامر التهيئة</param>
        /// <param name="endCommands">أوامر النهاية</param>
        /// <returns>true إذا تم الطباعة بنجاح</returns>
        public static bool PrintTextWithCommands(string printerName, string text, 
            byte[] initCommands = null, byte[] endCommands = null)
        {
            try
            {
                var allBytes = new List<byte>();

                // أوامر التهيئة
                if (initCommands != null)
                {
                    allBytes.AddRange(initCommands);
                }

                // النص
                allBytes.AddRange(Encoding.GetEncoding(1256).GetBytes(text));

                // أوامر النهاية
                if (endCommands != null)
                {
                    allBytes.AddRange(endCommands);
                }

                return SendBytesToPrinter(printerName, allBytes.ToArray());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في طباعة النص مع الأوامر: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// اختبار الطابعة
        /// </summary>
        /// <param name="printerName">اسم الطابعة</param>
        /// <returns>true إذا نجح الاختبار</returns>
        public static bool TestPrinter(string printerName)
        {
            try
            {
                var testText = "اختبار الطابعة - AredooPOS\n\n";
                var testBytes = Encoding.GetEncoding(1256).GetBytes(testText);
                
                // إضافة أوامر قطع الورق
                var allBytes = new List<byte>();
                allBytes.AddRange(ESCPOSCommands.Initialize);
                allBytes.AddRange(testBytes);
                allBytes.AddRange(ESCPOSCommands.LineFeed);
                allBytes.AddRange(ESCPOSCommands.LineFeed);
                allBytes.AddRange(ESCPOSCommands.CutPaper);

                return SendBytesToPrinter(printerName, allBytes.ToArray());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في اختبار الطابعة: {ex.Message}");
                return false;
            }
        }

        #endregion
    }

    #region التعدادات

    /// <summary>
    /// حالة الطابعة
    /// </summary>
    public enum PrinterStatus
    {
        Ready,
        Offline,
        Error,
        NotFound,
        Busy,
        PaperOut,
        Jammed
    }

    #endregion
}
