using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using AridooPOS.Models;

namespace AridooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات للفواتير
    /// </summary>
    public class InvoiceDAL
    {
        /// <summary>
        /// الحصول على رقم فاتورة جديد
        /// </summary>
        /// <returns>رقم الفاتورة الجديد</returns>
        public string GetNextInvoiceNumber()
        {
            try
            {
                var result = DatabaseConnection.ExecuteStoredProcedure("sp_GetNextInvoiceNumber");
                if (result.Rows.Count > 0)
                {
                    return result.Rows[0]["NextInvoiceNumber"].ToString();
                }
                return "INV000001";
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على رقم فاتورة جديد: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إدراج فاتورة جديدة
        /// </summary>
        /// <param name="invoice">بيانات الفاتورة</param>
        /// <returns>رقم الفاتورة المدرجة</returns>
        public int InsertInvoice(Invoice invoice)
        {
            try
            {
                string query = @"
                    INSERT INTO Invoices (
                        InvoiceNumber, InvoiceDate, CustomerID, CustomerName,
                        SubTotal, DiscountAmount, DiscountPercent, TaxAmount,
                        TotalAmount, PaidAmount, RemainingAmount, PaymentType,
                        InvoiceStatus, Notes, CashierName, CreatedBy
                    ) VALUES (
                        @InvoiceNumber, @InvoiceDate, @CustomerID, @CustomerName,
                        @SubTotal, @DiscountAmount, @DiscountPercent, @TaxAmount,
                        @TotalAmount, @PaidAmount, @RemainingAmount, @PaymentType,
                        @InvoiceStatus, @Notes, @CashierName, @CreatedBy
                    );
                    SELECT SCOPE_IDENTITY();";

                SqlParameter[] parameters = {
                    new SqlParameter("@InvoiceNumber", invoice.InvoiceNumber),
                    new SqlParameter("@InvoiceDate", invoice.InvoiceDate),
                    new SqlParameter("@CustomerID", (object)invoice.CustomerID ?? DBNull.Value),
                    new SqlParameter("@CustomerName", invoice.CustomerName ?? ""),
                    new SqlParameter("@SubTotal", invoice.SubTotal),
                    new SqlParameter("@DiscountAmount", invoice.DiscountAmount),
                    new SqlParameter("@DiscountPercent", invoice.DiscountPercent),
                    new SqlParameter("@TaxAmount", invoice.TaxAmount),
                    new SqlParameter("@TotalAmount", invoice.TotalAmount),
                    new SqlParameter("@PaidAmount", invoice.PaidAmount),
                    new SqlParameter("@RemainingAmount", invoice.RemainingAmount),
                    new SqlParameter("@PaymentType", invoice.PaymentType),
                    new SqlParameter("@InvoiceStatus", invoice.InvoiceStatus),
                    new SqlParameter("@Notes", invoice.Notes ?? ""),
                    new SqlParameter("@CashierName", invoice.CashierName ?? ""),
                    new SqlParameter("@CreatedBy", invoice.CreatedBy ?? "")
                };

                object result = DatabaseConnection.ExecuteScalar(query, parameters);
                return Convert.ToInt32(result);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إدراج الفاتورة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث فاتورة موجودة
        /// </summary>
        /// <param name="invoice">بيانات الفاتورة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateInvoice(Invoice invoice)
        {
            try
            {
                string query = @"
                    UPDATE Invoices SET
                        CustomerID = @CustomerID,
                        CustomerName = @CustomerName,
                        SubTotal = @SubTotal,
                        DiscountAmount = @DiscountAmount,
                        DiscountPercent = @DiscountPercent,
                        TaxAmount = @TaxAmount,
                        TotalAmount = @TotalAmount,
                        PaidAmount = @PaidAmount,
                        RemainingAmount = @RemainingAmount,
                        PaymentType = @PaymentType,
                        InvoiceStatus = @InvoiceStatus,
                        Notes = @Notes,
                        ModifiedDate = GETDATE()
                    WHERE InvoiceID = @InvoiceID";

                SqlParameter[] parameters = {
                    new SqlParameter("@InvoiceID", invoice.InvoiceID),
                    new SqlParameter("@CustomerID", (object)invoice.CustomerID ?? DBNull.Value),
                    new SqlParameter("@CustomerName", invoice.CustomerName ?? ""),
                    new SqlParameter("@SubTotal", invoice.SubTotal),
                    new SqlParameter("@DiscountAmount", invoice.DiscountAmount),
                    new SqlParameter("@DiscountPercent", invoice.DiscountPercent),
                    new SqlParameter("@TaxAmount", invoice.TaxAmount),
                    new SqlParameter("@TotalAmount", invoice.TotalAmount),
                    new SqlParameter("@PaidAmount", invoice.PaidAmount),
                    new SqlParameter("@RemainingAmount", invoice.RemainingAmount),
                    new SqlParameter("@PaymentType", invoice.PaymentType),
                    new SqlParameter("@InvoiceStatus", invoice.InvoiceStatus),
                    new SqlParameter("@Notes", invoice.Notes ?? "")
                };

                int rowsAffected = DatabaseConnection.ExecuteNonQuery(query, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث الفاتورة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على فاتورة بالرقم
        /// </summary>
        /// <param name="invoiceId">رقم الفاتورة</param>
        /// <returns>بيانات الفاتورة</returns>
        public Invoice GetInvoiceById(int invoiceId)
        {
            try
            {
                string query = @"
                    SELECT * FROM Invoices 
                    WHERE InvoiceID = @InvoiceID";

                SqlParameter[] parameters = {
                    new SqlParameter("@InvoiceID", invoiceId)
                };

                DataTable result = DatabaseConnection.ExecuteQuery(query, parameters);
                
                if (result.Rows.Count > 0)
                {
                    return MapDataRowToInvoice(result.Rows[0]);
                }
                
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على الفاتورة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// البحث عن الفواتير
        /// </summary>
        /// <param name="searchCriteria">معايير البحث</param>
        /// <returns>قائمة الفواتير</returns>
        public List<Invoice> SearchInvoices(InvoiceSearchCriteria searchCriteria)
        {
            try
            {
                List<SqlParameter> parameters = new List<SqlParameter>();
                string whereClause = BuildSearchWhereClause(searchCriteria, parameters);
                
                string query = $@"
                    SELECT * FROM Invoices 
                    {whereClause}
                    ORDER BY InvoiceDate DESC, InvoiceID DESC";

                DataTable result = DatabaseConnection.ExecuteQuery(query, parameters.ToArray());
                
                List<Invoice> invoices = new List<Invoice>();
                foreach (DataRow row in result.Rows)
                {
                    invoices.Add(MapDataRowToInvoice(row));
                }
                
                return invoices;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث عن الفواتير: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحويل صف البيانات إلى كائن فاتورة
        /// </summary>
        /// <param name="row">صف البيانات</param>
        /// <returns>كائن الفاتورة</returns>
        private Invoice MapDataRowToInvoice(DataRow row)
        {
            return new Invoice
            {
                InvoiceID = Convert.ToInt32(row["InvoiceID"]),
                InvoiceNumber = row["InvoiceNumber"].ToString(),
                InvoiceDate = Convert.ToDateTime(row["InvoiceDate"]),
                CustomerID = row["CustomerID"] == DBNull.Value ? (int?)null : Convert.ToInt32(row["CustomerID"]),
                CustomerName = row["CustomerName"].ToString(),
                SubTotal = Convert.ToDecimal(row["SubTotal"]),
                DiscountAmount = Convert.ToDecimal(row["DiscountAmount"]),
                DiscountPercent = Convert.ToDecimal(row["DiscountPercent"]),
                TaxAmount = Convert.ToDecimal(row["TaxAmount"]),
                TotalAmount = Convert.ToDecimal(row["TotalAmount"]),
                PaidAmount = Convert.ToDecimal(row["PaidAmount"]),
                RemainingAmount = Convert.ToDecimal(row["RemainingAmount"]),
                PaymentType = row["PaymentType"].ToString(),
                InvoiceStatus = row["InvoiceStatus"].ToString(),
                Notes = row["Notes"].ToString(),
                CashierName = row["CashierName"].ToString(),
                IsReturned = Convert.ToBoolean(row["IsReturned"]),
                ReturnedDate = row["ReturnedDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(row["ReturnedDate"]),
                CreatedBy = row["CreatedBy"].ToString(),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                ModifiedDate = Convert.ToDateTime(row["ModifiedDate"])
            };
        }

        /// <summary>
        /// بناء جملة WHERE للبحث
        /// </summary>
        /// <param name="criteria">معايير البحث</param>
        /// <param name="parameters">قائمة المعاملات</param>
        /// <returns>جملة WHERE</returns>
        private string BuildSearchWhereClause(InvoiceSearchCriteria criteria, List<SqlParameter> parameters)
        {
            List<string> conditions = new List<string>();

            if (!string.IsNullOrEmpty(criteria.InvoiceNumber))
            {
                conditions.Add("InvoiceNumber LIKE @InvoiceNumber");
                parameters.Add(new SqlParameter("@InvoiceNumber", $"%{criteria.InvoiceNumber}%"));
            }

            if (!string.IsNullOrEmpty(criteria.CustomerName))
            {
                conditions.Add("CustomerName LIKE @CustomerName");
                parameters.Add(new SqlParameter("@CustomerName", $"%{criteria.CustomerName}%"));
            }

            if (criteria.FromDate.HasValue)
            {
                conditions.Add("InvoiceDate >= @FromDate");
                parameters.Add(new SqlParameter("@FromDate", criteria.FromDate.Value.Date));
            }

            if (criteria.ToDate.HasValue)
            {
                conditions.Add("InvoiceDate <= @ToDate");
                parameters.Add(new SqlParameter("@ToDate", criteria.ToDate.Value.Date.AddDays(1).AddSeconds(-1)));
            }

            if (!string.IsNullOrEmpty(criteria.PaymentType))
            {
                conditions.Add("PaymentType = @PaymentType");
                parameters.Add(new SqlParameter("@PaymentType", criteria.PaymentType));
            }

            if (!string.IsNullOrEmpty(criteria.InvoiceStatus))
            {
                conditions.Add("InvoiceStatus = @InvoiceStatus");
                parameters.Add(new SqlParameter("@InvoiceStatus", criteria.InvoiceStatus));
            }

            return conditions.Count > 0 ? "WHERE " + string.Join(" AND ", conditions) : "";
        }
    }

    /// <summary>
    /// معايير البحث عن الفواتير
    /// </summary>
    public class InvoiceSearchCriteria
    {
        public string InvoiceNumber { get; set; }
        public string CustomerName { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string PaymentType { get; set; }
        public string InvoiceStatus { get; set; }
    }
}
