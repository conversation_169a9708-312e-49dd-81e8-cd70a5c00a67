using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AredooPOS.Models;
using AredooPOS.BLL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Services
{
    /// <summary>
    /// خدمة معالجة المدفوعات
    /// توفر واجهة موحدة لمعالجة جميع أنواع المدفوعات
    /// </summary>
    public class PaymentService
    {
        #region المتغيرات والخصائص الخاصة

        private readonly PaymentProcessingBLL _paymentBLL;
        private readonly CashRegisterBLL _cashRegisterBLL;
        private readonly ILogger<PaymentService> _logger;

        // إعدادات الخدمة
        private readonly PaymentServiceSettings _settings;

        // قائمة معالجات الدفع المخصصة
        private readonly Dictionary<string, IPaymentProcessor> _paymentProcessors;

        #endregion

        #region الأحداث

        /// <summary>
        /// حدث عند معالجة دفعة بنجاح
        /// </summary>
        public event EventHandler<PaymentProcessedEventArgs> PaymentProcessed;

        /// <summary>
        /// حدث عند فشل معالجة دفعة
        /// </summary>
        public event EventHandler<PaymentFailedEventArgs> PaymentFailed;

        /// <summary>
        /// حدث عند إلغاء دفعة
        /// </summary>
        public event EventHandler<PaymentVoidedEventArgs> PaymentVoided;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ خدمة معالجة المدفوعات
        /// </summary>
        /// <param name="settings">إعدادات الخدمة</param>
        /// <param name="logger">مسجل الأحداث</param>
        public PaymentService(PaymentServiceSettings settings = null, ILogger<PaymentService> logger = null)
        {
            _settings = settings ?? new PaymentServiceSettings();
            _logger = logger;
            _paymentBLL = new PaymentProcessingBLL(null, null);
            _cashRegisterBLL = new CashRegisterBLL(null, null);
            _paymentProcessors = new Dictionary<string, IPaymentProcessor>();

            // تسجيل معالجات الدفع الافتراضية
            RegisterDefaultPaymentProcessors();

            _logger?.LogInformation("تم تهيئة خدمة معالجة المدفوعات");
        }

        /// <summary>
        /// تسجيل معالجات الدفع الافتراضية
        /// </summary>
        private void RegisterDefaultPaymentProcessors()
        {
            // معالج الدفع النقدي
            RegisterPaymentProcessor(PaymentMethodTypes.Cash, new CashPaymentProcessor());

            // معالج دفع البطاقات
            RegisterPaymentProcessor(PaymentMethodTypes.Card, new CardPaymentProcessor(_settings));

            // معالج التحويل البنكي
            RegisterPaymentProcessor(PaymentMethodTypes.Transfer, new BankTransferProcessor(_settings));
        }

        #endregion

        #region معالجة المدفوعات

        /// <summary>
        /// معالجة دفعة واحدة
        /// </summary>
        /// <param name="paymentRequest">طلب الدفع</param>
        /// <returns>نتيجة معالجة الدفع</returns>
        public async Task<PaymentResult> ProcessPaymentAsync(PaymentRequest paymentRequest)
        {
            try
            {
                _logger?.LogInformation($"بدء معالجة دفعة بمبلغ {paymentRequest.Amount:C}");

                // التحقق من صحة الطلب
                var validationResult = await ValidatePaymentRequestAsync(paymentRequest);
                if (!validationResult.IsValid)
                {
                    var failedResult = new PaymentResult
                    {
                        IsSuccessful = false,
                        ErrorMessage = validationResult.ErrorMessage
                    };

                    PaymentFailed?.Invoke(this, new PaymentFailedEventArgs
                    {
                        PaymentRequest = paymentRequest,
                        ErrorMessage = validationResult.ErrorMessage
                    });

                    return failedResult;
                }

                // الحصول على معالج الدفع المناسب
                var processor = GetPaymentProcessor(paymentRequest.PaymentMethodID);
                if (processor != null)
                {
                    // معالجة الدفع باستخدام المعالج المخصص
                    var processorResult = await processor.ProcessPaymentAsync(paymentRequest);
                    if (!processorResult.IsSuccessful)
                    {
                        PaymentFailed?.Invoke(this, new PaymentFailedEventArgs
                        {
                            PaymentRequest = paymentRequest,
                            ErrorMessage = processorResult.ErrorMessage
                        });

                        return processorResult;
                    }
                }

                // معالجة الدفع في النظام
                var result = _paymentBLL.ProcessPayment(paymentRequest);

                if (result.IsSuccessful)
                {
                    PaymentProcessed?.Invoke(this, new PaymentProcessedEventArgs
                    {
                        PaymentRequest = paymentRequest,
                        PaymentResult = result
                    });

                    _logger?.LogInformation($"تم معالجة دفعة بنجاح برقم {result.TransactionID}");
                }
                else
                {
                    PaymentFailed?.Invoke(this, new PaymentFailedEventArgs
                    {
                        PaymentRequest = paymentRequest,
                        ErrorMessage = result.ErrorMessage
                    });

                    _logger?.LogWarning($"فشل في معالجة الدفعة: {result.ErrorMessage}");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة الدفع");
                
                var errorResult = new PaymentResult
                {
                    IsSuccessful = false,
                    ErrorMessage = "حدث خطأ غير متوقع في معالجة الدفع"
                };

                PaymentFailed?.Invoke(this, new PaymentFailedEventArgs
                {
                    PaymentRequest = paymentRequest,
                    ErrorMessage = errorResult.ErrorMessage
                });

                return errorResult;
            }
        }

        /// <summary>
        /// معالجة دفع متعدد الطرق
        /// </summary>
        /// <param name="multiPaymentRequest">طلب الدفع المتعدد</param>
        /// <returns>نتيجة معالجة الدفع المتعدد</returns>
        public async Task<MultiPaymentResult> ProcessMultiPaymentAsync(MultiPaymentRequest multiPaymentRequest)
        {
            try
            {
                _logger?.LogInformation($"بدء معالجة دفع متعدد بـ {multiPaymentRequest.Payments.Count} طريقة دفع");

                // التحقق من صحة الطلب
                var validationResult = await ValidateMultiPaymentRequestAsync(multiPaymentRequest);
                if (!validationResult.IsValid)
                {
                    return new MultiPaymentResult
                    {
                        IsSuccessful = false,
                        ErrorMessage = validationResult.ErrorMessage
                    };
                }

                // معالجة كل دفعة مع المعالج المناسب
                var processedPayments = new List<PaymentRequest>();
                foreach (var payment in multiPaymentRequest.Payments)
                {
                    var paymentRequest = CreatePaymentRequest(multiPaymentRequest, payment);
                    
                    var processor = GetPaymentProcessor(payment.PaymentMethodID);
                    if (processor != null)
                    {
                        var processorResult = await processor.ProcessPaymentAsync(paymentRequest);
                        if (!processorResult.IsSuccessful)
                        {
                            // إلغاء جميع المعاملات المعالجة سابقاً
                            await RollbackProcessedPayments(processedPayments);
                            
                            return new MultiPaymentResult
                            {
                                IsSuccessful = false,
                                ErrorMessage = $"فشل في معالجة دفعة: {processorResult.ErrorMessage}"
                            };
                        }
                    }

                    processedPayments.Add(paymentRequest);
                }

                // معالجة الدفع المتعدد في النظام
                var result = _paymentBLL.ProcessMultiPayment(multiPaymentRequest);

                if (result.IsSuccessful)
                {
                    _logger?.LogInformation($"تم معالجة دفع متعدد بنجاح بإجمالي {result.TotalProcessedAmount:C}");
                }
                else
                {
                    _logger?.LogWarning($"فشل في معالجة الدفع المتعدد: {result.ErrorMessage}");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة الدفع المتعدد");
                return new MultiPaymentResult
                {
                    IsSuccessful = false,
                    ErrorMessage = "حدث خطأ غير متوقع في معالجة الدفع المتعدد"
                };
            }
        }

        /// <summary>
        /// إلغاء دفعة
        /// </summary>
        /// <param name="transactionID">رقم المعاملة</param>
        /// <param name="reason">سبب الإلغاء</param>
        /// <param name="voidedBy">من ألغى الدفعة</param>
        /// <returns>true إذا تم الإلغاء بنجاح</returns>
        public async Task<bool> VoidPaymentAsync(int transactionID, string reason, string voidedBy)
        {
            try
            {
                _logger?.LogInformation($"بدء إلغاء المعاملة {transactionID}");

                var success = _paymentBLL.VoidPayment(transactionID, reason, voidedBy);

                if (success)
                {
                    PaymentVoided?.Invoke(this, new PaymentVoidedEventArgs
                    {
                        TransactionID = transactionID,
                        Reason = reason,
                        VoidedBy = voidedBy,
                        VoidedDate = DateTime.Now
                    });

                    _logger?.LogInformation($"تم إلغاء المعاملة {transactionID} بنجاح");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إلغاء المعاملة {transactionID}");
                throw;
            }
        }

        #endregion

        #region إدارة معالجات الدفع

        /// <summary>
        /// تسجيل معالج دفع مخصص
        /// </summary>
        /// <param name="paymentMethodCode">رمز طريقة الدفع</param>
        /// <param name="processor">معالج الدفع</param>
        public void RegisterPaymentProcessor(string paymentMethodCode, IPaymentProcessor processor)
        {
            if (string.IsNullOrWhiteSpace(paymentMethodCode))
                throw new ArgumentException("رمز طريقة الدفع مطلوب");

            if (processor == null)
                throw new ArgumentNullException(nameof(processor));

            _paymentProcessors[paymentMethodCode.ToUpper()] = processor;
            _logger?.LogInformation($"تم تسجيل معالج دفع لطريقة {paymentMethodCode}");
        }

        /// <summary>
        /// إلغاء تسجيل معالج دفع
        /// </summary>
        /// <param name="paymentMethodCode">رمز طريقة الدفع</param>
        public void UnregisterPaymentProcessor(string paymentMethodCode)
        {
            if (string.IsNullOrWhiteSpace(paymentMethodCode))
                return;

            _paymentProcessors.Remove(paymentMethodCode.ToUpper());
            _logger?.LogInformation($"تم إلغاء تسجيل معالج دفع لطريقة {paymentMethodCode}");
        }

        /// <summary>
        /// الحصول على معالج الدفع
        /// </summary>
        /// <param name="paymentMethodID">رقم طريقة الدفع</param>
        /// <returns>معالج الدفع أو null</returns>
        private IPaymentProcessor GetPaymentProcessor(int paymentMethodID)
        {
            // TODO: الحصول على رمز طريقة الدفع من قاعدة البيانات
            // هذا مثال مبسط
            var paymentMethodCode = GetPaymentMethodCode(paymentMethodID);
            
            if (string.IsNullOrWhiteSpace(paymentMethodCode))
                return null;

            _paymentProcessors.TryGetValue(paymentMethodCode.ToUpper(), out var processor);
            return processor;
        }

        /// <summary>
        /// الحصول على رمز طريقة الدفع
        /// </summary>
        /// <param name="paymentMethodID">رقم طريقة الدفع</param>
        /// <returns>رمز طريقة الدفع</returns>
        private string GetPaymentMethodCode(int paymentMethodID)
        {
            // TODO: تنفيذ الحصول على رمز طريقة الدفع من قاعدة البيانات
            return paymentMethodID switch
            {
                1 => PaymentMethodTypes.Cash,
                2 => PaymentMethodTypes.Card,
                3 => PaymentMethodTypes.Transfer,
                _ => null
            };
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// التحقق من صحة طلب الدفع
        /// </summary>
        /// <param name="request">طلب الدفع</param>
        /// <returns>نتيجة التحقق</returns>
        private async Task<ValidationResult> ValidatePaymentRequestAsync(PaymentRequest request)
        {
            if (request == null)
                return new ValidationResult { IsValid = false, ErrorMessage = "طلب الدفع مطلوب" };

            if (request.Amount <= 0)
                return new ValidationResult { IsValid = false, ErrorMessage = "مبلغ الدفع يجب أن يكون أكبر من صفر" };

            if (request.Amount > _settings.MaxPaymentAmount)
                return new ValidationResult { IsValid = false, ErrorMessage = $"مبلغ الدفع يتجاوز الحد الأقصى المسموح ({_settings.MaxPaymentAmount:C})" };

            // التحقق من صحة الجلسة والصندوق
            var validationResult = _cashRegisterBLL.ValidateTransaction(
                GetCashRegisterIdFromSession(request.SessionID), 
                request.Amount, 
                request.TransactionType == CashTransactionTypes.Withdrawal);

            if (!validationResult.IsValid)
                return new ValidationResult { IsValid = false, ErrorMessage = validationResult.ErrorMessage };

            return new ValidationResult { IsValid = true };
        }

        /// <summary>
        /// التحقق من صحة طلب الدفع المتعدد
        /// </summary>
        /// <param name="request">طلب الدفع المتعدد</param>
        /// <returns>نتيجة التحقق</returns>
        private async Task<ValidationResult> ValidateMultiPaymentRequestAsync(MultiPaymentRequest request)
        {
            if (request == null)
                return new ValidationResult { IsValid = false, ErrorMessage = "طلب الدفع المتعدد مطلوب" };

            if (request.Payments == null || !request.Payments.Any())
                return new ValidationResult { IsValid = false, ErrorMessage = "يجب تحديد طريقة دفع واحدة على الأقل" };

            var totalAmount = request.Payments.Sum(p => p.Amount);
            if (totalAmount <= 0)
                return new ValidationResult { IsValid = false, ErrorMessage = "إجمالي مبلغ الدفع يجب أن يكون أكبر من صفر" };

            if (totalAmount > _settings.MaxPaymentAmount)
                return new ValidationResult { IsValid = false, ErrorMessage = $"إجمالي مبلغ الدفع يتجاوز الحد الأقصى المسموح ({_settings.MaxPaymentAmount:C})" };

            return new ValidationResult { IsValid = true };
        }

        /// <summary>
        /// إنشاء طلب دفع من طلب دفع متعدد
        /// </summary>
        /// <param name="multiRequest">طلب الدفع المتعدد</param>
        /// <param name="payment">تفاصيل الدفع</param>
        /// <returns>طلب الدفع</returns>
        private PaymentRequest CreatePaymentRequest(MultiPaymentRequest multiRequest, PaymentDetail payment)
        {
            return new PaymentRequest
            {
                SessionID = multiRequest.SessionID,
                TransactionType = multiRequest.TransactionType,
                Amount = payment.Amount,
                PaymentMethodID = payment.PaymentMethodID,
                ReferenceNumber = payment.ReferenceNumber,
                Description = multiRequest.Description,
                Category = multiRequest.Category,
                RelatedDocumentType = multiRequest.RelatedDocumentType,
                RelatedDocumentID = multiRequest.RelatedDocumentID,
                CustomerID = multiRequest.CustomerID,
                SupplierID = multiRequest.SupplierID,
                UserID = multiRequest.UserID,
                ProcessedBy = multiRequest.ProcessedBy,
                Notes = payment.Notes
            };
        }

        /// <summary>
        /// التراجع عن المعاملات المعالجة
        /// </summary>
        /// <param name="processedPayments">المعاملات المعالجة</param>
        private async Task RollbackProcessedPayments(List<PaymentRequest> processedPayments)
        {
            foreach (var payment in processedPayments)
            {
                try
                {
                    var processor = GetPaymentProcessor(payment.PaymentMethodID);
                    if (processor != null)
                    {
                        await processor.RollbackPaymentAsync(payment);
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, $"خطأ في التراجع عن معاملة");
                }
            }
        }

        /// <summary>
        /// الحصول على رقم الصندوق من الجلسة
        /// </summary>
        /// <param name="sessionID">رقم الجلسة</param>
        /// <returns>رقم الصندوق</returns>
        private int GetCashRegisterIdFromSession(int sessionID)
        {
            // TODO: تنفيذ الحصول على رقم الصندوق من الجلسة
            return 1; // مثال مبسط
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// إعدادات خدمة الدفع
    /// </summary>
    public class PaymentServiceSettings
    {
        public decimal MaxPaymentAmount { get; set; } = 100000;
        public bool EnablePaymentValidation { get; set; } = true;
        public bool EnablePaymentLogging { get; set; } = true;
        public int PaymentTimeoutSeconds { get; set; } = 30;
    }

    /// <summary>
    /// بيانات حدث معالجة الدفع
    /// </summary>
    public class PaymentProcessedEventArgs : EventArgs
    {
        public PaymentRequest PaymentRequest { get; set; }
        public PaymentResult PaymentResult { get; set; }
    }

    /// <summary>
    /// بيانات حدث فشل الدفع
    /// </summary>
    public class PaymentFailedEventArgs : EventArgs
    {
        public PaymentRequest PaymentRequest { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// بيانات حدث إلغاء الدفع
    /// </summary>
    public class PaymentVoidedEventArgs : EventArgs
    {
        public int TransactionID { get; set; }
        public string Reason { get; set; }
        public string VoidedBy { get; set; }
        public DateTime VoidedDate { get; set; }
    }

    #endregion
}
