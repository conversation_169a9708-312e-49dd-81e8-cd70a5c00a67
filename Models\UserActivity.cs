using System;
using System.ComponentModel.DataAnnotations;

namespace AredooPOS.Models
{
    /// <summary>
    /// نموذج نشاط المستخدم
    /// يمثل نشاط أو عملية قام بها المستخدم في النظام
    /// </summary>
    public class UserActivity
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم النشاط في قاعدة البيانات (مفتاح أساسي)
        /// </summary>
        public int ActivityID { get; set; }

        /// <summary>
        /// رقم المستخدم (مفتاح خارجي)
        /// </summary>
        [Required(ErrorMessage = "رقم المستخدم مطلوب")]
        public int UserID { get; set; }

        /// <summary>
        /// رقم الجلسة (مفتاح خارجي اختياري)
        /// </summary>
        public int? SessionID { get; set; }

        /// <summary>
        /// نوع النشاط
        /// </summary>
        [Required(ErrorMessage = "نوع النشاط مطلوب")]
        [StringLength(50, ErrorMessage = "نوع النشاط لا يجب أن يتجاوز 50 حرف")]
        public string ActivityType { get; set; }

        /// <summary>
        /// وصف النشاط
        /// </summary>
        [Required(ErrorMessage = "وصف النشاط مطلوب")]
        [StringLength(500, ErrorMessage = "وصف النشاط لا يجب أن يتجاوز 500 حرف")]
        public string Description { get; set; }

        /// <summary>
        /// تاريخ ووقت النشاط
        /// </summary>
        public DateTime ActivityDateTime { get; set; }

        #endregion

        #region تفاصيل النشاط

        /// <summary>
        /// الوحدة أو القسم في النظام
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم الوحدة لا يجب أن يتجاوز 50 حرف")]
        public string Module { get; set; }

        /// <summary>
        /// الإجراء المحدد
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم الإجراء لا يجب أن يتجاوز 50 حرف")]
        public string Action { get; set; }

        /// <summary>
        /// الكائن المتأثر (مثل رقم المنتج، رقم العميل، إلخ)
        /// </summary>
        [StringLength(100, ErrorMessage = "الكائن المتأثر لا يجب أن يتجاوز 100 حرف")]
        public string TargetObject { get; set; }

        /// <summary>
        /// رقم الكائن المتأثر
        /// </summary>
        public int? TargetObjectID { get; set; }

        /// <summary>
        /// القيم القديمة (JSON)
        /// </summary>
        public string OldValues { get; set; }

        /// <summary>
        /// القيم الجديدة (JSON)
        /// </summary>
        public string NewValues { get; set; }

        #endregion

        #region معلومات النتيجة

        /// <summary>
        /// حالة النشاط
        /// </summary>
        [StringLength(20, ErrorMessage = "حالة النشاط لا يجب أن تتجاوز 20 حرف")]
        public string Status { get; set; }

        /// <summary>
        /// هل النشاط نجح
        /// </summary>
        public bool IsSuccessful { get; set; } = true;

        /// <summary>
        /// رسالة الخطأ (إن وجدت)
        /// </summary>
        [StringLength(1000, ErrorMessage = "رسالة الخطأ لا يجب أن تتجاوز 1000 حرف")]
        public string ErrorMessage { get; set; }

        /// <summary>
        /// مدة تنفيذ العملية (بالميلي ثانية)
        /// </summary>
        public int? ExecutionTimeMs { get; set; }

        #endregion

        #region معلومات الاتصال

        /// <summary>
        /// عنوان IP للمستخدم
        /// </summary>
        [StringLength(45, ErrorMessage = "عنوان IP لا يجب أن يتجاوز 45 حرف")]
        public string IPAddress { get; set; }

        /// <summary>
        /// معلومات المتصفح/التطبيق
        /// </summary>
        [StringLength(500, ErrorMessage = "معلومات المتصفح لا يجب أن تتجاوز 500 حرف")]
        public string UserAgent { get; set; }

        /// <summary>
        /// اسم الجهاز
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم الجهاز لا يجب أن يتجاوز 100 حرف")]
        public string DeviceName { get; set; }

        #endregion

        #region الأمان والمراجعة

        /// <summary>
        /// مستوى الأهمية
        /// </summary>
        [StringLength(20, ErrorMessage = "مستوى الأهمية لا يجب أن يتجاوز 20 حرف")]
        public string Severity { get; set; }

        /// <summary>
        /// هل النشاط حساس أمنياً
        /// </summary>
        public bool IsSecuritySensitive { get; set; }

        /// <summary>
        /// هل النشاط يتطلب مراجعة
        /// </summary>
        public bool RequiresReview { get; set; }

        /// <summary>
        /// هل تمت مراجعة النشاط
        /// </summary>
        public bool IsReviewed { get; set; }

        /// <summary>
        /// من راجع النشاط
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المراجع لا يجب أن يتجاوز 50 حرف")]
        public string ReviewedBy { get; set; }

        /// <summary>
        /// تاريخ المراجعة
        /// </summary>
        public DateTime? ReviewedDate { get; set; }

        /// <summary>
        /// ملاحظات المراجعة
        /// </summary>
        [StringLength(500, ErrorMessage = "ملاحظات المراجعة لا يجب أن تتجاوز 500 حرف")]
        public string ReviewNotes { get; set; }

        #endregion

        #region معلومات إضافية

        /// <summary>
        /// بيانات إضافية (JSON)
        /// </summary>
        public string AdditionalData { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 500 حرف")]
        public string Notes { get; set; }

        #endregion

        #region معلومات النظام

        /// <summary>
        /// تاريخ إنشاء السجل
        /// </summary>
        public DateTime CreatedDate { get; set; }

        #endregion

        #region العلاقات

        /// <summary>
        /// المستخدم المرتبط بالنشاط
        /// </summary>
        public User User { get; set; }

        /// <summary>
        /// الجلسة المرتبطة بالنشاط
        /// </summary>
        public UserSession Session { get; set; }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ نشاط المستخدم مع التهيئة الافتراضية
        /// </summary>
        public UserActivity()
        {
            // تهيئة التواريخ
            ActivityDateTime = DateTime.Now;
            CreatedDate = DateTime.Now;

            // تهيئة القيم الافتراضية
            IsSuccessful = true;
            Status = ActivityStatus.Completed;
            Severity = ActivitySeverity.Low;
            IsSecuritySensitive = false;
            RequiresReview = false;
            IsReviewed = false;
        }

        /// <summary>
        /// منشئ نشاط المستخدم مع معاملات
        /// </summary>
        /// <param name="userID">رقم المستخدم</param>
        /// <param name="activityType">نوع النشاط</param>
        /// <param name="description">وصف النشاط</param>
        /// <param name="module">الوحدة</param>
        /// <param name="action">الإجراء</param>
        public UserActivity(int userID, string activityType, string description, string module = null, string action = null) : this()
        {
            UserID = userID;
            ActivityType = activityType;
            Description = description;
            Module = module;
            Action = action;
        }

        #endregion

        #region العمليات والتحقق

        /// <summary>
        /// التحقق من صحة بيانات النشاط
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return UserID > 0 &&
                   !string.IsNullOrWhiteSpace(ActivityType) &&
                   !string.IsNullOrWhiteSpace(Description);
        }

        /// <summary>
        /// تسجيل نجاح العملية
        /// </summary>
        /// <param name="executionTimeMs">مدة التنفيذ بالميلي ثانية</param>
        public void MarkAsSuccessful(int? executionTimeMs = null)
        {
            IsSuccessful = true;
            Status = ActivityStatus.Completed;
            ErrorMessage = null;
            ExecutionTimeMs = executionTimeMs;
        }

        /// <summary>
        /// تسجيل فشل العملية
        /// </summary>
        /// <param name="errorMessage">رسالة الخطأ</param>
        /// <param name="executionTimeMs">مدة التنفيذ بالميلي ثانية</param>
        public void MarkAsFailed(string errorMessage, int? executionTimeMs = null)
        {
            IsSuccessful = false;
            Status = ActivityStatus.Failed;
            ErrorMessage = errorMessage;
            ExecutionTimeMs = executionTimeMs;
        }

        /// <summary>
        /// تعيين النشاط كحساس أمنياً
        /// </summary>
        /// <param name="requiresReview">هل يتطلب مراجعة</param>
        public void MarkAsSecuritySensitive(bool requiresReview = true)
        {
            IsSecuritySensitive = true;
            RequiresReview = requiresReview;
            Severity = ActivitySeverity.High;
        }

        /// <summary>
        /// مراجعة النشاط
        /// </summary>
        /// <param name="reviewedBy">من راجع النشاط</param>
        /// <param name="notes">ملاحظات المراجعة</param>
        public void Review(string reviewedBy, string notes = null)
        {
            IsReviewed = true;
            ReviewedBy = reviewedBy;
            ReviewedDate = DateTime.Now;
            ReviewNotes = notes;
        }

        /// <summary>
        /// تعيين القيم القديمة والجديدة
        /// </summary>
        /// <param name="oldValues">القيم القديمة</param>
        /// <param name="newValues">القيم الجديدة</param>
        public void SetValues(object oldValues, object newValues)
        {
            if (oldValues != null)
                OldValues = System.Text.Json.JsonSerializer.Serialize(oldValues);
            
            if (newValues != null)
                NewValues = System.Text.Json.JsonSerializer.Serialize(newValues);
        }

        /// <summary>
        /// تعيين الكائن المتأثر
        /// </summary>
        /// <param name="objectType">نوع الكائن</param>
        /// <param name="objectID">رقم الكائن</param>
        public void SetTargetObject(string objectType, int objectID)
        {
            TargetObject = objectType;
            TargetObjectID = objectID;
        }

        /// <summary>
        /// تعيين معلومات الاتصال
        /// </summary>
        /// <param name="ipAddress">عنوان IP</param>
        /// <param name="userAgent">معلومات المتصفح</param>
        /// <param name="deviceName">اسم الجهاز</param>
        public void SetConnectionInfo(string ipAddress, string userAgent = null, string deviceName = null)
        {
            IPAddress = ipAddress;
            UserAgent = userAgent;
            DeviceName = deviceName;
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// الحصول على نص حالة النشاط
        /// </summary>
        /// <returns>نص الحالة</returns>
        public string GetStatusText()
        {
            return Status switch
            {
                ActivityStatus.Pending => "قيد الانتظار",
                ActivityStatus.InProgress => "قيد التنفيذ",
                ActivityStatus.Completed => "مكتمل",
                ActivityStatus.Failed => "فاشل",
                ActivityStatus.Cancelled => "ملغي",
                _ => "غير معروف"
            };
        }

        /// <summary>
        /// الحصول على نص مستوى الأهمية
        /// </summary>
        /// <returns>نص مستوى الأهمية</returns>
        public string GetSeverityText()
        {
            return Severity switch
            {
                ActivitySeverity.Low => "منخفض",
                ActivitySeverity.Medium => "متوسط",
                ActivitySeverity.High => "عالي",
                ActivitySeverity.Critical => "حرج",
                _ => "غير محدد"
            };
        }

        /// <summary>
        /// الحصول على مدة التنفيذ كنص
        /// </summary>
        /// <returns>مدة التنفيذ</returns>
        public string GetExecutionTimeText()
        {
            if (!ExecutionTimeMs.HasValue)
                return "غير محدد";

            if (ExecutionTimeMs.Value < 1000)
                return $"{ExecutionTimeMs.Value} مللي ثانية";

            var seconds = ExecutionTimeMs.Value / 1000.0;
            return $"{seconds:F2} ثانية";
        }

        #endregion

        #region عمليات النسخ والتحويل

        /// <summary>
        /// تحويل النشاط إلى نص وصفي
        /// </summary>
        /// <returns>وصف نصي للنشاط</returns>
        public override string ToString()
        {
            return $"{ActivityType} - {Description} - {GetStatusText()} ({ActivityDateTime:yyyy-MM-dd HH:mm})";
        }

        #endregion
    }

    #region الثوابت والتعدادات

    /// <summary>
    /// أنواع الأنشطة
    /// </summary>
    public static class ActivityTypes
    {
        public const string Login = "تسجيل دخول";
        public const string Logout = "تسجيل خروج";
        public const string Create = "إنشاء";
        public const string Update = "تحديث";
        public const string Delete = "حذف";
        public const string View = "عرض";
        public const string Search = "بحث";
        public const string Export = "تصدير";
        public const string Import = "استيراد";
        public const string Print = "طباعة";
        public const string Sale = "بيع";
        public const string Return = "إرجاع";
        public const string Payment = "دفع";
        public const string Discount = "خصم";
        public const string Inventory = "مخزون";
        public const string Report = "تقرير";
        public const string Settings = "إعدادات";
        public const string Security = "أمان";
        public const string Error = "خطأ";
        public const string Warning = "تحذير";
        public const string Information = "معلومات";
    }

    /// <summary>
    /// حالات النشاط
    /// </summary>
    public static class ActivityStatus
    {
        public const string Pending = "قيد الانتظار";
        public const string InProgress = "قيد التنفيذ";
        public const string Completed = "مكتمل";
        public const string Failed = "فاشل";
        public const string Cancelled = "ملغي";
    }

    /// <summary>
    /// مستويات الأهمية
    /// </summary>
    public static class ActivitySeverity
    {
        public const string Low = "منخفض";
        public const string Medium = "متوسط";
        public const string High = "عالي";
        public const string Critical = "حرج";
    }

    /// <summary>
    /// وحدات النظام
    /// </summary>
    public static class SystemModules
    {
        public const string Authentication = "المصادقة";
        public const string UserManagement = "إدارة المستخدمين";
        public const string ProductManagement = "إدارة المنتجات";
        public const string InventoryManagement = "إدارة المخزون";
        public const string SalesManagement = "إدارة المبيعات";
        public const string CustomerManagement = "إدارة العملاء";
        public const string FinancialManagement = "الإدارة المالية";
        public const string ReportsManagement = "إدارة التقارير";
        public const string SystemSettings = "إعدادات النظام";
        public const string Security = "الأمان";
    }

    /// <summary>
    /// الإجراءات
    /// </summary>
    public static class SystemActions
    {
        public const string Add = "إضافة";
        public const string Edit = "تعديل";
        public const string Delete = "حذف";
        public const string View = "عرض";
        public const string List = "قائمة";
        public const string Search = "بحث";
        public const string Filter = "فلترة";
        public const string Sort = "ترتيب";
        public const string Export = "تصدير";
        public const string Import = "استيراد";
        public const string Print = "طباعة";
        public const string Backup = "نسخ احتياطي";
        public const string Restore = "استعادة";
        public const string Configure = "تكوين";
        public const string Activate = "تفعيل";
        public const string Deactivate = "إلغاء تفعيل";
        public const string Lock = "قفل";
        public const string Unlock = "إلغاء قفل";
        public const string Reset = "إعادة تعيين";
        public const string Approve = "موافقة";
        public const string Reject = "رفض";
        public const string Submit = "إرسال";
        public const string Cancel = "إلغاء";
    }

    #endregion
}
