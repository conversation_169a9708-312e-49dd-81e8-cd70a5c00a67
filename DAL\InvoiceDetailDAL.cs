using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using AridooPOS.Models;

namespace AridooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات لتفاصيل الفواتير
    /// </summary>
    public class InvoiceDetailDAL
    {
        /// <summary>
        /// إدراج تفاصيل الفاتورة
        /// </summary>
        /// <param name="invoiceDetails">قائمة تفاصيل الفاتورة</param>
        /// <returns>true إذا تم الإدراج بنجاح</returns>
        public bool InsertInvoiceDetails(List<InvoiceDetail> invoiceDetails)
        {
            try
            {
                foreach (var detail in invoiceDetails)
                {
                    InsertInvoiceDetail(detail);
                }
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إدراج تفاصيل الفاتورة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إدراج تفصيل فاتورة واحد
        /// </summary>
        /// <param name="detail">تفصيل الفاتورة</param>
        /// <returns>رقم التفصيل المدرج</returns>
        public int InsertInvoiceDetail(InvoiceDetail detail)
        {
            try
            {
                string query = @"
                    INSERT INTO InvoiceDetails (
                        InvoiceID, ProductID, ProductCode, ProductName,
                        Quantity, UnitPrice, DiscountAmount, DiscountPercent,
                        TaxRate, TaxAmount, LineTotal
                    ) VALUES (
                        @InvoiceID, @ProductID, @ProductCode, @ProductName,
                        @Quantity, @UnitPrice, @DiscountAmount, @DiscountPercent,
                        @TaxRate, @TaxAmount, @LineTotal
                    );
                    SELECT SCOPE_IDENTITY();";

                SqlParameter[] parameters = {
                    new SqlParameter("@InvoiceID", detail.InvoiceID),
                    new SqlParameter("@ProductID", detail.ProductID),
                    new SqlParameter("@ProductCode", detail.ProductCode ?? ""),
                    new SqlParameter("@ProductName", detail.ProductName),
                    new SqlParameter("@Quantity", detail.Quantity),
                    new SqlParameter("@UnitPrice", detail.UnitPrice),
                    new SqlParameter("@DiscountAmount", detail.DiscountAmount),
                    new SqlParameter("@DiscountPercent", detail.DiscountPercent),
                    new SqlParameter("@TaxRate", detail.TaxRate),
                    new SqlParameter("@TaxAmount", detail.TaxAmount),
                    new SqlParameter("@LineTotal", detail.LineTotal)
                };

                object result = DatabaseConnection.ExecuteScalar(query, parameters);
                return Convert.ToInt32(result);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إدراج تفصيل الفاتورة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث تفصيل فاتورة
        /// </summary>
        /// <param name="detail">تفصيل الفاتورة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateInvoiceDetail(InvoiceDetail detail)
        {
            try
            {
                string query = @"
                    UPDATE InvoiceDetails SET
                        ProductID = @ProductID,
                        ProductCode = @ProductCode,
                        ProductName = @ProductName,
                        Quantity = @Quantity,
                        UnitPrice = @UnitPrice,
                        DiscountAmount = @DiscountAmount,
                        DiscountPercent = @DiscountPercent,
                        TaxRate = @TaxRate,
                        TaxAmount = @TaxAmount,
                        LineTotal = @LineTotal
                    WHERE InvoiceDetailID = @InvoiceDetailID";

                SqlParameter[] parameters = {
                    new SqlParameter("@InvoiceDetailID", detail.InvoiceDetailID),
                    new SqlParameter("@ProductID", detail.ProductID),
                    new SqlParameter("@ProductCode", detail.ProductCode ?? ""),
                    new SqlParameter("@ProductName", detail.ProductName),
                    new SqlParameter("@Quantity", detail.Quantity),
                    new SqlParameter("@UnitPrice", detail.UnitPrice),
                    new SqlParameter("@DiscountAmount", detail.DiscountAmount),
                    new SqlParameter("@DiscountPercent", detail.DiscountPercent),
                    new SqlParameter("@TaxRate", detail.TaxRate),
                    new SqlParameter("@TaxAmount", detail.TaxAmount),
                    new SqlParameter("@LineTotal", detail.LineTotal)
                };

                int rowsAffected = DatabaseConnection.ExecuteNonQuery(query, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث تفصيل الفاتورة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حذف تفاصيل الفاتورة
        /// </summary>
        /// <param name="invoiceId">رقم الفاتورة</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public bool DeleteInvoiceDetails(int invoiceId)
        {
            try
            {
                string query = "DELETE FROM InvoiceDetails WHERE InvoiceID = @InvoiceID";
                
                SqlParameter[] parameters = {
                    new SqlParameter("@InvoiceID", invoiceId)
                };

                DatabaseConnection.ExecuteNonQuery(query, parameters);
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف تفاصيل الفاتورة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على تفاصيل الفاتورة
        /// </summary>
        /// <param name="invoiceId">رقم الفاتورة</param>
        /// <returns>قائمة تفاصيل الفاتورة</returns>
        public List<InvoiceDetail> GetInvoiceDetails(int invoiceId)
        {
            try
            {
                string query = @"
                    SELECT * FROM InvoiceDetails 
                    WHERE InvoiceID = @InvoiceID
                    ORDER BY InvoiceDetailID";

                SqlParameter[] parameters = {
                    new SqlParameter("@InvoiceID", invoiceId)
                };

                DataTable result = DatabaseConnection.ExecuteQuery(query, parameters);
                
                List<InvoiceDetail> details = new List<InvoiceDetail>();
                foreach (DataRow row in result.Rows)
                {
                    details.Add(MapDataRowToInvoiceDetail(row));
                }
                
                return details;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على تفاصيل الفاتورة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على تفصيل فاتورة واحد
        /// </summary>
        /// <param name="detailId">رقم التفصيل</param>
        /// <returns>تفصيل الفاتورة</returns>
        public InvoiceDetail GetInvoiceDetailById(int detailId)
        {
            try
            {
                string query = @"
                    SELECT * FROM InvoiceDetails 
                    WHERE InvoiceDetailID = @InvoiceDetailID";

                SqlParameter[] parameters = {
                    new SqlParameter("@InvoiceDetailID", detailId)
                };

                DataTable result = DatabaseConnection.ExecuteQuery(query, parameters);
                
                if (result.Rows.Count > 0)
                {
                    return MapDataRowToInvoiceDetail(result.Rows[0]);
                }
                
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على تفصيل الفاتورة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحويل صف البيانات إلى كائن تفصيل فاتورة
        /// </summary>
        /// <param name="row">صف البيانات</param>
        /// <returns>كائن تفصيل الفاتورة</returns>
        private InvoiceDetail MapDataRowToInvoiceDetail(DataRow row)
        {
            return new InvoiceDetail
            {
                InvoiceDetailID = Convert.ToInt32(row["InvoiceDetailID"]),
                InvoiceID = Convert.ToInt32(row["InvoiceID"]),
                ProductID = Convert.ToInt32(row["ProductID"]),
                ProductCode = row["ProductCode"].ToString(),
                ProductName = row["ProductName"].ToString(),
                Quantity = Convert.ToDecimal(row["Quantity"]),
                UnitPrice = Convert.ToDecimal(row["UnitPrice"]),
                DiscountAmount = Convert.ToDecimal(row["DiscountAmount"]),
                DiscountPercent = Convert.ToDecimal(row["DiscountPercent"]),
                TaxRate = Convert.ToDecimal(row["TaxRate"]),
                TaxAmount = Convert.ToDecimal(row["TaxAmount"]),
                LineTotal = Convert.ToDecimal(row["LineTotal"])
            };
        }

        /// <summary>
        /// حفظ تفاصيل الفاتورة (إدراج أو تحديث)
        /// </summary>
        /// <param name="invoiceId">رقم الفاتورة</param>
        /// <param name="details">قائمة التفاصيل</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SaveInvoiceDetails(int invoiceId, List<InvoiceDetail> details)
        {
            try
            {
                // حذف التفاصيل الموجودة
                DeleteInvoiceDetails(invoiceId);
                
                // إدراج التفاصيل الجديدة
                foreach (var detail in details)
                {
                    detail.InvoiceID = invoiceId;
                    InsertInvoiceDetail(detail);
                }
                
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ تفاصيل الفاتورة: {ex.Message}", ex);
            }
        }
    }
}
