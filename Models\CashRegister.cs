using System;
using System.ComponentModel.DataAnnotations;

namespace AredooPOS.Models
{
    /// <summary>
    /// نموذج الصندوق
    /// يمثل صندوق النقدية في النظام
    /// </summary>
    public class CashRegister
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم الصندوق
        /// </summary>
        public int CashRegisterID { get; set; }

        /// <summary>
        /// اسم الصندوق
        /// </summary>
        [Required(ErrorMessage = "اسم الصندوق مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الصندوق يجب أن يكون أقل من 100 حرف")]
        public string RegisterName { get; set; }

        /// <summary>
        /// رمز الصندوق
        /// </summary>
        [Required(ErrorMessage = "رمز الصندوق مطلوب")]
        [StringLength(20, ErrorMessage = "رمز الصندوق يجب أن يكون أقل من 20 حرف")]
        public string RegisterCode { get; set; }

        /// <summary>
        /// موقع الصندوق
        /// </summary>
        [StringLength(100, ErrorMessage = "موقع الصندوق يجب أن يكون أقل من 100 حرف")]
        public string Location { get; set; }

        /// <summary>
        /// وصف الصندوق
        /// </summary>
        [StringLength(255, ErrorMessage = "وصف الصندوق يجب أن يكون أقل من 255 حرف")]
        public string Description { get; set; }

        /// <summary>
        /// هل الصندوق نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// الرصيد الافتتاحي
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الرصيد الافتتاحي يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal OpeningBalance { get; set; } = 0;

        /// <summary>
        /// الرصيد الحالي
        /// </summary>
        public decimal CurrentBalance { get; set; } = 0;

        /// <summary>
        /// الحد الأقصى للنقدية
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الحد الأقصى للنقدية يجب أن يكون أكبر من صفر")]
        public decimal? MaxCashLimit { get; set; }

        /// <summary>
        /// الحد الأدنى للنقدية
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الحد الأدنى للنقدية يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal MinCashLimit { get; set; } = 0;

        /// <summary>
        /// هل يتطلب موافقة المدير
        /// </summary>
        public bool RequireManagerApproval { get; set; } = false;

        /// <summary>
        /// هل يسمح بالرصيد السالب
        /// </summary>
        public bool AllowNegativeBalance { get; set; } = false;

        /// <summary>
        /// وقت الإغلاق التلقائي
        /// </summary>
        public TimeSpan? AutoCloseTime { get; set; }

        /// <summary>
        /// رقم آخر جلسة
        /// </summary>
        public int? LastSessionID { get; set; }

        /// <summary>
        /// تاريخ آخر معاملة
        /// </summary>
        public DateTime? LastTransactionDate { get; set; }

        #endregion

        #region معلومات النظام

        /// <summary>
        /// من أنشأ السجل
        /// </summary>
        [Required]
        [StringLength(50)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// من عدل السجل
        /// </summary>
        [StringLength(50)]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// تاريخ التعديل
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// هل السجل محذوف
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// من حذف السجل
        /// </summary>
        [StringLength(50)]
        public string DeletedBy { get; set; }

        /// <summary>
        /// تاريخ الحذف
        /// </summary>
        public DateTime? DeletedDate { get; set; }

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// اسم العرض الكامل
        /// </summary>
        public string DisplayName => $"{RegisterName} ({RegisterCode})";

        /// <summary>
        /// هل الصندوق له حد أقصى
        /// </summary>
        public bool HasMaxLimit => MaxCashLimit.HasValue && MaxCashLimit.Value > 0;

        /// <summary>
        /// هل الرصيد الحالي يتجاوز الحد الأقصى
        /// </summary>
        public bool IsOverMaxLimit => HasMaxLimit && CurrentBalance > MaxCashLimit.Value;

        /// <summary>
        /// هل الرصيد الحالي أقل من الحد الأدنى
        /// </summary>
        public bool IsBelowMinLimit => CurrentBalance < MinCashLimit;

        /// <summary>
        /// هل الرصيد سالب
        /// </summary>
        public bool HasNegativeBalance => CurrentBalance < 0;

        /// <summary>
        /// هل يحتاج إلى تنبيه
        /// </summary>
        public bool NeedsAlert => IsOverMaxLimit || IsBelowMinLimit || (HasNegativeBalance && !AllowNegativeBalance);

        /// <summary>
        /// نوع التنبيه
        /// </summary>
        public string AlertType
        {
            get
            {
                if (HasNegativeBalance && !AllowNegativeBalance)
                    return "رصيد سالب";
                if (IsOverMaxLimit)
                    return "تجاوز الحد الأقصى";
                if (IsBelowMinLimit)
                    return "أقل من الحد الأدنى";
                return null;
            }
        }

        /// <summary>
        /// هل يمكن فتح جلسة جديدة
        /// </summary>
        public bool CanOpenSession => IsActive && !IsDeleted;

        /// <summary>
        /// عدد الأيام منذ آخر معاملة
        /// </summary>
        public int? DaysSinceLastTransaction
        {
            get
            {
                if (!LastTransactionDate.HasValue)
                    return null;
                return (DateTime.Now.Date - LastTransactionDate.Value.Date).Days;
            }
        }

        /// <summary>
        /// هل الصندوق خامل
        /// </summary>
        public bool IsIdle => DaysSinceLastTransaction.HasValue && DaysSinceLastTransaction.Value > 7;

        #endregion

        #region الدوال المساعدة

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            if (string.IsNullOrWhiteSpace(RegisterName))
                return false;

            if (string.IsNullOrWhiteSpace(RegisterCode))
                return false;

            if (OpeningBalance < 0)
                return false;

            if (MinCashLimit < 0)
                return false;

            if (HasMaxLimit && MaxCashLimit.Value <= MinCashLimit)
                return false;

            return true;
        }

        /// <summary>
        /// التحقق من إمكانية إجراء معاملة
        /// </summary>
        /// <param name="amount">مبلغ المعاملة</param>
        /// <param name="isWithdrawal">هل هي عملية سحب</param>
        /// <returns>true إذا كان يمكن إجراء المعاملة</returns>
        public bool CanProcessTransaction(decimal amount, bool isWithdrawal = false)
        {
            if (!IsActive || IsDeleted)
                return false;

            if (amount <= 0)
                return false;

            if (isWithdrawal)
            {
                var newBalance = CurrentBalance - amount;
                
                // التحقق من السماح بالرصيد السالب
                if (newBalance < 0 && !AllowNegativeBalance)
                    return false;

                // التحقق من الحد الأدنى
                if (newBalance < MinCashLimit)
                    return false;
            }
            else
            {
                var newBalance = CurrentBalance + amount;
                
                // التحقق من الحد الأقصى
                if (HasMaxLimit && newBalance > MaxCashLimit.Value)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// تحديث الرصيد
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="isWithdrawal">هل هي عملية سحب</param>
        public void UpdateBalance(decimal amount, bool isWithdrawal = false)
        {
            if (isWithdrawal)
                CurrentBalance -= amount;
            else
                CurrentBalance += amount;

            LastTransactionDate = DateTime.Now;
        }

        /// <summary>
        /// إعادة تعيين الرصيد
        /// </summary>
        /// <param name="newBalance">الرصيد الجديد</param>
        public void ResetBalance(decimal newBalance)
        {
            CurrentBalance = newBalance;
            LastTransactionDate = DateTime.Now;
        }

        /// <summary>
        /// نسخ البيانات من صندوق آخر
        /// </summary>
        /// <param name="source">المصدر</param>
        public void CopyFrom(CashRegister source)
        {
            if (source == null)
                return;

            RegisterName = source.RegisterName;
            RegisterCode = source.RegisterCode;
            Location = source.Location;
            Description = source.Description;
            IsActive = source.IsActive;
            OpeningBalance = source.OpeningBalance;
            MaxCashLimit = source.MaxCashLimit;
            MinCashLimit = source.MinCashLimit;
            RequireManagerApproval = source.RequireManagerApproval;
            AllowNegativeBalance = source.AllowNegativeBalance;
            AutoCloseTime = source.AutoCloseTime;
        }

        /// <summary>
        /// تحويل إلى نص
        /// </summary>
        /// <returns>تمثيل نصي للكائن</returns>
        public override string ToString()
        {
            return DisplayName;
        }

        /// <summary>
        /// مقارنة مع كائن آخر
        /// </summary>
        /// <param name="obj">الكائن المراد المقارنة معه</param>
        /// <returns>true إذا كانا متساويين</returns>
        public override bool Equals(object obj)
        {
            if (obj is CashRegister other)
            {
                return CashRegisterID == other.CashRegisterID;
            }
            return false;
        }

        /// <summary>
        /// الحصول على رمز التجمع
        /// </summary>
        /// <returns>رمز التجمع</returns>
        public override int GetHashCode()
        {
            return CashRegisterID.GetHashCode();
        }

        #endregion
    }

    #region التعدادات المساعدة

    /// <summary>
    /// حالات الصندوق
    /// </summary>
    public enum CashRegisterStatus
    {
        /// <summary>
        /// نشط
        /// </summary>
        Active,

        /// <summary>
        /// غير نشط
        /// </summary>
        Inactive,

        /// <summary>
        /// قيد الصيانة
        /// </summary>
        Maintenance,

        /// <summary>
        /// محذوف
        /// </summary>
        Deleted
    }

    /// <summary>
    /// أنواع التنبيهات
    /// </summary>
    public enum CashRegisterAlertType
    {
        /// <summary>
        /// لا يوجد تنبيه
        /// </summary>
        None,

        /// <summary>
        /// رصيد سالب
        /// </summary>
        NegativeBalance,

        /// <summary>
        /// أقل من الحد الأدنى
        /// </summary>
        BelowMinimum,

        /// <summary>
        /// تجاوز الحد الأقصى
        /// </summary>
        OverMaximum,

        /// <summary>
        /// صندوق خامل
        /// </summary>
        Idle
    }

    #endregion
}
