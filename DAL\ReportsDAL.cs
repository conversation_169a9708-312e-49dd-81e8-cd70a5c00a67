using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using AredooPOS.Models.Reports;
using Microsoft.Extensions.Logging;

namespace AredooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات للتقارير
    /// تحتوي على جميع العمليات المتعلقة بقاعدة البيانات للتقارير المختلفة
    /// </summary>
    public class ReportsDAL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly string _connectionString;
        private readonly ILogger<ReportsDAL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة الوصول للبيانات للتقارير
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public ReportsDAL(string connectionString = null, ILogger<ReportsDAL> logger = null)
        {
            _connectionString = connectionString ?? DatabaseConfig.GetConnectionString();
            _logger = logger;
        }

        #endregion

        #region تقارير المبيعات

        /// <summary>
        /// الحصول على تقرير المبيعات
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="reportType">نوع التقرير</param>
        /// <returns>تقرير المبيعات</returns>
        public SalesReport GetSalesReport(DateTime fromDate, DateTime toDate, string reportType = "Daily")
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetSalesReport", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@FromDate", fromDate);
                command.Parameters.AddWithValue("@ToDate", toDate);
                command.Parameters.AddWithValue("@ReportType", reportType);

                connection.Open();
                using var reader = command.ExecuteReader();

                var report = new SalesReport
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    ReportType = reportType,
                    GeneratedDate = DateTime.Now
                };

                // قراءة الإجماليات
                if (reader.Read())
                {
                    report.TotalSales = reader.GetDecimal("TotalSales");
                    report.TotalQuantitySold = reader.GetDecimal("TotalQuantitySold");
                    report.TotalInvoices = reader.GetInt32("TotalInvoices");
                    report.TotalDiscounts = reader.GetDecimal("TotalDiscounts");
                    report.TotalTaxes = reader.GetDecimal("TotalTaxes");
                    report.CashSales = reader.GetDecimal("CashSales");
                    report.CardSales = reader.GetDecimal("CardSales");
                    report.TransferSales = reader.GetDecimal("TransferSales");
                    report.CreditSales = reader.GetDecimal("CreditSales");
                    report.TotalReturns = reader.GetDecimal("TotalReturns");
                    report.ReturnInvoicesCount = reader.GetInt32("ReturnInvoicesCount");
                }

                // قراءة تفاصيل المنتجات
                if (reader.NextResult())
                {
                    while (reader.Read())
                    {
                        report.ProductSalesDetails.Add(new ProductSalesDetail
                        {
                            ProductID = reader.GetInt32("ProductID"),
                            ProductName = reader.GetString("ProductName"),
                            ProductCode = reader.GetString("ProductCode"),
                            CategoryName = reader.GetString("CategoryName"),
                            QuantitySold = reader.GetDecimal("QuantitySold"),
                            UnitPrice = reader.GetDecimal("UnitPrice"),
                            TotalSales = reader.GetDecimal("TotalSales"),
                            TotalCost = reader.GetDecimal("TotalCost"),
                            TotalProfit = reader.GetDecimal("TotalProfit"),
                            TotalDiscount = reader.GetDecimal("TotalDiscount"),
                            InvoiceCount = reader.GetInt32("InvoiceCount")
                        });
                    }
                }

                // قراءة التفاصيل اليومية
                if (reader.NextResult())
                {
                    while (reader.Read())
                    {
                        report.DailySalesDetails.Add(new DailySalesDetail
                        {
                            SalesDate = reader.GetDateTime("SalesDate"),
                            TotalSales = reader.GetDecimal("TotalSales"),
                            InvoiceCount = reader.GetInt32("InvoiceCount"),
                            TotalQuantity = reader.GetDecimal("TotalQuantity"),
                            TotalDiscount = reader.GetDecimal("TotalDiscount"),
                            TotalTax = reader.GetDecimal("TotalTax"),
                            TotalReturns = reader.GetDecimal("TotalReturns"),
                            ReturnCount = reader.GetInt32("ReturnCount")
                        });
                    }
                }

                report.CalculateTotals();
                return report;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على تقرير المبيعات");
                throw;
            }
        }

        /// <summary>
        /// الحصول على تفاصيل مبيعات المنتجات
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>قائمة تفاصيل مبيعات المنتجات</returns>
        public List<ProductSalesDetail> GetProductSalesDetails(DateTime fromDate, DateTime toDate)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetProductSalesDetails", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@FromDate", fromDate);
                command.Parameters.AddWithValue("@ToDate", toDate);

                connection.Open();
                using var reader = command.ExecuteReader();

                var details = new List<ProductSalesDetail>();
                while (reader.Read())
                {
                    details.Add(new ProductSalesDetail
                    {
                        ProductID = reader.GetInt32("ProductID"),
                        ProductName = reader.GetString("ProductName"),
                        ProductCode = reader.GetString("ProductCode"),
                        CategoryName = reader.GetString("CategoryName"),
                        QuantitySold = reader.GetDecimal("QuantitySold"),
                        UnitPrice = reader.GetDecimal("UnitPrice"),
                        TotalSales = reader.GetDecimal("TotalSales"),
                        TotalCost = reader.GetDecimal("TotalCost"),
                        TotalProfit = reader.GetDecimal("TotalProfit"),
                        TotalDiscount = reader.GetDecimal("TotalDiscount"),
                        TotalTax = reader.GetDecimal("TotalTax"),
                        InvoiceCount = reader.GetInt32("InvoiceCount")
                    });
                }

                return details;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على تفاصيل مبيعات المنتجات");
                throw;
            }
        }

        #endregion

        #region تقارير المصاريف

        /// <summary>
        /// الحصول على تقرير المصاريف
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="reportType">نوع التقرير</param>
        /// <returns>تقرير المصاريف</returns>
        public ExpenseReport GetExpenseReport(DateTime fromDate, DateTime toDate, string reportType = "Daily")
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetExpenseReport", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@FromDate", fromDate);
                command.Parameters.AddWithValue("@ToDate", toDate);
                command.Parameters.AddWithValue("@ReportType", reportType);

                connection.Open();
                using var reader = command.ExecuteReader();

                var report = new ExpenseReport
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    ReportType = reportType,
                    GeneratedDate = DateTime.Now
                };

                // قراءة الإجماليات
                if (reader.Read())
                {
                    report.TotalExpenses = reader.GetDecimal("TotalExpenses");
                    report.ExpenseCount = reader.GetInt32("ExpenseCount");
                    report.OperationalExpenses = reader.GetDecimal("OperationalExpenses");
                    report.AdministrativeExpenses = reader.GetDecimal("AdministrativeExpenses");
                    report.MarketingExpenses = reader.GetDecimal("MarketingExpenses");
                    report.MaintenanceExpenses = reader.GetDecimal("MaintenanceExpenses");
                    report.UtilityExpenses = reader.GetDecimal("UtilityExpenses");
                    report.CashExpenses = reader.GetDecimal("CashExpenses");
                    report.CardExpenses = reader.GetDecimal("CardExpenses");
                    report.TransferExpenses = reader.GetDecimal("TransferExpenses");
                    report.ApprovedExpenses = reader.GetDecimal("ApprovedExpenses");
                    report.PendingExpenses = reader.GetDecimal("PendingExpenses");
                    report.RejectedExpenses = reader.GetDecimal("RejectedExpenses");
                }

                // قراءة تفاصيل الفئات
                if (reader.NextResult())
                {
                    while (reader.Read())
                    {
                        report.CategoryDetails.Add(new ExpenseCategoryDetail
                        {
                            CategoryID = reader.GetInt32("CategoryID"),
                            CategoryName = reader.GetString("CategoryName"),
                            CategoryCode = reader.GetString("CategoryCode"),
                            TotalAmount = reader.GetDecimal("TotalAmount"),
                            ExpenseCount = reader.GetInt32("ExpenseCount"),
                            AverageAmount = reader.GetDecimal("AverageAmount"),
                            ApprovedAmount = reader.GetDecimal("ApprovedAmount"),
                            PendingAmount = reader.GetDecimal("PendingAmount"),
                            RejectedAmount = reader.GetDecimal("RejectedAmount"),
                            LastExpenseDate = reader.GetDateTime("LastExpenseDate")
                        });
                    }
                }

                report.CalculateTotals();
                return report;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على تقرير المصاريف");
                throw;
            }
        }

        #endregion

        #region تقارير الأرباح

        /// <summary>
        /// الحصول على تقرير الأرباح
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="reportType">نوع التقرير</param>
        /// <returns>تقرير الأرباح</returns>
        public ProfitReport GetProfitReport(DateTime fromDate, DateTime toDate, string reportType = "Daily")
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetProfitReport", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@FromDate", fromDate);
                command.Parameters.AddWithValue("@ToDate", toDate);
                command.Parameters.AddWithValue("@ReportType", reportType);

                connection.Open();
                using var reader = command.ExecuteReader();

                var report = new ProfitReport
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    ReportType = reportType,
                    GeneratedDate = DateTime.Now
                };

                // قراءة الإجماليات
                if (reader.Read())
                {
                    report.TotalSales = reader.GetDecimal("TotalSales");
                    report.TotalReturns = reader.GetDecimal("TotalReturns");
                    report.TotalDiscounts = reader.GetDecimal("TotalDiscounts");
                    report.CostOfGoodsSold = reader.GetDecimal("CostOfGoodsSold");
                    report.SellingExpenses = reader.GetDecimal("SellingExpenses");
                    report.AdministrativeExpenses = reader.GetDecimal("AdministrativeExpenses");
                    report.MarketingExpenses = reader.GetDecimal("MarketingExpenses");
                    report.FinancialExpenses = reader.GetDecimal("FinancialExpenses");
                    report.OtherExpenses = reader.GetDecimal("OtherExpenses");
                    report.Taxes = reader.GetDecimal("Taxes");
                    report.OtherRevenue = reader.GetDecimal("OtherRevenue");
                }

                // قراءة تفاصيل الأرباح حسب المنتج
                if (reader.NextResult())
                {
                    while (reader.Read())
                    {
                        report.ProductProfitDetails.Add(new ProductProfitDetail
                        {
                            ProductID = reader.GetInt32("ProductID"),
                            ProductName = reader.GetString("ProductName"),
                            ProductCode = reader.GetString("ProductCode"),
                            TotalSales = reader.GetDecimal("TotalSales"),
                            TotalCost = reader.GetDecimal("TotalCost"),
                            TotalProfit = reader.GetDecimal("TotalProfit"),
                            TotalDiscount = reader.GetDecimal("TotalDiscount"),
                            QuantitySold = reader.GetDecimal("QuantitySold")
                        });
                    }
                }

                return report;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على تقرير الأرباح");
                throw;
            }
        }

        #endregion

        #region تقارير الأقساط

        /// <summary>
        /// الحصول على تقرير الأقساط
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="reportType">نوع التقرير</param>
        /// <returns>تقرير الأقساط</returns>
        public InstallmentReport GetInstallmentReport(DateTime fromDate, DateTime toDate, string reportType = "Due")
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetInstallmentReport", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@FromDate", fromDate);
                command.Parameters.AddWithValue("@ToDate", toDate);
                command.Parameters.AddWithValue("@ReportType", reportType);

                connection.Open();
                using var reader = command.ExecuteReader();

                var report = new InstallmentReport
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    ReportType = reportType,
                    GeneratedDate = DateTime.Now
                };

                // قراءة الإجماليات
                if (reader.Read())
                {
                    report.TotalInstallmentValue = reader.GetDecimal("TotalInstallmentValue");
                    report.TotalPaidAmount = reader.GetDecimal("TotalPaidAmount");
                    report.TotalOutstandingAmount = reader.GetDecimal("TotalOutstandingAmount");
                    report.TotalInstallments = reader.GetInt32("TotalInstallments");
                    report.PaidInstallments = reader.GetInt32("PaidInstallments");
                    report.OutstandingInstallments = reader.GetInt32("OutstandingInstallments");
                    report.OverdueInstallments = reader.GetInt32("OverdueInstallments");
                    report.TodayDueAmount = reader.GetDecimal("TodayDueAmount");
                    report.WeekDueAmount = reader.GetDecimal("WeekDueAmount");
                    report.MonthDueAmount = reader.GetDecimal("MonthDueAmount");
                }

                // قراءة تفاصيل العملاء
                if (reader.NextResult())
                {
                    while (reader.Read())
                    {
                        report.CustomerDetails.Add(new CustomerInstallmentDetail
                        {
                            CustomerID = reader.GetInt32("CustomerID"),
                            CustomerName = reader.GetString("CustomerName"),
                            CustomerPhone = reader.GetString("CustomerPhone"),
                            TotalInstallmentValue = reader.GetDecimal("TotalInstallmentValue"),
                            PaidAmount = reader.GetDecimal("PaidAmount"),
                            OutstandingAmount = reader.GetDecimal("OutstandingAmount"),
                            TotalInstallments = reader.GetInt32("TotalInstallments"),
                            PaidInstallments = reader.GetInt32("PaidInstallments"),
                            OutstandingInstallments = reader.GetInt32("OutstandingInstallments"),
                            OverdueInstallments = reader.GetInt32("OverdueInstallments"),
                            LastPaymentDate = reader.IsDBNull("LastPaymentDate") ? DateTime.MinValue : reader.GetDateTime("LastPaymentDate"),
                            NextDueDate = reader.GetDateTime("NextDueDate"),
                            NextDueAmount = reader.GetDecimal("NextDueAmount"),
                            DaysOverdue = reader.GetInt32("DaysOverdue")
                        });
                    }
                }

                report.CalculateTotals();
                return report;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على تقرير الأقساط");
                throw;
            }
        }

        #endregion

        #region تقارير الديون

        /// <summary>
        /// الحصول على تقرير الديون
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="reportType">نوع التقرير</param>
        /// <returns>تقرير الديون</returns>
        public DebtReport GetDebtReport(DateTime fromDate, DateTime toDate, string reportType = "Outstanding")
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetDebtReport", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@FromDate", fromDate);
                command.Parameters.AddWithValue("@ToDate", toDate);
                command.Parameters.AddWithValue("@ReportType", reportType);

                connection.Open();
                using var reader = command.ExecuteReader();

                var report = new DebtReport
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    ReportType = reportType,
                    GeneratedDate = DateTime.Now
                };

                // قراءة الإجماليات
                if (reader.Read())
                {
                    report.TotalDebt = reader.GetDecimal("TotalDebt");
                    report.TotalPaidAmount = reader.GetDecimal("TotalPaidAmount");
                    report.TotalOutstandingAmount = reader.GetDecimal("TotalOutstandingAmount");
                    report.DebtorCustomersCount = reader.GetInt32("DebtorCustomersCount");
                    report.OutstandingInvoicesCount = reader.GetInt32("OutstandingInvoicesCount");
                    report.CurrentDebt = reader.GetDecimal("CurrentDebt");
                    report.Debt30To60Days = reader.GetDecimal("Debt30To60Days");
                    report.Debt61To90Days = reader.GetDecimal("Debt61To90Days");
                    report.Debt91To120Days = reader.GetDecimal("Debt91To120Days");
                    report.DebtOver120Days = reader.GetDecimal("DebtOver120Days");
                }

                // قراءة تفاصيل العملاء
                if (reader.NextResult())
                {
                    while (reader.Read())
                    {
                        report.CustomerDebtDetails.Add(new CustomerDebtDetail
                        {
                            CustomerID = reader.GetInt32("CustomerID"),
                            CustomerName = reader.GetString("CustomerName"),
                            CustomerPhone = reader.GetString("CustomerPhone"),
                            TotalDebt = reader.GetDecimal("TotalDebt"),
                            PaidAmount = reader.GetDecimal("PaidAmount"),
                            OutstandingAmount = reader.GetDecimal("OutstandingAmount"),
                            InvoiceCount = reader.GetInt32("InvoiceCount"),
                            LastInvoiceDate = reader.GetDateTime("LastInvoiceDate"),
                            LastPaymentDate = reader.IsDBNull("LastPaymentDate") ? DateTime.MinValue : reader.GetDateTime("LastPaymentDate"),
                            CreditStatus = reader.GetString("CreditStatus"),
                            CreditLimit = reader.GetDecimal("CreditLimit")
                        });
                    }
                }

                // قراءة تفاصيل الفواتير
                if (reader.NextResult())
                {
                    while (reader.Read())
                    {
                        report.InvoiceDebtDetails.Add(new InvoiceDebtDetail
                        {
                            InvoiceID = reader.GetInt32("InvoiceID"),
                            InvoiceNumber = reader.GetString("InvoiceNumber"),
                            CustomerID = reader.GetInt32("CustomerID"),
                            CustomerName = reader.GetString("CustomerName"),
                            InvoiceDate = reader.GetDateTime("InvoiceDate"),
                            DueDate = reader.GetDateTime("DueDate"),
                            InvoiceAmount = reader.GetDecimal("InvoiceAmount"),
                            PaidAmount = reader.GetDecimal("PaidAmount"),
                            OutstandingAmount = reader.GetDecimal("OutstandingAmount"),
                            DaysOutstanding = reader.GetInt32("DaysOutstanding"),
                            Status = reader.GetString("Status")
                        });
                    }
                }

                report.CalculateTotals();
                return report;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على تقرير الديون");
                throw;
            }
        }

        #endregion

        #region تقارير حركة الصندوق

        /// <summary>
        /// الحصول على تقرير حركة الصندوق
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="cashRegisterID">رقم الصندوق (اختياري)</param>
        /// <param name="reportType">نوع التقرير</param>
        /// <returns>تقرير حركة الصندوق</returns>
        public CashFlowReport GetCashFlowReport(DateTime fromDate, DateTime toDate, int? cashRegisterID = null, string reportType = "Daily")
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetCashFlowReport", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@FromDate", fromDate);
                command.Parameters.AddWithValue("@ToDate", toDate);
                command.Parameters.AddWithValue("@CashRegisterID", cashRegisterID ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ReportType", reportType);

                connection.Open();
                using var reader = command.ExecuteReader();

                var report = new CashFlowReport
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    CashRegisterID = cashRegisterID,
                    ReportType = reportType,
                    GeneratedDate = DateTime.Now
                };

                // قراءة الإجماليات
                if (reader.Read())
                {
                    report.OpeningBalance = reader.GetDecimal("OpeningBalance");
                    report.ClosingBalance = reader.GetDecimal("ClosingBalance");
                    report.CashSales = reader.GetDecimal("CashSales");
                    report.DebtCollections = reader.GetDecimal("DebtCollections");
                    report.InstallmentCollections = reader.GetDecimal("InstallmentCollections");
                    report.CashDeposits = reader.GetDecimal("CashDeposits");
                    report.OtherIncome = reader.GetDecimal("OtherIncome");
                    report.CashExpenses = reader.GetDecimal("CashExpenses");
                    report.CashWithdrawals = reader.GetDecimal("CashWithdrawals");
                    report.SupplierPayments = reader.GetDecimal("SupplierPayments");
                    report.CashReturns = reader.GetDecimal("CashReturns");
                    report.OtherExpenses = reader.GetDecimal("OtherExpenses");
                    report.CashTransactions = reader.GetDecimal("CashTransactions");
                    report.CardTransactions = reader.GetDecimal("CardTransactions");
                    report.TransferTransactions = reader.GetDecimal("TransferTransactions");
                    report.CheckTransactions = reader.GetDecimal("CheckTransactions");
                    report.CashRegisterName = reader.IsDBNull("CashRegisterName") ? null : reader.GetString("CashRegisterName");
                }

                // قراءة تفاصيل المعاملات
                if (reader.NextResult())
                {
                    while (reader.Read())
                    {
                        report.TransactionDetails.Add(new CashTransactionDetail
                        {
                            TransactionID = reader.GetInt32("TransactionID"),
                            TransactionNumber = reader.GetString("TransactionNumber"),
                            TransactionDate = reader.GetDateTime("TransactionDate"),
                            TransactionType = reader.GetString("TransactionType"),
                            Amount = reader.GetDecimal("Amount"),
                            PaymentMethod = reader.GetString("PaymentMethod"),
                            Description = reader.GetString("Description"),
                            Category = reader.GetString("Category"),
                            UserName = reader.GetString("UserName"),
                            ReferenceNumber = reader.IsDBNull("ReferenceNumber") ? null : reader.GetString("ReferenceNumber"),
                            IsVoided = reader.GetBoolean("IsVoided")
                        });
                    }
                }

                // قراءة التفاصيل اليومية
                if (reader.NextResult())
                {
                    while (reader.Read())
                    {
                        report.DailyDetails.Add(new DailyCashFlowDetail
                        {
                            FlowDate = reader.GetDateTime("FlowDate"),
                            OpeningBalance = reader.GetDecimal("OpeningBalance"),
                            CashInflows = reader.GetDecimal("CashInflows"),
                            CashOutflows = reader.GetDecimal("CashOutflows"),
                            NetCashFlow = reader.GetDecimal("NetCashFlow"),
                            ClosingBalance = reader.GetDecimal("ClosingBalance"),
                            TransactionCount = reader.GetInt32("TransactionCount"),
                            SessionCount = reader.GetInt32("SessionCount")
                        });
                    }
                }

                report.CalculateTotals();
                return report;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على تقرير حركة الصندوق");
                throw;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// الحصول على قائمة التقارير المحفوظة
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>قائمة التقارير</returns>
        public List<ReportInfo> GetSavedReports(string reportType = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var sql = "SELECT * FROM SavedReports WHERE 1=1";
                var parameters = new List<SqlParameter>();

                if (!string.IsNullOrWhiteSpace(reportType))
                {
                    sql += " AND ReportType = @ReportType";
                    parameters.Add(new SqlParameter("@ReportType", reportType));
                }

                if (fromDate.HasValue)
                {
                    sql += " AND GeneratedDate >= @FromDate";
                    parameters.Add(new SqlParameter("@FromDate", fromDate.Value));
                }

                if (toDate.HasValue)
                {
                    sql += " AND GeneratedDate <= @ToDate";
                    parameters.Add(new SqlParameter("@ToDate", toDate.Value));
                }

                sql += " ORDER BY GeneratedDate DESC";

                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddRange(parameters.ToArray());

                connection.Open();
                using var reader = command.ExecuteReader();

                var reports = new List<ReportInfo>();
                while (reader.Read())
                {
                    reports.Add(new ReportInfo
                    {
                        ReportID = reader.GetInt32("ReportID"),
                        ReportType = reader.GetString("ReportType"),
                        ReportName = reader.GetString("ReportName"),
                        GeneratedDate = reader.GetDateTime("GeneratedDate"),
                        GeneratedBy = reader.GetString("GeneratedBy"),
                        FilePath = reader.IsDBNull("FilePath") ? null : reader.GetString("FilePath"),
                        FileSize = reader.IsDBNull("FileSize") ? 0 : reader.GetInt64("FileSize")
                    });
                }

                return reports;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على التقارير المحفوظة");
                throw;
            }
        }

        /// <summary>
        /// حفظ معلومات التقرير
        /// </summary>
        /// <param name="reportInfo">معلومات التقرير</param>
        /// <returns>رقم التقرير المحفوظ</returns>
        public int SaveReportInfo(ReportInfo reportInfo)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand(@"
                    INSERT INTO SavedReports (ReportType, ReportName, GeneratedDate, GeneratedBy, FilePath, FileSize)
                    OUTPUT INSERTED.ReportID
                    VALUES (@ReportType, @ReportName, @GeneratedDate, @GeneratedBy, @FilePath, @FileSize)", connection);

                command.Parameters.AddWithValue("@ReportType", reportInfo.ReportType);
                command.Parameters.AddWithValue("@ReportName", reportInfo.ReportName);
                command.Parameters.AddWithValue("@GeneratedDate", reportInfo.GeneratedDate);
                command.Parameters.AddWithValue("@GeneratedBy", reportInfo.GeneratedBy);
                command.Parameters.AddWithValue("@FilePath", reportInfo.FilePath ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@FileSize", reportInfo.FileSize);

                connection.Open();
                var reportId = (int)command.ExecuteScalar();

                _logger?.LogInformation($"تم حفظ معلومات التقرير برقم {reportId}");
                return reportId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ معلومات التقرير");
                throw;
            }
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// معلومات التقرير المحفوظ
    /// </summary>
    public class ReportInfo
    {
        public int ReportID { get; set; }
        public string ReportType { get; set; }
        public string ReportName { get; set; }
        public DateTime GeneratedDate { get; set; }
        public string GeneratedBy { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
    }

    #endregion
}
