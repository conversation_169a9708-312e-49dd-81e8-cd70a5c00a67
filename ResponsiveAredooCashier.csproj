<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net472</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <AssemblyTitle>أريدو الكاشير المتجاوب - نظام نقاط البيع المتكيف</AssemblyTitle>
    <AssemblyDescription>نظام كاشير عربي متجاوب يتكيف مع جميع أحجام الشاشات من 1366x768 إلى 4K، مع دعم كامل للغة العربية وتصميم Fluent/Material</AssemblyDescription>
    <AssemblyCompany>أريدو للتقنية المتقدمة</AssemblyCompany>
    <AssemblyProduct>أريدو الكاشير المتجاوب</AssemblyProduct>
    <AssemblyCopyright>© 2024 أريدو للتقنية. جميع الحقوق محفوظة.</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <StartupObject>AredooCashier.ResponsiveProgram</StartupObject>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <Deterministic>true</Deterministic>
    <LangVersion>latest</LangVersion>
    <Nullable>disable</Nullable>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE;RESPONSIVE_DESIGN</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>false</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE;RESPONSIVE_DESIGN</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <!-- مراجع النظام -->
  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing.Design" />
  </ItemGroup>

  <!-- ملفات الكود المتجاوب -->
  <ItemGroup>
    <!-- البرنامج الرئيسي المتجاوب -->
    <Compile Include="ResponsiveProgram.cs" />
    <Compile Include="ResponsiveAredooCashierApp.cs" />
    
    <!-- نظام التصميم المتجاوب -->
    <Compile Include="UI\ResponsiveLayoutSystem.cs" />
    <Compile Include="UI\ResponsiveDesignSystem.cs" />
    
    <!-- المكونات المتجاوبة -->
    <Compile Include="UI\ResponsiveTopBar.cs" />
    <Compile Include="UI\ResponsiveSidebar.cs" />
    <Compile Include="UI\ResponsiveDashboard.cs" />
    <Compile Include="UI\ResponsiveInvoiceForm.cs" />
    <Compile Include="UI\ResponsiveCustomersView.cs" />
  </ItemGroup>

  <!-- الموارد والأصول -->
  <ItemGroup>
    <Content Include="Assets\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Fonts\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <!-- المجلدات -->
  <ItemGroup>
    <Folder Include="Assets\" />
    <Folder Include="Resources\" />
    <Folder Include="Fonts\" />
    <Folder Include="UI\" />
    <Folder Include="Data\" />
    <Folder Include="Reports\" />
    <Folder Include="Receipts\" />
    <Folder Include="Backups\" />
    <Folder Include="Logs\" />
    <Folder Include="Temp\" />
  </ItemGroup>

  <!-- إنشاء المجلدات المطلوبة -->
  <Target Name="CreateDirectories" BeforeTargets="Build">
    <MakeDir Directories="$(OutputPath)Assets" Condition="!Exists('$(OutputPath)Assets')" />
    <MakeDir Directories="$(OutputPath)Resources" Condition="!Exists('$(OutputPath)Resources')" />
    <MakeDir Directories="$(OutputPath)Fonts" Condition="!Exists('$(OutputPath)Fonts')" />
    <MakeDir Directories="$(OutputPath)Data" Condition="!Exists('$(OutputPath)Data')" />
    <MakeDir Directories="$(OutputPath)Reports" Condition="!Exists('$(OutputPath)Reports')" />
    <MakeDir Directories="$(OutputPath)Receipts" Condition="!Exists('$(OutputPath)Receipts')" />
    <MakeDir Directories="$(OutputPath)Backups" Condition="!Exists('$(OutputPath)Backups')" />
    <MakeDir Directories="$(OutputPath)Logs" Condition="!Exists('$(OutputPath)Logs')" />
    <MakeDir Directories="$(OutputPath)Temp" Condition="!Exists('$(OutputPath)Temp')" />
  </Target>

  <!-- إجراءات ما بعد البناء -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="echo 🎉 تم بناء تطبيق أريدو الكاشير المتجاوب بنجاح!" />
    <Exec Command="echo 📁 الملف التنفيذي: $(OutputPath)$(AssemblyName).exe" />
    <Exec Command="echo 🎨 تصميم متجاوب يتكيف مع جميع أحجام الشاشات" />
    <Exec Command="echo 📱 من 1366x768 إلى 4K Ultra HD" />
    <Exec Command="echo 🌐 دعم كامل للغة العربية مع RTL" />
    <Exec Command="echo 💻 متوافق مع Windows 7+ وجميع الإصدارات" />
    <Exec Command="echo 🚀 جاهز للاستخدام في نقاط البيع المختلفة" />
  </Target>

  <!-- معلومات إضافية للنشر -->
  <PropertyGroup>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>2.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <ProductName>أريدو الكاشير المتجاوب - نظام نقاط البيع المتكيف</ProductName>
    <PublisherName>أريدو للتقنية المتقدمة</PublisherName>
    <SuiteName>أريدو لحلول نقاط البيع المتجاوبة</SuiteName>
  </PropertyGroup>

  <!-- إعدادات DPI Awareness -->
  <ItemGroup>
    <None Include="app.manifest" />
  </ItemGroup>



  <!-- إعدادات خاصة بالتطبيقات المتجاوبة -->
  <PropertyGroup>
    <ApplicationHighDpiMode>SystemAware</ApplicationHighDpiMode>
    <EnableWindowsTargeting>true</EnableWindowsTargeting>
    <SupportedOSPlatformVersion>7.0</SupportedOSPlatformVersion>
  </PropertyGroup>

</Project>
