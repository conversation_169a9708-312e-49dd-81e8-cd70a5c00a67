using System;

namespace AredooPOS.Models
{
    /// <summary>
    /// نموذج بيانات العميل
    /// يمثل عميل المتجر مع جميع بياناته ومعاملاته
    /// </summary>
    public class Customer
    {
        #region الخصائص الأساسية

        /// <summary>
        /// معرف العميل الفريد
        /// </summary>
        public int CustomerID { get; set; }

        /// <summary>
        /// كود العميل
        /// </summary>
        public string CustomerCode { get; set; }

        /// <summary>
        /// الاسم الكامل
        /// </summary>
        public string FullName { get; set; }

        /// <summary>
        /// رقم الهاتف
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// العنوان
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// المدينة
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// رقم الهوية الوطنية
        /// </summary>
        public string NationalID { get; set; }

        /// <summary>
        /// تاريخ الميلاد
        /// </summary>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// نوع العميل (عادي، مميز، جملة)
        /// </summary>
        public string CustomerType { get; set; }

        /// <summary>
        /// حد الائتمان المسموح
        /// </summary>
        public decimal CreditLimit { get; set; }

        /// <summary>
        /// الرصيد الحالي (موجب = دين على العميل، سالب = دين للعميل)
        /// </summary>
        public decimal CurrentBalance { get; set; }

        /// <summary>
        /// نقاط الولاء
        /// </summary>
        public int LoyaltyPoints { get; set; }

        /// <summary>
        /// حالة العميل (نشط/غير نشط)
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// تاريخ إنشاء الحساب
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// معرف منشئ الحساب
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معرف آخر من عدل الحساب
        /// </summary>
        public int? ModifiedBy { get; set; }

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// عرض نوع العميل كنص عربي
        /// </summary>
        public string CustomerTypeText
        {
            get
            {
                return CustomerType switch
                {
                    "Regular" => "عادي",
                    "VIP" => "مميز",
                    "Wholesale" => "جملة",
                    _ => "غير محدد"
                };
            }
        }

        /// <summary>
        /// عرض حالة العميل كنص
        /// </summary>
        public string StatusText => IsActive ? "نشط" : "غير نشط";

        /// <summary>
        /// عرض الرصيد الحالي كنص
        /// </summary>
        public string CurrentBalanceText
        {
            get
            {
                if (CurrentBalance > 0)
                    return $"مدين بـ {CurrentBalance:F2} ر.س";
                else if (CurrentBalance < 0)
                    return $"له رصيد {Math.Abs(CurrentBalance):F2} ر.س";
                else
                    return "لا يوجد رصيد";
            }
        }

        /// <summary>
        /// عرض العمر
        /// </summary>
        public int? Age
        {
            get
            {
                if (DateOfBirth.HasValue)
                {
                    var today = DateTime.Today;
                    var age = today.Year - DateOfBirth.Value.Year;
                    if (DateOfBirth.Value.Date > today.AddYears(-age))
                        age--;
                    return age;
                }
                return null;
            }
        }

        /// <summary>
        /// عرض تاريخ الإنشاء كنص
        /// </summary>
        public string CreatedDateText => CreatedDate.ToString("yyyy/MM/dd");

        /// <summary>
        /// التحقق من وجود دين على العميل
        /// </summary>
        public bool HasDebt => CurrentBalance > 0;

        /// <summary>
        /// التحقق من وجود رصيد للعميل
        /// </summary>
        public bool HasCredit => CurrentBalance < 0;

        /// <summary>
        /// التحقق من تجاوز حد الائتمان
        /// </summary>
        public bool IsOverCreditLimit => CurrentBalance > CreditLimit;

        #endregion

        #region البناء

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public Customer()
        {
            CustomerType = "Regular";
            CreditLimit = 0;
            CurrentBalance = 0;
            LoyaltyPoints = 0;
            IsActive = true;
            CreatedDate = DateTime.Now;
        }

        /// <summary>
        /// منشئ مع البيانات الأساسية
        /// </summary>
        /// <param name="fullName">الاسم الكامل</param>
        /// <param name="phone">رقم الهاتف</param>
        public Customer(string fullName, string phone) : this()
        {
            FullName = fullName;
            Phone = phone;
        }

        /// <summary>
        /// منشئ مع البيانات الكاملة
        /// </summary>
        /// <param name="customerCode">كود العميل</param>
        /// <param name="fullName">الاسم الكامل</param>
        /// <param name="phone">رقم الهاتف</param>
        /// <param name="customerType">نوع العميل</param>
        public Customer(string customerCode, string fullName, string phone, string customerType) : this()
        {
            CustomerCode = customerCode;
            FullName = fullName;
            Phone = phone;
            CustomerType = customerType;
        }

        #endregion

        #region طرق المساعدة

        /// <summary>
        /// التحقق من صحة بيانات العميل
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(FullName);
        }

        /// <summary>
        /// التحقق من صحة رقم الهاتف السعودي
        /// </summary>
        /// <returns>true إذا كان رقم الهاتف صحيح</returns>
        public bool IsValidSaudiPhone()
        {
            if (string.IsNullOrWhiteSpace(Phone))
                return false;

            // إزالة المسافات والرموز
            var cleanPhone = Phone.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");

            // التحقق من الأرقام السعودية
            return cleanPhone.StartsWith("05") && cleanPhone.Length == 10 ||
                   cleanPhone.StartsWith("+9665") && cleanPhone.Length == 13 ||
                   cleanPhone.StartsWith("9665") && cleanPhone.Length == 12;
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        /// <returns>true إذا كان البريد الإلكتروني صحيح</returns>
        public bool IsValidEmail()
        {
            if (string.IsNullOrWhiteSpace(Email))
                return true; // البريد الإلكتروني اختياري

            try
            {
                var addr = new System.Net.Mail.MailAddress(Email);
                return addr.Address == Email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إضافة نقاط ولاء
        /// </summary>
        /// <param name="points">عدد النقاط</param>
        public void AddLoyaltyPoints(int points)
        {
            if (points > 0)
            {
                LoyaltyPoints += points;
            }
        }

        /// <summary>
        /// استخدام نقاط الولاء
        /// </summary>
        /// <param name="points">عدد النقاط المراد استخدامها</param>
        /// <returns>true إذا تم الاستخدام بنجاح</returns>
        public bool UseLoyaltyPoints(int points)
        {
            if (points > 0 && LoyaltyPoints >= points)
            {
                LoyaltyPoints -= points;
                return true;
            }
            return false;
        }

        /// <summary>
        /// تحديث الرصيد
        /// </summary>
        /// <param name="amount">المبلغ (موجب للدين، سالب للدفع)</param>
        public void UpdateBalance(decimal amount)
        {
            CurrentBalance += amount;
        }

        /// <summary>
        /// تصفير الرصيد
        /// </summary>
        public void ClearBalance()
        {
            CurrentBalance = 0;
        }

        /// <summary>
        /// الحصول على كود عميل جديد
        /// </summary>
        /// <param name="lastCustomerNumber">آخر رقم عميل</param>
        /// <returns>كود العميل الجديد</returns>
        public static string GenerateCustomerCode(int lastCustomerNumber)
        {
            return $"CUST{(lastCustomerNumber + 1):D6}";
        }

        #endregion

        #region تجاوز الطرق الأساسية

        /// <summary>
        /// تمثيل نصي للعميل
        /// </summary>
        /// <returns>النص الممثل للعميل</returns>
        public override string ToString()
        {
            return $"{FullName} - {Phone}";
        }

        /// <summary>
        /// مقارنة العملاء
        /// </summary>
        /// <param name="obj">الكائن المراد مقارنته</param>
        /// <returns>true إذا كان نفس العميل</returns>
        public override bool Equals(object obj)
        {
            if (obj is Customer other)
            {
                return CustomerID == other.CustomerID;
            }
            return false;
        }

        /// <summary>
        /// الحصول على Hash Code
        /// </summary>
        /// <returns>Hash Code للعميل</returns>
        public override int GetHashCode()
        {
            return CustomerID.GetHashCode();
        }

        #endregion
    }
}
