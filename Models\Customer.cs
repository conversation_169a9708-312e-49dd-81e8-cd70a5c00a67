using System;
using System.ComponentModel.DataAnnotations;

namespace AridooPOS.Models
{
    /// <summary>
    /// نموذج العميل
    /// </summary>
    public class Customer
    {
        public int CustomerID { get; set; }
        
        [Required(ErrorMessage = "كود العميل مطلوب")]
        [StringLength(20, ErrorMessage = "كود العميل لا يجب أن يتجاوز 20 حرف")]
        public string CustomerCode { get; set; }
        
        [Required(ErrorMessage = "اسم العميل مطلوب")]
        [StringLength(100, ErrorMessage = "اسم العميل لا يجب أن يتجاوز 100 حرف")]
        public string CustomerName { get; set; }
        
        [StringLength(20, ErrorMessage = "رقم الهاتف لا يجب أن يتجاوز 20 حرف")]
        public string Phone { get; set; }
        
        [StringLength(200, ErrorMessage = "العنوان لا يجب أن يتجاوز 200 حرف")]
        public string Address { get; set; }
        
        [StringLength(100, ErrorMessage = "البريد الإلكتروني لا يجب أن يتجاوز 100 حرف")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        public string Email { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "حد الائتمان يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal CreditLimit { get; set; }
        
        public decimal CurrentBalance { get; set; }
        
        public bool IsActive { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public DateTime ModifiedDate { get; set; }
        
        public Customer()
        {
            IsActive = true;
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;
            CreditLimit = 0;
            CurrentBalance = 0;
        }
        
        /// <summary>
        /// التحقق من إمكانية منح ائتمان إضافي
        /// </summary>
        /// <param name="amount">المبلغ المطلوب</param>
        /// <returns>true إذا كان بإمكان منح الائتمان</returns>
        public bool CanGrantCredit(decimal amount)
        {
            return IsActive && (CurrentBalance + amount) <= CreditLimit;
        }
        
        /// <summary>
        /// إضافة مبلغ إلى رصيد العميل
        /// </summary>
        /// <param name="amount">المبلغ</param>
        public void AddToBalance(decimal amount)
        {
            CurrentBalance += amount;
            ModifiedDate = DateTime.Now;
        }
        
        /// <summary>
        /// خصم مبلغ من رصيد العميل
        /// </summary>
        /// <param name="amount">المبلغ</param>
        public void DeductFromBalance(decimal amount)
        {
            CurrentBalance -= amount;
            ModifiedDate = DateTime.Now;
        }
        
        /// <summary>
        /// الحصول على الرصيد المتاح
        /// </summary>
        /// <returns>الرصيد المتاح</returns>
        public decimal GetAvailableCredit()
        {
            return CreditLimit - CurrentBalance;
        }
        
        /// <summary>
        /// التحقق من صحة بيانات العميل
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(CustomerCode) &&
                   !string.IsNullOrEmpty(CustomerName) &&
                   CreditLimit >= 0;
        }
    }
}
