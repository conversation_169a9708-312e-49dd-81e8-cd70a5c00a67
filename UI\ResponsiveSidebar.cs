using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// الشريط الجانبي المتجاوب مع أيقونات قابلة للتكيف
    /// </summary>
    public class ResponsiveSidebar : UserControl
    {
        #region المتغيرات

        private bool _isCollapsed = false;
        private List<ResponsiveSidebarItem> _menuItems;
        private ResponsiveSidebarItem _selectedItem;
        private Panel _headerPanel;
        private Panel _menuPanel;
        private Panel _footerPanel;
        private Button _collapseButton;
        private Timer _animationTimer;
        private int _targetWidth;
        private int _currentWidth;

        #endregion

        #region الخصائص

        public bool IsCollapsed
        {
            get => _isCollapsed;
            set
            {
                _isCollapsed = value;
                AnimateToTargetWidth();
            }
        }

        public ResponsiveSidebarItem SelectedItem
        {
            get => _selectedItem;
            set
            {
                if (_selectedItem != value)
                {
                    _selectedItem = value;
                    UpdateSelection();
                    ItemSelected?.Invoke(this, new ResponsiveSidebarItemEventArgs(value));
                }
            }
        }

        #endregion

        #region الأحداث

        public event EventHandler<ResponsiveSidebarItemEventArgs> ItemSelected;

        #endregion

        #region البناء والتهيئة

        public ResponsiveSidebar()
        {
            InitializeMenuItems();
            InitializeComponent();
            SetupResponsiveDesign();
            SetupAnimation();
        }

        private void InitializeMenuItems()
        {
            _menuItems = new List<ResponsiveSidebarItem>
            {
                new ResponsiveSidebarItem("dashboard", "لوحة المعلومات", ResponsiveDesignSystem.Icons.Dashboard, ResponsiveDesignSystem.Colors.Primary),
                new ResponsiveSidebarItem("sales", "المبيعات", ResponsiveDesignSystem.Icons.Sales, ResponsiveDesignSystem.Colors.Success),
                new ResponsiveSidebarItem("invoices", "الفواتير", ResponsiveDesignSystem.Icons.Invoices, ResponsiveDesignSystem.Colors.Info),
                new ResponsiveSidebarItem("customers", "العملاء", ResponsiveDesignSystem.Icons.Customers, ResponsiveDesignSystem.Colors.Secondary),
                new ResponsiveSidebarItem("products", "المنتجات", ResponsiveDesignSystem.Icons.Products, Color.FromArgb(255, 140, 0)),
                new ResponsiveSidebarItem("installments", "الأقساط", ResponsiveDesignSystem.Icons.Installments, Color.FromArgb(138, 43, 226)),
                new ResponsiveSidebarItem("debts", "الديون", ResponsiveDesignSystem.Icons.Debts, ResponsiveDesignSystem.Colors.Warning),
                new ResponsiveSidebarItem("reports", "التقارير", ResponsiveDesignSystem.Icons.Reports, Color.FromArgb(220, 20, 60)),
                new ResponsiveSidebarItem("settings", "الإعدادات", ResponsiveDesignSystem.Icons.Settings, ResponsiveDesignSystem.Colors.TextSecondary)
            };

            _selectedItem = _menuItems.FirstOrDefault();
        }

        private void InitializeComponent()
        {
            // إعدادات الشريط الجانبي المتجاوبة
            _currentWidth = ResponsiveLayoutSystem.ResponsiveDimensions.GetSidebarWidth();
            _targetWidth = _currentWidth;
            
            Size = new Size(_currentWidth, 600);
            BackColor = ResponsiveDesignSystem.Colors.Surface;
            Dock = DockStyle.Left;
            Font = ResponsiveDesignSystem.Fonts.GetBody();

            CreateResponsivePanels();
            CreateResponsiveControls();
            CreateMenuItems();
        }

        private void CreateResponsivePanels()
        {
            var headerHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetTopBarHeight();
            var footerHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight() + 
                              ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();

            // لوحة الرأس
            _headerPanel = new Panel
            {
                Size = new Size(Width, headerHeight),
                Location = new Point(0, 0),
                BackColor = Color.Transparent,
                Dock = DockStyle.Top
            };

            // لوحة القائمة
            _menuPanel = new Panel
            {
                BackColor = Color.Transparent,
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetSmallSpacing())
            };

            // لوحة التذييل
            _footerPanel = new Panel
            {
                Size = new Size(Width, footerHeight),
                BackColor = Color.Transparent,
                Dock = DockStyle.Bottom
            };

            Controls.AddRange(new Control[] { _menuPanel, _headerPanel, _footerPanel });
        }

        private void CreateResponsiveControls()
        {
            var buttonSize = ResponsiveLayoutSystem.ResponsiveDimensions.GetLargeButtonHeight();
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();

            // زر الطي/التوسيع
            _collapseButton = new Button
            {
                Text = "☰",
                Font = ResponsiveDesignSystem.Fonts.GetHighDpiFont(20),
                Size = new Size(buttonSize, buttonSize),
                Location = new Point(Width - buttonSize - spacing, spacing),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent,
                ForeColor = ResponsiveDesignSystem.Colors.TextSecondary,
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };

            _collapseButton.FlatAppearance.BorderSize = 0;
            _collapseButton.FlatAppearance.MouseOverBackColor = ResponsiveDesignSystem.Colors.SurfaceHover;
            _collapseButton.FlatAppearance.MouseDownBackColor = ResponsiveDesignSystem.Colors.SurfacePressed;
            _collapseButton.Click += OnCollapseButtonClick;

            _headerPanel.Controls.Add(_collapseButton);
        }

        private void CreateMenuItems()
        {
            _menuPanel.Controls.Clear();

            var itemHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetLargeButtonHeight() + 
                           ResponsiveLayoutSystem.ResponsiveSpacing.GetSmallSpacing();
            var yPosition = ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();

            foreach (var item in _menuItems)
            {
                var menuButton = CreateResponsiveMenuButton(item, yPosition, itemHeight);
                _menuPanel.Controls.Add(menuButton);
                yPosition += itemHeight;
            }
        }

        private Control CreateResponsiveMenuButton(ResponsiveSidebarItem item, int yPosition, int height)
        {
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetSmallSpacing();
            var button = new Panel
            {
                Size = new Size(Width - (spacing * 2), height - spacing),
                Location = new Point(spacing, yPosition),
                BackColor = Color.Transparent,
                Cursor = Cursors.Hand,
                Tag = item,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            // إضافة الأحداث
            button.Click += (s, e) => OnMenuItemClick(item);
            button.MouseEnter += (s, e) => OnMenuItemHover(button, true);
            button.MouseLeave += (s, e) => OnMenuItemHover(button, false);
            button.Paint += (s, e) => OnMenuItemPaint(button, e, item);

            return button;
        }

        private void SetupResponsiveDesign()
        {
            RightToLeft = RightToLeft.Yes;
            Paint += OnPaint;
            Resize += OnResize;
        }

        private void SetupAnimation()
        {
            _animationTimer = new Timer
            {
                Interval = 16 // 60 FPS
            };
            _animationTimer.Tick += OnAnimationTick;
        }

        #endregion

        #region الرسم والتخطيط

        private void OnMenuItemPaint(Panel button, PaintEventArgs e, ResponsiveSidebarItem item)
        {
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            var bounds = button.ClientRectangle;
            var isSelected = item == _selectedItem;
            var cornerRadius = ResponsiveLayoutSystem.ResponsiveSpacing.GetSmallSpacing();

            // رسم الخلفية للعنصر المحدد أو عند التمرير
            if (isSelected || button.BackColor != Color.Transparent)
            {
                var backgroundColor = isSelected ? 
                    Color.FromArgb(30, item.Color) : 
                    button.BackColor;

                using (var backgroundBrush = new SolidBrush(backgroundColor))
                using (var backgroundPath = ResponsiveDesignSystem.CreateRoundedRectangle(bounds, cornerRadius))
                {
                    g.FillPath(backgroundBrush, backgroundPath);
                }
            }

            // رسم خط التحديد للعنصر المحدد
            if (isSelected)
            {
                var lineWidth = ResponsiveLayoutSystem.ResponsiveSpacing.GetSmallSpacing() / 2;
                using (var selectedPen = new Pen(item.Color, lineWidth))
                {
                    g.DrawLine(selectedPen, 
                        bounds.Width - lineWidth, 
                        ResponsiveLayoutSystem.ResponsiveSpacing.GetSmallSpacing(),
                        bounds.Width - lineWidth, 
                        bounds.Height - ResponsiveLayoutSystem.ResponsiveSpacing.GetSmallSpacing());
                }
            }

            // رسم الأيقونة
            var iconSize = ResponsiveDesignSystem.Icons.GetLargeIconSize();
            var iconX = _isCollapsed ? 
                (bounds.Width - iconSize) / 2 : 
                bounds.Width - iconSize - ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();
            var iconY = (bounds.Height - iconSize) / 2;
            var iconRect = new Rectangle(iconX, iconY, iconSize, iconSize);

            var iconColor = isSelected ? item.Color : ResponsiveDesignSystem.Colors.TextSecondary;
            var iconFont = ResponsiveDesignSystem.Fonts.GetHighDpiFont(iconSize * 0.8f);
            
            using (var iconBrush = new SolidBrush(iconColor))
            {
                var iconFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };
                g.DrawString(item.Icon, iconFont, iconBrush, iconRect, iconFormat);
            }

            // رسم النص (إذا لم يكن مطوياً)
            if (!_isCollapsed)
            {
                var textColor = isSelected ? item.Color : ResponsiveDesignSystem.Colors.TextPrimary;
                var textFont = isSelected ? 
                    ResponsiveDesignSystem.Fonts.GetBody() : 
                    ResponsiveDesignSystem.Fonts.GetBody();

                var textRect = new Rectangle(
                    ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing(),
                    0,
                    iconX - ResponsiveLayoutSystem.ResponsiveSpacing.GetLargeSpacing(),
                    bounds.Height
                );

                using (var textBrush = new SolidBrush(textColor))
                {
                    var textFormat = new StringFormat
                    {
                        Alignment = StringAlignment.Far,
                        LineAlignment = StringAlignment.Center,
                        FormatFlags = StringFormatFlags.DirectionRightToLeft
                    };
                    g.DrawString(item.Title, textFont, textBrush, textRect, textFormat);
                }
            }
        }

        private void OnPaint(object sender, PaintEventArgs e)
        {
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // رسم الخلفية
            using (var backgroundBrush = new SolidBrush(ResponsiveDesignSystem.Colors.Surface))
            {
                g.FillRectangle(backgroundBrush, ClientRectangle);
            }

            // رسم الحد الأيمن
            using (var borderPen = new Pen(ResponsiveDesignSystem.Colors.Border, 1))
            {
                g.DrawLine(borderPen, Width - 1, 0, Width - 1, Height);
            }

            // رسم عنوان الشريط الجانبي (إذا لم يكن مطوياً)
            if (!_isCollapsed)
            {
                var titleRect = new Rectangle(
                    ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing(),
                    ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing(),
                    Width - ResponsiveLayoutSystem.ResponsiveDimensions.GetLargeButtonHeight() - 
                    ResponsiveLayoutSystem.ResponsiveSpacing.GetLargeSpacing(),
                    ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight()
                );

                using (var titleBrush = new SolidBrush(ResponsiveDesignSystem.Colors.TextPrimary))
                {
                    var titleFormat = new StringFormat
                    {
                        Alignment = StringAlignment.Far,
                        LineAlignment = StringAlignment.Center,
                        FormatFlags = StringFormatFlags.DirectionRightToLeft
                    };
                    g.DrawString("القائمة الرئيسية", ResponsiveDesignSystem.Fonts.GetHeading5(), 
                        titleBrush, titleRect, titleFormat);
                }
            }
        }

        #endregion

        #region الأحداث والتفاعل

        private void OnCollapseButtonClick(object sender, EventArgs e)
        {
            IsCollapsed = !IsCollapsed;
        }

        private void OnMenuItemClick(ResponsiveSidebarItem item)
        {
            SelectedItem = item;
        }

        private void OnMenuItemHover(Panel button, bool isHovered)
        {
            var item = (ResponsiveSidebarItem)button.Tag;
            if (item != _selectedItem)
            {
                button.BackColor = isHovered ?
                    ResponsiveDesignSystem.Colors.SurfaceHover :
                    Color.Transparent;
            }
        }

        private void OnResize(object sender, EventArgs e)
        {
            UpdateLayout();
        }

        #endregion

        #region الرسوم المتحركة

        private void AnimateToTargetWidth()
        {
            _targetWidth = _isCollapsed ?
                ResponsiveLayoutSystem.ResponsiveDimensions.GetCollapsedSidebarWidth() :
                ResponsiveLayoutSystem.ResponsiveDimensions.GetSidebarWidth();

            _collapseButton.Text = _isCollapsed ? "☰" : "✖";
            _animationTimer.Start();
        }

        private void OnAnimationTick(object sender, EventArgs e)
        {
            var step = Math.Abs(_targetWidth - _currentWidth) / 10;
            step = Math.Max(step, 5); // حد أدنى للخطوة

            if (_currentWidth < _targetWidth)
            {
                _currentWidth = Math.Min(_currentWidth + step, _targetWidth);
            }
            else if (_currentWidth > _targetWidth)
            {
                _currentWidth = Math.Max(_currentWidth - step, _targetWidth);
            }

            Width = _currentWidth;

            if (_currentWidth == _targetWidth)
            {
                _animationTimer.Stop();
                UpdateLayout();
            }
        }

        #endregion

        #region التحديثات

        private void UpdateLayout()
        {
            // تحديث موقع زر الطي
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();
            var buttonSize = ResponsiveLayoutSystem.ResponsiveDimensions.GetLargeButtonHeight();
            _collapseButton.Location = new Point(Width - buttonSize - spacing, spacing);

            // إعادة إنشاء عناصر القائمة
            CreateMenuItems();
            Invalidate();
        }

        private void UpdateSelection()
        {
            // إعادة رسم جميع العناصر
            foreach (Control control in _menuPanel.Controls)
            {
                control.Invalidate();
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _animationTimer?.Stop();
                _animationTimer?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }

    #region الكلاسات المساعدة

    public class ResponsiveSidebarItem
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string Icon { get; set; }
        public Color Color { get; set; }

        public ResponsiveSidebarItem(string id, string title, string icon, Color color)
        {
            Id = id;
            Title = title;
            Icon = icon;
            Color = color;
        }
    }

    public class ResponsiveSidebarItemEventArgs : EventArgs
    {
        public ResponsiveSidebarItem Item { get; }

        public ResponsiveSidebarItemEventArgs(ResponsiveSidebarItem item)
        {
            Item = item;
        }
    }

    #endregion
}
