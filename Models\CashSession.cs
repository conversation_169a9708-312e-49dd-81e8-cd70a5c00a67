using System;
using System.ComponentModel.DataAnnotations;

namespace AredooPOS.Models
{
    /// <summary>
    /// نموذج جلسة الصندوق
    /// يمثل جلسة عمل واحدة للصندوق
    /// </summary>
    public class CashSession
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم الجلسة
        /// </summary>
        public int SessionID { get; set; }

        /// <summary>
        /// رقم الصندوق
        /// </summary>
        [Required(ErrorMessage = "رقم الصندوق مطلوب")]
        public int CashRegisterID { get; set; }

        /// <summary>
        /// رقم المستخدم
        /// </summary>
        [Required(ErrorMessage = "رقم المستخدم مطلوب")]
        public int UserID { get; set; }

        /// <summary>
        /// تاريخ الجلسة
        /// </summary>
        [Required(ErrorMessage = "تاريخ الجلسة مطلوب")]
        public DateTime SessionDate { get; set; }

        /// <summary>
        /// وقت الفتح
        /// </summary>
        [Required(ErrorMessage = "وقت الفتح مطلوب")]
        public DateTime OpenTime { get; set; }

        /// <summary>
        /// وقت الإغلاق
        /// </summary>
        public DateTime? CloseTime { get; set; }

        /// <summary>
        /// حالة الجلسة
        /// </summary>
        [Required(ErrorMessage = "حالة الجلسة مطلوبة")]
        [StringLength(20)]
        public string Status { get; set; } = CashSessionStatus.Open;

        /// <summary>
        /// الرصيد الافتتاحي
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الرصيد الافتتاحي يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal OpeningBalance { get; set; } = 0;

        /// <summary>
        /// الرصيد الختامي الفعلي
        /// </summary>
        public decimal? ClosingBalance { get; set; }

        /// <summary>
        /// الرصيد الختامي المتوقع
        /// </summary>
        public decimal? ExpectedClosingBalance { get; set; }

        #endregion

        #region إجماليات المبيعات

        /// <summary>
        /// المبيعات النقدية
        /// </summary>
        public decimal CashSales { get; set; } = 0;

        /// <summary>
        /// المبيعات بالبطاقة
        /// </summary>
        public decimal CardSales { get; set; } = 0;

        /// <summary>
        /// المبيعات بالتحويل
        /// </summary>
        public decimal TransferSales { get; set; } = 0;

        /// <summary>
        /// إجمالي المبيعات
        /// </summary>
        public decimal TotalSales { get; set; } = 0;

        #endregion

        #region إجماليات المعاملات النقدية

        /// <summary>
        /// المصاريف النقدية
        /// </summary>
        public decimal CashExpenses { get; set; } = 0;

        /// <summary>
        /// السحوبات النقدية
        /// </summary>
        public decimal CashWithdrawals { get; set; } = 0;

        /// <summary>
        /// الإيداعات النقدية
        /// </summary>
        public decimal CashDeposits { get; set; } = 0;

        /// <summary>
        /// إجمالي عدد المعاملات
        /// </summary>
        public int TotalTransactions { get; set; } = 0;

        #endregion

        #region معلومات الإغلاق

        /// <summary>
        /// الفرق في الرصيد
        /// </summary>
        public decimal? Variance { get; set; }

        /// <summary>
        /// سبب الفرق
        /// </summary>
        [StringLength(255)]
        public string VarianceReason { get; set; }

        /// <summary>
        /// ملاحظات
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; }

        /// <summary>
        /// من أغلق الجلسة
        /// </summary>
        [StringLength(50)]
        public string ClosedBy { get; set; }

        /// <summary>
        /// موافقة المدير
        /// </summary>
        public bool ManagerApproval { get; set; } = false;

        /// <summary>
        /// من وافق
        /// </summary>
        [StringLength(50)]
        public string ApprovedBy { get; set; }

        /// <summary>
        /// تاريخ الموافقة
        /// </summary>
        public DateTime? ApprovalDate { get; set; }

        #endregion

        #region معلومات النظام

        /// <summary>
        /// من أنشأ السجل
        /// </summary>
        [Required]
        [StringLength(50)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// من عدل السجل
        /// </summary>
        [StringLength(50)]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// تاريخ التعديل
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// هل الجلسة مفتوحة
        /// </summary>
        public bool IsOpen => Status == CashSessionStatus.Open;

        /// <summary>
        /// هل الجلسة مغلقة
        /// </summary>
        public bool IsClosed => Status == CashSessionStatus.Closed;

        /// <summary>
        /// هل الجلسة معلقة
        /// </summary>
        public bool IsSuspended => Status == CashSessionStatus.Suspended;

        /// <summary>
        /// هل الجلسة ملغاة
        /// </summary>
        public bool IsCancelled => Status == CashSessionStatus.Cancelled;

        /// <summary>
        /// مدة الجلسة بالدقائق
        /// </summary>
        public int? DurationMinutes
        {
            get
            {
                var endTime = CloseTime ?? DateTime.Now;
                return (int)(endTime - OpenTime).TotalMinutes;
            }
        }

        /// <summary>
        /// مدة الجلسة كنص
        /// </summary>
        public string DurationText
        {
            get
            {
                if (!DurationMinutes.HasValue)
                    return "غير محدد";

                var duration = TimeSpan.FromMinutes(DurationMinutes.Value);
                if (duration.TotalDays >= 1)
                    return $"{(int)duration.TotalDays} يوم {duration.Hours} ساعة {duration.Minutes} دقيقة";
                else if (duration.TotalHours >= 1)
                    return $"{duration.Hours} ساعة {duration.Minutes} دقيقة";
                else
                    return $"{duration.Minutes} دقيقة";
            }
        }

        /// <summary>
        /// هل يوجد فرق في الرصيد
        /// </summary>
        public bool HasVariance => Variance.HasValue && Variance.Value != 0;

        /// <summary>
        /// نوع الفرق
        /// </summary>
        public string VarianceType
        {
            get
            {
                if (!HasVariance)
                    return "لا يوجد فرق";
                return Variance.Value > 0 ? "زيادة" : "نقص";
            }
        }

        /// <summary>
        /// هل الفرق كبير
        /// </summary>
        public bool IsSignificantVariance => HasVariance && Math.Abs(Variance.Value) > 10;

        /// <summary>
        /// صافي الحركة النقدية
        /// </summary>
        public decimal NetCashFlow => CashSales - CashExpenses - CashWithdrawals + CashDeposits;

        /// <summary>
        /// متوسط قيمة المعاملة
        /// </summary>
        public decimal AverageTransactionValue
        {
            get
            {
                if (TotalTransactions == 0)
                    return 0;
                return Math.Round(TotalSales / TotalTransactions, 2);
            }
        }

        /// <summary>
        /// هل تحتاج موافقة المدير
        /// </summary>
        public bool RequiresManagerApproval => IsSignificantVariance || TotalSales > 10000;

        /// <summary>
        /// هل يمكن إغلاق الجلسة
        /// </summary>
        public bool CanClose => IsOpen && ClosingBalance.HasValue;

        #endregion

        #region الدوال المساعدة

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public CashSession()
        {
            SessionDate = DateTime.Today;
            OpenTime = DateTime.Now;
            Status = CashSessionStatus.Open;
        }

        /// <summary>
        /// منشئ مع المعاملات الأساسية
        /// </summary>
        /// <param name="cashRegisterID">رقم الصندوق</param>
        /// <param name="userID">رقم المستخدم</param>
        /// <param name="openingBalance">الرصيد الافتتاحي</param>
        /// <param name="createdBy">من أنشأ الجلسة</param>
        public CashSession(int cashRegisterID, int userID, decimal openingBalance, string createdBy)
        {
            CashRegisterID = cashRegisterID;
            UserID = userID;
            OpeningBalance = openingBalance;
            ExpectedClosingBalance = openingBalance;
            SessionDate = DateTime.Today;
            OpenTime = DateTime.Now;
            Status = CashSessionStatus.Open;
            CreatedBy = createdBy;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            if (CashRegisterID <= 0)
                return false;

            if (UserID <= 0)
                return false;

            if (OpeningBalance < 0)
                return false;

            if (string.IsNullOrWhiteSpace(Status))
                return false;

            if (IsClosed && !CloseTime.HasValue)
                return false;

            if (IsClosed && !ClosingBalance.HasValue)
                return false;

            return true;
        }

        /// <summary>
        /// فتح الجلسة
        /// </summary>
        /// <param name="openingBalance">الرصيد الافتتاحي</param>
        /// <param name="openedBy">من فتح الجلسة</param>
        public void Open(decimal openingBalance, string openedBy)
        {
            OpeningBalance = openingBalance;
            ExpectedClosingBalance = openingBalance;
            OpenTime = DateTime.Now;
            Status = CashSessionStatus.Open;
            CreatedBy = openedBy;
        }

        /// <summary>
        /// إغلاق الجلسة
        /// </summary>
        /// <param name="closingBalance">الرصيد الختامي</param>
        /// <param name="closedBy">من أغلق الجلسة</param>
        /// <param name="notes">ملاحظات</param>
        public void Close(decimal closingBalance, string closedBy, string notes = null)
        {
            ClosingBalance = closingBalance;
            CloseTime = DateTime.Now;
            Status = CashSessionStatus.Closed;
            ClosedBy = closedBy;
            Notes = notes;
            
            // حساب الفرق
            if (ExpectedClosingBalance.HasValue)
            {
                Variance = closingBalance - ExpectedClosingBalance.Value;
            }
        }

        /// <summary>
        /// تعليق الجلسة
        /// </summary>
        /// <param name="reason">سبب التعليق</param>
        /// <param name="suspendedBy">من علق الجلسة</param>
        public void Suspend(string reason, string suspendedBy)
        {
            Status = CashSessionStatus.Suspended;
            Notes = reason;
            ModifiedBy = suspendedBy;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// إلغاء الجلسة
        /// </summary>
        /// <param name="reason">سبب الإلغاء</param>
        /// <param name="cancelledBy">من ألغى الجلسة</param>
        public void Cancel(string reason, string cancelledBy)
        {
            Status = CashSessionStatus.Cancelled;
            CloseTime = DateTime.Now;
            Notes = reason;
            ClosedBy = cancelledBy;
        }

        /// <summary>
        /// موافقة المدير
        /// </summary>
        /// <param name="approvedBy">من وافق</param>
        public void ApproveByManager(string approvedBy)
        {
            ManagerApproval = true;
            ApprovedBy = approvedBy;
            ApprovalDate = DateTime.Now;
        }

        /// <summary>
        /// تحديث الإجماليات
        /// </summary>
        /// <param name="cashSales">المبيعات النقدية</param>
        /// <param name="cardSales">المبيعات بالبطاقة</param>
        /// <param name="transferSales">المبيعات بالتحويل</param>
        /// <param name="expenses">المصاريف</param>
        /// <param name="withdrawals">السحوبات</param>
        /// <param name="deposits">الإيداعات</param>
        /// <param name="transactionCount">عدد المعاملات</param>
        public void UpdateTotals(decimal cashSales, decimal cardSales, decimal transferSales,
            decimal expenses, decimal withdrawals, decimal deposits, int transactionCount)
        {
            CashSales = cashSales;
            CardSales = cardSales;
            TransferSales = transferSales;
            TotalSales = cashSales + cardSales + transferSales;
            CashExpenses = expenses;
            CashWithdrawals = withdrawals;
            CashDeposits = deposits;
            TotalTransactions = transactionCount;
            
            // تحديث الرصيد المتوقع
            ExpectedClosingBalance = OpeningBalance + NetCashFlow;
        }

        /// <summary>
        /// تحويل إلى نص
        /// </summary>
        /// <returns>تمثيل نصي للكائن</returns>
        public override string ToString()
        {
            return $"جلسة {SessionID} - {SessionDate:yyyy-MM-dd} ({Status})";
        }

        /// <summary>
        /// مقارنة مع كائن آخر
        /// </summary>
        /// <param name="obj">الكائن المراد المقارنة معه</param>
        /// <returns>true إذا كانا متساويين</returns>
        public override bool Equals(object obj)
        {
            if (obj is CashSession other)
            {
                return SessionID == other.SessionID;
            }
            return false;
        }

        /// <summary>
        /// الحصول على رمز التجمع
        /// </summary>
        /// <returns>رمز التجمع</returns>
        public override int GetHashCode()
        {
            return SessionID.GetHashCode();
        }

        #endregion
    }

    #region التعدادات المساعدة

    /// <summary>
    /// حالات جلسة الصندوق
    /// </summary>
    public static class CashSessionStatus
    {
        public const string Open = "Open";
        public const string Closed = "Closed";
        public const string Suspended = "Suspended";
        public const string Cancelled = "Cancelled";
    }

    #endregion
}
