# 🏪 أريدو الكاشير المتجاوب - نظام نقاط البيع المتكيف

## 🎯 نظرة عامة

**أريدو الكاشير المتجاوب** هو نظام نقاط بيع عربي متطور ومتجاوب بالكامل، مصمم خصيصاً للتجار العرب. يتكيف النظام تلقائياً مع جميع أحجام الشاشات من 1366x768 إلى 4K Ultra HD، مع دعم كامل للغة العربية وتصميم حديث مستوحى من Microsoft Fluent Design و Material Design.

## ✨ الميزات الرئيسية

### 🎨 **التصميم المتجاوب المتقدم**
- ✅ **تكيف تلقائي** مع جميع أحجام الشاشات (1366x768 → 4K)
- ✅ **نظام شبكة متجاوب** مع 12 عمود قابل للتخصيص
- ✅ **تكبير ذكي** للخطوط والعناصر حسب دقة الشاشة
- ✅ **تخطيط مرن** يعيد ترتيب العناصر حسب حجم الشاشة
- ✅ **دعم DPI متقدم** للشاشات عالية الدقة

### 🌐 **الدعم العربي الكامل**
- ✅ **RTL Layout** - تخطيط من اليمين لليسار
- ✅ **خطوط عربية حديثة** - Segoe UI مع تحسينات للعربية
- ✅ **تنسيق التواريخ والأرقام** العربي
- ✅ **واجهة مستخدم عربية** بالكامل
- ✅ **دعم الثقافة السعودية** (ar-SA)

### 💻 **التوافق الشامل**
- ✅ **Windows 7+** - متوافق مع جميع إصدارات Windows
- ✅ **.NET Framework 4.7.2** - استقرار وأداء عالي
- ✅ **دقة شاشة متعددة** - من HD إلى 4K
- ✅ **شاشات متعددة** - دعم إعداد الشاشات المتعددة
- ✅ **شاشات لمسية** - أزرار كبيرة ومناسبة للمس

## 🖥️ **أحجام الشاشات المدعومة**

| نوع الشاشة | الدقة | معامل التكبير | الاستخدام |
|------------|-------|--------------|-----------|
| 📱 **صغيرة** | 1366x768 - 1440x900 | 1.0x | أجهزة الكاشير الصغيرة |
| 💻 **متوسطة** | 1600x900 - 1920x1080 | 1.25x | أجهزة سطح المكتب |
| 🖥️ **كبيرة** | 2560x1440 | 1.5x | شاشات QHD |
| 📺 **عالية الدقة** | 3840x2160 (4K) | 2.0x | شاشات 4K Ultra HD |

## 🎛️ **المكونات الرئيسية**

### 📊 **الشريط العلوي المتجاوب**
- **معلومات المستخدم** - اسم ودور المستخدم
- **معلومات المتجر** - اسم المتجر والشعار
- **ساعة رقمية** - تحديث مباشر للوقت
- **حالة الاتصال** - مؤشر متصل/غير متصل
- **إشعارات ديناميكية** - تنبيهات المخزون والأقساط والديون

### 📋 **الشريط الجانبي التفاعلي**
- **9 وحدات رئيسية** مع أيقونات ملونة:
  - 📊 لوحة المعلومات
  - 💰 المبيعات
  - 📄 الفواتير
  - 👥 العملاء
  - 📦 المنتجات
  - 📅 الأقساط
  - 💳 الديون
  - 📈 التقارير
  - ⚙️ الإعدادات

### 📈 **لوحة المعلومات الذكية**
- **4 بطاقات إحصائيات** متجاوبة
- **رسوم بيانية تفاعلية**
- **قائمة تنبيهات ذكية**
- **أزرار إجراءات سريعة**

### 💰 **نموذج الفاتورة المتقدم**
- **معلومات العميل** - اختيار وإضافة العملاء
- **جدول المنتجات المتجاوب** - إضافة وتعديل المنتجات
- **حقل الباركود** - إدخال سريع بالمسح
- **حساب الإجماليات** - تلقائي مع الضريبة والخصم
- **طرق الدفع المتعددة** - نقدي وبطاقة وأقساط

### 👥 **إدارة العملاء المتجاوبة**
- **بحث متقدم** - بالاسم والهاتف والبريد
- **جدول عملاء متكيف** - يتكيف مع حجم الشاشة
- **إحصائيات العملاء** - إجمالي العملاء والمبيعات
- **إجراءات العملاء** - إضافة وتعديل وحذف

## 🚀 **التشغيل والاستخدام**

### 📋 **متطلبات النظام**
- **نظام التشغيل**: Windows 7 أو أحدث
- **المعالج**: Intel/AMD متوافق مع x86/x64
- **الذاكرة**: 2 GB RAM (4 GB مستحسن)
- **المساحة**: 500 MB مساحة فارغة
- **الشاشة**: 1366x768 كحد أدنى
- **.NET Framework**: 4.7.2 أو أحدث

### ⚡ **التشغيل السريع**
1. **تحميل** الملف التنفيذي `ResponsiveAredooCashier.exe`
2. **تشغيل** التطبيق بالنقر المزدوج
3. **مشاهدة** شاشة البداية المتجاوبة
4. **استكشاف** الواجهات المختلفة
5. **الاستمتاع** بالتجربة المتجاوبة!

### 🎮 **الاستخدام**
- **التنقل**: استخدم الشريط الجانبي للتنقل بين الوحدات
- **الإشعارات**: انقر على الإشعارات في الشريط العلوي
- **الفواتير**: ابدأ بإنشاء فاتورة جديدة من لوحة المعلومات
- **العملاء**: أضف وأدر العملاء من وحدة العملاء
- **التكيف**: غير حجم النافذة لمشاهدة التكيف التلقائي

## 🔧 **التطوير والتخصيص**

### 🛠️ **بناء المشروع**
```bash
# بناء المشروع
dotnet build ResponsiveAredooCashier.csproj

# تشغيل التطبيق
dotnet run --project ResponsiveAredooCashier.csproj
```

### 🎨 **تخصيص التصميم**
- **الألوان**: عدل `ResponsiveDesignSystem.Colors`
- **الخطوط**: عدل `ResponsiveDesignSystem.Fonts`
- **المسافات**: عدل `ResponsiveLayoutSystem.ResponsiveSpacing`
- **الأبعاد**: عدل `ResponsiveLayoutSystem.ResponsiveDimensions`

## 🏗️ **البنية التقنية**

### 📁 **هيكل المشروع**
```
📦 أريدو الكاشير المتجاوب
├── 🚀 ResponsiveAredooCashier.exe
├── 💻 ResponsiveProgram.cs
├── 🖥️ ResponsiveAredooCashierApp.cs
├── 📁 UI/
│   ├── 🎨 ResponsiveLayoutSystem.cs
│   ├── 🎨 ResponsiveDesignSystem.cs
│   ├── 📊 ResponsiveTopBar.cs
│   ├── 📋 ResponsiveSidebar.cs
│   ├── 📈 ResponsiveDashboard.cs
│   ├── 💰 ResponsiveInvoiceForm.cs
│   └── 👥 ResponsiveCustomersView.cs
└── 📁 Assets/
```

### 🔧 **التقنيات المستخدمة**
- **C# WinForms** - واجهة المستخدم الرئيسية
- **.NET Framework 4.7.2** - منصة التطوير
- **نظام التخطيط المتجاوب المخصص** - ResponsiveLayoutSystem
- **نظام التصميم المتقدم** - ResponsiveDesignSystem
- **دعم DPI المتقدم** - PerMonitorV2 DPI Awareness

## 📞 **الدعم والتواصل**

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 XX XXX XXXX
- **الموقع**: www.aredoo.com

---

**🎉 شكراً لاستخدام أريدو الكاشير المتجاوب - نظام نقاط البيع المستقبلي! 🎉**
