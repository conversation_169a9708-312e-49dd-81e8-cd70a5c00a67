using System;
using System.Data;
using System.Data.SQLite;
using System.IO;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Core.Database
{
    /// <summary>
    /// مدير قاعدة البيانات الرئيسي
    /// يوفر إدارة شاملة لقاعدة البيانات مع معالجة الأخطاء والتسجيل
    /// </summary>
    public class DatabaseManager : IDisposable
    {
        #region الخصائص والمتغيرات الخاصة

        private readonly ILogger<DatabaseManager> _logger;
        private readonly string _connectionString;
        private readonly string _databasePath;
        private bool _disposed = false;

        /// <summary>
        /// سلسلة الاتصال بقاعدة البيانات
        /// </summary>
        public string ConnectionString => _connectionString;

        /// <summary>
        /// مسار ملف قاعدة البيانات
        /// </summary>
        public string DatabasePath => _databasePath;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ مدير قاعدة البيانات
        /// </summary>
        /// <param name="logger">مسجل الأحداث</param>
        /// <param name="databasePath">مسار قاعدة البيانات (اختياري)</param>
        public DatabaseManager(ILogger<DatabaseManager> logger = null, string databasePath = null)
        {
            _logger = logger;
            
            // تحديد مسار قاعدة البيانات
            _databasePath = databasePath ?? Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "AredooPOS", "Database", "AredooPOS.db");

            // إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
            var directory = Path.GetDirectoryName(_databasePath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                _logger?.LogInformation("تم إنشاء مجلد قاعدة البيانات: {Directory}", directory);
            }

            // بناء سلسلة الاتصال
            _connectionString = BuildConnectionString(_databasePath);

            // تهيئة قاعدة البيانات
            InitializeDatabase();
        }

        /// <summary>
        /// بناء سلسلة الاتصال بقاعدة البيانات
        /// </summary>
        /// <param name="databasePath">مسار قاعدة البيانات</param>
        /// <returns>سلسلة الاتصال</returns>
        private string BuildConnectionString(string databasePath)
        {
            var builder = new SQLiteConnectionStringBuilder
            {
                DataSource = databasePath,
                Version = 3,
                ForeignKeys = true,
                JournalMode = SQLiteJournalModeEnum.Wal,
                SyncMode = SynchronizationModes.Normal,
                CacheSize = 10000,
                PageSize = 4096,
                Pooling = true,
                BinaryGUID = false
            };

            return builder.ConnectionString;
        }

        /// <summary>
        /// تهيئة قاعدة البيانات وإنشاء الجداول
        /// </summary>
        private void InitializeDatabase()
        {
            try
            {
                _logger?.LogInformation("بدء تهيئة قاعدة البيانات...");

                // إنشاء ملف قاعدة البيانات إذا لم يكن موجوداً
                if (!File.Exists(_databasePath))
                {
                    SQLiteConnection.CreateFile(_databasePath);
                    _logger?.LogInformation("تم إنشاء ملف قاعدة البيانات: {DatabasePath}", _databasePath);
                }

                // تنفيذ سكريبت إنشاء الجداول
                ExecuteDatabaseScript();

                _logger?.LogInformation("تم تهيئة قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تهيئة قاعدة البيانات");
                throw new DatabaseException("فشل في تهيئة قاعدة البيانات", ex);
            }
        }

        /// <summary>
        /// تنفيذ سكريبت إنشاء قاعدة البيانات
        /// </summary>
        private void ExecuteDatabaseScript()
        {
            var scriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, 
                "Database", "AredooPOS_Database.sql");

            if (!File.Exists(scriptPath))
            {
                _logger?.LogWarning("لم يتم العثور على سكريبت قاعدة البيانات: {ScriptPath}", scriptPath);
                return;
            }

            var script = File.ReadAllText(scriptPath);
            ExecuteNonQuery(script);
        }

        #endregion

        #region عمليات قاعدة البيانات الأساسية

        /// <summary>
        /// إنشاء اتصال جديد بقاعدة البيانات
        /// </summary>
        /// <returns>كائن الاتصال</returns>
        public SQLiteConnection CreateConnection()
        {
            try
            {
                var connection = new SQLiteConnection(_connectionString);
                return connection;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنشاء اتصال قاعدة البيانات");
                throw new DatabaseException("فشل في إنشاء اتصال قاعدة البيانات", ex);
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        /// <returns>true إذا كان الاتصال ناجحاً</returns>
        public bool TestConnection()
        {
            try
            {
                using var connection = CreateConnection();
                connection.Open();
                
                using var command = new SQLiteCommand("SELECT 1", connection);
                var result = command.ExecuteScalar();
                
                _logger?.LogDebug("تم اختبار الاتصال بقاعدة البيانات بنجاح");
                return result != null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "فشل في اختبار الاتصال بقاعدة البيانات");
                return false;
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع DataTable
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>DataTable مع النتائج</returns>
        public DataTable ExecuteQuery(string query, params SQLiteParameter[] parameters)
        {
            if (string.IsNullOrWhiteSpace(query))
                throw new ArgumentException("الاستعلام لا يمكن أن يكون فارغاً", nameof(query));

            var dataTable = new DataTable();

            try
            {
                using var connection = CreateConnection();
                connection.Open();

                using var command = new SQLiteCommand(query, connection);
                
                // إضافة المعاملات
                if (parameters != null && parameters.Length > 0)
                {
                    command.Parameters.AddRange(parameters);
                }

                using var adapter = new SQLiteDataAdapter(command);
                adapter.Fill(dataTable);

                _logger?.LogDebug("تم تنفيذ الاستعلام بنجاح. عدد الصفوف: {RowCount}", dataTable.Rows.Count);
                return dataTable;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تنفيذ الاستعلام: {Query}", query);
                throw new DatabaseException($"فشل في تنفيذ الاستعلام: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ أمر وإرجاع عدد الصفوف المتأثرة
        /// </summary>
        /// <param name="query">الأمر</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public int ExecuteNonQuery(string query, params SQLiteParameter[] parameters)
        {
            if (string.IsNullOrWhiteSpace(query))
                throw new ArgumentException("الأمر لا يمكن أن يكون فارغاً", nameof(query));

            try
            {
                using var connection = CreateConnection();
                connection.Open();

                using var command = new SQLiteCommand(query, connection);
                
                // إضافة المعاملات
                if (parameters != null && parameters.Length > 0)
                {
                    command.Parameters.AddRange(parameters);
                }

                var rowsAffected = command.ExecuteNonQuery();
                
                _logger?.LogDebug("تم تنفيذ الأمر بنجاح. الصفوف المتأثرة: {RowsAffected}", rowsAffected);
                return rowsAffected;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تنفيذ الأمر: {Query}", query);
                throw new DatabaseException($"فشل في تنفيذ الأمر: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ أمر وإرجاع قيمة واحدة
        /// </summary>
        /// <param name="query">الأمر</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>القيمة المرجعة</returns>
        public object ExecuteScalar(string query, params SQLiteParameter[] parameters)
        {
            if (string.IsNullOrWhiteSpace(query))
                throw new ArgumentException("الأمر لا يمكن أن يكون فارغاً", nameof(query));

            try
            {
                using var connection = CreateConnection();
                connection.Open();

                using var command = new SQLiteCommand(query, connection);
                
                // إضافة المعاملات
                if (parameters != null && parameters.Length > 0)
                {
                    command.Parameters.AddRange(parameters);
                }

                var result = command.ExecuteScalar();
                
                _logger?.LogDebug("تم تنفيذ الأمر بنجاح. النتيجة: {Result}", result);
                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تنفيذ الأمر: {Query}", query);
                throw new DatabaseException($"فشل في تنفيذ الأمر: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ أمر مع إرجاع القيمة المُدرجة الجديدة
        /// </summary>
        /// <param name="query">أمر الإدراج</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>رقم السجل المُدرج</returns>
        public long ExecuteInsert(string query, params SQLiteParameter[] parameters)
        {
            if (string.IsNullOrWhiteSpace(query))
                throw new ArgumentException("أمر الإدراج لا يمكن أن يكون فارغاً", nameof(query));

            try
            {
                using var connection = CreateConnection();
                connection.Open();

                using var command = new SQLiteCommand(query, connection);
                
                // إضافة المعاملات
                if (parameters != null && parameters.Length > 0)
                {
                    command.Parameters.AddRange(parameters);
                }

                command.ExecuteNonQuery();
                
                // الحصول على رقم السجل المُدرج
                var lastInsertId = connection.LastInsertRowId;
                
                _logger?.LogDebug("تم إدراج السجل بنجاح. الرقم: {InsertId}", lastInsertId);
                return lastInsertId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إدراج السجل: {Query}", query);
                throw new DatabaseException($"فشل في إدراج السجل: {ex.Message}", ex);
            }
        }

        #endregion

        #region إدارة المعاملات

        /// <summary>
        /// تنفيذ مجموعة من الأوامر في معاملة واحدة
        /// </summary>
        /// <param name="commands">قائمة الأوامر</param>
        /// <returns>true إذا تم التنفيذ بنجاح</returns>
        public bool ExecuteTransaction(params (string Query, SQLiteParameter[] Parameters)[] commands)
        {
            if (commands == null || commands.Length == 0)
                throw new ArgumentException("قائمة الأوامر لا يمكن أن تكون فارغة", nameof(commands));

            using var connection = CreateConnection();
            connection.Open();

            using var transaction = connection.BeginTransaction();
            
            try
            {
                foreach (var (query, parameters) in commands)
                {
                    using var command = new SQLiteCommand(query, connection, transaction);
                    
                    if (parameters != null && parameters.Length > 0)
                    {
                        command.Parameters.AddRange(parameters);
                    }
                    
                    command.ExecuteNonQuery();
                }

                transaction.Commit();
                _logger?.LogDebug("تم تنفيذ المعاملة بنجاح. عدد الأوامر: {CommandCount}", commands.Length);
                return true;
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                _logger?.LogError(ex, "خطأ في تنفيذ المعاملة. تم التراجع");
                throw new DatabaseException($"فشل في تنفيذ المعاملة: {ex.Message}", ex);
            }
        }

        #endregion

        #region تنظيف الموارد

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// تنظيف الموارد المُدارة وغير المُدارة
        /// </summary>
        /// <param name="disposing">هل يتم التنظيف من Dispose</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // تنظيف الموارد المُدارة
                    _logger?.LogInformation("تم تنظيف موارد مدير قاعدة البيانات");
                }

                _disposed = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// استثناء خاص بقاعدة البيانات
    /// </summary>
    public class DatabaseException : Exception
    {
        public DatabaseException(string message) : base(message) { }
        public DatabaseException(string message, Exception innerException) : base(message, innerException) { }
    }
}
