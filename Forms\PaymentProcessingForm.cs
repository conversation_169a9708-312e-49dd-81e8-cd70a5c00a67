using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using AredooPOS.Models;
using AredooPOS.BLL;
using AredooPOS.Services;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// نموذج معالجة المدفوعات
    /// يوفر واجهة شاملة لمعالجة جميع أنواع المدفوعات
    /// </summary>
    public partial class PaymentProcessingForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly PaymentService _paymentService;
        private readonly PaymentProcessingBLL _paymentBLL;
        private readonly ILogger<PaymentProcessingForm> _logger;
        private readonly string _currentUser;
        private readonly int _sessionID;

        private decimal _totalAmount;
        private decimal _remainingAmount;
        private List<PaymentDetail> _paymentDetails;
        private List<PaymentMethod> _paymentMethods;

        // إعدادات النموذج
        private bool _allowMultiplePayments = true;
        private bool _requireExactAmount = false;

        #endregion

        #region الخصائص العامة

        /// <summary>
        /// المبلغ الإجمالي المطلوب دفعه
        /// </summary>
        public decimal TotalAmount
        {
            get => _totalAmount;
            set
            {
                _totalAmount = value;
                _remainingAmount = value;
                UpdateAmountDisplay();
            }
        }

        /// <summary>
        /// نتيجة معالجة الدفع
        /// </summary>
        public MultiPaymentResult PaymentResult { get; private set; }

        /// <summary>
        /// هل تم إتمام الدفع بنجاح
        /// </summary>
        public bool IsPaymentCompleted { get; private set; }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ نموذج معالجة المدفوعات
        /// </summary>
        /// <param name="sessionID">رقم الجلسة</param>
        /// <param name="totalAmount">المبلغ الإجمالي</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <param name="logger">مسجل الأحداث</param>
        public PaymentProcessingForm(int sessionID, decimal totalAmount, string currentUser, 
            ILogger<PaymentProcessingForm> logger = null)
        {
            InitializeComponent();
            
            _sessionID = sessionID;
            _currentUser = currentUser;
            _logger = logger;
            
            _paymentService = new PaymentService(null, logger);
            _paymentBLL = new PaymentProcessingBLL(null, logger);
            
            _paymentDetails = new List<PaymentDetail>();
            
            InitializeForm();
            LoadPaymentMethods();
            
            TotalAmount = totalAmount;
        }

        /// <summary>
        /// تهيئة النموذج
        /// </summary>
        private void InitializeForm()
        {
            // تعيين النصوص العربية
            this.Text = "معالجة المدفوعات";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // تهيئة جدول تفاصيل الدفع
            InitializePaymentDetailsGrid();

            // تهيئة الأحداث
            InitializeEvents();

            // تعيين القيم الافتراضية
            SetDefaultValues();
        }

        /// <summary>
        /// تهيئة جدول تفاصيل الدفع
        /// </summary>
        private void InitializePaymentDetailsGrid()
        {
            dgvPaymentDetails.AutoGenerateColumns = false;
            dgvPaymentDetails.AllowUserToAddRows = false;
            dgvPaymentDetails.AllowUserToDeleteRows = false;
            dgvPaymentDetails.ReadOnly = true;
            dgvPaymentDetails.SelectionMode = DataGridViewSelectionMode.FullRowSelect;

            // إضافة الأعمدة
            dgvPaymentDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PaymentMethodName",
                HeaderText = "طريقة الدفع",
                DataPropertyName = "PaymentMethodName",
                Width = 120
            });

            dgvPaymentDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Amount",
                HeaderText = "المبلغ",
                DataPropertyName = "Amount",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "C2" }
            });

            dgvPaymentDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ReferenceNumber",
                HeaderText = "الرقم المرجعي",
                DataPropertyName = "ReferenceNumber",
                Width = 120
            });

            dgvPaymentDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Notes",
                HeaderText = "ملاحظات",
                DataPropertyName = "Notes",
                Width = 150
            });
        }

        /// <summary>
        /// تهيئة الأحداث
        /// </summary>
        private void InitializeEvents()
        {
            // أحداث الأزرار
            btnAddPayment.Click += BtnAddPayment_Click;
            btnRemovePayment.Click += BtnRemovePayment_Click;
            btnProcessPayment.Click += BtnProcessPayment_Click;
            btnCancel.Click += BtnCancel_Click;
            btnClear.Click += BtnClear_Click;

            // أحداث طرق الدفع
            cmbPaymentMethod.SelectedIndexChanged += CmbPaymentMethod_SelectedIndexChanged;

            // أحداث مربعات النص والأرقام
            numPaymentAmount.ValueChanged += NumPaymentAmount_ValueChanged;
            txtReferenceNumber.TextChanged += TxtReferenceNumber_TextChanged;

            // أحداث جدول البيانات
            dgvPaymentDetails.SelectionChanged += DgvPaymentDetails_SelectionChanged;

            // أحداث لوحة المفاتيح
            this.KeyPreview = true;
            this.KeyDown += PaymentProcessingForm_KeyDown;

            // أحداث النموذج
            this.Load += PaymentProcessingForm_Load;
        }

        /// <summary>
        /// تعيين القيم الافتراضية
        /// </summary>
        private void SetDefaultValues()
        {
            numPaymentAmount.DecimalPlaces = 2;
            numPaymentAmount.Minimum = 0;
            numPaymentAmount.Maximum = 999999;

            // تعطيل أزرار معينة في البداية
            btnRemovePayment.Enabled = false;
            btnProcessPayment.Enabled = false;
        }

        #endregion

        #region تحميل البيانات

        /// <summary>
        /// تحميل طرق الدفع
        /// </summary>
        private async void LoadPaymentMethods()
        {
            try
            {
                await Task.Run(() =>
                {
                    // TODO: تحميل طرق الدفع من قاعدة البيانات
                    _paymentMethods = GetAvailablePaymentMethods();
                });

                cmbPaymentMethod.DataSource = _paymentMethods;
                cmbPaymentMethod.DisplayMember = "DisplayName";
                cmbPaymentMethod.ValueMember = "PaymentMethodID";

                if (_paymentMethods.Any())
                {
                    cmbPaymentMethod.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل طرق الدفع");
                MessageBox.Show($"خطأ في تحميل طرق الدفع: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// الحصول على طرق الدفع المتاحة
        /// </summary>
        /// <returns>قائمة طرق الدفع</returns>
        private List<PaymentMethod> GetAvailablePaymentMethods()
        {
            // TODO: تحميل من قاعدة البيانات
            return new List<PaymentMethod>
            {
                new PaymentMethod { PaymentMethodID = 1, MethodName = "نقدي", DisplayName = "نقدي", IsActive = true },
                new PaymentMethod { PaymentMethodID = 2, MethodName = "بطاقة", DisplayName = "بطاقة ائتمان/مدين", IsActive = true },
                new PaymentMethod { PaymentMethodID = 3, MethodName = "تحويل", DisplayName = "تحويل بنكي", IsActive = true },
                new PaymentMethod { PaymentMethodID = 4, MethodName = "شيك", DisplayName = "شيك", IsActive = true },
                new PaymentMethod { PaymentMethodID = 5, MethodName = "بطاقة هدايا", DisplayName = "بطاقة هدايا", IsActive = true }
            };
        }

        #endregion

        #region أحداث الأزرار

        /// <summary>
        /// حدث زر إضافة دفعة
        /// </summary>
        private void BtnAddPayment_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidatePaymentInput())
                    return;

                var paymentDetail = CreatePaymentDetailFromInput();
                _paymentDetails.Add(paymentDetail);

                // تحديث المبلغ المتبقي
                _remainingAmount -= paymentDetail.Amount;

                // تحديث العرض
                RefreshPaymentDetailsGrid();
                UpdateAmountDisplay();
                ClearPaymentInput();

                // التحقق من إتمام الدفع
                CheckPaymentCompletion();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إضافة الدفعة");
                MessageBox.Show($"خطأ في إضافة الدفعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث زر حذف دفعة
        /// </summary>
        private void BtnRemovePayment_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvPaymentDetails.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار دفعة للحذف", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedIndex = dgvPaymentDetails.SelectedRows[0].Index;
                var paymentDetail = _paymentDetails[selectedIndex];

                // إعادة المبلغ للمتبقي
                _remainingAmount += paymentDetail.Amount;

                // حذف الدفعة
                _paymentDetails.RemoveAt(selectedIndex);

                // تحديث العرض
                RefreshPaymentDetailsGrid();
                UpdateAmountDisplay();
                CheckPaymentCompletion();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حذف الدفعة");
                MessageBox.Show($"خطأ في حذف الدفعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حدث زر معالجة الدفع
        /// </summary>
        private async void BtnProcessPayment_Click(object sender, EventArgs e)
        {
            try
            {
                if (!_paymentDetails.Any())
                {
                    MessageBox.Show("يرجى إضافة طريقة دفع واحدة على الأقل", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (_requireExactAmount && _remainingAmount != 0)
                {
                    MessageBox.Show("يجب أن يكون المبلغ المدفوع مساوياً للمبلغ المطلوب", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // تعطيل الأزرار أثناء المعالجة
                EnableControls(false);
                lblStatus.Text = "جاري معالجة الدفع...";
                progressBar.Visible = true;

                // إنشاء طلب الدفع المتعدد
                var multiPaymentRequest = CreateMultiPaymentRequest();

                // معالجة الدفع
                PaymentResult = await _paymentService.ProcessMultiPaymentAsync(multiPaymentRequest);

                if (PaymentResult.IsSuccessful)
                {
                    IsPaymentCompleted = true;
                    lblStatus.Text = "تم إتمام الدفع بنجاح";
                    
                    MessageBox.Show($"تم إتمام الدفع بنجاح\nالمبلغ المعالج: {PaymentResult.TotalProcessedAmount:C}", 
                        "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    lblStatus.Text = "فشل في معالجة الدفع";
                    MessageBox.Show($"فشل في معالجة الدفع: {PaymentResult.ErrorMessage}", 
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    
                    EnableControls(true);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة الدفع");
                MessageBox.Show($"خطأ في معالجة الدفع: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                
                lblStatus.Text = "خطأ في معالجة الدفع";
                EnableControls(true);
            }
            finally
            {
                progressBar.Visible = false;
            }
        }

        /// <summary>
        /// حدث زر إلغاء
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            if (_paymentDetails.Any())
            {
                var result = MessageBox.Show(
                    "هل أنت متأكد من إلغاء العملية؟ سيتم فقدان جميع البيانات المدخلة.",
                    "تأكيد الإلغاء",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.No)
                    return;
            }

            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// حدث زر مسح
        /// </summary>
        private void BtnClear_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من مسح جميع الدفعات؟",
                "تأكيد المسح",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                ClearAllPayments();
            }
        }

        #endregion

        #region أحداث أخرى

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void PaymentProcessingForm_Load(object sender, EventArgs e)
        {
            // تركيز على مبلغ الدفع
            numPaymentAmount.Focus();
            
            // تعيين المبلغ الافتراضي للمبلغ المتبقي
            if (_remainingAmount > 0)
            {
                numPaymentAmount.Value = _remainingAmount;
            }
        }

        /// <summary>
        /// حدث تغيير طريقة الدفع
        /// </summary>
        private void CmbPaymentMethod_SelectedIndexChanged(object sender, EventArgs e)
        {
            var selectedMethod = cmbPaymentMethod.SelectedItem as PaymentMethod;
            if (selectedMethod != null)
            {
                // تحديث متطلبات طريقة الدفع
                UpdatePaymentMethodRequirements(selectedMethod);
            }
        }

        /// <summary>
        /// حدث تغيير مبلغ الدفع
        /// </summary>
        private void NumPaymentAmount_ValueChanged(object sender, EventArgs e)
        {
            // التحقق من أن المبلغ لا يتجاوز المتبقي
            if (numPaymentAmount.Value > _remainingAmount)
            {
                numPaymentAmount.Value = _remainingAmount;
            }

            // تحديث حالة زر الإضافة
            btnAddPayment.Enabled = numPaymentAmount.Value > 0;
        }

        /// <summary>
        /// حدث تغيير الرقم المرجعي
        /// </summary>
        private void TxtReferenceNumber_TextChanged(object sender, EventArgs e)
        {
            // التحقق من متطلبات الرقم المرجعي
            ValidateReferenceNumber();
        }

        /// <summary>
        /// حدث تغيير التحديد في جدول الدفعات
        /// </summary>
        private void DgvPaymentDetails_SelectionChanged(object sender, EventArgs e)
        {
            btnRemovePayment.Enabled = dgvPaymentDetails.SelectedRows.Count > 0;
        }

        /// <summary>
        /// حدث الضغط على مفاتيح
        /// </summary>
        private void PaymentProcessingForm_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.F1:
                    BtnAddPayment_Click(sender, e);
                    e.Handled = true;
                    break;
                case Keys.F2:
                    BtnRemovePayment_Click(sender, e);
                    e.Handled = true;
                    break;
                case Keys.F10:
                    BtnProcessPayment_Click(sender, e);
                    e.Handled = true;
                    break;
                case Keys.Escape:
                    BtnCancel_Click(sender, e);
                    e.Handled = true;
                    break;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// تحديث عرض المبالغ
        /// </summary>
        private void UpdateAmountDisplay()
        {
            lblTotalAmount.Text = $"المبلغ الإجمالي: {_totalAmount:C}";
            lblPaidAmount.Text = $"المبلغ المدفوع: {(_totalAmount - _remainingAmount):C}";
            lblRemainingAmount.Text = $"المبلغ المتبقي: {_remainingAmount:C}";

            // تغيير لون المبلغ المتبقي
            if (_remainingAmount == 0)
            {
                lblRemainingAmount.ForeColor = Color.Green;
                btnProcessPayment.Enabled = true;
            }
            else if (_remainingAmount < 0)
            {
                lblRemainingAmount.ForeColor = Color.Blue;
                lblRemainingAmount.Text = $"المبلغ الزائد: {Math.Abs(_remainingAmount):C}";
                btnProcessPayment.Enabled = !_requireExactAmount;
            }
            else
            {
                lblRemainingAmount.ForeColor = Color.Red;
                btnProcessPayment.Enabled = !_requireExactAmount;
            }
        }

        /// <summary>
        /// تحديث متطلبات طريقة الدفع
        /// </summary>
        /// <param name="paymentMethod">طريقة الدفع</param>
        private void UpdatePaymentMethodRequirements(PaymentMethod paymentMethod)
        {
            // إظهار/إخفاء الرقم المرجعي حسب طريقة الدفع
            bool requiresReference = paymentMethod.PaymentMethodID != 1; // ليس نقدي

            lblReferenceNumber.Visible = requiresReference;
            txtReferenceNumber.Visible = requiresReference;
            txtReferenceNumber.Text = "";

            // تحديد الحد الأدنى للمبلغ
            switch (paymentMethod.PaymentMethodID)
            {
                case 2: // بطاقة
                    numPaymentAmount.Minimum = 10;
                    break;
                case 3: // تحويل
                    numPaymentAmount.Minimum = 50;
                    break;
                case 4: // شيك
                    numPaymentAmount.Minimum = 100;
                    break;
                default:
                    numPaymentAmount.Minimum = 0;
                    break;
            }
        }

        /// <summary>
        /// التحقق من صحة إدخال الدفع
        /// </summary>
        /// <returns>true إذا كان الإدخال صحيح</returns>
        private bool ValidatePaymentInput()
        {
            // التحقق من المبلغ
            if (numPaymentAmount.Value <= 0)
            {
                MessageBox.Show("مبلغ الدفع يجب أن يكون أكبر من صفر", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numPaymentAmount.Focus();
                return false;
            }

            // التحقق من عدم تجاوز المبلغ المتبقي (إذا كان مطلوب)
            if (_requireExactAmount && numPaymentAmount.Value > _remainingAmount)
            {
                MessageBox.Show("مبلغ الدفع لا يمكن أن يتجاوز المبلغ المتبقي", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numPaymentAmount.Focus();
                return false;
            }

            // التحقق من طريقة الدفع
            if (cmbPaymentMethod.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار طريقة الدفع", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbPaymentMethod.Focus();
                return false;
            }

            // التحقق من الرقم المرجعي إذا كان مطلوب
            var selectedMethod = cmbPaymentMethod.SelectedItem as PaymentMethod;
            if (selectedMethod.PaymentMethodID != 1 && string.IsNullOrWhiteSpace(txtReferenceNumber.Text))
            {
                MessageBox.Show("الرقم المرجعي مطلوب لهذه طريقة الدفع", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtReferenceNumber.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// التحقق من صحة الرقم المرجعي
        /// </summary>
        private void ValidateReferenceNumber()
        {
            var selectedMethod = cmbPaymentMethod.SelectedItem as PaymentMethod;
            if (selectedMethod == null) return;

            // التحقق حسب نوع طريقة الدفع
            switch (selectedMethod.PaymentMethodID)
            {
                case 3: // تحويل بنكي
                    if (txtReferenceNumber.Text.Length > 0 && txtReferenceNumber.Text.Length < 10)
                    {
                        lblReferenceValidation.Text = "الرقم المرجعي يجب أن يكون 10 أرقام على الأقل";
                        lblReferenceValidation.ForeColor = Color.Red;
                        lblReferenceValidation.Visible = true;
                    }
                    else
                    {
                        lblReferenceValidation.Visible = false;
                    }
                    break;
                default:
                    lblReferenceValidation.Visible = false;
                    break;
            }
        }

        /// <summary>
        /// إنشاء تفاصيل الدفع من الإدخال
        /// </summary>
        /// <returns>تفاصيل الدفع</returns>
        private PaymentDetail CreatePaymentDetailFromInput()
        {
            var selectedMethod = cmbPaymentMethod.SelectedItem as PaymentMethod;

            return new PaymentDetail
            {
                Amount = numPaymentAmount.Value,
                PaymentMethodID = selectedMethod.PaymentMethodID,
                PaymentMethodName = selectedMethod.DisplayName,
                ReferenceNumber = txtReferenceNumber.Text.Trim(),
                Notes = txtNotes.Text.Trim()
            };
        }

        /// <summary>
        /// إنشاء طلب الدفع المتعدد
        /// </summary>
        /// <returns>طلب الدفع المتعدد</returns>
        private MultiPaymentRequest CreateMultiPaymentRequest()
        {
            return new MultiPaymentRequest
            {
                SessionID = _sessionID,
                TransactionType = CashTransactionTypes.Sale,
                Description = "دفع فاتورة",
                UserID = GetCurrentUserID(),
                ProcessedBy = _currentUser,
                Payments = _paymentDetails
            };
        }

        /// <summary>
        /// تحديث جدول تفاصيل الدفع
        /// </summary>
        private void RefreshPaymentDetailsGrid()
        {
            dgvPaymentDetails.DataSource = null;
            dgvPaymentDetails.DataSource = _paymentDetails;
        }

        /// <summary>
        /// مسح إدخال الدفع
        /// </summary>
        private void ClearPaymentInput()
        {
            numPaymentAmount.Value = Math.Min(_remainingAmount, numPaymentAmount.Maximum);
            txtReferenceNumber.Clear();
            txtNotes.Clear();
            lblReferenceValidation.Visible = false;

            // التركيز على المبلغ
            numPaymentAmount.Focus();
        }

        /// <summary>
        /// مسح جميع الدفعات
        /// </summary>
        private void ClearAllPayments()
        {
            _paymentDetails.Clear();
            _remainingAmount = _totalAmount;

            RefreshPaymentDetailsGrid();
            UpdateAmountDisplay();
            ClearPaymentInput();

            btnProcessPayment.Enabled = false;
        }

        /// <summary>
        /// التحقق من إتمام الدفع
        /// </summary>
        private void CheckPaymentCompletion()
        {
            if (_remainingAmount <= 0)
            {
                // تم إتمام الدفع أو زيادة
                btnProcessPayment.Enabled = true;

                if (_remainingAmount == 0)
                {
                    lblStatus.Text = "تم إتمام المبلغ المطلوب";
                    lblStatus.ForeColor = Color.Green;
                }
                else
                {
                    lblStatus.Text = $"يوجد مبلغ زائد: {Math.Abs(_remainingAmount):C}";
                    lblStatus.ForeColor = Color.Blue;
                }
            }
            else
            {
                lblStatus.Text = $"المبلغ المتبقي: {_remainingAmount:C}";
                lblStatus.ForeColor = Color.Red;
                btnProcessPayment.Enabled = !_requireExactAmount;
            }
        }

        /// <summary>
        /// تفعيل/تعطيل عناصر التحكم
        /// </summary>
        /// <param name="enabled">تفعيل أم لا</param>
        private void EnableControls(bool enabled)
        {
            cmbPaymentMethod.Enabled = enabled;
            numPaymentAmount.Enabled = enabled;
            txtReferenceNumber.Enabled = enabled;
            txtNotes.Enabled = enabled;
            btnAddPayment.Enabled = enabled;
            btnRemovePayment.Enabled = enabled && dgvPaymentDetails.SelectedRows.Count > 0;
            btnProcessPayment.Enabled = enabled && _paymentDetails.Any();
            btnClear.Enabled = enabled;
        }

        /// <summary>
        /// الحصول على رقم المستخدم الحالي
        /// </summary>
        /// <returns>رقم المستخدم</returns>
        private int GetCurrentUserID()
        {
            // TODO: الحصول على رقم المستخدم من النظام
            return 1; // مثال مبسط
        }

        #endregion

        #region دوال إضافية

        /// <summary>
        /// تعيين إعدادات النموذج
        /// </summary>
        /// <param name="allowMultiple">السماح بدفعات متعددة</param>
        /// <param name="requireExact">طلب المبلغ الدقيق</param>
        public void SetPaymentSettings(bool allowMultiple, bool requireExact)
        {
            _allowMultiplePayments = allowMultiple;
            _requireExactAmount = requireExact;

            // إخفاء عناصر الدفع المتعدد إذا لم يكن مسموح
            if (!_allowMultiplePayments)
            {
                dgvPaymentDetails.Visible = false;
                btnRemovePayment.Visible = false;
                btnClear.Visible = false;

                // تصغير حجم النموذج
                this.Height -= dgvPaymentDetails.Height + 50;
            }
        }

        /// <summary>
        /// تعيين معلومات إضافية للدفع
        /// </summary>
        /// <param name="customerID">رقم العميل</param>
        /// <param name="invoiceNumber">رقم الفاتورة</param>
        /// <param name="description">الوصف</param>
        public void SetPaymentInfo(int? customerID, string invoiceNumber, string description)
        {
            // TODO: تعيين المعلومات الإضافية
        }

        /// <summary>
        /// الحصول على ملخص الدفع
        /// </summary>
        /// <returns>ملخص الدفع</returns>
        public PaymentSummary GetPaymentSummary()
        {
            return new PaymentSummary
            {
                TotalAmount = _totalAmount,
                PaidAmount = _totalAmount - _remainingAmount,
                RemainingAmount = _remainingAmount,
                PaymentCount = _paymentDetails.Count,
                PaymentMethods = _paymentDetails.Select(p => p.PaymentMethodName).Distinct().ToList()
            };
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// ملخص الدفع
    /// </summary>
    public class PaymentSummary
    {
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public int PaymentCount { get; set; }
        public List<string> PaymentMethods { get; set; }
    }

    #endregion
}
