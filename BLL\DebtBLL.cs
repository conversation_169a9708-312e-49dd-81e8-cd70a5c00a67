using System;
using System.Collections.Generic;
using System.Linq;
using AredooPOS.Models;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.BLL
{
    /// <summary>
    /// طبقة منطق الأعمال للديون
    /// تحتوي على جميع العمليات التجارية المتعلقة بإدارة الديون والتحصيل
    /// </summary>
    public class DebtBLL
    {
        #region المتغيرات الخاصة

        private readonly DebtDAL _debtDAL;
        private readonly CustomerDAL _customerDAL;
        private readonly ILogger<DebtBLL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة منطق الأعمال للديون
        /// </summary>
        /// <param name="debtDAL">طبقة الوصول للبيانات للديون</param>
        /// <param name="customerDAL">طبقة الوصول للبيانات للعملاء</param>
        /// <param name="logger">مسجل الأحداث</param>
        public DebtBLL(DebtDAL debtDAL, CustomerDAL customerDAL, ILogger<DebtBLL> logger = null)
        {
            _debtDAL = debtDAL ?? throw new ArgumentNullException(nameof(debtDAL));
            _customerDAL = customerDAL ?? throw new ArgumentNullException(nameof(customerDAL));
            _logger = logger;
        }

        #endregion

        #region عمليات إدارة الديون

        /// <summary>
        /// إنشاء دين جديد
        /// </summary>
        /// <param name="debt">بيانات الدين</param>
        /// <param name="updateCustomerBalance">تحديث رصيد العميل</param>
        /// <returns>رقم الدين المُنشأ</returns>
        public int CreateDebt(Debt debt, bool updateCustomerBalance = true)
        {
            try
            {
                _logger?.LogInformation("بدء إنشاء دين جديد للعميل {CustomerName} بمبلغ {Amount}",
                    debt.CustomerName, debt.OriginalAmount);

                // التحقق من صحة البيانات
                ValidateDebtData(debt);

                // التحقق من العميل
                var customer = _customerDAL.GetCustomerById(debt.CustomerID);
                if (customer == null)
                {
                    throw new ArgumentException("العميل غير موجود");
                }

                if (!customer.IsActive)
                {
                    throw new InvalidOperationException("العميل غير نشط");
                }

                // التحقق من حد الائتمان
                if (updateCustomerBalance && !customer.CanGrantCredit(debt.OriginalAmount))
                {
                    throw new InvalidOperationException($"تجاوز حد الائتمان المسموح. الحد المتاح: {customer.GetAvailableCredit():C}");
                }

                // توليد رقم الدين
                if (string.IsNullOrWhiteSpace(debt.DebtNumber))
                {
                    debt.DebtNumber = GenerateDebtNumber();
                }

                // تعيين القيم الافتراضية
                debt.CreatedDate = DateTime.Now;
                debt.ModifiedDate = DateTime.Now;

                // حساب المبالغ
                debt.CalculateAmounts();

                // إضافة الدين
                var debtId = _debtDAL.AddDebt(debt);

                // تحديث رصيد العميل
                if (updateCustomerBalance)
                {
                    customer.AddToBalance(debt.OriginalAmount, $"دين رقم {debt.DebtNumber}");
                    _customerDAL.UpdateCustomer(customer);
                }

                _logger?.LogInformation("تم إنشاء الدين بنجاح. رقم الدين: {DebtNumber}", debt.DebtNumber);
                return debtId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنشاء الدين للعميل {CustomerName}", debt.CustomerName);
                throw;
            }
        }

        /// <summary>
        /// تسجيل دفعة على دين
        /// </summary>
        /// <param name="debtId">رقم الدين</param>
        /// <param name="payment">بيانات الدفعة</param>
        /// <param name="updateCustomerBalance">تحديث رصيد العميل</param>
        /// <returns>true إذا تم التسجيل بنجاح</returns>
        public bool RecordPayment(int debtId, DebtPayment payment, bool updateCustomerBalance = true)
        {
            try
            {
                _logger?.LogInformation("بدء تسجيل دفعة بمبلغ {Amount} على الدين رقم {DebtID}",
                    payment.Amount, debtId);

                // الحصول على الدين
                var debt = _debtDAL.GetDebtById(debtId);
                if (debt == null)
                {
                    throw new ArgumentException("الدين غير موجود");
                }

                if (!debt.CanEdit())
                {
                    throw new InvalidOperationException("لا يمكن تعديل هذا الدين");
                }

                // التحقق من صحة مبلغ الدفعة
                if (payment.Amount <= 0)
                {
                    throw new ArgumentException("مبلغ الدفعة يجب أن يكون أكبر من صفر");
                }

                if (payment.Amount > debt.RemainingAmount)
                {
                    throw new ArgumentException($"مبلغ الدفعة أكبر من المبلغ المتبقي ({debt.RemainingAmount:C})");
                }

                // توليد رقم الدفعة
                if (string.IsNullOrWhiteSpace(payment.PaymentNumber))
                {
                    payment.PaymentNumber = GeneratePaymentNumber();
                }

                // تعيين القيم
                payment.DebtID = debtId;
                payment.CreatedDate = DateTime.Now;
                payment.ModifiedDate = DateTime.Now;
                payment.CalculateAmountInBaseCurrency();

                // إضافة الدفعة للدين
                debt.AddPayment(payment.Amount, payment.PaymentMethod, payment.Notes);

                // تحديث الدين
                var success = _debtDAL.UpdateDebt(debt);

                if (success && updateCustomerBalance)
                {
                    // تحديث رصيد العميل
                    var customer = _customerDAL.GetCustomerById(debt.CustomerID);
                    if (customer != null)
                    {
                        customer.DeductFromBalance(payment.Amount, $"دفعة على دين رقم {debt.DebtNumber}");
                        _customerDAL.UpdateCustomer(customer);
                    }
                }

                if (success)
                {
                    _logger?.LogInformation("تم تسجيل الدفعة بنجاح. رقم الدفعة: {PaymentNumber}", payment.PaymentNumber);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تسجيل الدفعة على الدين رقم {DebtID}", debtId);
                throw;
            }
        }

        /// <summary>
        /// إلغاء دين
        /// </summary>
        /// <param name="debtId">رقم الدين</param>
        /// <param name="reason">سبب الإلغاء</param>
        /// <param name="updateCustomerBalance">تحديث رصيد العميل</param>
        /// <returns>true إذا تم الإلغاء بنجاح</returns>
        public bool CancelDebt(int debtId, string reason, bool updateCustomerBalance = true)
        {
            try
            {
                _logger?.LogInformation("بدء إلغاء الدين رقم {DebtID}", debtId);

                // الحصول على الدين
                var debt = _debtDAL.GetDebtById(debtId);
                if (debt == null)
                {
                    throw new ArgumentException("الدين غير موجود");
                }

                if (!debt.CanCancel())
                {
                    throw new InvalidOperationException("لا يمكن إلغاء هذا الدين");
                }

                // تحديث حالة الدين
                debt.DebtStatus = DebtStatuses.Cancelled;
                debt.InternalNotes = $"تم الإلغاء: {reason}";
                debt.ModifiedDate = DateTime.Now;

                var success = _debtDAL.UpdateDebt(debt);

                if (success && updateCustomerBalance)
                {
                    // تحديث رصيد العميل
                    var customer = _customerDAL.GetCustomerById(debt.CustomerID);
                    if (customer != null)
                    {
                        customer.DeductFromBalance(debt.RemainingAmount, $"إلغاء دين رقم {debt.DebtNumber}");
                        _customerDAL.UpdateCustomer(customer);
                    }
                }

                if (success)
                {
                    _logger?.LogInformation("تم إلغاء الدين بنجاح. رقم الدين: {DebtNumber}", debt.DebtNumber);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إلغاء الدين رقم {DebtID}", debtId);
                throw;
            }
        }

        /// <summary>
        /// حساب رسوم التأخير للديون المتأخرة
        /// </summary>
        /// <param name="dailyFeeRate">معدل الرسوم اليومية</param>
        /// <returns>عدد الديون المحدثة</returns>
        public int CalculateLateFees(decimal dailyFeeRate = 0.1m)
        {
            try
            {
                _logger?.LogInformation("بدء حساب رسوم التأخير بمعدل {Rate}% يومياً", dailyFeeRate);

                var overdueDebts = _debtDAL.GetOverdueDebts();
                var updatedCount = 0;

                foreach (var debt in overdueDebts)
                {
                    var oldLateFees = debt.LateFees;
                    debt.CalculateLateFees(dailyFeeRate);

                    if (debt.LateFees != oldLateFees)
                    {
                        debt.ModifiedDate = DateTime.Now;
                        if (_debtDAL.UpdateDebt(debt))
                        {
                            updatedCount++;
                        }
                    }
                }

                _logger?.LogInformation("تم تحديث رسوم التأخير لـ {Count} دين", updatedCount);
                return updatedCount;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حساب رسوم التأخير");
                throw;
            }
        }

        #endregion

        #region عمليات البحث والتقارير

        /// <summary>
        /// البحث في الديون
        /// </summary>
        /// <param name="criteria">معايير البحث</param>
        /// <returns>قائمة الديون المطابقة</returns>
        public List<Debt> SearchDebts(DebtSearchCriteria criteria)
        {
            try
            {
                return _debtDAL.SearchDebts(criteria);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في البحث في الديون");
                throw;
            }
        }

        /// <summary>
        /// الحصول على ديون العميل
        /// </summary>
        /// <param name="customerId">رقم العميل</param>
        /// <param name="includeFullyPaid">تضمين الديون المدفوعة</param>
        /// <returns>قائمة ديون العميل</returns>
        public List<Debt> GetCustomerDebts(int customerId, bool includeFullyPaid = false)
        {
            try
            {
                return _debtDAL.GetCustomerDebts(customerId, includeFullyPaid);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على ديون العميل {CustomerID}", customerId);
                throw;
            }
        }

        /// <summary>
        /// الحصول على الديون المتأخرة
        /// </summary>
        /// <param name="daysOverdue">عدد أيام التأخير</param>
        /// <returns>قائمة الديون المتأخرة</returns>
        public List<Debt> GetOverdueDebts(int daysOverdue = 1)
        {
            try
            {
                return _debtDAL.GetOverdueDebts(daysOverdue);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على الديون المتأخرة");
                throw;
            }
        }

        /// <summary>
        /// الحصول على ملخص الديون
        /// </summary>
        /// <returns>ملخص الديون</returns>
        public DebtSummary GetDebtSummary()
        {
            try
            {
                var allDebts = _debtDAL.SearchDebts(new DebtSearchCriteria { OnlyOutstanding = true });
                var overdueDebts = _debtDAL.GetOverdueDebts();

                return new DebtSummary
                {
                    TotalOutstandingDebts = allDebts.Count,
                    TotalOutstandingAmount = allDebts.Sum(d => d.RemainingAmount),
                    TotalOverdueDebts = overdueDebts.Count,
                    TotalOverdueAmount = overdueDebts.Sum(d => d.RemainingAmount),
                    TotalLateFees = overdueDebts.Sum(d => d.LateFees),
                    AverageDebtAge = allDebts.Any() ? allDebts.Average(d => (DateTime.Now - d.DebtDate).Days) : 0
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على ملخص الديون");
                throw;
            }
        }

        /// <summary>
        /// الحصول على أكثر العملاء مديونية
        /// </summary>
        /// <param name="topCount">عدد العملاء المطلوب</param>
        /// <returns>قائمة العملاء الأكثر مديونية</returns>
        public List<CustomerDebtSummary> GetTopDebtorCustomers(int topCount = 10)
        {
            try
            {
                var criteria = new DebtSearchCriteria { OnlyOutstanding = true };
                var debts = _debtDAL.SearchDebts(criteria);

                var customerDebts = debts
                    .GroupBy(d => new { d.CustomerID, d.CustomerName, d.CustomerPhone })
                    .Select(g => new CustomerDebtSummary
                    {
                        CustomerID = g.Key.CustomerID,
                        CustomerName = g.Key.CustomerName,
                        CustomerPhone = g.Key.CustomerPhone,
                        TotalDebts = g.Count(),
                        TotalAmount = g.Sum(d => d.RemainingAmount),
                        OverdueDebts = g.Count(d => d.IsOverdue()),
                        OverdueAmount = g.Where(d => d.IsOverdue()).Sum(d => d.RemainingAmount),
                        OldestDebtDate = g.Min(d => d.DebtDate)
                    })
                    .OrderByDescending(c => c.TotalAmount)
                    .Take(topCount)
                    .ToList();

                return customerDebts;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على أكثر العملاء مديونية");
                throw;
            }
        }

        #endregion

        #region عمليات مساعدة

        /// <summary>
        /// التحقق من صحة بيانات الدين
        /// </summary>
        /// <param name="debt">بيانات الدين</param>
        private void ValidateDebtData(Debt debt)
        {
            if (debt == null)
                throw new ArgumentNullException(nameof(debt));

            if (!debt.IsValid())
                throw new ArgumentException("بيانات الدين غير صحيحة");

            if (debt.CustomerID <= 0)
                throw new ArgumentException("رقم العميل غير صحيح");

            if (debt.OriginalAmount <= 0)
                throw new ArgumentException("مبلغ الدين يجب أن يكون أكبر من صفر");
        }

        /// <summary>
        /// توليد رقم دين جديد
        /// </summary>
        /// <returns>رقم الدين</returns>
        private string GenerateDebtNumber()
        {
            var prefix = "DEBT";
            var timestamp = DateTime.Now.ToString("yyyyMMdd");
            var sequence = DateTime.Now.ToString("HHmmss");
            return $"{prefix}{timestamp}{sequence}";
        }

        /// <summary>
        /// توليد رقم دفعة جديد
        /// </summary>
        /// <returns>رقم الدفعة</returns>
        private string GeneratePaymentNumber()
        {
            var prefix = "PAY";
            var timestamp = DateTime.Now.ToString("yyyyMMdd");
            var sequence = DateTime.Now.ToString("HHmmss");
            return $"{prefix}{timestamp}{sequence}";
        }

        #endregion
    }

    #region نماذج التقارير

    /// <summary>
    /// ملخص الديون
    /// </summary>
    public class DebtSummary
    {
        public int TotalOutstandingDebts { get; set; }
        public decimal TotalOutstandingAmount { get; set; }
        public int TotalOverdueDebts { get; set; }
        public decimal TotalOverdueAmount { get; set; }
        public decimal TotalLateFees { get; set; }
        public double AverageDebtAge { get; set; }
    }

    /// <summary>
    /// ملخص ديون العميل
    /// </summary>
    public class CustomerDebtSummary
    {
        public int CustomerID { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public int TotalDebts { get; set; }
        public decimal TotalAmount { get; set; }
        public int OverdueDebts { get; set; }
        public decimal OverdueAmount { get; set; }
        public DateTime OldestDebtDate { get; set; }
    }

    #endregion
}