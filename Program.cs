using System;
using System.IO;
using System.Windows.Forms;
using System.Globalization;
using System.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using AredooPOS.Database;
using AredooPOS.Core;
using AredooPOS.UI;
using AredooPOS.Compatibility;
using AredooPOS.Printing;
using AredooPOS.Barcode;

namespace AredooPOS
{
    /// <summary>
    /// نقطة دخول التطبيق الرئيسية
    /// تحتوي على تهيئة النظام وإعداد الخدمات
    /// </summary>
    internal static class Program
    {
        #region المتغيرات الثابتة

        private static IServiceProvider _serviceProvider;
        private static ILogger<Program> _logger;
        private static IConfiguration _configuration;

        #endregion

        #region نقطة الدخول الرئيسية

        /// <summary>
        /// نقطة دخول التطبيق الرئيسية
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // تهيئة التطبيق الأساسية
                InitializeApplication();

                // تهيئة التكوين
                InitializeConfiguration();

                // تهيئة نظام التسجيل
                InitializeLogging();

                // تهيئة الخدمات
                InitializeServices();

                // تهيئة قاعدة البيانات
                InitializeDatabase();

                // تشغيل التطبيق
                RunApplication();
            }
            catch (Exception ex)
            {
                // معالجة الأخطاء الحرجة
                HandleCriticalError(ex);
            }
            finally
            {
                // تنظيف الموارد
                CleanupResources();
            }
        }

        #endregion

        #region تهيئة التطبيق

        /// <summary>
        /// تهيئة إعدادات التطبيق الأساسية
        /// </summary>
        private static void InitializeApplication()
        {
            // تفعيل الأنماط البصرية
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // تعيين الثقافة العربية
            var arabicCulture = new CultureInfo("ar-SA");
            Thread.CurrentThread.CurrentUICulture = arabicCulture;
            Thread.CurrentThread.CurrentCulture = arabicCulture;
            CultureInfo.DefaultThreadCurrentCulture = arabicCulture;
            CultureInfo.DefaultThreadCurrentUICulture = arabicCulture;

            // تعيين مجلد العمل
            var appDirectory = Path.GetDirectoryName(Application.ExecutablePath);
            Directory.SetCurrentDirectory(appDirectory);

            // معالج الأخطاء غير المعالجة
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            Application.ThreadException += Application_ThreadException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
        }

        /// <summary>
        /// تهيئة ملف التكوين
        /// </summary>
        private static void InitializeConfiguration()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{Environment.MachineName}.json", optional: true)
                .AddEnvironmentVariables();

            _configuration = builder.Build();
        }

        /// <summary>
        /// تهيئة نظام التسجيل
        /// </summary>
        private static void InitializeLogging()
        {
            // إنشاء مجلد السجلات
            var logPath = Path.Combine(Directory.GetCurrentDirectory(), "Logs");
            if (!Directory.Exists(logPath))
            {
                Directory.CreateDirectory(logPath);
            }

            // تكوين Serilog
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(_configuration)
                .Enrich.FromLogContext()
                .WriteTo.File(
                    path: Path.Combine(logPath, "aredoo-pos-.log"),
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 30,
                    fileSizeLimitBytes: 10 * 1024 * 1024, // 10 MB
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
                .CreateLogger();

            Log.Information("تم بدء تشغيل نظام أريدوو لنقاط البيع");
        }

        /// <summary>
        /// تهيئة حاوي الخدمات
        /// </summary>
        private static void InitializeServices()
        {
            var services = new ServiceCollection();

            // إضافة التكوين
            services.AddSingleton(_configuration);

            // إضافة نظام التسجيل
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddSerilog();
            });

            // إضافة خدمات قاعدة البيانات
            services.AddSingleton<DatabaseManager>(provider =>
            {
                var logger = provider.GetService<ILogger<DatabaseManager>>();
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                return new DatabaseManager(connectionString, logger);
            });

            services.AddSingleton<SyncManager>();

            // إضافة خدمات نقاط البيع
            services.AddSingleton<POSManager>();
            services.AddSingleton<CashRegisterManager>();
            services.AddSingleton<DebtManager>();

            // إضافة خدمات الطباعة
            services.AddSingleton<ThermalPrintManager>(provider =>
            {
                var logger = provider.GetService<ILogger<ThermalPrintManager>>();
                var settings = new ThermalPrinterSettings(); // يمكن تحميلها من التكوين
                return new ThermalPrintManager(settings, logger);
            });

            // إضافة خدمات الباركود
            services.AddSingleton<BarcodeManager>(provider =>
            {
                var logger = provider.GetService<ILogger<BarcodeManager>>();
                var settings = new BarcodeSettings(); // يمكن تحميلها من التكوين
                return new BarcodeManager(settings, logger);
            });

            // إضافة خدمات التوافق
            services.AddSingleton(provider =>
            {
                var logger = provider.GetService<ILogger<WindowsCompatibilityManager>>();
                return WindowsCompatibilityManager.Instance;
            });

            // إضافة النماذج
            services.AddTransient<MainForm>();

            // بناء مزود الخدمات
            _serviceProvider = services.BuildServiceProvider();

            // الحصول على المسجل
            _logger = _serviceProvider.GetService<ILogger<Program>>();
            _logger?.LogInformation("تم تهيئة الخدمات بنجاح");
        }

        /// <summary>
        /// تهيئة قاعدة البيانات
        /// </summary>
        private static void InitializeDatabase()
        {
            try
            {
                var databaseManager = _serviceProvider.GetService<DatabaseManager>();

                // اختبار الاتصال
                var connectionResult = databaseManager.TestConnectionAsync().Result;
                if (connectionResult)
                {
                    _logger?.LogInformation("تم الاتصال بقاعدة البيانات بنجاح");

                    // تهيئة قاعدة البيانات
                    var initResult = databaseManager.InitializeDatabaseAsync().Result;
                    if (initResult)
                    {
                        _logger?.LogInformation("تم تهيئة قاعدة البيانات بنجاح");
                    }
                    else
                    {
                        throw new Exception("فشل في تهيئة قاعدة البيانات");
                    }
                }
                else
                {
                    throw new Exception("فشل في الاتصال بقاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تهيئة قاعدة البيانات");

                // عرض رسالة خطأ للمستخدم
                var result = MessageBox.Show(
                    $"فشل في الاتصال بقاعدة البيانات:\n\n{ex.Message}\n\nهل تريد المتابعة بدون قاعدة البيانات؟",
                    "خطأ في قاعدة البيانات",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.No)
                {
                    throw;
                }
            }
        }

        /// <summary>
        /// تشغيل التطبيق الرئيسي
        /// </summary>
        private static void RunApplication()
        {
            _logger?.LogInformation("بدء تشغيل الواجهة الرئيسية");

            try
            {
                // فحص التوافق
                var compatibilityManager = _serviceProvider.GetService<WindowsCompatibilityManager>();
                if (!compatibilityManager.IsCompatible)
                {
                    var criticalIssues = compatibilityManager.Issues.FindAll(i => i.Severity == IssueSeverity.Critical);
                    if (criticalIssues.Count > 0)
                    {
                        var message = "تم اكتشاف مشاكل حرجة في التوافق:\n\n";
                        foreach (var issue in criticalIssues)
                        {
                            message += $"• {issue.Title}: {issue.Description}\n";
                        }

                        MessageBox.Show(message, "مشاكل التوافق", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                }

                // إنشاء النموذج الرئيسي
                var mainForm = CreateMainForm();

                if (mainForm != null)
                {
                    // تطبيق التخطيط العربي
                    ArabicLayoutManager.ApplyArabicLayout(mainForm);
                    ArabicResourceManager.ApplyArabicTexts(mainForm);

                    // تشغيل التطبيق
                    Application.Run(mainForm);
                }
                else
                {
                    throw new Exception("فشل في إنشاء النموذج الرئيسي");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تشغيل التطبيق");
                throw;
            }
        }

        /// <summary>
        /// إنشاء النموذج الرئيسي
        /// </summary>
        /// <returns>النموذج الرئيسي</returns>
        private static Form CreateMainForm()
        {
            try
            {
                // محاولة الحصول على النموذج من الخدمات
                var mainForm = _serviceProvider.GetService<MainForm>();
                if (mainForm != null)
                {
                    return mainForm;
                }

                // إنشاء نموذج بسيط إذا لم يكن متوفراً
                return CreateSimpleMainForm();
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "فشل في إنشاء النموذج المخصص، سيتم إنشاء نموذج بسيط");
                return CreateSimpleMainForm();
            }
        }

        /// <summary>
        /// إنشاء نموذج رئيسي بسيط
        /// </summary>
        /// <returns>النموذج البسيط</returns>
        private static Form CreateSimpleMainForm()
        {
            var form = new Form
            {
                Text = "أريدو POS - نظام نقاط البيع",
                Size = new System.Drawing.Size(1024, 768),
                StartPosition = FormStartPosition.CenterScreen,
                WindowState = FormWindowState.Maximized
            };

            // إضافة شريط القوائم
            var menuStrip = new MenuStrip();

            // قائمة ملف
            var fileMenu = new ToolStripMenuItem("ملف");
            fileMenu.DropDownItems.Add("خروج", null, (s, e) => Application.Exit());

            // قائمة المساعدة
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add("حول البرنامج", null, ShowAboutDialog);

            menuStrip.Items.AddRange(new ToolStripItem[] { fileMenu, helpMenu });

            // إضافة رسالة ترحيب
            var welcomeLabel = new Label
            {
                Text = "مرحباً بك في نظام أريدو POS\n\nالنظام جاهز للاستخدام",
                Font = new System.Drawing.Font("Tahoma", 16, System.Drawing.FontStyle.Bold),
                TextAlign = System.Drawing.ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                ForeColor = System.Drawing.Color.DarkBlue
            };

            form.MainMenuStrip = menuStrip;
            form.Controls.Add(menuStrip);
            form.Controls.Add(welcomeLabel);

            return form;
        }

        /// <summary>
        /// عرض نافذة حول البرنامج
        /// </summary>
        private static void ShowAboutDialog(object sender, EventArgs e)
        {
            MessageBox.Show(
                "نظام أريدو POS\n" +
                "الإصدار 1.0.0\n\n" +
                "نظام شامل لإدارة نقاط البيع\n" +
                "يدعم اللغة العربية والطابعات الحرارية\n\n" +
                "© 2024 جميع الحقوق محفوظة",
                "حول البرنامج",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        #endregion

        #region معالجة الأخطاء

        /// <summary>
        /// معالج أخطاء الخيوط
        /// </summary>
        private static void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
        {
            _logger?.LogError(e.Exception, "خطأ غير معالج في الخيط");

            var result = MessageBox.Show(
                $"حدث خطأ غير متوقع:\n\n{e.Exception.Message}\n\nهل تريد المتابعة؟",
                "خطأ في النظام",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Error);

            if (result == DialogResult.No)
            {
                Application.Exit();
            }
        }

        /// <summary>
        /// معالج الأخطاء غير المعالجة في النطاق
        /// </summary>
        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var exception = e.ExceptionObject as Exception;
            _logger?.LogCritical(exception, "خطأ حرج غير معالج");

            MessageBox.Show(
                $"حدث خطأ حرج في النظام:\n\n{exception?.Message}\n\nسيتم إغلاق التطبيق.",
                "خطأ حرج",
                MessageBoxButtons.OK,
                MessageBoxIcon.Stop);

            Environment.Exit(1);
        }

        /// <summary>
        /// معالجة الأخطاء الحرجة
        /// </summary>
        /// <param name="ex">الاستثناء</param>
        private static void HandleCriticalError(Exception ex)
        {
            try
            {
                // محاولة تسجيل الخطأ
                _logger?.LogCritical(ex, "خطأ حرج في بدء التشغيل");
            }
            catch
            {
                // تجاهل أخطاء التسجيل
            }

            // إظهار رسالة للمستخدم
            MessageBox.Show(
                $"فشل في بدء تشغيل النظام:\n\n{ex.Message}\n\nيرجى التواصل مع الدعم الفني.",
                "خطأ في بدء التشغيل",
                MessageBoxButtons.OK,
                MessageBoxIcon.Stop);

            Environment.Exit(1);
        }

        #endregion

        #region تنظيف الموارد

        /// <summary>
        /// تنظيف موارد التطبيق
        /// </summary>
        private static void CleanupResources()
        {
            try
            {
                _logger?.LogInformation("بدء تنظيف موارد التطبيق");

                // تنظيف مزود الخدمات
                if (_serviceProvider is IDisposable disposableServiceProvider)
                {
                    disposableServiceProvider.Dispose();
                }

                // إغلاق نظام التسجيل
                Log.CloseAndFlush();
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء التنظيف
                Console.WriteLine($"خطأ في تنظيف الموارد: {ex.Message}");
            }
        }

        #endregion
    }
}
