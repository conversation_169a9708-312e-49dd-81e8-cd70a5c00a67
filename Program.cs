using System;
using System.IO;
using System.Windows.Forms;
using System.Globalization;
using System.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using AredooPOS.Core.Database;
using AredooPOS.Forms;

namespace AredooPOS
{
    /// <summary>
    /// نقطة دخول التطبيق الرئيسية
    /// تحتوي على تهيئة النظام وإعداد الخدمات
    /// </summary>
    internal static class Program
    {
        #region المتغيرات الثابتة

        private static IServiceProvider _serviceProvider;
        private static ILogger<Program> _logger;
        private static IConfiguration _configuration;

        #endregion

        #region نقطة الدخول الرئيسية

        /// <summary>
        /// نقطة دخول التطبيق الرئيسية
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // تهيئة التطبيق الأساسية
                InitializeApplication();

                // تهيئة التكوين
                InitializeConfiguration();

                // تهيئة نظام التسجيل
                InitializeLogging();

                // تهيئة الخدمات
                InitializeServices();

                // تهيئة قاعدة البيانات
                InitializeDatabase();

                // تشغيل التطبيق
                RunApplication();
            }
            catch (Exception ex)
            {
                // معالجة الأخطاء الحرجة
                HandleCriticalError(ex);
            }
            finally
            {
                // تنظيف الموارد
                CleanupResources();
            }
        }

        #endregion

        #region تهيئة التطبيق

        /// <summary>
        /// تهيئة إعدادات التطبيق الأساسية
        /// </summary>
        private static void InitializeApplication()
        {
            // تفعيل الأنماط البصرية
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // تعيين الثقافة العربية
            var arabicCulture = new CultureInfo("ar-SA");
            Thread.CurrentThread.CurrentUICulture = arabicCulture;
            Thread.CurrentThread.CurrentCulture = arabicCulture;
            CultureInfo.DefaultThreadCurrentCulture = arabicCulture;
            CultureInfo.DefaultThreadCurrentUICulture = arabicCulture;

            // تعيين مجلد العمل
            var appDirectory = Path.GetDirectoryName(Application.ExecutablePath);
            Directory.SetCurrentDirectory(appDirectory);

            // معالج الأخطاء غير المعالجة
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            Application.ThreadException += Application_ThreadException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
        }

        /// <summary>
        /// تهيئة ملف التكوين
        /// </summary>
        private static void InitializeConfiguration()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{Environment.MachineName}.json", optional: true)
                .AddEnvironmentVariables();

            _configuration = builder.Build();
        }

        /// <summary>
        /// تهيئة نظام التسجيل
        /// </summary>
        private static void InitializeLogging()
        {
            // إنشاء مجلد السجلات
            var logPath = Path.Combine(Directory.GetCurrentDirectory(), "Logs");
            if (!Directory.Exists(logPath))
            {
                Directory.CreateDirectory(logPath);
            }

            // تكوين Serilog
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(_configuration)
                .Enrich.FromLogContext()
                .WriteTo.File(
                    path: Path.Combine(logPath, "aredoo-pos-.log"),
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 30,
                    fileSizeLimitBytes: 10 * 1024 * 1024, // 10 MB
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
                .CreateLogger();

            Log.Information("تم بدء تشغيل نظام أريدوو لنقاط البيع");
        }

        /// <summary>
        /// تهيئة حاوي الخدمات
        /// </summary>
        private static void InitializeServices()
        {
            var services = new ServiceCollection();

            // إضافة التكوين
            services.AddSingleton(_configuration);

            // إضافة نظام التسجيل
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddSerilog();
            });

            // إضافة خدمات قاعدة البيانات
            services.AddSingleton<DatabaseManager>(provider =>
            {
                var logger = provider.GetService<ILogger<DatabaseManager>>();
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                return new DatabaseManager(logger, connectionString);
            });

            // إضافة النماذج
            services.AddTransient<MainForm>();

            // بناء مزود الخدمات
            _serviceProvider = services.BuildServiceProvider();

            // الحصول على المسجل
            _logger = _serviceProvider.GetService<ILogger<Program>>();
            _logger?.LogInformation("تم تهيئة الخدمات بنجاح");
        }

        /// <summary>
        /// تهيئة قاعدة البيانات
        /// </summary>
        private static void InitializeDatabase()
        {
            try
            {
                var databaseManager = _serviceProvider.GetService<DatabaseManager>();

                if (databaseManager.TestConnection())
                {
                    _logger?.LogInformation("تم الاتصال بقاعدة البيانات بنجاح");
                }
                else
                {
                    throw new Exception("فشل في الاتصال بقاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تهيئة قاعدة البيانات");
                throw;
            }
        }

        /// <summary>
        /// تشغيل التطبيق الرئيسي
        /// </summary>
        private static void RunApplication()
        {
            _logger?.LogInformation("بدء تشغيل الواجهة الرئيسية");

            // إنشاء النموذج الرئيسي
            var mainForm = _serviceProvider.GetService<MainForm>();

            if (mainForm != null)
            {
                Application.Run(mainForm);
            }
            else
            {
                throw new Exception("فشل في إنشاء النموذج الرئيسي");
            }
        }

        #endregion

        #region معالجة الأخطاء

        /// <summary>
        /// معالج أخطاء الخيوط
        /// </summary>
        private static void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
        {
            _logger?.LogError(e.Exception, "خطأ غير معالج في الخيط");

            var result = MessageBox.Show(
                $"حدث خطأ غير متوقع:\n\n{e.Exception.Message}\n\nهل تريد المتابعة؟",
                "خطأ في النظام",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Error);

            if (result == DialogResult.No)
            {
                Application.Exit();
            }
        }

        /// <summary>
        /// معالج الأخطاء غير المعالجة في النطاق
        /// </summary>
        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var exception = e.ExceptionObject as Exception;
            _logger?.LogCritical(exception, "خطأ حرج غير معالج");

            MessageBox.Show(
                $"حدث خطأ حرج في النظام:\n\n{exception?.Message}\n\nسيتم إغلاق التطبيق.",
                "خطأ حرج",
                MessageBoxButtons.OK,
                MessageBoxIcon.Stop);

            Environment.Exit(1);
        }

        /// <summary>
        /// معالجة الأخطاء الحرجة
        /// </summary>
        /// <param name="ex">الاستثناء</param>
        private static void HandleCriticalError(Exception ex)
        {
            try
            {
                // محاولة تسجيل الخطأ
                _logger?.LogCritical(ex, "خطأ حرج في بدء التشغيل");
            }
            catch
            {
                // تجاهل أخطاء التسجيل
            }

            // إظهار رسالة للمستخدم
            MessageBox.Show(
                $"فشل في بدء تشغيل النظام:\n\n{ex.Message}\n\nيرجى التواصل مع الدعم الفني.",
                "خطأ في بدء التشغيل",
                MessageBoxButtons.OK,
                MessageBoxIcon.Stop);

            Environment.Exit(1);
        }

        #endregion

        #region تنظيف الموارد

        /// <summary>
        /// تنظيف موارد التطبيق
        /// </summary>
        private static void CleanupResources()
        {
            try
            {
                _logger?.LogInformation("بدء تنظيف موارد التطبيق");

                // تنظيف مزود الخدمات
                if (_serviceProvider is IDisposable disposableServiceProvider)
                {
                    disposableServiceProvider.Dispose();
                }

                // إغلاق نظام التسجيل
                Log.CloseAndFlush();
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء التنظيف
                Console.WriteLine($"خطأ في تنظيف الموارد: {ex.Message}");
            }
        }

        #endregion
    }
}
