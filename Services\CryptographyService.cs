using System;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Services
{
    /// <summary>
    /// خدمة التشفير والحماية
    /// توفر خدمات التشفير وإدارة كلمات المرور والأمان
    /// </summary>
    public class CryptographyService
    {
        #region المتغيرات والخصائص الخاصة

        private readonly ILogger<CryptographyService> _logger;
        
        // إعدادات التشفير
        private const int SaltSize = 32; // 256 bits
        private const int HashSize = 32; // 256 bits
        private const int Iterations = 10000; // PBKDF2 iterations

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ خدمة التشفير
        /// </summary>
        /// <param name="logger">مسجل الأحداث</param>
        public CryptographyService(ILogger<CryptographyService> logger = null)
        {
            _logger = logger;
        }

        #endregion

        #region تشفير كلمات المرور

        /// <summary>
        /// توليد ملح عشوائي آمن
        /// </summary>
        /// <returns>الملح المُولد</returns>
        public string GenerateSalt()
        {
            try
            {
                using var rng = RandomNumberGenerator.Create();
                var saltBytes = new byte[SaltSize];
                rng.GetBytes(saltBytes);
                return Convert.ToBase64String(saltBytes);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في توليد الملح");
                throw;
            }
        }

        /// <summary>
        /// تشفير كلمة المرور باستخدام PBKDF2
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <param name="salt">الملح</param>
        /// <returns>كلمة المرور المشفرة</returns>
        public string HashPassword(string password, string salt)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(password))
                    throw new ArgumentException("كلمة المرور لا يمكن أن تكون فارغة");

                if (string.IsNullOrWhiteSpace(salt))
                    throw new ArgumentException("الملح لا يمكن أن يكون فارغ");

                var saltBytes = Convert.FromBase64String(salt);
                
                using var pbkdf2 = new Rfc2898DeriveBytes(password, saltBytes, Iterations, HashAlgorithmName.SHA256);
                var hashBytes = pbkdf2.GetBytes(HashSize);
                
                return Convert.ToBase64String(hashBytes);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تشفير كلمة المرور");
                throw;
            }
        }

        /// <summary>
        /// التحقق من كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور المدخلة</param>
        /// <param name="hash">الهاش المحفوظ</param>
        /// <param name="salt">الملح المحفوظ</param>
        /// <returns>true إذا كانت كلمة المرور صحيحة</returns>
        public bool VerifyPassword(string password, string hash, string salt)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(password) || string.IsNullOrWhiteSpace(hash) || string.IsNullOrWhiteSpace(salt))
                    return false;

                var computedHash = HashPassword(password, salt);
                return SecureStringCompare(computedHash, hash);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في التحقق من كلمة المرور");
                return false;
            }
        }

        /// <summary>
        /// توليد كلمة مرور عشوائية آمنة
        /// </summary>
        /// <param name="length">طول كلمة المرور</param>
        /// <param name="includeSpecialChars">تضمين الرموز الخاصة</param>
        /// <returns>كلمة المرور المُولدة</returns>
        public string GenerateSecurePassword(int length = 12, bool includeSpecialChars = true)
        {
            try
            {
                if (length < 8)
                    throw new ArgumentException("طول كلمة المرور يجب أن يكون 8 أحرف على الأقل");

                const string upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
                const string lowerCase = "abcdefghijklmnopqrstuvwxyz";
                const string digits = "0123456789";
                const string specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";

                var allChars = upperCase + lowerCase + digits;
                if (includeSpecialChars)
                    allChars += specialChars;

                using var rng = RandomNumberGenerator.Create();
                var password = new StringBuilder();

                // ضمان وجود حرف واحد على الأقل من كل نوع
                password.Append(GetRandomChar(upperCase, rng));
                password.Append(GetRandomChar(lowerCase, rng));
                password.Append(GetRandomChar(digits, rng));
                
                if (includeSpecialChars)
                    password.Append(GetRandomChar(specialChars, rng));

                // إكمال باقي الأحرف عشوائياً
                var remainingLength = length - password.Length;
                for (int i = 0; i < remainingLength; i++)
                {
                    password.Append(GetRandomChar(allChars, rng));
                }

                // خلط الأحرف
                return ShuffleString(password.ToString(), rng);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في توليد كلمة مرور آمنة");
                throw;
            }
        }

        #endregion

        #region تشفير البيانات

        /// <summary>
        /// تشفير نص باستخدام AES
        /// </summary>
        /// <param name="plainText">النص المراد تشفيره</param>
        /// <param name="key">مفتاح التشفير</param>
        /// <returns>النص المشفر</returns>
        public string EncryptText(string plainText, string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(plainText))
                    return string.Empty;

                if (string.IsNullOrWhiteSpace(key))
                    throw new ArgumentException("مفتاح التشفير مطلوب");

                using var aes = Aes.Create();
                aes.Key = DeriveKeyFromPassword(key, aes.KeySize / 8);
                aes.GenerateIV();

                using var encryptor = aes.CreateEncryptor();
                var plainBytes = Encoding.UTF8.GetBytes(plainText);
                var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);

                // دمج IV مع البيانات المشفرة
                var result = new byte[aes.IV.Length + encryptedBytes.Length];
                Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
                Array.Copy(encryptedBytes, 0, result, aes.IV.Length, encryptedBytes.Length);

                return Convert.ToBase64String(result);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تشفير النص");
                throw;
            }
        }

        /// <summary>
        /// فك تشفير نص باستخدام AES
        /// </summary>
        /// <param name="encryptedText">النص المشفر</param>
        /// <param name="key">مفتاح فك التشفير</param>
        /// <returns>النص الأصلي</returns>
        public string DecryptText(string encryptedText, string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(encryptedText))
                    return string.Empty;

                if (string.IsNullOrWhiteSpace(key))
                    throw new ArgumentException("مفتاح فك التشفير مطلوب");

                var encryptedBytes = Convert.FromBase64String(encryptedText);

                using var aes = Aes.Create();
                aes.Key = DeriveKeyFromPassword(key, aes.KeySize / 8);

                // استخراج IV من البيانات
                var iv = new byte[aes.BlockSize / 8];
                var cipherText = new byte[encryptedBytes.Length - iv.Length];
                
                Array.Copy(encryptedBytes, 0, iv, 0, iv.Length);
                Array.Copy(encryptedBytes, iv.Length, cipherText, 0, cipherText.Length);
                
                aes.IV = iv;

                using var decryptor = aes.CreateDecryptor();
                var decryptedBytes = decryptor.TransformFinalBlock(cipherText, 0, cipherText.Length);

                return Encoding.UTF8.GetString(decryptedBytes);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فك تشفير النص");
                throw;
            }
        }

        #endregion

        #region التوقيع الرقمي والتحقق

        /// <summary>
        /// إنشاء توقيع رقمي للبيانات
        /// </summary>
        /// <param name="data">البيانات المراد توقيعها</param>
        /// <param name="privateKey">المفتاح الخاص</param>
        /// <returns>التوقيع الرقمي</returns>
        public string CreateDigitalSignature(string data, string privateKey)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(data))
                    throw new ArgumentException("البيانات المراد توقيعها مطلوبة");

                using var rsa = RSA.Create();
                rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey), out _);

                var dataBytes = Encoding.UTF8.GetBytes(data);
                var signatureBytes = rsa.SignData(dataBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);

                return Convert.ToBase64String(signatureBytes);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنشاء التوقيع الرقمي");
                throw;
            }
        }

        /// <summary>
        /// التحقق من التوقيع الرقمي
        /// </summary>
        /// <param name="data">البيانات الأصلية</param>
        /// <param name="signature">التوقيع الرقمي</param>
        /// <param name="publicKey">المفتاح العام</param>
        /// <returns>true إذا كان التوقيع صحيح</returns>
        public bool VerifyDigitalSignature(string data, string signature, string publicKey)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(data) || string.IsNullOrWhiteSpace(signature) || string.IsNullOrWhiteSpace(publicKey))
                    return false;

                using var rsa = RSA.Create();
                rsa.ImportRSAPublicKey(Convert.FromBase64String(publicKey), out _);

                var dataBytes = Encoding.UTF8.GetBytes(data);
                var signatureBytes = Convert.FromBase64String(signature);

                return rsa.VerifyData(dataBytes, signatureBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في التحقق من التوقيع الرقمي");
                return false;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// مقارنة آمنة للنصوص (مقاومة لهجمات التوقيت)
        /// </summary>
        /// <param name="a">النص الأول</param>
        /// <param name="b">النص الثاني</param>
        /// <returns>true إذا كان النصان متطابقان</returns>
        private bool SecureStringCompare(string a, string b)
        {
            if (a == null || b == null)
                return false;

            if (a.Length != b.Length)
                return false;

            var result = 0;
            for (int i = 0; i < a.Length; i++)
            {
                result |= a[i] ^ b[i];
            }

            return result == 0;
        }

        /// <summary>
        /// الحصول على حرف عشوائي من مجموعة أحرف
        /// </summary>
        /// <param name="chars">مجموعة الأحرف</param>
        /// <param name="rng">مولد الأرقام العشوائية</param>
        /// <returns>الحرف المختار</returns>
        private char GetRandomChar(string chars, RandomNumberGenerator rng)
        {
            var randomBytes = new byte[4];
            rng.GetBytes(randomBytes);
            var randomValue = BitConverter.ToUInt32(randomBytes, 0);
            return chars[(int)(randomValue % chars.Length)];
        }

        /// <summary>
        /// خلط أحرف النص عشوائياً
        /// </summary>
        /// <param name="input">النص المراد خلطه</param>
        /// <param name="rng">مولد الأرقام العشوائية</param>
        /// <returns>النص المخلوط</returns>
        private string ShuffleString(string input, RandomNumberGenerator rng)
        {
            var chars = input.ToCharArray();
            
            for (int i = chars.Length - 1; i > 0; i--)
            {
                var randomBytes = new byte[4];
                rng.GetBytes(randomBytes);
                var randomIndex = (int)(BitConverter.ToUInt32(randomBytes, 0) % (i + 1));
                
                // تبديل الأحرف
                (chars[i], chars[randomIndex]) = (chars[randomIndex], chars[i]);
            }
            
            return new string(chars);
        }

        /// <summary>
        /// اشتقاق مفتاح من كلمة مرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <param name="keyLength">طول المفتاح المطلوب</param>
        /// <returns>المفتاح المشتق</returns>
        private byte[] DeriveKeyFromPassword(string password, int keyLength)
        {
            // استخدام ملح ثابت للتشفير العام (في التطبيق الحقيقي يجب استخدام ملح فريد)
            var salt = Encoding.UTF8.GetBytes("AredooPOS_Salt_2024");
            
            using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, Iterations, HashAlgorithmName.SHA256);
            return pbkdf2.GetBytes(keyLength);
        }

        #endregion

        #region فحص قوة كلمة المرور

        /// <summary>
        /// فحص قوة كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <returns>نتيجة فحص قوة كلمة المرور</returns>
        public PasswordStrengthResult CheckPasswordStrength(string password)
        {
            var result = new PasswordStrengthResult();

            if (string.IsNullOrWhiteSpace(password))
            {
                result.Score = 0;
                result.Strength = PasswordStrength.VeryWeak;
                result.Issues.Add("كلمة المرور فارغة");
                return result;
            }

            var score = 0;
            var issues = result.Issues;

            // فحص الطول
            if (password.Length >= 8)
                score += 1;
            else
                issues.Add("كلمة المرور قصيرة جداً (أقل من 8 أحرف)");

            if (password.Length >= 12)
                score += 1;

            // فحص الأحرف الكبيرة
            if (password.Any(char.IsUpper))
                score += 1;
            else
                issues.Add("لا تحتوي على أحرف كبيرة");

            // فحص الأحرف الصغيرة
            if (password.Any(char.IsLower))
                score += 1;
            else
                issues.Add("لا تحتوي على أحرف صغيرة");

            // فحص الأرقام
            if (password.Any(char.IsDigit))
                score += 1;
            else
                issues.Add("لا تحتوي على أرقام");

            // فحص الرموز الخاصة
            if (password.Any(c => !char.IsLetterOrDigit(c)))
                score += 1;
            else
                issues.Add("لا تحتوي على رموز خاصة");

            // فحص التنوع
            var uniqueChars = password.Distinct().Count();
            if (uniqueChars >= password.Length * 0.7)
                score += 1;
            else
                issues.Add("تحتوي على أحرف متكررة كثيرة");

            result.Score = score;
            result.Strength = score switch
            {
                0 or 1 => PasswordStrength.VeryWeak,
                2 or 3 => PasswordStrength.Weak,
                4 or 5 => PasswordStrength.Medium,
                6 => PasswordStrength.Strong,
                _ => PasswordStrength.VeryStrong
            };

            return result;
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// نتيجة فحص قوة كلمة المرور
    /// </summary>
    public class PasswordStrengthResult
    {
        public int Score { get; set; }
        public PasswordStrength Strength { get; set; }
        public List<string> Issues { get; set; } = new List<string>();
        public bool IsStrong => Strength >= PasswordStrength.Strong;
    }

    /// <summary>
    /// مستويات قوة كلمة المرور
    /// </summary>
    public enum PasswordStrength
    {
        VeryWeak = 0,
        Weak = 1,
        Medium = 2,
        Strong = 3,
        VeryStrong = 4
    }

    #endregion
}
