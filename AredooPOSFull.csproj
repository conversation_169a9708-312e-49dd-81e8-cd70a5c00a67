<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net472</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <AssemblyTitle>أريدو POS - النظام الكامل</AssemblyTitle>
    <AssemblyDescription>نظام شامل ومتقدم لإدارة نقاط البيع مع دعم كامل للغة العربية والطابعات الحرارية</AssemblyDescription>
    <AssemblyCompany>أريدو للتقنية</AssemblyCompany>
    <AssemblyProduct>أريدو POS - النظام الكامل</AssemblyProduct>
    <AssemblyCopyright>© 2024 أريدو للتقنية. جميع الحقوق محفوظة.</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <StartupObject>AredooPOS.FullSystem.FullSystemProgram</StartupObject>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <Deterministic>true</Deterministic>
    <LangVersion>latest</LangVersion>
    <Nullable>disable</Nullable>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <ApplicationIcon>Assets\aredoo-icon.ico</ApplicationIcon>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>false</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <!-- المكتبات المطلوبة -->
  <ItemGroup>
    <!-- إدارة التكوين والحقن -->
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    
    <!-- نظام التسجيل -->
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="7.0.0" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="7.0.1" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    
    <!-- قاعدة البيانات -->
    <PackageReference Include="System.Data.SqlClient" Version="4.8.5" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.1" />
    
    <!-- الرسوميات والواجهات -->
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />
    
    <!-- الاتصالات والأجهزة -->
    <PackageReference Include="System.IO.Ports" Version="7.0.0" />
    <PackageReference Include="System.Management" Version="7.0.2" />
    
    <!-- معالجة البيانات -->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <!-- مراجع النظام -->
  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Security" />
    <Reference Include="System.Configuration" />
  </ItemGroup>

  <!-- ملفات الكود -->
  <ItemGroup>
    <!-- البرنامج الرئيسي -->
    <Compile Include="FullSystemProgram.cs" />

    <!-- المكونات المبسطة -->
    <Compile Include="SimplifiedComponents.cs" />

    <!-- النماذج -->
    <Compile Include="Forms\MainFormAdvanced.cs" />

    <!-- الواجهة العربية -->
    <Compile Include="UI\ArabicLayoutManager.cs" />
    <Compile Include="UI\ArabicResourceManager.cs" />

    <!-- التوافق -->
    <Compile Include="Compatibility\WindowsCompatibilityManager.cs" />
  </ItemGroup>

  <!-- ملفات التكوين -->
  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.*.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <DependentUpon>appsettings.json</DependentUpon>
    </None>
  </ItemGroup>

  <!-- الموارد والأصول -->
  <ItemGroup>
    <Content Include="Assets\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <!-- المجلدات -->
  <ItemGroup>
    <Folder Include="Assets\" />
    <Folder Include="Resources\" />
    <Folder Include="Logs\" />
    <Folder Include="Backups\" />
    <Folder Include="Reports\" />
    <Folder Include="Barcodes\" />
    <Folder Include="Database\" />
    <Folder Include="Temp\" />
  </ItemGroup>

  <!-- إنشاء المجلدات المطلوبة -->
  <Target Name="CreateDirectories" BeforeTargets="Build">
    <MakeDir Directories="$(OutputPath)Logs" Condition="!Exists('$(OutputPath)Logs')" />
    <MakeDir Directories="$(OutputPath)Backups" Condition="!Exists('$(OutputPath)Backups')" />
    <MakeDir Directories="$(OutputPath)Reports" Condition="!Exists('$(OutputPath)Reports')" />
    <MakeDir Directories="$(OutputPath)Barcodes" Condition="!Exists('$(OutputPath)Barcodes')" />
    <MakeDir Directories="$(OutputPath)Database" Condition="!Exists('$(OutputPath)Database')" />
    <MakeDir Directories="$(OutputPath)Temp" Condition="!Exists('$(OutputPath)Temp')" />
    <MakeDir Directories="$(OutputPath)Assets" Condition="!Exists('$(OutputPath)Assets')" />
    <MakeDir Directories="$(OutputPath)Resources" Condition="!Exists('$(OutputPath)Resources')" />
  </Target>

  <!-- إجراءات ما بعد البناء -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="echo 🎉 تم بناء نظام أريدو POS الكامل بنجاح!" />
    <Exec Command="echo 📁 الملف التنفيذي: $(OutputPath)$(AssemblyName).exe" />
    <Exec Command="echo 🚀 النظام جاهز للتشغيل والاستخدام" />
  </Target>

  <!-- معلومات إضافية للنشر -->
  <PropertyGroup>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>

</Project>
