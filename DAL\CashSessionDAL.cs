using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using AredooPOS.Models;
using Microsoft.Extensions.Logging;

namespace AredooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات لجلسات الصندوق
    /// تحتوي على جميع العمليات المتعلقة بقاعدة البيانات لجلسات الصندوق
    /// </summary>
    public class CashSessionDAL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly string _connectionString;
        private readonly ILogger<CashSessionDAL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة الوصول للبيانات لجلسات الصندوق
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public CashSessionDAL(string connectionString = null, ILogger<CashSessionDAL> logger = null)
        {
            _connectionString = connectionString ?? DatabaseConfig.GetConnectionString();
            _logger = logger;
        }

        #endregion

        #region العمليات الأساسية

        /// <summary>
        /// فتح جلسة صندوق جديدة
        /// </summary>
        /// <param name="cashSession">بيانات الجلسة</param>
        /// <returns>رقم الجلسة الجديدة</returns>
        public int OpenCashSession(CashSession cashSession)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_OpenCashSession", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@CashRegisterID", cashSession.CashRegisterID);
                command.Parameters.AddWithValue("@UserID", cashSession.UserID);
                command.Parameters.AddWithValue("@OpeningBalance", cashSession.OpeningBalance);
                command.Parameters.AddWithValue("@CreatedBy", cashSession.CreatedBy);

                var outputParam = new SqlParameter("@NewSessionID", SqlDbType.Int)
                {
                    Direction = ParameterDirection.Output
                };
                command.Parameters.Add(outputParam);

                connection.Open();
                command.ExecuteNonQuery();

                var sessionId = Convert.ToInt32(outputParam.Value);
                _logger?.LogInformation($"تم فتح جلسة جديدة برقم {sessionId} للصندوق {cashSession.CashRegisterID}");
                return sessionId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في فتح جلسة الصندوق {cashSession?.CashRegisterID}");
                throw;
            }
        }

        /// <summary>
        /// إغلاق جلسة الصندوق
        /// </summary>
        /// <param name="sessionID">رقم الجلسة</param>
        /// <param name="closingBalance">الرصيد الختامي</param>
        /// <param name="varianceReason">سبب الفرق</param>
        /// <param name="notes">ملاحظات</param>
        /// <param name="closedBy">من أغلق الجلسة</param>
        /// <returns>true إذا تم الإغلاق بنجاح</returns>
        public bool CloseCashSession(int sessionID, decimal closingBalance, string varianceReason = null, string notes = null, string closedBy = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_CloseCashSession", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@SessionID", sessionID);
                command.Parameters.AddWithValue("@ClosingBalance", closingBalance);
                command.Parameters.AddWithValue("@VarianceReason", varianceReason ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Notes", notes ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ClosedBy", closedBy);

                connection.Open();
                command.ExecuteNonQuery();

                _logger?.LogInformation($"تم إغلاق الجلسة {sessionID}");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إغلاق الجلسة {sessionID}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جلسة بالرقم
        /// </summary>
        /// <param name="sessionID">رقم الجلسة</param>
        /// <returns>بيانات الجلسة</returns>
        public CashSession GetCashSessionById(int sessionID)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("SELECT * FROM CashSessions WHERE SessionID = @SessionID", connection);

                command.Parameters.AddWithValue("@SessionID", sessionID);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapCashSessionFromReader(reader);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الجلسة {sessionID}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على الجلسة المفتوحة للصندوق
        /// </summary>
        /// <param name="cashRegisterID">رقم الصندوق</param>
        /// <returns>الجلسة المفتوحة أو null</returns>
        public CashSession GetOpenSessionForRegister(int cashRegisterID)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("SELECT * FROM CashSessions WHERE CashRegisterID = @CashRegisterID AND Status = 'Open'", connection);

                command.Parameters.AddWithValue("@CashRegisterID", cashRegisterID);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapCashSessionFromReader(reader);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الجلسة المفتوحة للصندوق {cashRegisterID}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جلسات الصندوق
        /// </summary>
        /// <param name="cashRegisterID">رقم الصندوق</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="status">حالة الجلسة</param>
        /// <returns>قائمة الجلسات</returns>
        public List<CashSession> GetCashSessions(int? cashRegisterID = null, DateTime? fromDate = null, DateTime? toDate = null, string status = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var sql = "SELECT * FROM CashSessions WHERE 1=1";
                
                if (cashRegisterID.HasValue)
                    sql += " AND CashRegisterID = @CashRegisterID";
                
                if (fromDate.HasValue)
                    sql += " AND SessionDate >= @FromDate";
                
                if (toDate.HasValue)
                    sql += " AND SessionDate <= @ToDate";
                
                if (!string.IsNullOrWhiteSpace(status))
                    sql += " AND Status = @Status";
                
                sql += " ORDER BY SessionDate DESC, OpenTime DESC";

                using var command = new SqlCommand(sql, connection);
                
                if (cashRegisterID.HasValue)
                    command.Parameters.AddWithValue("@CashRegisterID", cashRegisterID.Value);
                
                if (fromDate.HasValue)
                    command.Parameters.AddWithValue("@FromDate", fromDate.Value);
                
                if (toDate.HasValue)
                    command.Parameters.AddWithValue("@ToDate", toDate.Value);
                
                if (!string.IsNullOrWhiteSpace(status))
                    command.Parameters.AddWithValue("@Status", status);

                connection.Open();
                using var reader = command.ExecuteReader();

                var sessions = new List<CashSession>();
                while (reader.Read())
                {
                    sessions.Add(MapCashSessionFromReader(reader));
                }

                return sessions;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على جلسات الصندوق");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جلسات المستخدم
        /// </summary>
        /// <param name="userID">رقم المستخدم</param>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <returns>قائمة جلسات المستخدم</returns>
        public List<CashSession> GetUserSessions(int userID, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var sql = "SELECT * FROM CashSessions WHERE UserID = @UserID";
                
                if (fromDate.HasValue)
                    sql += " AND SessionDate >= @FromDate";
                
                if (toDate.HasValue)
                    sql += " AND SessionDate <= @ToDate";
                
                sql += " ORDER BY SessionDate DESC, OpenTime DESC";

                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@UserID", userID);
                
                if (fromDate.HasValue)
                    command.Parameters.AddWithValue("@FromDate", fromDate.Value);
                
                if (toDate.HasValue)
                    command.Parameters.AddWithValue("@ToDate", toDate.Value);

                connection.Open();
                using var reader = command.ExecuteReader();

                var sessions = new List<CashSession>();
                while (reader.Read())
                {
                    sessions.Add(MapCashSessionFromReader(reader));
                }

                return sessions;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على جلسات المستخدم {userID}");
                throw;
            }
        }

        /// <summary>
        /// تحديث إجماليات الجلسة
        /// </summary>
        /// <param name="sessionID">رقم الجلسة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateSessionTotals(int sessionID)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_UpdateSessionTotals", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@SessionID", sessionID);

                connection.Open();
                command.ExecuteNonQuery();

                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث إجماليات الجلسة {sessionID}");
                throw;
            }
        }

        /// <summary>
        /// تعليق جلسة
        /// </summary>
        /// <param name="sessionID">رقم الجلسة</param>
        /// <param name="reason">سبب التعليق</param>
        /// <param name="suspendedBy">من علق الجلسة</param>
        /// <returns>true إذا تم التعليق بنجاح</returns>
        public bool SuspendSession(int sessionID, string reason, string suspendedBy)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand(@"
                    UPDATE CashSessions 
                    SET Status = 'Suspended',
                        Notes = @Reason,
                        ModifiedBy = @SuspendedBy,
                        ModifiedDate = GETDATE()
                    WHERE SessionID = @SessionID AND Status = 'Open'", connection);

                command.Parameters.AddWithValue("@SessionID", sessionID);
                command.Parameters.AddWithValue("@Reason", reason);
                command.Parameters.AddWithValue("@SuspendedBy", suspendedBy);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم تعليق الجلسة {sessionID}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تعليق الجلسة {sessionID}");
                throw;
            }
        }

        /// <summary>
        /// استئناف جلسة معلقة
        /// </summary>
        /// <param name="sessionID">رقم الجلسة</param>
        /// <param name="resumedBy">من استأنف الجلسة</param>
        /// <returns>true إذا تم الاستئناف بنجاح</returns>
        public bool ResumeSession(int sessionID, string resumedBy)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand(@"
                    UPDATE CashSessions 
                    SET Status = 'Open',
                        ModifiedBy = @ResumedBy,
                        ModifiedDate = GETDATE()
                    WHERE SessionID = @SessionID AND Status = 'Suspended'", connection);

                command.Parameters.AddWithValue("@SessionID", sessionID);
                command.Parameters.AddWithValue("@ResumedBy", resumedBy);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم استئناف الجلسة {sessionID}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في استئناف الجلسة {sessionID}");
                throw;
            }
        }

        /// <summary>
        /// إلغاء جلسة
        /// </summary>
        /// <param name="sessionID">رقم الجلسة</param>
        /// <param name="reason">سبب الإلغاء</param>
        /// <param name="cancelledBy">من ألغى الجلسة</param>
        /// <returns>true إذا تم الإلغاء بنجاح</returns>
        public bool CancelSession(int sessionID, string reason, string cancelledBy)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand(@"
                    UPDATE CashSessions 
                    SET Status = 'Cancelled',
                        CloseTime = GETDATE(),
                        Notes = @Reason,
                        ClosedBy = @CancelledBy,
                        ModifiedBy = @CancelledBy,
                        ModifiedDate = GETDATE()
                    WHERE SessionID = @SessionID AND Status IN ('Open', 'Suspended')", connection);

                command.Parameters.AddWithValue("@SessionID", sessionID);
                command.Parameters.AddWithValue("@Reason", reason);
                command.Parameters.AddWithValue("@CancelledBy", cancelledBy);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم إلغاء الجلسة {sessionID}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إلغاء الجلسة {sessionID}");
                throw;
            }
        }

        /// <summary>
        /// موافقة المدير على الجلسة
        /// </summary>
        /// <param name="sessionID">رقم الجلسة</param>
        /// <param name="approvedBy">من وافق</param>
        /// <returns>true إذا تمت الموافقة بنجاح</returns>
        public bool ApproveSession(int sessionID, string approvedBy)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand(@"
                    UPDATE CashSessions 
                    SET ManagerApproval = 1,
                        ApprovedBy = @ApprovedBy,
                        ApprovalDate = GETDATE(),
                        ModifiedBy = @ApprovedBy,
                        ModifiedDate = GETDATE()
                    WHERE SessionID = @SessionID", connection);

                command.Parameters.AddWithValue("@SessionID", sessionID);
                command.Parameters.AddWithValue("@ApprovedBy", approvedBy);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تمت موافقة المدير على الجلسة {sessionID}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في موافقة المدير على الجلسة {sessionID}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على ملخص الجلسة
        /// </summary>
        /// <param name="sessionID">رقم الجلسة</param>
        /// <returns>ملخص الجلسة</returns>
        public DataSet GetSessionSummary(int sessionID)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetSessionSummary", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@SessionID", sessionID);

                using var adapter = new SqlDataAdapter(command);
                var dataSet = new DataSet();
                adapter.Fill(dataSet);

                return dataSet;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على ملخص الجلسة {sessionID}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على التقرير اليومي
        /// </summary>
        /// <param name="reportDate">تاريخ التقرير</param>
        /// <param name="cashRegisterID">رقم الصندوق (اختياري)</param>
        /// <returns>التقرير اليومي</returns>
        public DataSet GetDailyReport(DateTime reportDate, int? cashRegisterID = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetDailyReport", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@ReportDate", reportDate);
                command.Parameters.AddWithValue("@CashRegisterID", cashRegisterID ?? (object)DBNull.Value);

                using var adapter = new SqlDataAdapter(command);
                var dataSet = new DataSet();
                adapter.Fill(dataSet);

                return dataSet;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على التقرير اليومي {reportDate:yyyy-MM-dd}");
                throw;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// تحويل بيانات القارئ إلى كائن جلسة
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن الجلسة</returns>
        private CashSession MapCashSessionFromReader(SqlDataReader reader)
        {
            return new CashSession
            {
                SessionID = reader.GetInt32("SessionID"),
                CashRegisterID = reader.GetInt32("CashRegisterID"),
                UserID = reader.GetInt32("UserID"),
                SessionDate = reader.GetDateTime("SessionDate"),
                OpenTime = reader.GetDateTime("OpenTime"),
                CloseTime = reader.IsDBNull("CloseTime") ? null : reader.GetDateTime("CloseTime"),
                Status = reader.GetString("Status"),
                OpeningBalance = reader.GetDecimal("OpeningBalance"),
                ClosingBalance = reader.IsDBNull("ClosingBalance") ? null : reader.GetDecimal("ClosingBalance"),
                ExpectedClosingBalance = reader.IsDBNull("ExpectedClosingBalance") ? null : reader.GetDecimal("ExpectedClosingBalance"),
                CashSales = reader.GetDecimal("CashSales"),
                CardSales = reader.GetDecimal("CardSales"),
                TransferSales = reader.GetDecimal("TransferSales"),
                TotalSales = reader.GetDecimal("TotalSales"),
                CashExpenses = reader.GetDecimal("CashExpenses"),
                CashWithdrawals = reader.GetDecimal("CashWithdrawals"),
                CashDeposits = reader.GetDecimal("CashDeposits"),
                TotalTransactions = reader.GetInt32("TotalTransactions"),
                Variance = reader.IsDBNull("Variance") ? null : reader.GetDecimal("Variance"),
                VarianceReason = reader.IsDBNull("VarianceReason") ? null : reader.GetString("VarianceReason"),
                Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes"),
                ClosedBy = reader.IsDBNull("ClosedBy") ? null : reader.GetString("ClosedBy"),
                ManagerApproval = reader.GetBoolean("ManagerApproval"),
                ApprovedBy = reader.IsDBNull("ApprovedBy") ? null : reader.GetString("ApprovedBy"),
                ApprovalDate = reader.IsDBNull("ApprovalDate") ? null : reader.GetDateTime("ApprovalDate"),
                CreatedBy = reader.GetString("CreatedBy"),
                CreatedDate = reader.GetDateTime("CreatedDate"),
                ModifiedBy = reader.IsDBNull("ModifiedBy") ? null : reader.GetString("ModifiedBy"),
                ModifiedDate = reader.IsDBNull("ModifiedDate") ? null : reader.GetDateTime("ModifiedDate")
            };
        }

        #endregion
    }
}
