-- =============================================
-- نظام أريدوو للكاشير المتكامل
-- قاعدة البيانات الرئيسية الكاملة
-- SQL Server Database Script
-- الإصدار: 1.0.0
-- =============================================

USE master;
GO

-- إنشاء قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = N'AredooPOS')
BEGIN
    CREATE DATABASE [AredooPOS]
    ON (NAME = 'AredooPOS_Data', FILENAME = 'C:\AredooPOS\Data\AredooPOS.mdf', SIZE = 100MB, MAXSIZE = 1GB, FILEGROWTH = 10MB)
    LOG ON (NAME = 'AredooPOS_Log', FILENAME = 'C:\AredooPOS\Data\AredooPOS.ldf', SIZE = 10MB, MAXSIZE = 100MB, FILEGROWTH = 1MB);
END
GO

USE [AredooPOS];
GO

-- =============================================
-- جدول المستخدمين والصلاحيات
-- =============================================

-- جدول الأدوار والصلاحيات
CREATE TABLE [dbo].[Roles] (
    [RoleID] INT IDENTITY(1,1) PRIMARY KEY,
    [RoleName] NVARCHAR(50) NOT NULL UNIQUE,
    [RoleNameAr] NVARCHAR(100) NOT NULL,
    [Description] NVARCHAR(255),
    [Permissions] NVARCHAR(MAX), -- JSON format للصلاحيات
    [IsActive] BIT DEFAULT 1,
    [CreatedDate] DATETIME2 DEFAULT GETDATE(),
    [CreatedBy] INT
);

-- جدول المستخدمين
CREATE TABLE [dbo].[Users] (
    [UserID] INT IDENTITY(1,1) PRIMARY KEY,
    [Username] NVARCHAR(50) NOT NULL UNIQUE,
    [PasswordHash] NVARCHAR(255) NOT NULL,
    [PasswordSalt] NVARCHAR(255) NOT NULL,
    [FullName] NVARCHAR(100) NOT NULL,
    [Email] NVARCHAR(100),
    [Phone] NVARCHAR(20),
    [RoleID] INT NOT NULL,
    [IsActive] BIT DEFAULT 1,
    [LastLogin] DATETIME2,
    [CreatedDate] DATETIME2 DEFAULT GETDATE(),
    [CreatedBy] INT,
    [ModifiedDate] DATETIME2,
    [ModifiedBy] INT,
    FOREIGN KEY ([RoleID]) REFERENCES [Roles]([RoleID])
);

-- جدول جلسات المستخدمين
CREATE TABLE [dbo].[UserSessions] (
    [SessionID] INT IDENTITY(1,1) PRIMARY KEY,
    [UserID] INT NOT NULL,
    [LoginTime] DATETIME2 DEFAULT GETDATE(),
    [LogoutTime] DATETIME2,
    [IPAddress] NVARCHAR(45),
    [ComputerName] NVARCHAR(100),
    [IsActive] BIT DEFAULT 1,
    FOREIGN KEY ([UserID]) REFERENCES [Users]([UserID])
);

-- =============================================
-- جداول العملاء
-- =============================================

-- جدول العملاء
CREATE TABLE [dbo].[Customers] (
    [CustomerID] INT IDENTITY(1,1) PRIMARY KEY,
    [CustomerCode] NVARCHAR(20) UNIQUE,
    [FullName] NVARCHAR(100) NOT NULL,
    [Phone] NVARCHAR(20),
    [Email] NVARCHAR(100),
    [Address] NVARCHAR(255),
    [City] NVARCHAR(50),
    [NationalID] NVARCHAR(20),
    [DateOfBirth] DATE,
    [CustomerType] NVARCHAR(20) DEFAULT 'Regular', -- Regular, VIP, Wholesale
    [CreditLimit] DECIMAL(18,2) DEFAULT 0,
    [CurrentBalance] DECIMAL(18,2) DEFAULT 0,
    [LoyaltyPoints] INT DEFAULT 0,
    [IsActive] BIT DEFAULT 1,
    [Notes] NVARCHAR(500),
    [CreatedDate] DATETIME2 DEFAULT GETDATE(),
    [CreatedBy] INT,
    [ModifiedDate] DATETIME2,
    [ModifiedBy] INT
);

-- =============================================
-- جداول المنتجات والمخزون
-- =============================================

-- جدول فئات المنتجات
CREATE TABLE [dbo].[Categories] (
    [CategoryID] INT IDENTITY(1,1) PRIMARY KEY,
    [CategoryName] NVARCHAR(100) NOT NULL,
    [Description] NVARCHAR(255),
    [ParentCategoryID] INT,
    [IsActive] BIT DEFAULT 1,
    [CreatedDate] DATETIME2 DEFAULT GETDATE(),
    [CreatedBy] INT,
    FOREIGN KEY ([ParentCategoryID]) REFERENCES [Categories]([CategoryID])
);

-- جدول الموردين
CREATE TABLE [dbo].[Suppliers] (
    [SupplierID] INT IDENTITY(1,1) PRIMARY KEY,
    [SupplierName] NVARCHAR(100) NOT NULL,
    [ContactPerson] NVARCHAR(100),
    [Phone] NVARCHAR(20),
    [Email] NVARCHAR(100),
    [Address] NVARCHAR(255),
    [IsActive] BIT DEFAULT 1,
    [CreatedDate] DATETIME2 DEFAULT GETDATE(),
    [CreatedBy] INT
);

-- جدول المنتجات
CREATE TABLE [dbo].[Products] (
    [ProductID] INT IDENTITY(1,1) PRIMARY KEY,
    [ProductCode] NVARCHAR(50) UNIQUE,
    [Barcode] NVARCHAR(50) UNIQUE,
    [ProductName] NVARCHAR(200) NOT NULL,
    [ProductNameAr] NVARCHAR(200),
    [Description] NVARCHAR(500),
    [CategoryID] INT,
    [SupplierID] INT,
    [UnitOfMeasure] NVARCHAR(20) DEFAULT 'قطعة',
    [CostPrice] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [SellingPrice] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [WholesalePrice] DECIMAL(18,2) DEFAULT 0,
    [MinimumStock] INT DEFAULT 0,
    [CurrentStock] INT DEFAULT 0,
    [ReorderLevel] INT DEFAULT 0,
    [ProductImage] NVARCHAR(255),
    [IsActive] BIT DEFAULT 1,
    [HasExpiry] BIT DEFAULT 0,
    [ExpiryDate] DATE,
    [CreatedDate] DATETIME2 DEFAULT GETDATE(),
    [CreatedBy] INT,
    [ModifiedDate] DATETIME2,
    [ModifiedBy] INT,
    FOREIGN KEY ([CategoryID]) REFERENCES [Categories]([CategoryID]),
    FOREIGN KEY ([SupplierID]) REFERENCES [Suppliers]([SupplierID])
);

-- جدول حركة المخزون
CREATE TABLE [dbo].[StockMovements] (
    [MovementID] INT IDENTITY(1,1) PRIMARY KEY,
    [ProductID] INT NOT NULL,
    [MovementType] NVARCHAR(20) NOT NULL, -- IN, OUT, ADJUSTMENT, RETURN
    [Quantity] INT NOT NULL,
    [UnitCost] DECIMAL(18,2),
    [TotalCost] DECIMAL(18,2),
    [ReferenceType] NVARCHAR(20), -- PURCHASE, SALE, ADJUSTMENT, RETURN
    [ReferenceID] INT,
    [Notes] NVARCHAR(255),
    [MovementDate] DATETIME2 DEFAULT GETDATE(),
    [CreatedBy] INT,
    FOREIGN KEY ([ProductID]) REFERENCES [Products]([ProductID])
);

-- =============================================
-- جداول الفواتير والمبيعات
-- =============================================

-- جدول الفواتير
CREATE TABLE [dbo].[Invoices] (
    [InvoiceID] INT IDENTITY(1,1) PRIMARY KEY,
    [InvoiceNumber] NVARCHAR(20) UNIQUE NOT NULL,
    [InvoiceDate] DATETIME2 DEFAULT GETDATE(),
    [CustomerID] INT,
    [InvoiceType] NVARCHAR(20) DEFAULT 'SALE', -- SALE, RETURN, QUOTE
    [PaymentMethod] NVARCHAR(20) DEFAULT 'CASH', -- CASH, CARD, INSTALLMENT, MIXED
    [SubTotal] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [DiscountAmount] DECIMAL(18,2) DEFAULT 0,
    [DiscountPercentage] DECIMAL(5,2) DEFAULT 0,
    [TaxAmount] DECIMAL(18,2) DEFAULT 0,
    [TaxPercentage] DECIMAL(5,2) DEFAULT 15,
    [TotalAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [PaidAmount] DECIMAL(18,2) DEFAULT 0,
    [RemainingAmount] DECIMAL(18,2) DEFAULT 0,
    [Status] NVARCHAR(20) DEFAULT 'COMPLETED', -- DRAFT, COMPLETED, CANCELLED, RETURNED
    [Notes] NVARCHAR(500),
    [CashierID] INT NOT NULL,
    [SessionID] INT,
    [CreatedDate] DATETIME2 DEFAULT GETDATE(),
    [ModifiedDate] DATETIME2,
    [ModifiedBy] INT,
    FOREIGN KEY ([CustomerID]) REFERENCES [Customers]([CustomerID]),
    FOREIGN KEY ([CashierID]) REFERENCES [Users]([UserID]),
    FOREIGN KEY ([SessionID]) REFERENCES [UserSessions]([SessionID])
);

-- جدول تفاصيل الفواتير
CREATE TABLE [dbo].[InvoiceDetails] (
    [DetailID] INT IDENTITY(1,1) PRIMARY KEY,
    [InvoiceID] INT NOT NULL,
    [ProductID] INT NOT NULL,
    [Quantity] INT NOT NULL,
    [UnitPrice] DECIMAL(18,2) NOT NULL,
    [DiscountAmount] DECIMAL(18,2) DEFAULT 0,
    [DiscountPercentage] DECIMAL(5,2) DEFAULT 0,
    [TotalPrice] DECIMAL(18,2) NOT NULL,
    [Notes] NVARCHAR(255),
    FOREIGN KEY ([InvoiceID]) REFERENCES [Invoices]([InvoiceID]) ON DELETE CASCADE,
    FOREIGN KEY ([ProductID]) REFERENCES [Products]([ProductID])
);

-- =============================================
-- جداول الأقساط
-- =============================================

-- جدول خطط الأقساط
CREATE TABLE [dbo].[InstallmentPlans] (
    [PlanID] INT IDENTITY(1,1) PRIMARY KEY,
    [InvoiceID] INT NOT NULL,
    [CustomerID] INT NOT NULL,
    [TotalAmount] DECIMAL(18,2) NOT NULL,
    [DownPayment] DECIMAL(18,2) DEFAULT 0,
    [InstallmentAmount] DECIMAL(18,2) NOT NULL,
    [NumberOfInstallments] INT NOT NULL,
    [InstallmentFrequency] NVARCHAR(20) DEFAULT 'MONTHLY', -- WEEKLY, MONTHLY
    [StartDate] DATE NOT NULL,
    [EndDate] DATE NOT NULL,
    [Status] NVARCHAR(20) DEFAULT 'ACTIVE', -- ACTIVE, COMPLETED, CANCELLED
    [CreatedDate] DATETIME2 DEFAULT GETDATE(),
    [CreatedBy] INT,
    FOREIGN KEY ([InvoiceID]) REFERENCES [Invoices]([InvoiceID]),
    FOREIGN KEY ([CustomerID]) REFERENCES [Customers]([CustomerID])
);

-- جدول دفعات الأقساط
CREATE TABLE [dbo].[InstallmentPayments] (
    [PaymentID] INT IDENTITY(1,1) PRIMARY KEY,
    [PlanID] INT NOT NULL,
    [InstallmentNumber] INT NOT NULL,
    [DueDate] DATE NOT NULL,
    [Amount] DECIMAL(18,2) NOT NULL,
    [PaidAmount] DECIMAL(18,2) DEFAULT 0,
    [PaymentDate] DATETIME2,
    [Status] NVARCHAR(20) DEFAULT 'PENDING', -- PENDING, PAID, OVERDUE, PARTIAL
    [Notes] NVARCHAR(255),
    [ReceivedBy] INT,
    FOREIGN KEY ([PlanID]) REFERENCES [InstallmentPlans]([PlanID]),
    FOREIGN KEY ([ReceivedBy]) REFERENCES [Users]([UserID])
);

-- =============================================
-- جداول الديون
-- =============================================

-- جدول الديون
CREATE TABLE [dbo].[Debts] (
    [DebtID] INT IDENTITY(1,1) PRIMARY KEY,
    [CustomerID] INT NOT NULL,
    [InvoiceID] INT,
    [DebtType] NVARCHAR(20) DEFAULT 'INVOICE', -- INVOICE, MANUAL
    [Amount] DECIMAL(18,2) NOT NULL,
    [PaidAmount] DECIMAL(18,2) DEFAULT 0,
    [RemainingAmount] DECIMAL(18,2) NOT NULL,
    [DueDate] DATE,
    [Status] NVARCHAR(20) DEFAULT 'PENDING', -- PENDING, PAID, OVERDUE, PARTIAL
    [Description] NVARCHAR(255),
    [CreatedDate] DATETIME2 DEFAULT GETDATE(),
    [CreatedBy] INT,
    FOREIGN KEY ([CustomerID]) REFERENCES [Customers]([CustomerID]),
    FOREIGN KEY ([InvoiceID]) REFERENCES [Invoices]([InvoiceID])
);

-- جدول مدفوعات الديون
CREATE TABLE [dbo].[DebtPayments] (
    [PaymentID] INT IDENTITY(1,1) PRIMARY KEY,
    [DebtID] INT NOT NULL,
    [Amount] DECIMAL(18,2) NOT NULL,
    [PaymentMethod] NVARCHAR(20) DEFAULT 'CASH',
    [PaymentDate] DATETIME2 DEFAULT GETDATE(),
    [Notes] NVARCHAR(255),
    [ReceivedBy] INT NOT NULL,
    FOREIGN KEY ([DebtID]) REFERENCES [Debts]([DebtID]),
    FOREIGN KEY ([ReceivedBy]) REFERENCES [Users]([UserID])
);

-- =============================================
-- جداول إدارة النقدية والصندوق
-- =============================================

-- جدول جلسات الصندوق
CREATE TABLE [dbo].[CashSessions] (
    [SessionID] INT IDENTITY(1,1) PRIMARY KEY,
    [UserID] INT NOT NULL,
    [OpeningBalance] DECIMAL(18,2) NOT NULL DEFAULT 0,
    [ClosingBalance] DECIMAL(18,2),
    [TotalSales] DECIMAL(18,2) DEFAULT 0,
    [TotalExpenses] DECIMAL(18,2) DEFAULT 0,
    [TotalWithdrawals] DECIMAL(18,2) DEFAULT 0,
    [OpenTime] DATETIME2 DEFAULT GETDATE(),
    [CloseTime] DATETIME2,
    [Status] NVARCHAR(20) DEFAULT 'OPEN', -- OPEN, CLOSED
    [Notes] NVARCHAR(500),
    FOREIGN KEY ([UserID]) REFERENCES [Users]([UserID])
);

-- جدول حركات النقدية
CREATE TABLE [dbo].[CashTransactions] (
    [TransactionID] INT IDENTITY(1,1) PRIMARY KEY,
    [SessionID] INT NOT NULL,
    [TransactionType] NVARCHAR(20) NOT NULL, -- SALE, EXPENSE, WITHDRAWAL, DEPOSIT
    [Amount] DECIMAL(18,2) NOT NULL,
    [PaymentMethod] NVARCHAR(20) DEFAULT 'CASH',
    [ReferenceType] NVARCHAR(20), -- INVOICE, EXPENSE, MANUAL
    [ReferenceID] INT,
    [Description] NVARCHAR(255),
    [TransactionDate] DATETIME2 DEFAULT GETDATE(),
    [CreatedBy] INT,
    FOREIGN KEY ([SessionID]) REFERENCES [CashSessions]([SessionID])
);

-- جدول المصاريف
CREATE TABLE [dbo].[Expenses] (
    [ExpenseID] INT IDENTITY(1,1) PRIMARY KEY,
    [ExpenseCategory] NVARCHAR(50) NOT NULL,
    [Amount] DECIMAL(18,2) NOT NULL,
    [Description] NVARCHAR(255),
    [ExpenseDate] DATETIME2 DEFAULT GETDATE(),
    [PaymentMethod] NVARCHAR(20) DEFAULT 'CASH',
    [ReceiptNumber] NVARCHAR(50),
    [SessionID] INT,
    [CreatedBy] INT NOT NULL,
    FOREIGN KEY ([SessionID]) REFERENCES [CashSessions]([SessionID]),
    FOREIGN KEY ([CreatedBy]) REFERENCES [Users]([UserID])
);

-- =============================================
-- جداول الإعدادات والنظام
-- =============================================

-- جدول إعدادات النظام
CREATE TABLE [dbo].[SystemSettings] (
    [SettingID] INT IDENTITY(1,1) PRIMARY KEY,
    [SettingKey] NVARCHAR(100) NOT NULL UNIQUE,
    [SettingValue] NVARCHAR(MAX),
    [SettingType] NVARCHAR(20) DEFAULT 'STRING', -- STRING, NUMBER, BOOLEAN, JSON
    [Description] NVARCHAR(255),
    [Category] NVARCHAR(50),
    [IsUserEditable] BIT DEFAULT 1,
    [ModifiedDate] DATETIME2 DEFAULT GETDATE(),
    [ModifiedBy] INT
);

-- جدول سجل العمليات
CREATE TABLE [dbo].[ActivityLog] (
    [LogID] INT IDENTITY(1,1) PRIMARY KEY,
    [UserID] INT,
    [Action] NVARCHAR(100) NOT NULL,
    [TableName] NVARCHAR(50),
    [RecordID] INT,
    [OldValues] NVARCHAR(MAX),
    [NewValues] NVARCHAR(MAX),
    [IPAddress] NVARCHAR(45),
    [UserAgent] NVARCHAR(255),
    [CreatedDate] DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY ([UserID]) REFERENCES [Users]([UserID])
);

GO
