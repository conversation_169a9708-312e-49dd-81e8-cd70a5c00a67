using System;
using System.Drawing;
using System.Windows.Forms;
using System.Threading.Tasks;
using AredooPOS.UI;
using AredooPOS.Database;
using AredooPOS.Core;
using AredooPOS.Printing;
using AredooPOS.Barcode;
using AredooPOS.Compatibility;

namespace AredooPOS.Forms
{
    /// <summary>
    /// النموذج الرئيسي المتقدم لنظام أريدو POS
    /// </summary>
    public partial class MainFormAdvanced : Form
    {
        #region المتغيرات

        private readonly DatabaseManager _databaseManager;
        private readonly POSManager _posManager;
        private readonly ThermalPrintManager _printManager;
        private readonly BarcodeManager _barcodeManager;
        private readonly WindowsCompatibilityManager _compatibilityManager;

        private Panel _mainPanel;
        private Panel _sidePanel;
        private Panel _contentPanel;
        private StatusStrip _statusStrip;
        private MenuStrip _menuStrip;
        private ToolStrip _toolStrip;

        // أزرار التنقل السريع
        private Button _salesButton;
        private Button _inventoryButton;
        private Button _customersButton;
        private Button _reportsButton;
        private Button _settingsButton;

        // معلومات الحالة
        private ToolStripStatusLabel _statusLabel;
        private ToolStripStatusLabel _userLabel;
        private ToolStripStatusLabel _timeLabel;
        private ToolStripStatusLabel _dbStatusLabel;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ النموذج الرئيسي المتقدم
        /// </summary>
        public MainFormAdvanced(
            DatabaseManager databaseManager = null,
            POSManager posManager = null,
            ThermalPrintManager printManager = null,
            BarcodeManager barcodeManager = null)
        {
            _databaseManager = databaseManager;
            _posManager = posManager;
            _printManager = printManager;
            _barcodeManager = barcodeManager;
            _compatibilityManager = WindowsCompatibilityManager.Instance;

            InitializeComponent();
            InitializeAdvancedForm();
        }

        /// <summary>
        /// تهيئة النموذج المتقدم
        /// </summary>
        private async void InitializeAdvancedForm()
        {
            try
            {
                // تطبيق التخطيط العربي
                ArabicLayoutManager.ApplyArabicLayout(this);
                ArabicResourceManager.ApplyArabicTexts(this);

                // إعداد النموذج
                SetupFormProperties();

                // إنشاء الواجهة
                CreateAdvancedInterface();

                // تهيئة النظام
                await InitializeSystemAsync();

                // تحديث الحالة
                UpdateStatus("النظام جاهز للاستخدام");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النظام: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد خصائص النموذج
        /// </summary>
        private void SetupFormProperties()
        {
            Text = "أريدو POS - نظام نقاط البيع المتقدم";
            Size = new Size(1400, 900);
            StartPosition = FormStartPosition.CenterScreen;
            WindowState = FormWindowState.Maximized;
            MinimumSize = new Size(1024, 768);
            Icon = SystemIcons.Application;
            BackColor = ArabicLayoutManager.ArabicColors["Background"];
        }

        /// <summary>
        /// إنشاء الواجهة المتقدمة
        /// </summary>
        private void CreateAdvancedInterface()
        {
            // إنشاء شريط القوائم
            CreateAdvancedMenuStrip();

            // إنشاء شريط الأدوات
            CreateAdvancedToolStrip();

            // إنشاء اللوحة الجانبية
            CreateSidePanel();

            // إنشاء اللوحة الرئيسية
            CreateMainPanel();

            // إنشاء شريط الحالة
            CreateAdvancedStatusStrip();

            // ترتيب العناصر
            ArrangeControls();
        }

        /// <summary>
        /// إنشاء شريط القوائم المتقدم
        /// </summary>
        private void CreateAdvancedMenuStrip()
        {
            _menuStrip = new MenuStrip
            {
                BackColor = ArabicLayoutManager.ArabicColors["Surface"],
                ForeColor = ArabicLayoutManager.ArabicColors["Text"]
            };

            // قائمة ملف
            var fileMenu = new ToolStripMenuItem("ملف");
            fileMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("جلسة جديدة", null, OnNewSession),
                new ToolStripMenuItem("إغلاق الجلسة", null, OnCloseSession),
                new ToolStripSeparator(),
                new ToolStripMenuItem("نسخ احتياطي", null, OnBackup),
                new ToolStripMenuItem("استعادة", null, OnRestore),
                new ToolStripSeparator(),
                new ToolStripMenuItem("خروج", null, OnExit)
            });

            // قائمة المبيعات
            var salesMenu = new ToolStripMenuItem("المبيعات");
            salesMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("فاتورة جديدة", null, OnNewInvoice),
                new ToolStripMenuItem("البحث في الفواتير", null, OnSearchInvoices),
                new ToolStripMenuItem("إرجاع مبيعات", null, OnReturnSales),
                new ToolStripSeparator(),
                new ToolStripMenuItem("إدارة الأقساط", null, OnManageInstallments),
                new ToolStripMenuItem("المبيعات الآجلة", null, OnCreditSales)
            });

            // قائمة المخزون
            var inventoryMenu = new ToolStripMenuItem("المخزون");
            inventoryMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("إدارة المنتجات", null, OnManageProducts),
                new ToolStripMenuItem("إدارة الفئات", null, OnManageCategories),
                new ToolStripMenuItem("حركة المخزون", null, OnInventoryMovement),
                new ToolStripSeparator(),
                new ToolStripMenuItem("جرد المخزون", null, OnInventoryCount),
                new ToolStripMenuItem("تنبيهات المخزون", null, OnInventoryAlerts)
            });

            // قائمة العملاء
            var customersMenu = new ToolStripMenuItem("العملاء");
            customersMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("إدارة العملاء", null, OnManageCustomers),
                new ToolStripMenuItem("كشف حساب عميل", null, OnCustomerStatement),
                new ToolStripMenuItem("إدارة الديون", null, OnManageDebts),
                new ToolStripSeparator(),
                new ToolStripMenuItem("مجموعات العملاء", null, OnCustomerGroups),
                new ToolStripMenuItem("برنامج الولاء", null, OnLoyaltyProgram)
            });

            // قائمة التقارير
            var reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("تقرير المبيعات", null, OnSalesReport),
                new ToolStripMenuItem("تقرير المخزون", null, OnInventoryReport),
                new ToolStripMenuItem("تقرير العملاء", null, OnCustomersReport),
                new ToolStripSeparator(),
                new ToolStripMenuItem("تقرير الأرباح والخسائر", null, OnProfitLossReport),
                new ToolStripMenuItem("تقرير الصندوق", null, OnCashReport),
                new ToolStripMenuItem("تقرير الديون", null, OnDebtsReport)
            });

            // قائمة الأدوات
            var toolsMenu = new ToolStripMenuItem("أدوات");
            toolsMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("إدارة المستخدمين", null, OnManageUsers),
                new ToolStripMenuItem("صلاحيات النظام", null, OnSystemPermissions),
                new ToolStripSeparator(),
                new ToolStripMenuItem("اختبار الطابعة", null, OnTestPrinter),
                new ToolStripMenuItem("إعدادات الطابعة", null, OnPrinterSettings),
                new ToolStripSeparator(),
                new ToolStripMenuItem("مولد الباركود", null, OnBarcodeGenerator),
                new ToolStripMenuItem("قارئ الباركود", null, OnBarcodeReader)
            });

            // قائمة الإعدادات
            var settingsMenu = new ToolStripMenuItem("الإعدادات");
            settingsMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("إعدادات النظام", null, OnSystemSettings),
                new ToolStripMenuItem("إعدادات المتجر", null, OnStoreSettings),
                new ToolStripMenuItem("إعدادات الطباعة", null, OnPrintSettings),
                new ToolStripSeparator(),
                new ToolStripMenuItem("إعدادات قاعدة البيانات", null, OnDatabaseSettings),
                new ToolStripMenuItem("إعدادات النسخ الاحتياطي", null, OnBackupSettings)
            });

            // قائمة المساعدة
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("دليل المستخدم", null, OnUserGuide),
                new ToolStripMenuItem("الدعم الفني", null, OnTechnicalSupport),
                new ToolStripMenuItem("تحديثات النظام", null, OnSystemUpdates),
                new ToolStripSeparator(),
                new ToolStripMenuItem("حول البرنامج", null, OnAbout)
            });

            _menuStrip.Items.AddRange(new ToolStripItem[]
            {
                fileMenu, salesMenu, inventoryMenu, customersMenu,
                reportsMenu, toolsMenu, settingsMenu, helpMenu
            });

            MainMenuStrip = _menuStrip;
            Controls.Add(_menuStrip);
        }

        #endregion

        #region إنشاء شريط الأدوات والواجهة

        /// <summary>
        /// إنشاء شريط الأدوات المتقدم
        /// </summary>
        private void CreateAdvancedToolStrip()
        {
            _toolStrip = new ToolStrip
            {
                BackColor = ArabicLayoutManager.ArabicColors["Surface"],
                ForeColor = ArabicLayoutManager.ArabicColors["Text"],
                ImageScalingSize = new Size(32, 32)
            };

            // أزرار شريط الأدوات
            _toolStrip.Items.AddRange(new ToolStripItem[]
            {
                CreateToolStripButton("فاتورة جديدة", "إنشاء فاتورة مبيعات جديدة", OnNewInvoice),
                new ToolStripSeparator(),
                CreateToolStripButton("إدارة المنتجات", "إدارة المنتجات والمخزون", OnManageProducts),
                CreateToolStripButton("إدارة العملاء", "إدارة العملاء والديون", OnManageCustomers),
                new ToolStripSeparator(),
                CreateToolStripButton("التقارير", "عرض التقارير والإحصائيات", OnSalesReport),
                CreateToolStripButton("الصندوق", "إدارة الصندوق والجلسات", OnCashManagement),
                new ToolStripSeparator(),
                CreateToolStripButton("الطابعة", "اختبار وإعداد الطابعة", OnTestPrinter),
                CreateToolStripButton("الباركود", "مولد ومعالج الباركود", OnBarcodeGenerator),
                new ToolStripSeparator(),
                CreateToolStripButton("الإعدادات", "إعدادات النظام", OnSystemSettings)
            });

            Controls.Add(_toolStrip);
        }

        /// <summary>
        /// إنشاء زر شريط الأدوات
        /// </summary>
        private ToolStripButton CreateToolStripButton(string text, string tooltip, EventHandler clickHandler)
        {
            var button = new ToolStripButton
            {
                Text = text,
                ToolTipText = tooltip,
                DisplayStyle = ToolStripItemDisplayStyle.ImageAndText,
                TextImageRelation = TextImageRelation.ImageAboveText,
                AutoSize = true
            };

            if (clickHandler != null)
                button.Click += clickHandler;

            return button;
        }

        /// <summary>
        /// إنشاء اللوحة الجانبية
        /// </summary>
        private void CreateSidePanel()
        {
            _sidePanel = new Panel
            {
                Width = 200,
                Dock = DockStyle.Right,
                BackColor = ArabicLayoutManager.ArabicColors["Dark"],
                Padding = new Padding(10)
            };

            // عنوان اللوحة الجانبية
            var titleLabel = new Label
            {
                Text = "التنقل السريع",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Dock = DockStyle.Top,
                Height = 40,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // أزرار التنقل السريع
            _salesButton = CreateNavigationButton("💰 المبيعات", OnShowSales);
            _inventoryButton = CreateNavigationButton("📦 المخزون", OnShowInventory);
            _customersButton = CreateNavigationButton("👥 العملاء", OnShowCustomers);
            _reportsButton = CreateNavigationButton("📊 التقارير", OnShowReports);
            _settingsButton = CreateNavigationButton("⚙️ الإعدادات", OnShowSettings);

            // ترتيب الأزرار
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent
            };

            var yPosition = 10;
            foreach (var button in new[] { _salesButton, _inventoryButton, _customersButton, _reportsButton, _settingsButton })
            {
                button.Location = new Point(10, yPosition);
                button.Width = 180;
                buttonsPanel.Controls.Add(button);
                yPosition += 60;
            }

            _sidePanel.Controls.Add(titleLabel);
            _sidePanel.Controls.Add(buttonsPanel);
            Controls.Add(_sidePanel);
        }

        /// <summary>
        /// إنشاء زر التنقل
        /// </summary>
        private Button CreateNavigationButton(string text, EventHandler clickHandler)
        {
            var button = new Button
            {
                Text = text,
                Height = 50,
                FlatStyle = FlatStyle.Flat,
                BackColor = ArabicLayoutManager.ArabicColors["Primary"],
                ForeColor = Color.White,
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ArabicLayoutManager.ArabicColors["Secondary"];

            if (clickHandler != null)
                button.Click += clickHandler;

            return button;
        }

        /// <summary>
        /// إنشاء اللوحة الرئيسية
        /// </summary>
        private void CreateMainPanel()
        {
            _mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = ArabicLayoutManager.ArabicColors["Background"],
                Padding = new Padding(10)
            };

            // إنشاء لوحة المحتوى
            _contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = ArabicLayoutManager.ArabicColors["Surface"],
                BorderStyle = BorderStyle.FixedSingle
            };

            // إضافة محتوى ترحيبي
            CreateWelcomeContent();

            _mainPanel.Controls.Add(_contentPanel);
            Controls.Add(_mainPanel);
        }

        /// <summary>
        /// إنشاء المحتوى الترحيبي
        /// </summary>
        private void CreateWelcomeContent()
        {
            var welcomePanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // عنوان الترحيب
            var titleLabel = new Label
            {
                Text = "🏪 مرحباً بك في نظام أريدو POS المتقدم",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = ArabicLayoutManager.ArabicColors["Primary"],
                Dock = DockStyle.Top,
                Height = 60,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // معلومات النظام
            var infoLabel = new Label
            {
                Text = "🎯 نظام شامل لإدارة نقاط البيع مع دعم كامل للغة العربية\n\n" +
                       "✅ الميزات المتوفرة:\n" +
                       "• إدارة المبيعات والفواتير مع الطباعة الحرارية\n" +
                       "• إدارة المخزون والمنتجات مع نظام الباركود\n" +
                       "• إدارة العملاء والديون والأقساط\n" +
                       "• نظام الصندوق وإدارة الجلسات\n" +
                       "• التقارير المتقدمة والإحصائيات\n" +
                       "• النسخ الاحتياطي والمزامنة\n" +
                       "• واجهة عربية كاملة مع RTL\n" +
                       "• التوافق مع Windows 7+\n\n" +
                       "🚀 ابدأ باختيار أحد الخيارات من اللوحة الجانبية أو شريط الأدوات",
                Font = new Font("Tahoma", 11, FontStyle.Regular),
                ForeColor = ArabicLayoutManager.ArabicColors["Text"],
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.TopRight
            };

            welcomePanel.Controls.Add(titleLabel);
            welcomePanel.Controls.Add(infoLabel);
            _contentPanel.Controls.Add(welcomePanel);
        }

        /// <summary>
        /// إنشاء شريط الحالة المتقدم
        /// </summary>
        private void CreateAdvancedStatusStrip()
        {
            _statusStrip = new StatusStrip
            {
                BackColor = ArabicLayoutManager.ArabicColors["Dark"],
                ForeColor = Color.White
            };

            _statusLabel = new ToolStripStatusLabel("جاري التهيئة...")
            {
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };

            _userLabel = new ToolStripStatusLabel($"👤 المستخدم: {Environment.UserName}")
            {
                BorderSides = ToolStripStatusLabelBorderSides.Left
            };

            _dbStatusLabel = new ToolStripStatusLabel("🗄️ قاعدة البيانات: غير متصل")
            {
                BorderSides = ToolStripStatusLabelBorderSides.Left
            };

            _timeLabel = new ToolStripStatusLabel(DateTime.Now.ToString("yyyy/MM/dd HH:mm"))
            {
                BorderSides = ToolStripStatusLabelBorderSides.Left
            };

            _statusStrip.Items.AddRange(new ToolStripItem[]
            {
                _statusLabel, _userLabel, _dbStatusLabel, _timeLabel
            });

            // تحديث الوقت كل دقيقة
            var timer = new Timer { Interval = 60000 };
            timer.Tick += (s, e) => _timeLabel.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
            timer.Start();

            Controls.Add(_statusStrip);
        }

        /// <summary>
        /// ترتيب العناصر
        /// </summary>
        private void ArrangeControls()
        {
            // ترتيب العناصر حسب الأولوية
            _statusStrip.SendToBack();
            _mainPanel.SendToBack();
            _sidePanel.SendToBack();
            _toolStrip.SendToBack();
            _menuStrip.SendToBack();
        }

        #endregion

        #region تهيئة النظام والوظائف المتقدمة

        /// <summary>
        /// تهيئة النظام بشكل غير متزامن
        /// </summary>
        private async Task InitializeSystemAsync()
        {
            try
            {
                UpdateStatus("جاري تهيئة النظام...");

                // فحص التوافق
                await Task.Run(() => CheckSystemCompatibility());

                // تهيئة قاعدة البيانات
                await InitializeDatabaseAsync();

                // تهيئة المكونات
                await InitializeComponentsAsync();

                UpdateStatus("تم تهيئة النظام بنجاح - جاهز للاستخدام");
                _dbStatusLabel.Text = "🗄️ قاعدة البيانات: متصل";
            }
            catch (Exception ex)
            {
                UpdateStatus($"خطأ في التهيئة: {ex.Message}");
                _dbStatusLabel.Text = "🗄️ قاعدة البيانات: خطأ";
            }
        }

        /// <summary>
        /// فحص توافق النظام
        /// </summary>
        private void CheckSystemCompatibility()
        {
            if (_compatibilityManager != null && !_compatibilityManager.IsCompatible)
            {
                var criticalIssues = _compatibilityManager.Issues.FindAll(i => i.Severity == IssueSeverity.Critical);
                if (criticalIssues.Count > 0)
                {
                    throw new Exception("النظام غير متوافق مع هذا الإصدار من Windows");
                }
            }
        }

        /// <summary>
        /// تهيئة قاعدة البيانات
        /// </summary>
        private async Task InitializeDatabaseAsync()
        {
            if (_databaseManager != null)
            {
                var connected = await _databaseManager.TestConnectionAsync();
                if (connected)
                {
                    await _databaseManager.InitializeDatabaseAsync();
                }
                else
                {
                    throw new Exception("فشل في الاتصال بقاعدة البيانات");
                }
            }
        }

        /// <summary>
        /// تهيئة المكونات
        /// </summary>
        private async Task InitializeComponentsAsync()
        {
            // تهيئة مدير الطباعة
            if (_printManager != null)
            {
                await _printManager.InitializeAsync();
            }

            // تهيئة مدير نقاط البيع
            if (_posManager != null)
            {
                // تهيئة إضافية حسب الحاجة
            }
        }

        /// <summary>
        /// تحديث حالة النظام
        /// </summary>
        private void UpdateStatus(string message)
        {
            if (_statusLabel != null)
            {
                _statusLabel.Text = message;
                Application.DoEvents();
            }
        }

        #endregion

        #region معالجات أحداث القوائم

        // معالجات قائمة ملف
        private void OnNewSession(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح جلسة جديدة", "جلسة جديدة");
        }

        private void OnCloseSession(object sender, EventArgs e)
        {
            ShowMessage("سيتم إغلاق الجلسة الحالية", "إغلاق الجلسة");
        }

        private void OnBackup(object sender, EventArgs e)
        {
            ShowMessage("سيتم إنشاء نسخة احتياطية", "نسخ احتياطي");
        }

        private void OnRestore(object sender, EventArgs e)
        {
            ShowMessage("سيتم استعادة النسخة الاحتياطية", "استعادة");
        }

        private void OnExit(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل تريد الخروج من النظام؟", "تأكيد الخروج",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        // معالجات قائمة المبيعات
        private void OnNewInvoice(object sender, EventArgs e)
        {
            LoadContent(new SalesForm());
        }

        private void OnSearchInvoices(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح نافذة البحث في الفواتير", "البحث في الفواتير");
        }

        private void OnReturnSales(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح نافذة إرجاع المبيعات", "إرجاع المبيعات");
        }

        private void OnManageInstallments(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح نافذة إدارة الأقساط", "إدارة الأقساط");
        }

        private void OnCreditSales(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح نافذة المبيعات الآجلة", "المبيعات الآجلة");
        }

        // معالجات قائمة المخزون
        private void OnManageProducts(object sender, EventArgs e)
        {
            LoadContent(new InventoryForm());
        }

        private void OnManageCategories(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح نافذة إدارة الفئات", "إدارة الفئات");
        }

        private void OnInventoryMovement(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح نافذة حركة المخزون", "حركة المخزون");
        }

        private void OnInventoryCount(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح نافذة جرد المخزون", "جرد المخزون");
        }

        private void OnInventoryAlerts(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح نافذة تنبيهات المخزون", "تنبيهات المخزون");
        }

        // معالجات قائمة العملاء
        private void OnManageCustomers(object sender, EventArgs e)
        {
            LoadContent(new CustomersForm());
        }

        private void OnCustomerStatement(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح كشف حساب العميل", "كشف حساب العميل");
        }

        private void OnManageDebts(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح نافذة إدارة الديون", "إدارة الديون");
        }

        private void OnCustomerGroups(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح نافذة مجموعات العملاء", "مجموعات العملاء");
        }

        private void OnLoyaltyProgram(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح نافذة برنامج الولاء", "برنامج الولاء");
        }

        #endregion

        #region معالجات أحداث التقارير والأدوات

        // معالجات قائمة التقارير
        private void OnSalesReport(object sender, EventArgs e)
        {
            LoadContent(new ReportsForm());
        }

        private void OnInventoryReport(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح تقرير المخزون", "تقرير المخزون");
        }

        private void OnCustomersReport(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح تقرير العملاء", "تقرير العملاء");
        }

        private void OnProfitLossReport(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح تقرير الأرباح والخسائر", "تقرير الأرباح والخسائر");
        }

        private void OnCashReport(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح تقرير الصندوق", "تقرير الصندوق");
        }

        private void OnDebtsReport(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح تقرير الديون", "تقرير الديون");
        }

        // معالجات قائمة الأدوات
        private void OnManageUsers(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح نافذة إدارة المستخدمين", "إدارة المستخدمين");
        }

        private void OnSystemPermissions(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح نافذة صلاحيات النظام", "صلاحيات النظام");
        }

        private void OnTestPrinter(object sender, EventArgs e)
        {
            TestPrinter();
        }

        private void OnPrinterSettings(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح إعدادات الطابعة", "إعدادات الطابعة");
        }

        private void OnBarcodeGenerator(object sender, EventArgs e)
        {
            LoadContent(new BarcodeForm());
        }

        private void OnBarcodeReader(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح قارئ الباركود", "قارئ الباركود");
        }

        // معالجات قائمة الإعدادات
        private void OnSystemSettings(object sender, EventArgs e)
        {
            LoadContent(new SettingsForm());
        }

        private void OnStoreSettings(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح إعدادات المتجر", "إعدادات المتجر");
        }

        private void OnPrintSettings(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح إعدادات الطباعة", "إعدادات الطباعة");
        }

        private void OnDatabaseSettings(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح إعدادات قاعدة البيانات", "إعدادات قاعدة البيانات");
        }

        private void OnBackupSettings(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح إعدادات النسخ الاحتياطي", "إعدادات النسخ الاحتياطي");
        }

        // معالجات قائمة المساعدة
        private void OnUserGuide(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح دليل المستخدم", "دليل المستخدم");
        }

        private void OnTechnicalSupport(object sender, EventArgs e)
        {
            MessageBox.Show(
                "للدعم الفني يرجى الاتصال على:\n\n" +
                "📞 الهاتف: +966 XX XXX XXXX\n" +
                "📧 البريد الإلكتروني: <EMAIL>\n" +
                "🌐 الموقع الإلكتروني: www.aredoo.com\n\n" +
                "ساعات العمل: من الأحد إلى الخميس، 9 صباحاً - 6 مساءً",
                "الدعم الفني",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        private void OnSystemUpdates(object sender, EventArgs e)
        {
            ShowMessage("سيتم فحص التحديثات المتاحة", "تحديثات النظام");
        }

        private void OnAbout(object sender, EventArgs e)
        {
            MessageBox.Show(
                "🏪 نظام أريدو POS المتقدم\n" +
                "📅 الإصدار 1.0.0\n" +
                "🏢 شركة أريدو للتقنية\n\n" +
                "📝 وصف النظام:\n" +
                "نظام شامل ومتقدم لإدارة نقاط البيع مع دعم كامل\n" +
                "للغة العربية والطابعات الحرارية وجميع الميزات المطلوبة\n\n" +
                "🔧 الميزات الرئيسية:\n" +
                "• إدارة المبيعات والفواتير مع الطباعة الحرارية\n" +
                "• إدارة المخزون والمنتجات مع نظام الباركود المتكامل\n" +
                "• إدارة العملاء والديون والأقساط\n" +
                "• نظام الصندوق وإدارة الجلسات المتقدم\n" +
                "• التقارير المتقدمة والإحصائيات التفصيلية\n" +
                "• النسخ الاحتياطي التلقائي والمزامنة\n" +
                "• واجهة عربية كاملة مع دعم RTL\n" +
                "• التوافق الكامل مع Windows 7+\n" +
                "• قاعدة بيانات SQL Server موثوقة\n" +
                "• أمان متقدم وإدارة المستخدمين\n\n" +
                "🛡️ الأمان والموثوقية:\n" +
                "• تشفير البيانات الحساسة\n" +
                "• نسخ احتياطية تلقائية\n" +
                "• تسجيل شامل للعمليات\n" +
                "• صلاحيات متدرجة للمستخدمين\n\n" +
                "© 2024 أريدو للتقنية. جميع الحقوق محفوظة.\n" +
                "تم التطوير بعناية فائقة لخدمة التجار العرب",
                "حول نظام أريدو POS",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        #endregion

        #region معالجات أحداث التنقل السريع

        private void OnShowSales(object sender, EventArgs e)
        {
            LoadContent(new SalesForm());
            HighlightNavigationButton(_salesButton);
        }

        private void OnShowInventory(object sender, EventArgs e)
        {
            LoadContent(new InventoryForm());
            HighlightNavigationButton(_inventoryButton);
        }

        private void OnShowCustomers(object sender, EventArgs e)
        {
            LoadContent(new CustomersForm());
            HighlightNavigationButton(_customersButton);
        }

        private void OnShowReports(object sender, EventArgs e)
        {
            LoadContent(new ReportsForm());
            HighlightNavigationButton(_reportsButton);
        }

        private void OnShowSettings(object sender, EventArgs e)
        {
            LoadContent(new SettingsForm());
            HighlightNavigationButton(_settingsButton);
        }

        private void OnCashManagement(object sender, EventArgs e)
        {
            ShowMessage("سيتم فتح نافذة إدارة الصندوق", "إدارة الصندوق");
        }

        #endregion

        #region الوظائف المساعدة

        /// <summary>
        /// تحميل محتوى في اللوحة الرئيسية
        /// </summary>
        /// <param name="form">النموذج المراد تحميله</param>
        private void LoadContent(Form form)
        {
            try
            {
                // مسح المحتوى الحالي
                _contentPanel.Controls.Clear();

                // إعداد النموذج الجديد
                form.TopLevel = false;
                form.FormBorderStyle = FormBorderStyle.None;
                form.Dock = DockStyle.Fill;

                // تطبيق التخطيط العربي
                ArabicLayoutManager.ApplyArabicLayout(form);
                ArabicResourceManager.ApplyArabicTexts(form);

                // إضافة النموذج
                _contentPanel.Controls.Add(form);
                form.Show();

                UpdateStatus($"تم تحميل: {form.Text}");
            }
            catch (Exception ex)
            {
                ShowMessage($"خطأ في تحميل المحتوى: {ex.Message}", "خطأ");
            }
        }

        /// <summary>
        /// تمييز زر التنقل النشط
        /// </summary>
        /// <param name="activeButton">الزر النشط</param>
        private void HighlightNavigationButton(Button activeButton)
        {
            // إعادة تعيين جميع الأزرار
            foreach (var button in new[] { _salesButton, _inventoryButton, _customersButton, _reportsButton, _settingsButton })
            {
                button.BackColor = ArabicLayoutManager.ArabicColors["Primary"];
            }

            // تمييز الزر النشط
            activeButton.BackColor = ArabicLayoutManager.ArabicColors["Success"];
        }

        /// <summary>
        /// عرض رسالة للمستخدم
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        private void ShowMessage(string message, string title = "معلومات")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// اختبار الطابعة
        /// </summary>
        private async void TestPrinter()
        {
            try
            {
                UpdateStatus("جاري اختبار الطابعة...");

                if (_printManager != null)
                {
                    // محاولة طباعة صفحة اختبار
                    var testResult = await Task.Run(() =>
                    {
                        // هنا يمكن إضافة كود اختبار الطابعة الفعلي
                        System.Threading.Thread.Sleep(2000); // محاكاة الاختبار
                        return true;
                    });

                    if (testResult)
                    {
                        UpdateStatus("تم اختبار الطابعة بنجاح");
                        ShowMessage("تم اختبار الطابعة بنجاح!\nالطابعة جاهزة للاستخدام.", "اختبار الطابعة");
                    }
                    else
                    {
                        UpdateStatus("فشل في اختبار الطابعة");
                        ShowMessage("فشل في اختبار الطابعة.\nيرجى التحقق من الاتصال والإعدادات.", "خطأ في الطابعة");
                    }
                }
                else
                {
                    ShowMessage("مدير الطباعة غير متوفر.\nيرجى التحقق من إعدادات النظام.", "خطأ");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus("خطأ في اختبار الطابعة");
                ShowMessage($"خطأ في اختبار الطابعة: {ex.Message}", "خطأ");
            }
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // تنظيف الموارد المدارة
                _databaseManager?.Dispose();
                _printManager?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion

        #region تهيئة المكونات (Designer)

        /// <summary>
        /// تهيئة المكونات - مطلوب للـ Designer
        /// </summary>
        private void InitializeComponent()
        {
            SuspendLayout();

            // إعدادات النموذج الأساسية
            AutoScaleDimensions = new SizeF(7F, 16F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1400, 900);
            Font = new Font("Tahoma", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 178);

            ResumeLayout(false);
        }

        #endregion
    }

    #region النماذج الفرعية البسيطة

    /// <summary>
    /// نموذج المبيعات البسيط
    /// </summary>
    public partial class SalesForm : Form
    {
        public SalesForm()
        {
            InitializeComponent();
            CreateSalesInterface();
        }

        private void InitializeComponent()
        {
            Text = "إدارة المبيعات";
            Size = new Size(800, 600);
        }

        private void CreateSalesInterface()
        {
            var label = new Label
            {
                Text = "💰 نموذج إدارة المبيعات\n\n" +
                       "هنا سيتم عرض:\n" +
                       "• إنشاء فاتورة جديدة\n" +
                       "• البحث في الفواتير\n" +
                       "• إدارة المرتجعات\n" +
                       "• المبيعات الآجلة\n\n" +
                       "🚧 قيد التطوير...",
                Font = new Font("Tahoma", 12, FontStyle.Regular),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = ArabicLayoutManager.ArabicColors["Text"]
            };
            Controls.Add(label);
        }
    }

    /// <summary>
    /// نموذج المخزون البسيط
    /// </summary>
    public partial class InventoryForm : Form
    {
        public InventoryForm()
        {
            InitializeComponent();
            CreateInventoryInterface();
        }

        private void InitializeComponent()
        {
            Text = "إدارة المخزون";
            Size = new Size(800, 600);
        }

        private void CreateInventoryInterface()
        {
            var label = new Label
            {
                Text = "📦 نموذج إدارة المخزون\n\n" +
                       "هنا سيتم عرض:\n" +
                       "• إدارة المنتجات\n" +
                       "• إدارة الفئات\n" +
                       "• حركة المخزون\n" +
                       "• جرد المخزون\n" +
                       "• تنبيهات النفاد\n\n" +
                       "🚧 قيد التطوير...",
                Font = new Font("Tahoma", 12, FontStyle.Regular),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = ArabicLayoutManager.ArabicColors["Text"]
            };
            Controls.Add(label);
        }
    }

    /// <summary>
    /// نموذج العملاء البسيط
    /// </summary>
    public partial class CustomersForm : Form
    {
        public CustomersForm()
        {
            InitializeComponent();
            CreateCustomersInterface();
        }

        private void InitializeComponent()
        {
            Text = "إدارة العملاء";
            Size = new Size(800, 600);
        }

        private void CreateCustomersInterface()
        {
            var label = new Label
            {
                Text = "👥 نموذج إدارة العملاء\n\n" +
                       "هنا سيتم عرض:\n" +
                       "• إدارة بيانات العملاء\n" +
                       "• كشوف الحسابات\n" +
                       "• إدارة الديون\n" +
                       "• مجموعات العملاء\n" +
                       "• برنامج الولاء\n\n" +
                       "🚧 قيد التطوير...",
                Font = new Font("Tahoma", 12, FontStyle.Regular),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = ArabicLayoutManager.ArabicColors["Text"]
            };
            Controls.Add(label);
        }
    }

    /// <summary>
    /// نموذج التقارير البسيط
    /// </summary>
    public partial class ReportsForm : Form
    {
        public ReportsForm()
        {
            InitializeComponent();
            CreateReportsInterface();
        }

        private void InitializeComponent()
        {
            Text = "التقارير والإحصائيات";
            Size = new Size(800, 600);
        }

        private void CreateReportsInterface()
        {
            var label = new Label
            {
                Text = "📊 نموذج التقارير والإحصائيات\n\n" +
                       "هنا سيتم عرض:\n" +
                       "• تقارير المبيعات\n" +
                       "• تقارير المخزون\n" +
                       "• تقارير العملاء\n" +
                       "• تقارير الأرباح والخسائر\n" +
                       "• تقارير الصندوق\n\n" +
                       "🚧 قيد التطوير...",
                Font = new Font("Tahoma", 12, FontStyle.Regular),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = ArabicLayoutManager.ArabicColors["Text"]
            };
            Controls.Add(label);
        }
    }

    /// <summary>
    /// نموذج الباركود البسيط
    /// </summary>
    public partial class BarcodeForm : Form
    {
        public BarcodeForm()
        {
            InitializeComponent();
            CreateBarcodeInterface();
        }

        private void InitializeComponent()
        {
            Text = "مولد ومعالج الباركود";
            Size = new Size(800, 600);
        }

        private void CreateBarcodeInterface()
        {
            var label = new Label
            {
                Text = "📊 نموذج مولد ومعالج الباركود\n\n" +
                       "هنا سيتم عرض:\n" +
                       "• إنتاج الباركود\n" +
                       "• قراءة الباركود\n" +
                       "• أنواع مختلفة من الباركود\n" +
                       "• طباعة ملصقات الباركود\n" +
                       "• إدارة أكواد المنتجات\n\n" +
                       "🚧 قيد التطوير...",
                Font = new Font("Tahoma", 12, FontStyle.Regular),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = ArabicLayoutManager.ArabicColors["Text"]
            };
            Controls.Add(label);
        }
    }

    /// <summary>
    /// نموذج الإعدادات البسيط
    /// </summary>
    public partial class SettingsForm : Form
    {
        public SettingsForm()
        {
            InitializeComponent();
            CreateSettingsInterface();
        }

        private void InitializeComponent()
        {
            Text = "إعدادات النظام";
            Size = new Size(800, 600);
        }

        private void CreateSettingsInterface()
        {
            var label = new Label
            {
                Text = "⚙️ نموذج إعدادات النظام\n\n" +
                       "هنا سيتم عرض:\n" +
                       "• إعدادات عامة\n" +
                       "• إعدادات المتجر\n" +
                       "• إعدادات الطباعة\n" +
                       "• إعدادات قاعدة البيانات\n" +
                       "• إعدادات النسخ الاحتياطي\n" +
                       "• إعدادات المستخدمين\n\n" +
                       "🚧 قيد التطوير...",
                Font = new Font("Tahoma", 12, FontStyle.Regular),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = ArabicLayoutManager.ArabicColors["Text"]
            };
            Controls.Add(label);
        }
    }

    #endregion
}