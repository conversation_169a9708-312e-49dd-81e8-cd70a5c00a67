-- =============================================
-- نظام إدارة النقدية والصندوق - جداول قاعدة البيانات
-- تاريخ الإنشاء: 2024-07-11
-- الوصف: جداول شاملة لإدارة الصناديق والجلسات والمعاملات النقدية
-- =============================================

USE AredooPOS;
GO

-- =============================================
-- جدول أصناف الدفع
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PaymentMethods]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[PaymentMethods] (
        [PaymentMethodID] INT IDENTITY(1,1) NOT NULL,
        [MethodName] NVARCHAR(50) NOT NULL,
        [MethodNameArabic] NVARCHAR(50) NOT NULL,
        [MethodCode] NVARCHAR(10) NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [RequiresReference] BIT NOT NULL DEFAULT 0,
        [AllowPartialPayment] BIT NOT NULL DEFAULT 1,
        [ProcessingFeePercentage] DECIMAL(5,2) NOT NULL DEFAULT 0,
        [MinimumAmount] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [MaximumAmount] DECIMAL(18,2) NULL,
        [Description] NVARCHAR(255) NULL,
        [SortOrder] INT NOT NULL DEFAULT 0,
        [CreatedBy] NVARCHAR(50) NOT NULL,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [ModifiedBy] NVARCHAR(50) NULL,
        [ModifiedDate] DATETIME2 NULL,
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        [DeletedBy] NVARCHAR(50) NULL,
        [DeletedDate] DATETIME2 NULL,
        
        CONSTRAINT [PK_PaymentMethods] PRIMARY KEY CLUSTERED ([PaymentMethodID] ASC),
        CONSTRAINT [UK_PaymentMethods_MethodCode] UNIQUE ([MethodCode]),
        CONSTRAINT [UK_PaymentMethods_MethodName] UNIQUE ([MethodName])
    );
    
    -- إدراج طرق الدفع الافتراضية
    INSERT INTO [PaymentMethods] ([MethodName], [MethodNameArabic], [MethodCode], [RequiresReference], [CreatedBy])
    VALUES 
        ('Cash', N'نقدي', 'CASH', 0, 'SYSTEM'),
        ('Credit Card', N'بطاقة ائتمان', 'CARD', 1, 'SYSTEM'),
        ('Bank Transfer', N'تحويل بنكي', 'TRANSFER', 1, 'SYSTEM'),
        ('Check', N'شيك', 'CHECK', 1, 'SYSTEM'),
        ('Gift Card', N'بطاقة هدايا', 'GIFT', 1, 'SYSTEM');
    
    PRINT 'تم إنشاء جدول PaymentMethods بنجاح';
END
GO

-- =============================================
-- جدول الصناديق
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CashRegisters]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CashRegisters] (
        [CashRegisterID] INT IDENTITY(1,1) NOT NULL,
        [RegisterName] NVARCHAR(100) NOT NULL,
        [RegisterCode] NVARCHAR(20) NOT NULL,
        [Location] NVARCHAR(100) NULL,
        [Description] NVARCHAR(255) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [OpeningBalance] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [CurrentBalance] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [MaxCashLimit] DECIMAL(18,2) NULL,
        [MinCashLimit] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [RequireManagerApproval] BIT NOT NULL DEFAULT 0,
        [AllowNegativeBalance] BIT NOT NULL DEFAULT 0,
        [AutoCloseTime] TIME NULL,
        [LastSessionID] INT NULL,
        [LastTransactionDate] DATETIME2 NULL,
        [CreatedBy] NVARCHAR(50) NOT NULL,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [ModifiedBy] NVARCHAR(50) NULL,
        [ModifiedDate] DATETIME2 NULL,
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        [DeletedBy] NVARCHAR(50) NULL,
        [DeletedDate] DATETIME2 NULL,
        
        CONSTRAINT [PK_CashRegisters] PRIMARY KEY CLUSTERED ([CashRegisterID] ASC),
        CONSTRAINT [UK_CashRegisters_RegisterCode] UNIQUE ([RegisterCode])
    );
    
    -- إدراج صندوق افتراضي
    INSERT INTO [CashRegisters] ([RegisterName], [RegisterCode], [Location], [CreatedBy])
    VALUES (N'الصندوق الرئيسي', 'MAIN001', N'الفرع الرئيسي', 'SYSTEM');
    
    PRINT 'تم إنشاء جدول CashRegisters بنجاح';
END
GO

-- =============================================
-- جدول جلسات الصندوق
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CashSessions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CashSessions] (
        [SessionID] INT IDENTITY(1,1) NOT NULL,
        [CashRegisterID] INT NOT NULL,
        [UserID] INT NOT NULL,
        [SessionDate] DATE NOT NULL,
        [OpenTime] DATETIME2 NOT NULL,
        [CloseTime] DATETIME2 NULL,
        [Status] NVARCHAR(20) NOT NULL DEFAULT 'Open',
        [OpeningBalance] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [ClosingBalance] DECIMAL(18,2) NULL,
        [ExpectedClosingBalance] DECIMAL(18,2) NULL,
        [CashSales] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [CardSales] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [TransferSales] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [TotalSales] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [CashExpenses] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [CashWithdrawals] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [CashDeposits] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [TotalTransactions] INT NOT NULL DEFAULT 0,
        [Variance] DECIMAL(18,2) NULL,
        [VarianceReason] NVARCHAR(255) NULL,
        [Notes] NVARCHAR(500) NULL,
        [ClosedBy] NVARCHAR(50) NULL,
        [ManagerApproval] BIT NOT NULL DEFAULT 0,
        [ApprovedBy] NVARCHAR(50) NULL,
        [ApprovalDate] DATETIME2 NULL,
        [CreatedBy] NVARCHAR(50) NOT NULL,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [ModifiedBy] NVARCHAR(50) NULL,
        [ModifiedDate] DATETIME2 NULL,
        
        CONSTRAINT [PK_CashSessions] PRIMARY KEY CLUSTERED ([SessionID] ASC),
        CONSTRAINT [FK_CashSessions_CashRegisters] FOREIGN KEY ([CashRegisterID]) 
            REFERENCES [CashRegisters]([CashRegisterID]),
        CONSTRAINT [FK_CashSessions_Users] FOREIGN KEY ([UserID]) 
            REFERENCES [Users]([UserID]),
        CONSTRAINT [CK_CashSessions_Status] CHECK ([Status] IN ('Open', 'Closed', 'Suspended', 'Cancelled'))
    );
    
    -- إنشاء فهارس
    CREATE NONCLUSTERED INDEX [IX_CashSessions_CashRegisterID] ON [CashSessions] ([CashRegisterID]);
    CREATE NONCLUSTERED INDEX [IX_CashSessions_UserID] ON [CashSessions] ([UserID]);
    CREATE NONCLUSTERED INDEX [IX_CashSessions_SessionDate] ON [CashSessions] ([SessionDate]);
    CREATE NONCLUSTERED INDEX [IX_CashSessions_Status] ON [CashSessions] ([Status]);
    
    PRINT 'تم إنشاء جدول CashSessions بنجاح';
END
GO

-- =============================================
-- جدول المعاملات النقدية
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CashTransactions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CashTransactions] (
        [TransactionID] INT IDENTITY(1,1) NOT NULL,
        [SessionID] INT NOT NULL,
        [TransactionNumber] NVARCHAR(50) NOT NULL,
        [TransactionType] NVARCHAR(20) NOT NULL,
        [TransactionDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [Amount] DECIMAL(18,2) NOT NULL,
        [PaymentMethodID] INT NOT NULL,
        [ReferenceNumber] NVARCHAR(100) NULL,
        [Description] NVARCHAR(255) NULL,
        [Category] NVARCHAR(50) NULL,
        [RelatedDocumentType] NVARCHAR(20) NULL,
        [RelatedDocumentID] INT NULL,
        [CustomerID] INT NULL,
        [SupplierID] INT NULL,
        [UserID] INT NOT NULL,
        [IsVoided] BIT NOT NULL DEFAULT 0,
        [VoidedBy] NVARCHAR(50) NULL,
        [VoidedDate] DATETIME2 NULL,
        [VoidReason] NVARCHAR(255) NULL,
        [RequiresApproval] BIT NOT NULL DEFAULT 0,
        [IsApproved] BIT NOT NULL DEFAULT 1,
        [ApprovedBy] NVARCHAR(50) NULL,
        [ApprovalDate] DATETIME2 NULL,
        [Notes] NVARCHAR(500) NULL,
        [CreatedBy] NVARCHAR(50) NOT NULL,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [ModifiedBy] NVARCHAR(50) NULL,
        [ModifiedDate] DATETIME2 NULL,
        
        CONSTRAINT [PK_CashTransactions] PRIMARY KEY CLUSTERED ([TransactionID] ASC),
        CONSTRAINT [FK_CashTransactions_CashSessions] FOREIGN KEY ([SessionID]) 
            REFERENCES [CashSessions]([SessionID]),
        CONSTRAINT [FK_CashTransactions_PaymentMethods] FOREIGN KEY ([PaymentMethodID]) 
            REFERENCES [PaymentMethods]([PaymentMethodID]),
        CONSTRAINT [FK_CashTransactions_Users] FOREIGN KEY ([UserID]) 
            REFERENCES [Users]([UserID]),
        CONSTRAINT [UK_CashTransactions_TransactionNumber] UNIQUE ([TransactionNumber]),
        CONSTRAINT [CK_CashTransactions_TransactionType] CHECK ([TransactionType] IN 
            ('Sale', 'Return', 'Expense', 'Withdrawal', 'Deposit', 'Opening', 'Closing', 'Adjustment')),
        CONSTRAINT [CK_CashTransactions_Amount] CHECK ([Amount] <> 0)
    );
    
    -- إنشاء فهارس
    CREATE NONCLUSTERED INDEX [IX_CashTransactions_SessionID] ON [CashTransactions] ([SessionID]);
    CREATE NONCLUSTERED INDEX [IX_CashTransactions_TransactionType] ON [CashTransactions] ([TransactionType]);
    CREATE NONCLUSTERED INDEX [IX_CashTransactions_TransactionDate] ON [CashTransactions] ([TransactionDate]);
    CREATE NONCLUSTERED INDEX [IX_CashTransactions_PaymentMethodID] ON [CashTransactions] ([PaymentMethodID]);
    CREATE NONCLUSTERED INDEX [IX_CashTransactions_UserID] ON [CashTransactions] ([UserID]);
    CREATE NONCLUSTERED INDEX [IX_CashTransactions_RelatedDocument] ON [CashTransactions] ([RelatedDocumentType], [RelatedDocumentID]);
    
    PRINT 'تم إنشاء جدول CashTransactions بنجاح';
END
GO

-- =============================================
-- جدول تفاصيل الدفع المتعدد
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PaymentDetails]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[PaymentDetails] (
        [PaymentDetailID] INT IDENTITY(1,1) NOT NULL,
        [TransactionID] INT NOT NULL,
        [PaymentMethodID] INT NOT NULL,
        [Amount] DECIMAL(18,2) NOT NULL,
        [ReferenceNumber] NVARCHAR(100) NULL,
        [CardType] NVARCHAR(20) NULL,
        [CardLastFourDigits] NVARCHAR(4) NULL,
        [BankName] NVARCHAR(100) NULL,
        [ProcessingFee] DECIMAL(18,2) NOT NULL DEFAULT 0,
        [ExchangeRate] DECIMAL(10,4) NOT NULL DEFAULT 1,
        [CurrencyCode] NVARCHAR(3) NOT NULL DEFAULT 'SAR',
        [Status] NVARCHAR(20) NOT NULL DEFAULT 'Completed',
        [ProcessedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [Notes] NVARCHAR(255) NULL,
        [CreatedBy] NVARCHAR(50) NOT NULL,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        
        CONSTRAINT [PK_PaymentDetails] PRIMARY KEY CLUSTERED ([PaymentDetailID] ASC),
        CONSTRAINT [FK_PaymentDetails_CashTransactions] FOREIGN KEY ([TransactionID]) 
            REFERENCES [CashTransactions]([TransactionID]) ON DELETE CASCADE,
        CONSTRAINT [FK_PaymentDetails_PaymentMethods] FOREIGN KEY ([PaymentMethodID]) 
            REFERENCES [PaymentMethods]([PaymentMethodID]),
        CONSTRAINT [CK_PaymentDetails_Amount] CHECK ([Amount] > 0),
        CONSTRAINT [CK_PaymentDetails_Status] CHECK ([Status] IN ('Pending', 'Completed', 'Failed', 'Cancelled'))
    );
    
    -- إنشاء فهارس
    CREATE NONCLUSTERED INDEX [IX_PaymentDetails_TransactionID] ON [PaymentDetails] ([TransactionID]);
    CREATE NONCLUSTERED INDEX [IX_PaymentDetails_PaymentMethodID] ON [PaymentDetails] ([PaymentMethodID]);
    
    PRINT 'تم إنشاء جدول PaymentDetails بنجاح';
END
GO

-- =============================================
-- جدول أرصدة العملات
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CashDenominations]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CashDenominations] (
        [DenominationID] INT IDENTITY(1,1) NOT NULL,
        [SessionID] INT NOT NULL,
        [DenominationType] NVARCHAR(10) NOT NULL,
        [Value] DECIMAL(10,2) NOT NULL,
        [Quantity] INT NOT NULL,
        [TotalAmount] AS ([Value] * [Quantity]) PERSISTED,
        [CountType] NVARCHAR(10) NOT NULL DEFAULT 'Closing',
        [CreatedBy] NVARCHAR(50) NOT NULL,
        [CreatedDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        
        CONSTRAINT [PK_CashDenominations] PRIMARY KEY CLUSTERED ([DenominationID] ASC),
        CONSTRAINT [FK_CashDenominations_CashSessions] FOREIGN KEY ([SessionID]) 
            REFERENCES [CashSessions]([SessionID]) ON DELETE CASCADE,
        CONSTRAINT [CK_CashDenominations_Type] CHECK ([DenominationType] IN ('Note', 'Coin')),
        CONSTRAINT [CK_CashDenominations_CountType] CHECK ([CountType] IN ('Opening', 'Closing')),
        CONSTRAINT [CK_CashDenominations_Value] CHECK ([Value] > 0),
        CONSTRAINT [CK_CashDenominations_Quantity] CHECK ([Quantity] >= 0)
    );
    
    -- إنشاء فهرس
    CREATE NONCLUSTERED INDEX [IX_CashDenominations_SessionID] ON [CashDenominations] ([SessionID]);
    
    PRINT 'تم إنشاء جدول CashDenominations بنجاح';
END
GO

-- =============================================
-- جدول سجل التدقيق للصندوق
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CashAuditLog]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CashAuditLog] (
        [AuditID] INT IDENTITY(1,1) NOT NULL,
        [TableName] NVARCHAR(50) NOT NULL,
        [RecordID] INT NOT NULL,
        [Action] NVARCHAR(20) NOT NULL,
        [OldValues] NVARCHAR(MAX) NULL,
        [NewValues] NVARCHAR(MAX) NULL,
        [UserID] INT NOT NULL,
        [SessionID] INT NULL,
        [AuditDate] DATETIME2 NOT NULL DEFAULT GETDATE(),
        [IPAddress] NVARCHAR(45) NULL,
        [UserAgent] NVARCHAR(255) NULL,
        
        CONSTRAINT [PK_CashAuditLog] PRIMARY KEY CLUSTERED ([AuditID] ASC),
        CONSTRAINT [FK_CashAuditLog_Users] FOREIGN KEY ([UserID]) 
            REFERENCES [Users]([UserID]),
        CONSTRAINT [CK_CashAuditLog_Action] CHECK ([Action] IN ('INSERT', 'UPDATE', 'DELETE'))
    );
    
    -- إنشاء فهارس
    CREATE NONCLUSTERED INDEX [IX_CashAuditLog_TableRecord] ON [CashAuditLog] ([TableName], [RecordID]);
    CREATE NONCLUSTERED INDEX [IX_CashAuditLog_UserID] ON [CashAuditLog] ([UserID]);
    CREATE NONCLUSTERED INDEX [IX_CashAuditLog_AuditDate] ON [CashAuditLog] ([AuditDate]);
    
    PRINT 'تم إنشاء جدول CashAuditLog بنجاح';
END
GO

-- =============================================
-- إنشاء تسلسل أرقام المعاملات
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.sequences WHERE name = 'TransactionNumberSequence')
BEGIN
    CREATE SEQUENCE TransactionNumberSequence
        START WITH 1000001
        INCREMENT BY 1
        MINVALUE 1000001
        MAXVALUE 9999999999
        CYCLE
        CACHE 50;
    
    PRINT 'تم إنشاء تسلسل TransactionNumberSequence بنجاح';
END
GO

-- =============================================
-- إنشاء دالة لتوليد رقم المعاملة
-- =============================================
IF OBJECT_ID('dbo.GenerateTransactionNumber', 'FN') IS NOT NULL
    DROP FUNCTION dbo.GenerateTransactionNumber;
GO

CREATE FUNCTION dbo.GenerateTransactionNumber()
RETURNS NVARCHAR(50)
AS
BEGIN
    DECLARE @NextVal BIGINT;
    DECLARE @TransactionNumber NVARCHAR(50);
    
    SET @NextVal = NEXT VALUE FOR TransactionNumberSequence;
    SET @TransactionNumber = 'TXN' + FORMAT(GETDATE(), 'yyyyMMdd') + FORMAT(@NextVal, '000000');
    
    RETURN @TransactionNumber;
END
GO

PRINT 'تم إنشاء دالة GenerateTransactionNumber بنجاح';
GO

-- =============================================
-- إنشاء مشاهدة ملخص الجلسات
-- =============================================
IF OBJECT_ID('dbo.vw_CashSessionSummary', 'V') IS NOT NULL
    DROP VIEW dbo.vw_CashSessionSummary;
GO

CREATE VIEW dbo.vw_CashSessionSummary
AS
SELECT 
    cs.SessionID,
    cs.CashRegisterID,
    cr.RegisterName,
    cr.RegisterCode,
    cs.UserID,
    u.Username,
    u.FirstName + ' ' + u.LastName AS UserFullName,
    cs.SessionDate,
    cs.OpenTime,
    cs.CloseTime,
    cs.Status,
    cs.OpeningBalance,
    cs.ClosingBalance,
    cs.ExpectedClosingBalance,
    cs.CashSales,
    cs.CardSales,
    cs.TransferSales,
    cs.TotalSales,
    cs.CashExpenses,
    cs.CashWithdrawals,
    cs.CashDeposits,
    cs.TotalTransactions,
    cs.Variance,
    cs.VarianceReason,
    cs.ManagerApproval,
    cs.ApprovedBy,
    cs.ApprovalDate,
    DATEDIFF(MINUTE, cs.OpenTime, ISNULL(cs.CloseTime, GETDATE())) AS SessionDurationMinutes
FROM CashSessions cs
INNER JOIN CashRegisters cr ON cs.CashRegisterID = cr.CashRegisterID
INNER JOIN Users u ON cs.UserID = u.UserID
WHERE cs.SessionID IS NOT NULL;
GO

PRINT 'تم إنشاء مشاهدة vw_CashSessionSummary بنجاح';
GO

-- =============================================
-- إنشاء مشاهدة ملخص المعاملات
-- =============================================
IF OBJECT_ID('dbo.vw_CashTransactionSummary', 'V') IS NOT NULL
    DROP VIEW dbo.vw_CashTransactionSummary;
GO

CREATE VIEW dbo.vw_CashTransactionSummary
AS
SELECT 
    ct.TransactionID,
    ct.SessionID,
    cs.SessionDate,
    cr.RegisterName,
    ct.TransactionNumber,
    ct.TransactionType,
    ct.TransactionDate,
    ct.Amount,
    pm.MethodNameArabic AS PaymentMethodName,
    ct.ReferenceNumber,
    ct.Description,
    ct.Category,
    u.Username,
    u.FirstName + ' ' + u.LastName AS UserFullName,
    ct.IsVoided,
    ct.VoidedBy,
    ct.VoidedDate,
    ct.IsApproved,
    ct.ApprovedBy,
    ct.ApprovalDate
FROM CashTransactions ct
INNER JOIN CashSessions cs ON ct.SessionID = cs.SessionID
INNER JOIN CashRegisters cr ON cs.CashRegisterID = cr.CashRegisterID
INNER JOIN PaymentMethods pm ON ct.PaymentMethodID = pm.PaymentMethodID
INNER JOIN Users u ON ct.UserID = u.UserID
WHERE ct.TransactionID IS NOT NULL;
GO

PRINT 'تم إنشاء مشاهدة vw_CashTransactionSummary بنجاح';
GO

PRINT '==============================================';
PRINT 'تم إنشاء جميع جداول نظام إدارة النقدية والصندوق بنجاح';
PRINT 'الجداول المُنشأة:';
PRINT '- PaymentMethods: طرق الدفع';
PRINT '- CashRegisters: الصناديق';
PRINT '- CashSessions: جلسات الصندوق';
PRINT '- CashTransactions: المعاملات النقدية';
PRINT '- PaymentDetails: تفاصيل الدفع المتعدد';
PRINT '- CashDenominations: أرصدة العملات';
PRINT '- CashAuditLog: سجل التدقيق';
PRINT '==============================================';
