using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// نظام التصميم الحديث لتطبيق أريدو الكاشير
    /// مستوحى من Microsoft Fluent Design و Material Design
    /// </summary>
    public static class ModernDesignSystem
    {
        #region الألوان الأساسية

        /// <summary>
        /// مجموعة الألوان الأساسية للتطبيق
        /// </summary>
        public static class Colors
        {
            // الألوان الأساسية
            public static readonly Color Primary = Color.FromArgb(0, 120, 215);        // أزرق Microsoft
            public static readonly Color PrimaryDark = Color.FromArgb(0, 90, 158);     // أزرق داكن
            public static readonly Color PrimaryLight = Color.FromArgb(173, 216, 255); // أزرق فاتح
            
            public static readonly Color Secondary = Color.FromArgb(16, 124, 16);      // أخضر
            public static readonly Color SecondaryDark = Color.FromArgb(12, 94, 12);   // أخضر داكن
            public static readonly Color SecondaryLight = Color.FromArgb(198, 239, 206); // أخضر فاتح
            
            // ألوان الخلفية
            public static readonly Color Background = Color.FromArgb(248, 249, 250);   // رمادي فاتح جداً
            public static readonly Color Surface = Color.White;                        // أبيض
            public static readonly Color SurfaceVariant = Color.FromArgb(245, 245, 245); // رمادي فاتح
            
            // ألوان النص
            public static readonly Color TextPrimary = Color.FromArgb(32, 31, 30);     // أسود ناعم
            public static readonly Color TextSecondary = Color.FromArgb(96, 94, 92);   // رمادي متوسط
            public static readonly Color TextOnPrimary = Color.White;                  // أبيض على الأزرق
            
            // ألوان الحالة
            public static readonly Color Success = Color.FromArgb(16, 124, 16);        // أخضر نجاح
            public static readonly Color Warning = Color.FromArgb(255, 185, 0);        // أصفر تحذير
            public static readonly Color Error = Color.FromArgb(196, 43, 28);          // أحمر خطأ
            public static readonly Color Info = Color.FromArgb(0, 120, 215);           // أزرق معلومات
            
            // ألوان الحدود والظلال
            public static readonly Color Border = Color.FromArgb(225, 223, 221);       // حدود فاتحة
            public static readonly Color BorderDark = Color.FromArgb(200, 198, 196);   // حدود داكنة
            public static readonly Color Shadow = Color.FromArgb(50, 0, 0, 0);         // ظل شفاف
            
            // ألوان التفاعل
            public static readonly Color Hover = Color.FromArgb(243, 242, 241);        // تمرير الماوس
            public static readonly Color Pressed = Color.FromArgb(237, 235, 233);      // ضغط
            public static readonly Color Selected = Color.FromArgb(0, 120, 215);       // محدد
            public static readonly Color Focus = Color.FromArgb(0, 120, 215);          // تركيز
        }

        #endregion

        #region الخطوط العربية

        /// <summary>
        /// مجموعة الخطوط العربية الحديثة
        /// </summary>
        public static class Fonts
        {
            // الخط الأساسي العربي
            public static readonly Font Primary = new Font("Segoe UI", 10F, FontStyle.Regular);
            public static readonly Font PrimaryBold = new Font("Segoe UI", 10F, FontStyle.Bold);
            
            // خطوط العناوين
            public static readonly Font Heading1 = new Font("Segoe UI", 24F, FontStyle.Bold);
            public static readonly Font Heading2 = new Font("Segoe UI", 20F, FontStyle.Bold);
            public static readonly Font Heading3 = new Font("Segoe UI", 16F, FontStyle.Bold);
            public static readonly Font Heading4 = new Font("Segoe UI", 14F, FontStyle.Bold);
            
            // خطوط النص
            public static readonly Font Body = new Font("Segoe UI", 10F, FontStyle.Regular);
            public static readonly Font BodyBold = new Font("Segoe UI", 10F, FontStyle.Bold);
            public static readonly Font Caption = new Font("Segoe UI", 9F, FontStyle.Regular);
            public static readonly Font Small = new Font("Segoe UI", 8F, FontStyle.Regular);
            
            // خطوط الأزرار
            public static readonly Font Button = new Font("Segoe UI", 10F, FontStyle.Bold);
            public static readonly Font ButtonLarge = new Font("Segoe UI", 12F, FontStyle.Bold);
            
            // خطوط الأرقام
            public static readonly Font Numbers = new Font("Segoe UI", 11F, FontStyle.Bold);
            public static readonly Font NumbersLarge = new Font("Segoe UI", 14F, FontStyle.Bold);
        }

        #endregion

        #region الأبعاد والمسافات

        /// <summary>
        /// نظام المسافات والأبعاد
        /// </summary>
        public static class Spacing
        {
            public const int XSmall = 4;
            public const int Small = 8;
            public const int Medium = 16;
            public const int Large = 24;
            public const int XLarge = 32;
            public const int XXLarge = 48;
            
            // مسافات خاصة
            public const int CardPadding = 16;
            public const int SectionSpacing = 24;
            public const int ComponentSpacing = 8;
        }

        /// <summary>
        /// أبعاد المكونات
        /// </summary>
        public static class Dimensions
        {
            // أبعاد الأزرار
            public const int ButtonHeight = 36;
            public const int ButtonHeightLarge = 48;
            public const int ButtonMinWidth = 120;
            
            // أبعاد الحقول
            public const int InputHeight = 32;
            public const int InputHeightLarge = 40;
            
            // أبعاد الشريط الجانبي
            public const int SidebarWidth = 240;
            public const int SidebarCollapsedWidth = 60;
            
            // أبعاد الشريط العلوي
            public const int TopBarHeight = 60;
            
            // أبعاد البطاقات
            public const int CardMinHeight = 120;
            public const int CardBorderRadius = 8;
            
            // أبعاد الأيقونات
            public const int IconSmall = 16;
            public const int IconMedium = 24;
            public const int IconLarge = 32;
            public const int IconXLarge = 48;
        }

        #endregion

        #region تأثيرات الظلال

        /// <summary>
        /// تأثيرات الظلال الحديثة
        /// </summary>
        public static class Shadows
        {
            /// <summary>
            /// ظل خفيف للبطاقات
            /// </summary>
            public static void ApplyCardShadow(Graphics g, Rectangle bounds)
            {
                using (var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    g.FillRectangle(shadowBrush, bounds.X + 2, bounds.Y + 2, bounds.Width, bounds.Height);
                }
            }

            /// <summary>
            /// ظل متوسط للعناصر المرفوعة
            /// </summary>
            public static void ApplyElevatedShadow(Graphics g, Rectangle bounds)
            {
                using (var shadowBrush = new SolidBrush(Color.FromArgb(30, 0, 0, 0)))
                {
                    g.FillRectangle(shadowBrush, bounds.X + 3, bounds.Y + 3, bounds.Width, bounds.Height);
                }
            }

            /// <summary>
            /// ظل قوي للنوافذ المنبثقة
            /// </summary>
            public static void ApplyModalShadow(Graphics g, Rectangle bounds)
            {
                using (var shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0)))
                {
                    g.FillRectangle(shadowBrush, bounds.X + 5, bounds.Y + 5, bounds.Width, bounds.Height);
                }
            }
        }

        #endregion

        #region الأيقونات

        /// <summary>
        /// مجموعة الأيقونات المستخدمة في التطبيق
        /// </summary>
        public static class Icons
        {
            // أيقونات الوحدات الرئيسية
            public const string Dashboard = "📊";
            public const string Sales = "💰";
            public const string Invoices = "📄";
            public const string Customers = "👥";
            public const string Products = "📦";
            public const string Installments = "📅";
            public const string Debts = "💳";
            public const string Reports = "📈";
            public const string Settings = "⚙️";
            
            // أيقونات الإجراءات
            public const string Add = "➕";
            public const string Edit = "✏️";
            public const string Delete = "🗑️";
            public const string Search = "🔍";
            public const string Print = "🖨️";
            public const string Save = "💾";
            public const string Cancel = "❌";
            public const string Confirm = "✅";
            
            // أيقونات الحالة
            public const string Success = "✅";
            public const string Warning = "⚠️";
            public const string Error = "❌";
            public const string Info = "ℹ️";
            
            // أيقونات التنقل
            public const string Back = "⬅️";
            public const string Forward = "➡️";
            public const string Up = "⬆️";
            public const string Down = "⬇️";
            public const string Menu = "☰";
            public const string Close = "✖️";
        }

        #endregion

        #region مساعدات الرسم

        /// <summary>
        /// رسم بطاقة حديثة مع ظل وحواف مدورة
        /// </summary>
        public static void DrawModernCard(Graphics g, Rectangle bounds, Color backgroundColor, int cornerRadius = 8)
        {
            // رسم الظل
            using (var shadowBrush = new SolidBrush(Color.FromArgb(15, 0, 0, 0)))
            {
                var shadowBounds = new Rectangle(bounds.X + 2, bounds.Y + 2, bounds.Width, bounds.Height);
                using (var shadowPath = CreateRoundedRectangle(shadowBounds, cornerRadius))
                {
                    g.FillPath(shadowBrush, shadowPath);
                }
            }

            // رسم البطاقة
            using (var cardBrush = new SolidBrush(backgroundColor))
            using (var cardPath = CreateRoundedRectangle(bounds, cornerRadius))
            {
                g.FillPath(cardBrush, cardPath);
            }

            // رسم الحدود
            using (var borderPen = new Pen(Colors.Border, 1))
            using (var borderPath = CreateRoundedRectangle(bounds, cornerRadius))
            {
                g.DrawPath(borderPen, borderPath);
            }
        }

        /// <summary>
        /// رسم زر حديث مع تدرج وحواف مدورة
        /// </summary>
        public static void DrawModernButton(Graphics g, Rectangle bounds, Color baseColor, string text, Font font, bool isHovered = false, bool isPressed = false)
        {
            var buttonColor = baseColor;
            if (isPressed)
                buttonColor = DarkenColor(baseColor, 0.2f);
            else if (isHovered)
                buttonColor = LightenColor(baseColor, 0.1f);

            // رسم الزر
            using (var buttonBrush = new SolidBrush(buttonColor))
            using (var buttonPath = CreateRoundedRectangle(bounds, 6))
            {
                g.FillPath(buttonBrush, buttonPath);
            }

            // رسم النص
            var textColor = IsLightColor(buttonColor) ? Colors.TextPrimary : Colors.TextOnPrimary;
            using (var textBrush = new SolidBrush(textColor))
            {
                var textFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center,
                    FormatFlags = StringFormatFlags.DirectionRightToLeft
                };
                g.DrawString(text, font, textBrush, bounds, textFormat);
            }
        }

        /// <summary>
        /// إنشاء مستطيل بحواف مدورة
        /// </summary>
        public static GraphicsPath CreateRoundedRectangle(Rectangle bounds, int cornerRadius)
        {
            var path = new GraphicsPath();
            var diameter = cornerRadius * 2;

            path.AddArc(bounds.X, bounds.Y, diameter, diameter, 180, 90);
            path.AddArc(bounds.Right - diameter, bounds.Y, diameter, diameter, 270, 90);
            path.AddArc(bounds.Right - diameter, bounds.Bottom - diameter, diameter, diameter, 0, 90);
            path.AddArc(bounds.X, bounds.Bottom - diameter, diameter, diameter, 90, 90);
            path.CloseFigure();

            return path;
        }

        /// <summary>
        /// تفتيح لون
        /// </summary>
        public static Color LightenColor(Color color, float factor)
        {
            var r = (int)(color.R + (255 - color.R) * factor);
            var g = (int)(color.G + (255 - color.G) * factor);
            var b = (int)(color.B + (255 - color.B) * factor);
            return Color.FromArgb(color.A, Math.Min(255, r), Math.Min(255, g), Math.Min(255, b));
        }

        /// <summary>
        /// تغميق لون
        /// </summary>
        public static Color DarkenColor(Color color, float factor)
        {
            var r = (int)(color.R * (1 - factor));
            var g = (int)(color.G * (1 - factor));
            var b = (int)(color.B * (1 - factor));
            return Color.FromArgb(color.A, Math.Max(0, r), Math.Max(0, g), Math.Max(0, b));
        }

        /// <summary>
        /// تحديد ما إذا كان اللون فاتحاً
        /// </summary>
        public static bool IsLightColor(Color color)
        {
            var brightness = (color.R * 0.299 + color.G * 0.587 + color.B * 0.114) / 255;
            return brightness > 0.5;
        }

        #endregion
    }
}
