using System;
using System.Drawing;
using System.Globalization;
using System.Threading;
using System.Windows.Forms;

namespace AredooCashier
{
    /// <summary>
    /// البرنامج الرئيسي لتطبيق أريدو الكاشير المتجاوب
    /// يدعم جميع أحجام الشاشات من 1366x768 إلى 4K
    /// </summary>
    internal static class ResponsiveProgram
    {
        /// <summary>
        /// نقطة الدخول الرئيسية للتطبيق المتجاوب
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // تهيئة التطبيق المتجاوب
                InitializeResponsiveApplication();

                // تعيين الثقافة العربية
                SetArabicCulture();

                // تعيين معالج الأخطاء العام
                SetupErrorHandling();

                // كشف معلومات الشاشة وتطبيق الإعدادات المناسبة
                DetectAndConfigureScreen();

                // عرض شاشة البداية المتجاوبة
                ShowResponsiveSplashScreen();

                // تشغيل التطبيق الرئيسي المتجاوب
                Application.Run(new ResponsiveAredooCashierApp());
            }
            catch (Exception ex)
            {
                ShowResponsiveErrorMessage("خطأ في بدء التشغيل", ex);
            }
        }

        /// <summary>
        /// تهيئة التطبيق المتجاوب
        /// </summary>
        private static void InitializeResponsiveApplication()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            // تمكين الوعي بـ DPI للتطبيقات المتجاوبة
            if (Environment.OSVersion.Version.Major >= 6)
            {
                SetProcessDPIAware();
            }
        }

        /// <summary>
        /// تمكين الوعي بـ DPI
        /// </summary>
        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool SetProcessDPIAware();

        /// <summary>
        /// تعيين الثقافة العربية
        /// </summary>
        private static void SetArabicCulture()
        {
            try
            {
                var arabicCulture = new CultureInfo("ar-SA");
                Thread.CurrentThread.CurrentCulture = arabicCulture;
                Thread.CurrentThread.CurrentUICulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentCulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentUICulture = arabicCulture;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"تحذير: فشل في تعيين الثقافة العربية - {ex.Message}");
            }
        }

        /// <summary>
        /// إعداد معالجة الأخطاء
        /// </summary>
        private static void SetupErrorHandling()
        {
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            Application.ThreadException += OnThreadException;
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
        }

        /// <summary>
        /// كشف وتكوين الشاشة
        /// </summary>
        private static void DetectAndConfigureScreen()
        {
            var primaryScreen = Screen.PrimaryScreen;
            var screenWidth = primaryScreen.Bounds.Width;
            var screenHeight = primaryScreen.Bounds.Height;
            var workingArea = primaryScreen.WorkingArea;

            Console.WriteLine($"معلومات الشاشة:");
            Console.WriteLine($"الدقة: {screenWidth}x{screenHeight}");
            Console.WriteLine($"منطقة العمل: {workingArea.Width}x{workingArea.Height}");
            Console.WriteLine($"عدد الشاشات: {Screen.AllScreens.Length}");

            // تحديد نوع الشاشة وتطبيق الإعدادات المناسبة
            var screenType = DetermineScreenType(screenWidth, screenHeight);
            Console.WriteLine($"نوع الشاشة: {screenType}");

            // تطبيق إعدادات خاصة بنوع الشاشة
            ApplyScreenSpecificSettings(screenType);
        }

        /// <summary>
        /// تحديد نوع الشاشة
        /// </summary>
        private static string DetermineScreenType(int width, int height)
        {
            return (width, height) switch
            {
                var (w, h) when w >= 3840 && h >= 2160 => "4K Ultra HD",
                var (w, h) when w >= 2560 && h >= 1440 => "QHD/2K",
                var (w, h) when w >= 1920 && h >= 1080 => "Full HD",
                var (w, h) when w >= 1600 && h >= 900 => "HD+",
                var (w, h) when w >= 1366 && h >= 768 => "HD",
                _ => "قياسي"
            };
        }

        /// <summary>
        /// تطبيق إعدادات خاصة بنوع الشاشة
        /// </summary>
        private static void ApplyScreenSpecificSettings(string screenType)
        {
            // يمكن إضافة إعدادات خاصة لكل نوع شاشة هنا
            switch (screenType)
            {
                case "4K Ultra HD":
                    Console.WriteLine("تطبيق إعدادات 4K - تكبير 200%");
                    break;
                case "QHD/2K":
                    Console.WriteLine("تطبيق إعدادات QHD - تكبير 150%");
                    break;
                case "Full HD":
                    Console.WriteLine("تطبيق إعدادات Full HD - تكبير 125%");
                    break;
                default:
                    Console.WriteLine("تطبيق إعدادات قياسية - تكبير 100%");
                    break;
            }
        }

        /// <summary>
        /// عرض شاشة البداية المتجاوبة
        /// </summary>
        private static void ShowResponsiveSplashScreen()
        {
            var screenBounds = Screen.PrimaryScreen.Bounds;
            var scaleFactor = Math.Max(1.0f, Math.Min(screenBounds.Width / 1920f, screenBounds.Height / 1080f));
            
            var splashWidth = (int)(600 * scaleFactor);
            var splashHeight = (int)(400 * scaleFactor);

            var splash = new Form
            {
                Text = "أريدو الكاشير المتجاوب",
                Size = new Size(splashWidth, splashHeight),
                StartPosition = FormStartPosition.CenterScreen,
                FormBorderStyle = FormBorderStyle.None,
                BackColor = Color.FromArgb(0, 120, 215),
                ShowInTaskbar = false,
                TopMost = true
            };

            // شعار التطبيق
            var logoLabel = new Label
            {
                Text = "🏪",
                Font = new Font("Segoe UI Emoji", 48 * scaleFactor),
                ForeColor = Color.White,
                Size = new Size((int)(120 * scaleFactor), (int)(80 * scaleFactor)),
                Location = new Point((splashWidth - (int)(120 * scaleFactor)) / 2, (int)(60 * scaleFactor)),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // عنوان التطبيق
            var titleLabel = new Label
            {
                Text = "أريدو الكاشير المتجاوب",
                Font = new Font("Segoe UI", 28 * scaleFactor, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(splashWidth - (int)(40 * scaleFactor), (int)(50 * scaleFactor)),
                Location = new Point((int)(20 * scaleFactor), (int)(160 * scaleFactor)),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            // العنوان الفرعي
            var subtitleLabel = new Label
            {
                Text = "نظام نقاط البيع المتكيف مع جميع الشاشات",
                Font = new Font("Segoe UI", 14 * scaleFactor),
                ForeColor = Color.FromArgb(200, 255, 255, 255),
                Size = new Size(splashWidth - (int)(40 * scaleFactor), (int)(30 * scaleFactor)),
                Location = new Point((int)(20 * scaleFactor), (int)(220 * scaleFactor)),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            // معلومات الشاشة
            var screenInfo = $"الشاشة: {screenBounds.Width}x{screenBounds.Height} | التكبير: {scaleFactor:F1}x";
            var screenLabel = new Label
            {
                Text = screenInfo,
                Font = new Font("Segoe UI", 10 * scaleFactor),
                ForeColor = Color.FromArgb(180, 255, 255, 255),
                Size = new Size(splashWidth - (int)(40 * scaleFactor), (int)(25 * scaleFactor)),
                Location = new Point((int)(20 * scaleFactor), (int)(260 * scaleFactor)),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // رسالة التحميل
            var loadingLabel = new Label
            {
                Text = "جاري التحميل والتكيف مع الشاشة...",
                Font = new Font("Segoe UI", 12 * scaleFactor),
                ForeColor = Color.FromArgb(180, 255, 255, 255),
                Size = new Size(splashWidth - (int)(40 * scaleFactor), (int)(25 * scaleFactor)),
                Location = new Point((int)(20 * scaleFactor), (int)(320 * scaleFactor)),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            splash.Controls.AddRange(new Control[] { logoLabel, titleLabel, subtitleLabel, screenLabel, loadingLabel });

            // عرض الشاشة مع تأثير تدريجي
            splash.Show();
            Application.DoEvents();

            // محاكاة التحميل مع تحديث الرسائل
            var loadingMessages = new[]
            {
                "تحليل دقة الشاشة...",
                "تطبيق التكبير المناسب...",
                "تحميل الخطوط العربية...",
                "إعداد التخطيط المتجاوب...",
                "تهيئة المكونات...",
                "جاهز للاستخدام!"
            };

            foreach (var message in loadingMessages)
            {
                loadingLabel.Text = message;
                Application.DoEvents();
                System.Threading.Thread.Sleep(400);
            }

            splash.Close();
        }

        /// <summary>
        /// معالج أخطاء الخيوط
        /// </summary>
        private static void OnThreadException(object sender, ThreadExceptionEventArgs e)
        {
            ShowResponsiveErrorMessage("خطأ في التطبيق", e.Exception);
        }

        /// <summary>
        /// معالج الأخطاء غير المعالجة
        /// </summary>
        private static void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                ShowResponsiveErrorMessage("خطأ حرج", ex);
            }
        }

        /// <summary>
        /// عرض رسالة خطأ متجاوبة
        /// </summary>
        private static void ShowResponsiveErrorMessage(string title, Exception ex)
        {
            var screenBounds = Screen.PrimaryScreen.Bounds;
            var scaleFactor = Math.Max(1.0f, Math.Min(screenBounds.Width / 1920f, screenBounds.Height / 1080f));

            var message = $"❌ حدث خطأ في تطبيق أريدو الكاشير المتجاوب:\n\n{ex.Message}";
            
            if (ex.InnerException != null)
            {
                message += $"\n\n🔍 تفاصيل إضافية:\n{ex.InnerException.Message}";
            }

            message += $"\n\n📊 معلومات النظام:" +
                      $"\n🖥️ دقة الشاشة: {screenBounds.Width}x{screenBounds.Height}" +
                      $"\n📏 معامل التكبير: {scaleFactor:F1}x" +
                      $"\n⏰ الوقت: {DateTime.Now:yyyy/MM/dd HH:mm:ss}";

            message += "\n\n📞 يرجى الاتصال بالدعم الفني إذا استمر هذا الخطأ:\n" +
                      "الهاتف: +966 XX XXX XXXX\n" +
                      "البريد: <EMAIL>";

            MessageBox.Show(message, $"خطأ - {title}", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
