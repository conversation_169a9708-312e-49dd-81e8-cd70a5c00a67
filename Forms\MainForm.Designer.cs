using System;
using System.Drawing;
using System.Windows.Forms;

namespace AredooPOS.Forms
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            
            // إنشاء العناصر
            this.pnlHeader = new Panel();
            this.lblTitle = new Label();
            this.lblUserInfo = new Label();
            this.lblDateTime = new Label();
            
            this.pnlToolbar = new Panel();
            this.btnNewInvoice = new Button();
            this.btnSearchInvoices = new Button();
            this.btnProducts = new Button();
            this.btnCustomers = new Button();
            this.btnReports = new Button();
            this.btnSettings = new Button();
            
            this.pnlStats = new Panel();
            this.lblTodayInvoices = new Label();
            this.lblTodaySales = new Label();
            this.lblLowStock = new Label();
            
            this.statusStrip = new StatusStrip();
            this.lblStatus = new ToolStripStatusLabel();
            this.lblDatabase = new ToolStripStatusLabel();
            this.lblTime = new ToolStripStatusLabel();
            
            this.timerDateTime = new Timer(this.components);
            
            // تعليق التخطيط
            this.pnlHeader.SuspendLayout();
            this.pnlToolbar.SuspendLayout();
            this.pnlStats.SuspendLayout();
            this.statusStrip.SuspendLayout();
            this.SuspendLayout();

            // ===== شريط العنوان =====
            this.pnlHeader.Controls.Add(this.lblTitle);
            this.pnlHeader.Controls.Add(this.lblUserInfo);
            this.pnlHeader.Controls.Add(this.lblDateTime);
            this.pnlHeader.Dock = DockStyle.Top;
            this.pnlHeader.Location = new Point(0, 0);
            this.pnlHeader.Name = "pnlHeader";
            this.pnlHeader.Size = new Size(1200, 80);
            this.pnlHeader.TabIndex = 0;

            // عنوان النظام
            this.lblTitle.AutoSize = false;
            this.lblTitle.Font = new Font("Tahoma", 24F, FontStyle.Bold);
            this.lblTitle.Location = new Point(20, 15);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(400, 50);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "أريدوو - نظام نقاط البيع";
            this.lblTitle.TextAlign = ContentAlignment.MiddleLeft;

            // معلومات المستخدم
            this.lblUserInfo.AutoSize = false;
            this.lblUserInfo.Font = new Font("Tahoma", 12F);
            this.lblUserInfo.Location = new Point(450, 20);
            this.lblUserInfo.Name = "lblUserInfo";
            this.lblUserInfo.Size = new Size(200, 25);
            this.lblUserInfo.TabIndex = 1;
            this.lblUserInfo.Text = "المستخدم: كاشير";
            this.lblUserInfo.TextAlign = ContentAlignment.MiddleLeft;

            // التاريخ والوقت
            this.lblDateTime.AutoSize = false;
            this.lblDateTime.Font = new Font("Tahoma", 12F);
            this.lblDateTime.Location = new Point(450, 45);
            this.lblDateTime.Name = "lblDateTime";
            this.lblDateTime.Size = new Size(200, 25);
            this.lblDateTime.TabIndex = 2;
            this.lblDateTime.Text = "2024/12/19 10:30:00";
            this.lblDateTime.TextAlign = ContentAlignment.MiddleLeft;

            // ===== شريط الأدوات =====
            this.pnlToolbar.Controls.Add(this.btnNewInvoice);
            this.pnlToolbar.Controls.Add(this.btnSearchInvoices);
            this.pnlToolbar.Controls.Add(this.btnProducts);
            this.pnlToolbar.Controls.Add(this.btnCustomers);
            this.pnlToolbar.Controls.Add(this.btnReports);
            this.pnlToolbar.Controls.Add(this.btnSettings);
            this.pnlToolbar.Dock = DockStyle.Top;
            this.pnlToolbar.Location = new Point(0, 80);
            this.pnlToolbar.Name = "pnlToolbar";
            this.pnlToolbar.Size = new Size(1200, 120);
            this.pnlToolbar.TabIndex = 1;

            // زر فاتورة جديدة
            this.btnNewInvoice.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            this.btnNewInvoice.Location = new Point(20, 20);
            this.btnNewInvoice.Name = "btnNewInvoice";
            this.btnNewInvoice.Size = new Size(180, 80);
            this.btnNewInvoice.TabIndex = 0;
            this.btnNewInvoice.Text = "فاتورة جديدة\nF1";
            this.btnNewInvoice.UseVisualStyleBackColor = false;

            // زر البحث في الفواتير
            this.btnSearchInvoices.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            this.btnSearchInvoices.Location = new Point(220, 20);
            this.btnSearchInvoices.Name = "btnSearchInvoices";
            this.btnSearchInvoices.Size = new Size(180, 80);
            this.btnSearchInvoices.TabIndex = 1;
            this.btnSearchInvoices.Text = "البحث في الفواتير\nF2";
            this.btnSearchInvoices.UseVisualStyleBackColor = false;

            // زر إدارة المنتجات
            this.btnProducts.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            this.btnProducts.Location = new Point(420, 20);
            this.btnProducts.Name = "btnProducts";
            this.btnProducts.Size = new Size(180, 80);
            this.btnProducts.TabIndex = 2;
            this.btnProducts.Text = "إدارة المنتجات\nF3";
            this.btnProducts.UseVisualStyleBackColor = false;

            // زر إدارة العملاء
            this.btnCustomers.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            this.btnCustomers.Location = new Point(620, 20);
            this.btnCustomers.Name = "btnCustomers";
            this.btnCustomers.Size = new Size(180, 80);
            this.btnCustomers.TabIndex = 3;
            this.btnCustomers.Text = "إدارة العملاء\nF4";
            this.btnCustomers.UseVisualStyleBackColor = false;

            // زر التقارير
            this.btnReports.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            this.btnReports.Location = new Point(820, 20);
            this.btnReports.Name = "btnReports";
            this.btnReports.Size = new Size(180, 80);
            this.btnReports.TabIndex = 4;
            this.btnReports.Text = "التقارير\nF5";
            this.btnReports.UseVisualStyleBackColor = false;

            // زر الإعدادات
            this.btnSettings.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            this.btnSettings.Location = new Point(1020, 20);
            this.btnSettings.Name = "btnSettings";
            this.btnSettings.Size = new Size(160, 80);
            this.btnSettings.TabIndex = 5;
            this.btnSettings.Text = "الإعدادات\nF6";
            this.btnSettings.UseVisualStyleBackColor = false;

            // ===== لوحة الإحصائيات =====
            this.pnlStats.Controls.Add(this.lblTodayInvoices);
            this.pnlStats.Controls.Add(this.lblTodaySales);
            this.pnlStats.Controls.Add(this.lblLowStock);
            this.pnlStats.Dock = DockStyle.Top;
            this.pnlStats.Location = new Point(0, 200);
            this.pnlStats.Name = "pnlStats";
            this.pnlStats.Size = new Size(1200, 60);
            this.pnlStats.TabIndex = 2;
            this.pnlStats.BackColor = Color.FromArgb(236, 240, 241);

            // إحصائية فواتير اليوم
            this.lblTodayInvoices.AutoSize = false;
            this.lblTodayInvoices.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblTodayInvoices.Location = new Point(20, 15);
            this.lblTodayInvoices.Name = "lblTodayInvoices";
            this.lblTodayInvoices.Size = new Size(200, 30);
            this.lblTodayInvoices.TabIndex = 0;
            this.lblTodayInvoices.Text = "فواتير اليوم: 0";
            this.lblTodayInvoices.TextAlign = ContentAlignment.MiddleLeft;

            // إحصائية مبيعات اليوم
            this.lblTodaySales.AutoSize = false;
            this.lblTodaySales.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblTodaySales.Location = new Point(250, 15);
            this.lblTodaySales.Name = "lblTodaySales";
            this.lblTodaySales.Size = new Size(250, 30);
            this.lblTodaySales.TabIndex = 1;
            this.lblTodaySales.Text = "مبيعات اليوم: 0.00 ر.س";
            this.lblTodaySales.TextAlign = ContentAlignment.MiddleLeft;

            // إحصائية المنتجات منخفضة المخزون
            this.lblLowStock.AutoSize = false;
            this.lblLowStock.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            this.lblLowStock.Location = new Point(530, 15);
            this.lblLowStock.Name = "lblLowStock";
            this.lblLowStock.Size = new Size(300, 30);
            this.lblLowStock.TabIndex = 2;
            this.lblLowStock.Text = "منتجات منخفضة المخزون: 0";
            this.lblLowStock.TextAlign = ContentAlignment.MiddleLeft;

            // ===== شريط الحالة =====
            this.statusStrip.Items.AddRange(new ToolStripItem[] {
                this.lblStatus,
                this.lblDatabase,
                this.lblTime});
            this.statusStrip.Location = new Point(0, 700);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new Size(1200, 25);
            this.statusStrip.TabIndex = 3;

            // حالة النظام
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new Size(400, 20);
            this.lblStatus.Spring = true;
            this.lblStatus.Text = "جاهز";
            this.lblStatus.TextAlign = ContentAlignment.MiddleLeft;

            // حالة قاعدة البيانات
            this.lblDatabase.Name = "lblDatabase";
            this.lblDatabase.Size = new Size(200, 20);
            this.lblDatabase.Text = "قاعدة البيانات: متصلة";

            // الوقت الحالي
            this.lblTime.Name = "lblTime";
            this.lblTime.Size = new Size(150, 20);
            this.lblTime.Text = "2024/12/19 10:30:00";

            // ===== مؤقت التاريخ والوقت =====
            this.timerDateTime.Interval = 1000;
            this.timerDateTime.Enabled = true;

            // ===== النموذج الرئيسي =====
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 725);
            this.Controls.Add(this.pnlStats);
            this.Controls.Add(this.pnlToolbar);
            this.Controls.Add(this.pnlHeader);
            this.Controls.Add(this.statusStrip);
            this.Name = "MainForm";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "أريدوو - نظام نقاط البيع";
            this.WindowState = FormWindowState.Maximized;

            // استئناف التخطيط
            this.pnlHeader.ResumeLayout(false);
            this.pnlToolbar.ResumeLayout(false);
            this.pnlStats.ResumeLayout(false);
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        #region العناصر المُعرَّفة

        private Panel pnlHeader;
        private Label lblTitle;
        private Label lblUserInfo;
        private Label lblDateTime;
        
        private Panel pnlToolbar;
        private Button btnNewInvoice;
        private Button btnSearchInvoices;
        private Button btnProducts;
        private Button btnCustomers;
        private Button btnReports;
        private Button btnSettings;
        
        private Panel pnlStats;
        private Label lblTodayInvoices;
        private Label lblTodaySales;
        private Label lblLowStock;
        
        private StatusStrip statusStrip;
        private ToolStripStatusLabel lblStatus;
        private ToolStripStatusLabel lblDatabase;
        private ToolStripStatusLabel lblTime;
        
        private Timer timerDateTime;

        #endregion
    }
}
