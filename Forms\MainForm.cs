using System;
using System.Drawing;
using System.Windows.Forms;
using AredooPOS.Core.Database;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// الواجهة الرئيسية لنظام أريدوو لنقاط البيع
    /// تحتوي على جميع الوظائف الأساسية للكاشير
    /// </summary>
    public partial class MainForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly DatabaseManager _databaseManager;
        private readonly ILogger<MainForm> _logger;
        private string _currentUser;
        private string _currentShift;

        // ألوان النظام
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);      // أزرق أساسي
        private readonly Color SecondaryColor = Color.FromArgb(52, 152, 219);    // أزرق فاتح
        private readonly Color SuccessColor = Color.FromArgb(46, 204, 113);      // أخضر
        private readonly Color WarningColor = Color.FromArgb(241, 196, 15);      // أصفر
        private readonly Color DangerColor = Color.FromArgb(231, 76, 60);        // أحمر
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);        // رمادي فاتح
        private readonly Color DarkGray = Color.FromArgb(52, 73, 94);            // رمادي غامق

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ الواجهة الرئيسية
        /// </summary>
        /// <param name="databaseManager">مدير قاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        public MainForm(DatabaseManager databaseManager, ILogger<MainForm> logger = null, string currentUser = "كاشير")
        {
            _databaseManager = databaseManager ?? throw new ArgumentNullException(nameof(databaseManager));
            _logger = logger;
            _currentUser = currentUser;

            InitializeComponent();
            InitializeArabicUI();
            LoadInitialData();
            SetupEventHandlers();
        }

        /// <summary>
        /// تهيئة الواجهة العربية
        /// </summary>
        private void InitializeArabicUI()
        {
            // إعدادات النموذج الأساسية
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;
            this.Text = "أريدوو - نظام نقاط البيع";
            this.BackColor = LightGray;

            // تطبيق الألوان على العناصر
            ApplyThemeColors();

            // تحديث النصوص
            UpdateUITexts();
        }

        /// <summary>
        /// تطبيق ألوان النظام
        /// </summary>
        private void ApplyThemeColors()
        {
            // شريط العنوان
            pnlHeader.BackColor = PrimaryColor;
            lblTitle.ForeColor = Color.White;
            lblUserInfo.ForeColor = Color.White;
            lblDateTime.ForeColor = Color.White;

            // شريط الأدوات
            pnlToolbar.BackColor = SecondaryColor;

            // الأزرار الرئيسية
            btnNewInvoice.BackColor = SuccessColor;
            btnNewInvoice.ForeColor = Color.White;
            btnNewInvoice.FlatStyle = FlatStyle.Flat;
            btnNewInvoice.FlatAppearance.BorderSize = 0;

            btnSearchInvoices.BackColor = PrimaryColor;
            btnSearchInvoices.ForeColor = Color.White;
            btnSearchInvoices.FlatStyle = FlatStyle.Flat;
            btnSearchInvoices.FlatAppearance.BorderSize = 0;

            btnProducts.BackColor = WarningColor;
            btnProducts.ForeColor = Color.White;
            btnProducts.FlatStyle = FlatStyle.Flat;
            btnProducts.FlatAppearance.BorderSize = 0;

            btnCustomers.BackColor = SecondaryColor;
            btnCustomers.ForeColor = Color.White;
            btnCustomers.FlatStyle = FlatStyle.Flat;
            btnCustomers.FlatAppearance.BorderSize = 0;

            btnReports.BackColor = DarkGray;
            btnReports.ForeColor = Color.White;
            btnReports.FlatStyle = FlatStyle.Flat;
            btnReports.FlatAppearance.BorderSize = 0;

            btnSettings.BackColor = DangerColor;
            btnSettings.ForeColor = Color.White;
            btnSettings.FlatStyle = FlatStyle.Flat;
            btnSettings.FlatAppearance.BorderSize = 0;

            // شريط الحالة
            statusStrip.BackColor = DarkGray;
            statusStrip.ForeColor = Color.White;
        }

        /// <summary>
        /// تحديث النصوص في الواجهة
        /// </summary>
        private void UpdateUITexts()
        {
            lblTitle.Text = "أريدوو - نظام نقاط البيع";
            lblUserInfo.Text = $"المستخدم: {_currentUser}";
            
            btnNewInvoice.Text = "فاتورة جديدة\nF1";
            btnSearchInvoices.Text = "البحث في الفواتير\nF2";
            btnProducts.Text = "إدارة المنتجات\nF3";
            btnCustomers.Text = "إدارة العملاء\nF4";
            btnReports.Text = "التقارير\nF5";
            btnSettings.Text = "الإعدادات\nF6";

            // شريط الحالة
            lblStatus.Text = "جاهز";
            lblDatabase.Text = "قاعدة البيانات: متصلة";
            lblTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
        }

        /// <summary>
        /// تحميل البيانات الأولية
        /// </summary>
        private void LoadInitialData()
        {
            try
            {
                // اختبار الاتصال بقاعدة البيانات
                if (_databaseManager.TestConnection())
                {
                    lblDatabase.Text = "قاعدة البيانات: متصلة ✓";
                    lblDatabase.ForeColor = SuccessColor;
                    _logger?.LogInformation("تم الاتصال بقاعدة البيانات بنجاح");
                }
                else
                {
                    lblDatabase.Text = "قاعدة البيانات: غير متصلة ✗";
                    lblDatabase.ForeColor = DangerColor;
                    _logger?.LogError("فشل في الاتصال بقاعدة البيانات");
                }

                // تحميل إحصائيات سريعة
                LoadQuickStats();

                // بدء مؤقت تحديث الوقت
                timerDateTime.Start();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل البيانات الأولية");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل الإحصائيات السريعة
        /// </summary>
        private void LoadQuickStats()
        {
            try
            {
                // عدد الفواتير اليوم
                var todayInvoicesQuery = @"
                    SELECT COUNT(*) FROM Invoices 
                    WHERE DATE(InvoiceDate) = DATE('now') AND InvoiceStatus = 'مكتملة'";
                var todayInvoices = _databaseManager.ExecuteScalar(todayInvoicesQuery);
                
                // مبيعات اليوم
                var todaySalesQuery = @"
                    SELECT COALESCE(SUM(TotalAmount), 0) FROM Invoices 
                    WHERE DATE(InvoiceDate) = DATE('now') AND InvoiceStatus = 'مكتملة'";
                var todaySales = _databaseManager.ExecuteScalar(todaySalesQuery);

                // عدد المنتجات منخفضة المخزون
                var lowStockQuery = @"
                    SELECT COUNT(*) FROM Products 
                    WHERE IsActive = 1 AND StockQuantity <= MinStockLevel AND MinStockLevel > 0";
                var lowStockCount = _databaseManager.ExecuteScalar(lowStockQuery);

                // تحديث اللوحة الإحصائية
                UpdateStatsPanel(
                    Convert.ToInt32(todayInvoices ?? 0),
                    Convert.ToDecimal(todaySales ?? 0),
                    Convert.ToInt32(lowStockCount ?? 0)
                );
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل الإحصائيات");
            }
        }

        /// <summary>
        /// تحديث لوحة الإحصائيات
        /// </summary>
        /// <param name="todayInvoices">عدد فواتير اليوم</param>
        /// <param name="todaySales">مبيعات اليوم</param>
        /// <param name="lowStockCount">عدد المنتجات منخفضة المخزون</param>
        private void UpdateStatsPanel(int todayInvoices, decimal todaySales, int lowStockCount)
        {
            lblTodayInvoices.Text = $"فواتير اليوم: {todayInvoices:N0}";
            lblTodaySales.Text = $"مبيعات اليوم: {todaySales:C}";
            
            lblLowStock.Text = $"منتجات منخفضة المخزون: {lowStockCount:N0}";
            lblLowStock.ForeColor = lowStockCount > 0 ? DangerColor : SuccessColor;
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // أحداث الأزرار
            btnNewInvoice.Click += BtnNewInvoice_Click;
            btnSearchInvoices.Click += BtnSearchInvoices_Click;
            btnProducts.Click += BtnProducts_Click;
            btnCustomers.Click += BtnCustomers_Click;
            btnReports.Click += BtnReports_Click;
            btnSettings.Click += BtnSettings_Click;

            // أحداث لوحة المفاتيح
            this.KeyPreview = true;
            this.KeyDown += MainForm_KeyDown;

            // أحداث المؤقت
            timerDateTime.Tick += TimerDateTime_Tick;

            // أحداث النموذج
            this.FormClosing += MainForm_FormClosing;
            this.Load += MainForm_Load;
        }

        #endregion

        #region معالجات الأحداث

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void MainForm_Load(object sender, EventArgs e)
        {
            _logger?.LogInformation("تم تحميل الواجهة الرئيسية بنجاح");
            lblStatus.Text = "جاهز للعمل";
        }

        /// <summary>
        /// حدث إغلاق النموذج
        /// </summary>
        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد إغلاق النظام؟",
                "تأكيد الإغلاق",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.No)
            {
                e.Cancel = true;
                return;
            }

            _logger?.LogInformation("تم إغلاق النظام بواسطة المستخدم");
        }

        /// <summary>
        /// حدث الضغط على مفاتيح الاختصار
        /// </summary>
        private void MainForm_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.F1:
                    BtnNewInvoice_Click(sender, e);
                    break;
                case Keys.F2:
                    BtnSearchInvoices_Click(sender, e);
                    break;
                case Keys.F3:
                    BtnProducts_Click(sender, e);
                    break;
                case Keys.F4:
                    BtnCustomers_Click(sender, e);
                    break;
                case Keys.F5:
                    BtnReports_Click(sender, e);
                    break;
                case Keys.F6:
                    BtnSettings_Click(sender, e);
                    break;
                case Keys.Escape:
                    this.Close();
                    break;
            }
        }

        /// <summary>
        /// حدث تحديث الوقت
        /// </summary>
        private void TimerDateTime_Tick(object sender, EventArgs e)
        {
            lblTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
            
            // تحديث الإحصائيات كل دقيقة
            if (DateTime.Now.Second == 0)
            {
                LoadQuickStats();
            }
        }

        /// <summary>
        /// حدث النقر على زر فاتورة جديدة
        /// </summary>
        private void BtnNewInvoice_Click(object sender, EventArgs e)
        {
            try
            {
                lblStatus.Text = "فتح نافذة فاتورة جديدة...";
                
                // فتح نافذة الفاتورة الجديدة
                var invoiceForm = new InvoiceForm(_databaseManager, _logger);
                invoiceForm.ShowDialog();
                
                // تحديث الإحصائيات بعد إغلاق النافذة
                LoadQuickStats();
                lblStatus.Text = "جاهز";
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فتح نافذة الفاتورة الجديدة");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في فتح الفاتورة";
            }
        }

        /// <summary>
        /// حدث النقر على زر البحث في الفواتير
        /// </summary>
        private void BtnSearchInvoices_Click(object sender, EventArgs e)
        {
            try
            {
                lblStatus.Text = "فتح نافذة البحث في الفواتير...";
                
                var searchForm = new InvoiceSearchForm(_databaseManager, _logger);
                searchForm.ShowDialog();
                
                lblStatus.Text = "جاهز";
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فتح نافذة البحث");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في فتح البحث";
            }
        }

        /// <summary>
        /// حدث النقر على زر إدارة المنتجات
        /// </summary>
        private void BtnProducts_Click(object sender, EventArgs e)
        {
            try
            {
                lblStatus.Text = "فتح نافذة إدارة المنتجات...";
                
                var productsForm = new ProductsForm(_databaseManager, _logger);
                productsForm.ShowDialog();
                
                LoadQuickStats();
                lblStatus.Text = "جاهز";
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فتح نافذة المنتجات");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في فتح المنتجات";
            }
        }

        /// <summary>
        /// حدث النقر على زر إدارة العملاء
        /// </summary>
        private void BtnCustomers_Click(object sender, EventArgs e)
        {
            try
            {
                lblStatus.Text = "فتح نافذة إدارة العملاء...";
                
                var customersForm = new CustomersForm(_databaseManager, _logger);
                customersForm.ShowDialog();
                
                lblStatus.Text = "جاهز";
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فتح نافذة العملاء");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في فتح العملاء";
            }
        }

        /// <summary>
        /// حدث النقر على زر التقارير
        /// </summary>
        private void BtnReports_Click(object sender, EventArgs e)
        {
            try
            {
                lblStatus.Text = "فتح نافذة التقارير...";
                
                var reportsForm = new ReportsForm(_databaseManager, _logger);
                reportsForm.ShowDialog();
                
                lblStatus.Text = "جاهز";
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فتح نافذة التقارير");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في فتح التقارير";
            }
        }

        /// <summary>
        /// حدث النقر على زر الإعدادات
        /// </summary>
        private void BtnSettings_Click(object sender, EventArgs e)
        {
            try
            {
                lblStatus.Text = "فتح نافذة الإعدادات...";
                
                var settingsForm = new SettingsForm(_databaseManager, _logger);
                settingsForm.ShowDialog();
                
                lblStatus.Text = "جاهز";
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فتح نافذة الإعدادات");
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "خطأ في فتح الإعدادات";
            }
        }

        #endregion

        #region عمليات مساعدة

        /// <summary>
        /// تحديث حالة النظام
        /// </summary>
        /// <param name="message">رسالة الحالة</param>
        public void UpdateStatus(string message)
        {
            lblStatus.Text = message;
            Application.DoEvents();
        }

        /// <summary>
        /// إظهار رسالة نجاح
        /// </summary>
        /// <param name="message">نص الرسالة</param>
        public void ShowSuccess(string message)
        {
            MessageBox.Show(message, "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// إظهار رسالة خطأ
        /// </summary>
        /// <param name="message">نص الرسالة</param>
        public void ShowError(string message)
        {
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        /// <summary>
        /// إظهار رسالة تحذير
        /// </summary>
        /// <param name="message">نص الرسالة</param>
        public void ShowWarning(string message)
        {
            MessageBox.Show(message, "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        #endregion
    }

    // ملاحظة: سيتم إنشاء ملف MainForm.Designer.cs منفصل للتصميم
}
