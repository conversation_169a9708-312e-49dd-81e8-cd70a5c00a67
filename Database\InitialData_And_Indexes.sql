-- =============================================
-- نظام أريدوو - البيانات الأساسية والفهارس
-- =============================================

USE [AredooPOS];
GO

-- =============================================
-- إدراج البيانات الأساسية
-- =============================================

-- إدراج الأدوار الأساسية
INSERT INTO [Roles] ([RoleName], [RoleNameAr], [Description], [Permissions]) VALUES
('Admin', 'مدير النظام', 'صلاحيات كاملة لإدارة النظام', '{"all": true}'),
('Manager', 'مدير', 'صلاحيات إدارية محدودة', '{"sales": true, "reports": true, "customers": true, "products": true, "cash": true}'),
('Cashier', 'كاشير', 'صلاحيات البيع والفواتير', '{"sales": true, "customers": "read", "cash": "limited"}'),
('Supervisor', 'مشرف', 'صلاحيات الإشراف والمتابعة', '{"sales": true, "reports": "read", "customers": true, "products": "read"}');

-- إدراج المستخدم الافتراضي (admin/admin123)
-- كلمة المرور مشفرة باستخدام SHA256 + Salt
INSERT INTO [Users] ([Username], [PasswordHash], [PasswordSalt], [FullName], [RoleID]) VALUES
('admin', 'E3B0C44298FC1C149AFBF4C8996FB92427AE41E4649B934CA495991B7852B855', 'SALT123456', 'مدير النظام', 1),
('cashier', 'A665A45920422F9D417E4867EFDC4FB8A04A1F3FFF1FA07E998E86F7F7A27AE3', 'SALT789012', 'كاشير النظام', 3);

-- إدراج الإعدادات الأساسية
INSERT INTO [SystemSettings] ([SettingKey], [SettingValue], [SettingType], [Description], [Category]) VALUES
('StoreName', 'متجر أريدوو', 'STRING', 'اسم المتجر', 'Store'),
('StorePhone', '+966501234567', 'STRING', 'هاتف المتجر', 'Store'),
('StoreAddress', 'الرياض، المملكة العربية السعودية', 'STRING', 'عنوان المتجر', 'Store'),
('StoreLogo', '', 'STRING', 'شعار المتجر', 'Store'),
('Currency', 'ر.س', 'STRING', 'رمز العملة', 'General'),
('CurrencyName', 'ريال سعودي', 'STRING', 'اسم العملة', 'General'),
('TaxRate', '15', 'NUMBER', 'نسبة الضريبة المضافة', 'General'),
('TaxNumber', '', 'STRING', 'الرقم الضريبي للمتجر', 'General'),
('PrinterType', 'Thermal', 'STRING', 'نوع الطابعة (Thermal/A4)', 'Printing'),
('PrinterName', '', 'STRING', 'اسم الطابعة', 'Printing'),
('ReceiptHeader', 'أهلاً وسهلاً بكم', 'STRING', 'رأس الإيصال', 'Printing'),
('ReceiptFooter', 'شكراً لزيارتكم', 'STRING', 'ذيل الإيصال', 'Printing'),
('BackupPath', 'C:\AredooPOS\Backup', 'STRING', 'مسار النسخ الاحتياطي', 'System'),
('AutoBackup', 'true', 'BOOLEAN', 'النسخ الاحتياطي التلقائي', 'System'),
('BackupFrequency', 'Daily', 'STRING', 'تكرار النسخ الاحتياطي', 'System'),
('LowStockAlert', '10', 'NUMBER', 'حد التنبيه للمخزون المنخفض', 'Inventory'),
('AllowNegativeStock', 'false', 'BOOLEAN', 'السماح بالمخزون السالب', 'Inventory'),
('DefaultPaymentMethod', 'CASH', 'STRING', 'طريقة الدفع الافتراضية', 'Sales'),
('AllowDiscount', 'true', 'BOOLEAN', 'السماح بالخصومات', 'Sales'),
('MaxDiscountPercentage', '50', 'NUMBER', 'أقصى نسبة خصم مسموحة', 'Sales'),
('InvoicePrefix', 'INV', 'STRING', 'بادئة رقم الفاتورة', 'Sales'),
('QuotePrefix', 'QUO', 'STRING', 'بادئة رقم عرض السعر', 'Sales'),
('ReturnPrefix', 'RET', 'STRING', 'بادئة رقم فاتورة الإرجاع', 'Sales');

-- إدراج فئات المنتجات الأساسية
INSERT INTO [Categories] ([CategoryName], [Description]) VALUES
('منتجات غذائية', 'المواد الغذائية والمشروبات'),
('منتجات تنظيف', 'مواد التنظيف والمطهرات'),
('منتجات شخصية', 'منتجات العناية الشخصية'),
('أدوات منزلية', 'الأدوات والمعدات المنزلية'),
('ألعاب وترفيه', 'ألعاب الأطفال ومواد الترفيه'),
('قرطاسية', 'الأدوات المكتبية والقرطاسية'),
('إلكترونيات', 'الأجهزة الإلكترونية والاكسسوارات'),
('ملابس', 'الملابس والأزياء'),
('أخرى', 'منتجات متنوعة أخرى');

-- إدراج عميل افتراضي (عميل نقدي)
INSERT INTO [Customers] ([CustomerCode], [FullName], [CustomerType], [Phone]) VALUES
('CASH001', 'عميل نقدي', 'Regular', ''),
('CUST001', 'أحمد محمد علي', 'Regular', '0501234567'),
('CUST002', 'فاطمة أحمد', 'VIP', '0507654321'),
('CUST003', 'محمد عبدالله', 'Wholesale', '0551122334');

-- إدراج منتجات تجريبية
INSERT INTO [Products] ([ProductCode], [Barcode], [ProductName], [CategoryID], [CostPrice], [SellingPrice], [CurrentStock], [MinimumStock]) VALUES
('PROD001', '1234567890123', 'شامبو الأطفال', 3, 15.00, 25.00, 50, 10),
('PROD002', '1234567890124', 'معجون أسنان', 3, 8.00, 12.00, 75, 15),
('PROD003', '1234567890125', 'صابون طبيعي', 3, 5.00, 8.00, 100, 20),
('PROD004', '1234567890126', 'منظف أرضيات', 2, 12.00, 18.00, 30, 5),
('PROD005', '1234567890127', 'عصير برتقال', 1, 3.00, 5.00, 200, 50);

GO

-- =============================================
-- إنشاء الفهارس لتحسين الأداء
-- =============================================

-- فهارس جدول المستخدمين
CREATE NONCLUSTERED INDEX [IX_Users_Username] ON [Users] ([Username]);
CREATE NONCLUSTERED INDEX [IX_Users_RoleID] ON [Users] ([RoleID]);
CREATE NONCLUSTERED INDEX [IX_Users_IsActive] ON [Users] ([IsActive]);

-- فهارس جدول العملاء
CREATE NONCLUSTERED INDEX [IX_Customers_CustomerCode] ON [Customers] ([CustomerCode]);
CREATE NONCLUSTERED INDEX [IX_Customers_Phone] ON [Customers] ([Phone]);
CREATE NONCLUSTERED INDEX [IX_Customers_Email] ON [Customers] ([Email]);
CREATE NONCLUSTERED INDEX [IX_Customers_IsActive] ON [Customers] ([IsActive]);

-- فهارس جدول المنتجات
CREATE NONCLUSTERED INDEX [IX_Products_ProductCode] ON [Products] ([ProductCode]);
CREATE NONCLUSTERED INDEX [IX_Products_Barcode] ON [Products] ([Barcode]);
CREATE NONCLUSTERED INDEX [IX_Products_CategoryID] ON [Products] ([CategoryID]);
CREATE NONCLUSTERED INDEX [IX_Products_IsActive] ON [Products] ([IsActive]);
CREATE NONCLUSTERED INDEX [IX_Products_CurrentStock] ON [Products] ([CurrentStock]);

-- فهارس جدول الفواتير
CREATE NONCLUSTERED INDEX [IX_Invoices_InvoiceNumber] ON [Invoices] ([InvoiceNumber]);
CREATE NONCLUSTERED INDEX [IX_Invoices_InvoiceDate] ON [Invoices] ([InvoiceDate]);
CREATE NONCLUSTERED INDEX [IX_Invoices_CustomerID] ON [Invoices] ([CustomerID]);
CREATE NONCLUSTERED INDEX [IX_Invoices_CashierID] ON [Invoices] ([CashierID]);
CREATE NONCLUSTERED INDEX [IX_Invoices_Status] ON [Invoices] ([Status]);
CREATE NONCLUSTERED INDEX [IX_Invoices_PaymentMethod] ON [Invoices] ([PaymentMethod]);

-- فهارس جدول تفاصيل الفواتير
CREATE NONCLUSTERED INDEX [IX_InvoiceDetails_InvoiceID] ON [InvoiceDetails] ([InvoiceID]);
CREATE NONCLUSTERED INDEX [IX_InvoiceDetails_ProductID] ON [InvoiceDetails] ([ProductID]);

-- فهارس جدول حركة المخزون
CREATE NONCLUSTERED INDEX [IX_StockMovements_ProductID] ON [StockMovements] ([ProductID]);
CREATE NONCLUSTERED INDEX [IX_StockMovements_MovementDate] ON [StockMovements] ([MovementDate]);
CREATE NONCLUSTERED INDEX [IX_StockMovements_MovementType] ON [StockMovements] ([MovementType]);

-- فهارس جدول الديون
CREATE NONCLUSTERED INDEX [IX_Debts_CustomerID] ON [Debts] ([CustomerID]);
CREATE NONCLUSTERED INDEX [IX_Debts_Status] ON [Debts] ([Status]);
CREATE NONCLUSTERED INDEX [IX_Debts_DueDate] ON [Debts] ([DueDate]);

-- فهارس جدول الأقساط
CREATE NONCLUSTERED INDEX [IX_InstallmentPlans_CustomerID] ON [InstallmentPlans] ([CustomerID]);
CREATE NONCLUSTERED INDEX [IX_InstallmentPlans_InvoiceID] ON [InstallmentPlans] ([InvoiceID]);
CREATE NONCLUSTERED INDEX [IX_InstallmentPayments_PlanID] ON [InstallmentPayments] ([PlanID]);
CREATE NONCLUSTERED INDEX [IX_InstallmentPayments_DueDate] ON [InstallmentPayments] ([DueDate]);
CREATE NONCLUSTERED INDEX [IX_InstallmentPayments_Status] ON [InstallmentPayments] ([Status]);

-- فهارس جدول النقدية
CREATE NONCLUSTERED INDEX [IX_CashSessions_UserID] ON [CashSessions] ([UserID]);
CREATE NONCLUSTERED INDEX [IX_CashSessions_OpenTime] ON [CashSessions] ([OpenTime]);
CREATE NONCLUSTERED INDEX [IX_CashSessions_Status] ON [CashSessions] ([Status]);
CREATE NONCLUSTERED INDEX [IX_CashTransactions_SessionID] ON [CashTransactions] ([SessionID]);
CREATE NONCLUSTERED INDEX [IX_CashTransactions_TransactionDate] ON [CashTransactions] ([TransactionDate]);

-- فهارس جدول سجل العمليات
CREATE NONCLUSTERED INDEX [IX_ActivityLog_UserID] ON [ActivityLog] ([UserID]);
CREATE NONCLUSTERED INDEX [IX_ActivityLog_CreatedDate] ON [ActivityLog] ([CreatedDate]);
CREATE NONCLUSTERED INDEX [IX_ActivityLog_TableName] ON [ActivityLog] ([TableName]);

GO

-- =============================================
-- إنشاء Views للتقارير
-- =============================================

-- عرض تفاصيل الفواتير مع أسماء المنتجات والعملاء
CREATE VIEW [dbo].[vw_InvoiceDetails] AS
SELECT
    i.InvoiceID,
    i.InvoiceNumber,
    i.InvoiceDate,
    c.FullName AS CustomerName,
    c.Phone AS CustomerPhone,
    i.PaymentMethod,
    i.SubTotal,
    i.DiscountAmount,
    i.TaxAmount,
    i.TotalAmount,
    i.PaidAmount,
    i.RemainingAmount,
    i.Status,
    u.FullName AS CashierName,
    id.ProductID,
    p.ProductName,
    id.Quantity,
    id.UnitPrice,
    id.TotalPrice
FROM Invoices i
LEFT JOIN Customers c ON i.CustomerID = c.CustomerID
LEFT JOIN Users u ON i.CashierID = u.UserID
LEFT JOIN InvoiceDetails id ON i.InvoiceID = id.InvoiceID
LEFT JOIN Products p ON id.ProductID = p.ProductID;
GO

-- عرض المخزون الحالي مع التنبيهات
CREATE VIEW [dbo].[vw_StockStatus] AS
SELECT
    p.ProductID,
    p.ProductCode,
    p.Barcode,
    p.ProductName,
    c.CategoryName,
    p.CurrentStock,
    p.MinimumStock,
    p.ReorderLevel,
    p.CostPrice,
    p.SellingPrice,
    CASE
        WHEN p.CurrentStock <= 0 THEN 'نفد المخزون'
        WHEN p.CurrentStock <= p.MinimumStock THEN 'مخزون منخفض'
        WHEN p.CurrentStock <= p.ReorderLevel THEN 'يحتاج إعادة طلب'
        ELSE 'متوفر'
    END AS StockStatus,
    p.IsActive
FROM Products p
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID;
GO

-- عرض ديون العملاء
CREATE VIEW [dbo].[vw_CustomerDebts] AS
SELECT
    c.CustomerID,
    c.CustomerCode,
    c.FullName AS CustomerName,
    c.Phone,
    d.DebtID,
    d.Amount AS DebtAmount,
    d.PaidAmount,
    d.RemainingAmount,
    d.DueDate,
    d.Status,
    d.Description,
    DATEDIFF(DAY, d.DueDate, GETDATE()) AS DaysOverdue
FROM Customers c
INNER JOIN Debts d ON c.CustomerID = d.CustomerID
WHERE d.RemainingAmount > 0;
GO

-- عرض الأقساط المستحقة
CREATE VIEW [dbo].[vw_DueInstallments] AS
SELECT
    c.CustomerID,
    c.CustomerCode,
    c.FullName AS CustomerName,
    c.Phone,
    ip.PlanID,
    ipm.PaymentID,
    ipm.InstallmentNumber,
    ipm.DueDate,
    ipm.Amount,
    ipm.PaidAmount,
    ipm.Amount - ipm.PaidAmount AS RemainingAmount,
    ipm.Status,
    DATEDIFF(DAY, ipm.DueDate, GETDATE()) AS DaysOverdue
FROM Customers c
INNER JOIN InstallmentPlans ip ON c.CustomerID = ip.CustomerID
INNER JOIN InstallmentPayments ipm ON ip.PlanID = ipm.PlanID
WHERE ipm.Status IN ('PENDING', 'PARTIAL', 'OVERDUE');

GO