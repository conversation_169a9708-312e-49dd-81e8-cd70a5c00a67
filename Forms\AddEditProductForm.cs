using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using AredooPOS.Models;
using AredooPOS.BLL;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// واجهة إضافة وتعديل المنتجات
    /// توفر إدارة شاملة لبيانات المنتج مع دعم الصور والباركود
    /// </summary>
    public partial class AddEditProductForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly ProductBLL _productBLL;
        private readonly CategoryDAL _categoryDAL;
        private readonly Product _product;
        private readonly string _currentUser;
        private readonly bool _isEditMode;
        private readonly ILogger<AddEditProductForm> _logger;

        // ألوان النظام
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SuccessColor = Color.FromArgb(46, 204, 113);
        private readonly Color DangerColor = Color.FromArgb(231, 76, 60);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);

        // مسار الصورة المختارة
        private string _selectedImagePath;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ واجهة إضافة وتعديل المنتجات
        /// </summary>
        /// <param name="productBLL">طبقة منطق الأعمال للمنتجات</param>
        /// <param name="product">المنتج للتعديل (null للإضافة)</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <param name="logger">مسجل الأحداث</param>
        public AddEditProductForm(ProductBLL productBLL, Product product = null, string currentUser = "النظام", ILogger<AddEditProductForm> logger = null)
        {
            _productBLL = productBLL ?? throw new ArgumentNullException(nameof(productBLL));
            _categoryDAL = new CategoryDAL();
            _product = product;
            _currentUser = currentUser;
            _isEditMode = product != null;
            _logger = logger;

            InitializeComponent();
            InitializeArabicUI();
            LoadCategories();
            LoadUnits();
            LoadBarcodeTypes();

            if (_isEditMode)
            {
                LoadProductData();
            }
            else
            {
                SetDefaultValues();
            }

            SetupEventHandlers();
        }

        /// <summary>
        /// تهيئة الواجهة العربية
        /// </summary>
        private void InitializeArabicUI()
        {
            // إعدادات النموذج الأساسية
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = _isEditMode ? "تعديل منتج" : "إضافة منتج جديد";
            this.Size = new Size(800, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = LightGray;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            // تطبيق الألوان والأنماط
            ApplyThemeColors();
            UpdateUITexts();
        }

        /// <summary>
        /// تطبيق ألوان النظام
        /// </summary>
        private void ApplyThemeColors()
        {
            // شريط العنوان
            pnlHeader.BackColor = PrimaryColor;
            lblTitle.ForeColor = Color.White;

            // أزرار العمليات
            btnSave.BackColor = SuccessColor;
            btnSave.ForeColor = Color.White;
            btnSave.FlatStyle = FlatStyle.Flat;

            btnCancel.BackColor = Color.Gray;
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;

            btnGenerateBarcode.BackColor = PrimaryColor;
            btnGenerateBarcode.ForeColor = Color.White;
            btnGenerateBarcode.FlatStyle = FlatStyle.Flat;

            btnSelectImage.BackColor = PrimaryColor;
            btnSelectImage.ForeColor = Color.White;
            btnSelectImage.FlatStyle = FlatStyle.Flat;

            btnRemoveImage.BackColor = DangerColor;
            btnRemoveImage.ForeColor = Color.White;
            btnRemoveImage.FlatStyle = FlatStyle.Flat;

            // علامات التبويب
            tabControl.BackColor = Color.White;

            // لوحات البيانات
            foreach (TabPage tab in tabControl.TabPages)
            {
                tab.BackColor = Color.White;
                foreach (Control control in tab.Controls)
                {
                    if (control is GroupBox groupBox)
                    {
                        groupBox.BackColor = Color.White;
                    }
                }
            }

            // صندوق الصورة
            picProductImage.BackColor = LightGray;
            picProductImage.BorderStyle = BorderStyle.FixedSingle;
        }

        /// <summary>
        /// تحديث النصوص في الواجهة
        /// </summary>
        private void UpdateUITexts()
        {
            lblTitle.Text = _isEditMode ? "تعديل منتج" : "إضافة منتج جديد";

            // أزرار العمليات
            btnSave.Text = _isEditMode ? "حفظ التعديلات" : "إضافة المنتج";
            btnCancel.Text = "إلغاء";
            btnGenerateBarcode.Text = "توليد باركود";
            btnSelectImage.Text = "اختيار صورة";
            btnRemoveImage.Text = "إزالة الصورة";

            // علامات التبويب
            tabBasicInfo.Text = "المعلومات الأساسية";
            tabPricing.Text = "الأسعار والتكلفة";
            tabStock.Text = "المخزون";
            tabImage.Text = "الصورة";
            tabAdditional.Text = "معلومات إضافية";

            // المعلومات الأساسية
            grpBasicInfo.Text = "البيانات الأساسية";
            lblProductCode.Text = "كود المنتج: *";
            lblProductName.Text = "اسم المنتج: *";
            lblProductNameEn.Text = "الاسم بالإنجليزية:";
            lblCategory.Text = "الفئة: *";
            lblDescription.Text = "الوصف:";
            lblBarcode.Text = "الباركود:";
            lblBarcodeType.Text = "نوع الباركود:";

            // الأسعار والتكلفة
            grpPricing.Text = "الأسعار والتكلفة";
            lblCostPrice.Text = "سعر التكلفة:";
            lblUnitPrice.Text = "سعر البيع: *";
            lblWholesalePrice.Text = "سعر الجملة:";
            lblMinSalePrice.Text = "الحد الأدنى للسعر:";
            lblProfitMargin.Text = "نسبة الربح (%):";
            lblTaxRate.Text = "نسبة الضريبة (%):";
            chkIsTaxable.Text = "خاضع للضريبة";

            // المخزون
            grpStock.Text = "إدارة المخزون";
            lblStockQuantity.Text = "الكمية الحالية:";
            lblUnit.Text = "وحدة القياس: *";
            lblMinStockLevel.Text = "الحد الأدنى للمخزون:";
            lblMaxStockLevel.Text = "الحد الأقصى للمخزون:";
            chkTrackStock.Text = "تتبع المخزون";

            // الإعدادات
            grpSettings.Text = "الإعدادات";
            chkIsActive.Text = "نشط";
            chkIsAvailableForSale.Text = "متاح للبيع";
            chkIsReturnable.Text = "قابل للإرجاع";
            lblExpiryDate.Text = "تاريخ انتهاء الصلاحية:";

            // معلومات إضافية
            grpAdditional.Text = "معلومات إضافية";
            lblNotes.Text = "ملاحظات:";
        }

        /// <summary>
        /// تحميل الفئات
        /// </summary>
        private void LoadCategories()
        {
            try
            {
                var categories = _categoryDAL.GetAllCategories(false);
                cmbCategory.DataSource = categories;
                cmbCategory.DisplayMember = "CategoryName";
                cmbCategory.ValueMember = "CategoryID";
                cmbCategory.SelectedIndex = -1;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل الفئات");
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل وحدات القياس
        /// </summary>
        private void LoadUnits()
        {
            cmbUnit.Items.Clear();
            cmbUnit.Items.AddRange(new string[]
            {
                "قطعة",
                "كيلوجرام",
                "جرام",
                "لتر",
                "متر",
                "سنتيمتر",
                "صندوق",
                "كرتون",
                "عبوة",
                "زجاجة",
                "علبة",
                "كيس"
            });
            cmbUnit.SelectedIndex = 0; // قطعة
        }

        /// <summary>
        /// تحميل أنواع الباركود
        /// </summary>
        private void LoadBarcodeTypes()
        {
            cmbBarcodeType.Items.Clear();
            cmbBarcodeType.Items.AddRange(new string[]
            {
                BarcodeTypes.EAN13,
                BarcodeTypes.EAN8,
                BarcodeTypes.Code128,
                BarcodeTypes.Code39,
                BarcodeTypes.QRCode
            });
            cmbBarcodeType.SelectedIndex = 0; // EAN13
        }

        /// <summary>
        /// تحميل بيانات المنتج للتعديل
        /// </summary>
        private void LoadProductData()
        {
            if (_product == null) return;

            try
            {
                // المعلومات الأساسية
                txtProductCode.Text = _product.ProductCode;
                txtProductName.Text = _product.ProductName;
                txtProductNameEn.Text = _product.ProductNameEn;
                txtDescription.Text = _product.Description;
                txtBarcode.Text = _product.Barcode;
                cmbBarcodeType.Text = _product.BarcodeType;

                // تحديد الفئة
                if (_product.CategoryID > 0)
                {
                    cmbCategory.SelectedValue = _product.CategoryID;
                }

                // الأسعار والتكلفة
                numCostPrice.Value = _product.CostPrice;
                numUnitPrice.Value = _product.UnitPrice;
                numWholesalePrice.Value = _product.WholesalePrice;
                numMinSalePrice.Value = _product.MinSalePrice;
                numProfitMargin.Value = _product.ProfitMargin;
                numTaxRate.Value = _product.TaxRate;
                chkIsTaxable.Checked = _product.IsTaxable;

                // المخزون
                numStockQuantity.Value = _product.StockQuantity;
                cmbUnit.Text = _product.Unit;
                numMinStockLevel.Value = _product.MinStockLevel;
                numMaxStockLevel.Value = _product.MaxStockLevel;
                chkTrackStock.Checked = _product.TrackStock;

                // الإعدادات
                chkIsActive.Checked = _product.IsActive;
                chkIsAvailableForSale.Checked = _product.IsAvailableForSale;
                chkIsReturnable.Checked = _product.IsReturnable;

                if (_product.ExpiryDate.HasValue)
                {
                    dtpExpiryDate.Value = _product.ExpiryDate.Value;
                    dtpExpiryDate.Checked = true;
                }

                // الملاحظات
                txtNotes.Text = _product.Notes;

                // الصورة
                LoadProductImage();

                _logger?.LogInformation($"تم تحميل بيانات المنتج {_product.ProductCode}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحميل بيانات المنتج {_product?.ProductCode}");
                MessageBox.Show($"خطأ في تحميل بيانات المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تعيين القيم الافتراضية للمنتج الجديد
        /// </summary>
        private void SetDefaultValues()
        {
            // الإعدادات الافتراضية
            chkIsActive.Checked = true;
            chkIsAvailableForSale.Checked = true;
            chkIsReturnable.Checked = true;
            chkIsTaxable.Checked = true;
            chkTrackStock.Checked = true;

            numTaxRate.Value = 15; // 15% ضريبة القيمة المضافة
            numStockQuantity.Value = 0;
            numMinStockLevel.Value = 5;

            dtpExpiryDate.Checked = false;
        }

        /// <summary>
        /// تحميل صورة المنتج
        /// </summary>
        private void LoadProductImage()
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(_product?.ImagePath) && File.Exists(_product.ImagePath))
                {
                    picProductImage.Image = Image.FromFile(_product.ImagePath);
                    picProductImage.SizeMode = PictureBoxSizeMode.Zoom;
                    btnRemoveImage.Enabled = true;
                }
                else
                {
                    picProductImage.Image = null;
                    btnRemoveImage.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل صورة المنتج");
                picProductImage.Image = null;
                btnRemoveImage.Enabled = false;
            }
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // أحداث الأزرار
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnGenerateBarcode.Click += BtnGenerateBarcode_Click;
            btnSelectImage.Click += BtnSelectImage_Click;
            btnRemoveImage.Click += BtnRemoveImage_Click;

            // أحداث حساب الأسعار
            numCostPrice.ValueChanged += NumCostPrice_ValueChanged;
            numUnitPrice.ValueChanged += NumUnitPrice_ValueChanged;
            numProfitMargin.ValueChanged += NumProfitMargin_ValueChanged;

            // أحداث التحقق من البيانات
            txtProductCode.Leave += TxtProductCode_Leave;
            txtBarcode.Leave += TxtBarcode_Leave;

            // أحداث النموذج
            this.Load += AddEditProductForm_Load;
        }

        #endregion

        #region معالجات الأحداث

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void AddEditProductForm_Load(object sender, EventArgs e)
        {
            _logger?.LogInformation($"تم تحميل واجهة {(_isEditMode ? "تعديل" : "إضافة")} المنتج");
        }

        /// <summary>
        /// حفظ المنتج
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var product = CreateProductFromInput();

                if (_isEditMode)
                {
                    product.ProductID = _product.ProductID;
                    var success = _productBLL.UpdateProduct(product, _currentUser);

                    if (success)
                    {
                        MessageBox.Show("تم تحديث المنتج بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                }
                else
                {
                    var productId = _productBLL.AddProduct(product, _currentUser);

                    if (productId > 0)
                    {
                        MessageBox.Show("تم إضافة المنتج بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في {(_isEditMode ? "تحديث" : "إضافة")} المنتج");
                MessageBox.Show($"خطأ في {(_isEditMode ? "تحديث" : "إضافة")} المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        /// <summary>
        /// توليد باركود
        /// </summary>
        private void BtnGenerateBarcode_Click(object sender, EventArgs e)
        {
            try
            {
                var barcodeType = cmbBarcodeType.SelectedItem?.ToString() ?? BarcodeTypes.EAN13;

                string generatedBarcode;
                switch (barcodeType)
                {
                    case BarcodeTypes.EAN13:
                        generatedBarcode = GenerateEAN13Barcode();
                        break;
                    case BarcodeTypes.EAN8:
                        generatedBarcode = GenerateEAN8Barcode();
                        break;
                    default:
                        generatedBarcode = GenerateRandomBarcode();
                        break;
                }

                txtBarcode.Text = generatedBarcode;
                _logger?.LogInformation($"تم توليد باركود جديد: {generatedBarcode}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في توليد الباركود");
                MessageBox.Show($"خطأ في توليد الباركود: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختيار صورة
        /// </summary>
        private void BtnSelectImage_Click(object sender, EventArgs e)
        {
            try
            {
                using (var openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Filter = "ملفات الصور|*.jpg;*.jpeg;*.png;*.bmp;*.gif|جميع الملفات|*.*";
                    openFileDialog.Title = "اختيار صورة المنتج";

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        _selectedImagePath = openFileDialog.FileName;
                        picProductImage.Image = Image.FromFile(_selectedImagePath);
                        picProductImage.SizeMode = PictureBoxSizeMode.Zoom;
                        btnRemoveImage.Enabled = true;

                        _logger?.LogInformation($"تم اختيار صورة: {_selectedImagePath}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في اختيار الصورة");
                MessageBox.Show($"خطأ في اختيار الصورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إزالة الصورة
        /// </summary>
        private void BtnRemoveImage_Click(object sender, EventArgs e)
        {
            picProductImage.Image = null;
            _selectedImagePath = null;
            btnRemoveImage.Enabled = false;
        }

        /// <summary>
        /// تغيير سعر التكلفة
        /// </summary>
        private void NumCostPrice_ValueChanged(object sender, EventArgs e)
        {
            CalculateProfitMargin();
        }

        /// <summary>
        /// تغيير سعر البيع
        /// </summary>
        private void NumUnitPrice_ValueChanged(object sender, EventArgs e)
        {
            CalculateProfitMargin();
        }

        /// <summary>
        /// تغيير نسبة الربح
        /// </summary>
        private void NumProfitMargin_ValueChanged(object sender, EventArgs e)
        {
            CalculateSalePriceFromMargin();
        }

        /// <summary>
        /// التحقق من كود المنتج
        /// </summary>
        private void TxtProductCode_Leave(object sender, EventArgs e)
        {
            if (!_isEditMode && !string.IsNullOrWhiteSpace(txtProductCode.Text))
            {
                try
                {
                    var existingProduct = _productBLL.GetProductByCode(txtProductCode.Text.Trim());
                    if (existingProduct != null)
                    {
                        MessageBox.Show("كود المنتج موجود مسبقاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtProductCode.Focus();
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "خطأ في التحقق من كود المنتج");
                }
            }
        }

        /// <summary>
        /// التحقق من الباركود
        /// </summary>
        private void TxtBarcode_Leave(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtBarcode.Text))
            {
                try
                {
                    var existingProduct = _productBLL.GetProductByBarcode(txtBarcode.Text.Trim());
                    if (existingProduct != null && (!_isEditMode || existingProduct.ProductID != _product.ProductID))
                    {
                        MessageBox.Show("الباركود موجود مسبقاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtBarcode.Focus();
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "خطأ في التحقق من الباركود");
                }
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        private bool ValidateInput()
        {
            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(txtProductCode.Text))
            {
                MessageBox.Show("كود المنتج مطلوب", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                tabControl.SelectedTab = tabBasicInfo;
                txtProductCode.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtProductName.Text))
            {
                MessageBox.Show("اسم المنتج مطلوب", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                tabControl.SelectedTab = tabBasicInfo;
                txtProductName.Focus();
                return false;
            }

            if (cmbCategory.SelectedValue == null)
            {
                MessageBox.Show("فئة المنتج مطلوبة", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                tabControl.SelectedTab = tabBasicInfo;
                cmbCategory.Focus();
                return false;
            }

            if (numUnitPrice.Value <= 0)
            {
                MessageBox.Show("سعر البيع يجب أن يكون أكبر من صفر", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                tabControl.SelectedTab = tabPricing;
                numUnitPrice.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(cmbUnit.Text))
            {
                MessageBox.Show("وحدة القياس مطلوبة", "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                tabControl.SelectedTab = tabStock;
                cmbUnit.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// إنشاء كائن المنتج من البيانات المدخلة
        /// </summary>
        /// <returns>كائن المنتج</returns>
        private Product CreateProductFromInput()
        {
            var product = new Product
            {
                // المعلومات الأساسية
                ProductCode = txtProductCode.Text.Trim(),
                ProductName = txtProductName.Text.Trim(),
                ProductNameEn = txtProductNameEn.Text.Trim(),
                Description = txtDescription.Text.Trim(),
                CategoryID = Convert.ToInt32(cmbCategory.SelectedValue),
                Barcode = txtBarcode.Text.Trim(),
                BarcodeType = cmbBarcodeType.SelectedItem?.ToString() ?? BarcodeTypes.EAN13,

                // الأسعار والتكلفة
                CostPrice = numCostPrice.Value,
                UnitPrice = numUnitPrice.Value,
                WholesalePrice = numWholesalePrice.Value,
                MinSalePrice = numMinSalePrice.Value,
                ProfitMargin = numProfitMargin.Value,
                TaxRate = numTaxRate.Value,
                IsTaxable = chkIsTaxable.Checked,

                // المخزون
                StockQuantity = numStockQuantity.Value,
                Unit = cmbUnit.Text.Trim(),
                MinStockLevel = numMinStockLevel.Value,
                MaxStockLevel = numMaxStockLevel.Value,
                TrackStock = chkTrackStock.Checked,

                // الإعدادات
                IsActive = chkIsActive.Checked,
                IsAvailableForSale = chkIsAvailableForSale.Checked,
                IsReturnable = chkIsReturnable.Checked,
                ExpiryDate = dtpExpiryDate.Checked ? dtpExpiryDate.Value : (DateTime?)null,

                // معلومات إضافية
                Notes = txtNotes.Text.Trim(),

                // الصورة
                ImagePath = CopyImageToProductFolder()
            };

            return product;
        }

        /// <summary>
        /// نسخ الصورة إلى مجلد المنتجات
        /// </summary>
        /// <returns>مسار الصورة الجديد</returns>
        private string CopyImageToProductFolder()
        {
            if (string.IsNullOrWhiteSpace(_selectedImagePath))
                return _isEditMode ? _product?.ImagePath : null;

            try
            {
                var productImagesFolder = Path.Combine(Application.StartupPath, "Images", "Products");
                if (!Directory.Exists(productImagesFolder))
                {
                    Directory.CreateDirectory(productImagesFolder);
                }

                var fileName = $"{txtProductCode.Text.Trim()}_{DateTime.Now:yyyyMMddHHmmss}{Path.GetExtension(_selectedImagePath)}";
                var destinationPath = Path.Combine(productImagesFolder, fileName);

                File.Copy(_selectedImagePath, destinationPath, true);
                return destinationPath;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في نسخ صورة المنتج");
                return null;
            }
        }

        /// <summary>
        /// حساب نسبة الربح
        /// </summary>
        private void CalculateProfitMargin()
        {
            if (numCostPrice.Value > 0 && numUnitPrice.Value > numCostPrice.Value)
            {
                var margin = ((numUnitPrice.Value - numCostPrice.Value) / numCostPrice.Value) * 100;
                numProfitMargin.Value = Math.Round(margin, 2);
            }
        }

        /// <summary>
        /// حساب سعر البيع من نسبة الربح
        /// </summary>
        private void CalculateSalePriceFromMargin()
        {
            if (numCostPrice.Value > 0 && numProfitMargin.Value > 0)
            {
                var salePrice = numCostPrice.Value * (1 + numProfitMargin.Value / 100);
                numUnitPrice.Value = Math.Round(salePrice, 2);
            }
        }

        /// <summary>
        /// توليد باركود EAN13
        /// </summary>
        /// <returns>باركود EAN13</returns>
        private string GenerateEAN13Barcode()
        {
            var random = new Random();
            var barcode = "621"; // كود البلد للسعودية

            // إضافة 9 أرقام عشوائية
            for (int i = 0; i < 9; i++)
            {
                barcode += random.Next(0, 10).ToString();
            }

            // حساب رقم التحقق
            var checkDigit = CalculateEAN13CheckDigit(barcode);
            barcode += checkDigit;

            return barcode;
        }

        /// <summary>
        /// توليد باركود EAN8
        /// </summary>
        /// <returns>باركود EAN8</returns>
        private string GenerateEAN8Barcode()
        {
            var random = new Random();
            var barcode = "";

            // إضافة 7 أرقام عشوائية
            for (int i = 0; i < 7; i++)
            {
                barcode += random.Next(0, 10).ToString();
            }

            // حساب رقم التحقق
            var checkDigit = CalculateEAN8CheckDigit(barcode);
            barcode += checkDigit;

            return barcode;
        }

        /// <summary>
        /// توليد باركود عشوائي
        /// </summary>
        /// <returns>باركود عشوائي</returns>
        private string GenerateRandomBarcode()
        {
            var random = new Random();
            var barcode = "";

            for (int i = 0; i < 12; i++)
            {
                barcode += random.Next(0, 10).ToString();
            }

            return barcode;
        }

        /// <summary>
        /// حساب رقم التحقق لباركود EAN13
        /// </summary>
        /// <param name="barcode">الباركود بدون رقم التحقق</param>
        /// <returns>رقم التحقق</returns>
        private int CalculateEAN13CheckDigit(string barcode)
        {
            var sum = 0;
            for (int i = 0; i < 12; i++)
            {
                var digit = int.Parse(barcode[i].ToString());
                sum += (i % 2 == 0) ? digit : digit * 3;
            }

            var checkDigit = (10 - (sum % 10)) % 10;
            return checkDigit;
        }

        /// <summary>
        /// حساب رقم التحقق لباركود EAN8
        /// </summary>
        /// <param name="barcode">الباركود بدون رقم التحقق</param>
        /// <returns>رقم التحقق</returns>
        private int CalculateEAN8CheckDigit(string barcode)
        {
            var sum = 0;
            for (int i = 0; i < 7; i++)
            {
                var digit = int.Parse(barcode[i].ToString());
                sum += (i % 2 == 0) ? digit * 3 : digit;
            }

            var checkDigit = (10 - (sum % 10)) % 10;
            return checkDigit;
        }

        #endregion
    }
}