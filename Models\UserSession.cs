using System;
using System.ComponentModel.DataAnnotations;

namespace AredooPOS.Models
{
    /// <summary>
    /// نموذج جلسة المستخدم
    /// يمثل جلسة تسجيل دخول المستخدم مع تفاصيل الجلسة
    /// </summary>
    public class UserSession
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم الجلسة في قاعدة البيانات (مفتاح أساسي)
        /// </summary>
        public int SessionID { get; set; }

        /// <summary>
        /// رقم المستخدم (مفتاح خارجي)
        /// </summary>
        [Required(ErrorMessage = "رقم المستخدم مطلوب")]
        public int UserID { get; set; }

        /// <summary>
        /// معرف الجلسة الفريد
        /// </summary>
        [Required(ErrorMessage = "معرف الجلسة مطلوب")]
        [StringLength(255, ErrorMessage = "معرف الجلسة لا يجب أن يتجاوز 255 حرف")]
        public string SessionToken { get; set; }

        /// <summary>
        /// تاريخ ووقت بداية الجلسة
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// تاريخ ووقت انتهاء الجلسة
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// تاريخ انتهاء صلاحية الجلسة
        /// </summary>
        public DateTime ExpiryTime { get; set; }

        /// <summary>
        /// هل الجلسة نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        #endregion

        #region معلومات الاتصال

        /// <summary>
        /// عنوان IP للمستخدم
        /// </summary>
        [StringLength(45, ErrorMessage = "عنوان IP لا يجب أن يتجاوز 45 حرف")]
        public string IPAddress { get; set; }

        /// <summary>
        /// معلومات المتصفح/التطبيق
        /// </summary>
        [StringLength(500, ErrorMessage = "معلومات المتصفح لا يجب أن تتجاوز 500 حرف")]
        public string UserAgent { get; set; }

        /// <summary>
        /// اسم الجهاز/الكمبيوتر
        /// </summary>
        [StringLength(100, ErrorMessage = "اسم الجهاز لا يجب أن يتجاوز 100 حرف")]
        public string DeviceName { get; set; }

        /// <summary>
        /// نوع الجهاز
        /// </summary>
        [StringLength(50, ErrorMessage = "نوع الجهاز لا يجب أن يتجاوز 50 حرف")]
        public string DeviceType { get; set; }

        /// <summary>
        /// نظام التشغيل
        /// </summary>
        [StringLength(100, ErrorMessage = "نظام التشغيل لا يجب أن يتجاوز 100 حرف")]
        public string OperatingSystem { get; set; }

        /// <summary>
        /// الموقع الجغرافي (إن وجد)
        /// </summary>
        [StringLength(100, ErrorMessage = "الموقع الجغرافي لا يجب أن يتجاوز 100 حرف")]
        public string Location { get; set; }

        #endregion

        #region إحصائيات الجلسة

        /// <summary>
        /// آخر نشاط في الجلسة
        /// </summary>
        public DateTime LastActivity { get; set; }

        /// <summary>
        /// عدد العمليات المنجزة في الجلسة
        /// </summary>
        public int OperationsCount { get; set; }

        /// <summary>
        /// عدد الصفحات المزارة
        /// </summary>
        public int PagesVisited { get; set; }

        /// <summary>
        /// مدة الجلسة بالدقائق
        /// </summary>
        public int? SessionDurationMinutes
        {
            get
            {
                if (EndTime.HasValue)
                    return (int)(EndTime.Value - StartTime).TotalMinutes;
                
                if (IsActive)
                    return (int)(DateTime.Now - StartTime).TotalMinutes;
                
                return null;
            }
        }

        #endregion

        #region سبب انتهاء الجلسة

        /// <summary>
        /// سبب انتهاء الجلسة
        /// </summary>
        [StringLength(50, ErrorMessage = "سبب انتهاء الجلسة لا يجب أن يتجاوز 50 حرف")]
        public string EndReason { get; set; }

        /// <summary>
        /// تفاصيل إضافية عن انتهاء الجلسة
        /// </summary>
        [StringLength(200, ErrorMessage = "تفاصيل انتهاء الجلسة لا يجب أن تتجاوز 200 حرف")]
        public string EndDetails { get; set; }

        #endregion

        #region الأمان والتحقق

        /// <summary>
        /// هل تم التحقق من الجلسة
        /// </summary>
        public bool IsVerified { get; set; } = true;

        /// <summary>
        /// هل الجلسة مشبوهة
        /// </summary>
        public bool IsSuspicious { get; set; }

        /// <summary>
        /// سبب الشك في الجلسة
        /// </summary>
        [StringLength(200, ErrorMessage = "سبب الشك لا يجب أن يتجاوز 200 حرف")]
        public string SuspiciousReason { get; set; }

        /// <summary>
        /// هل تم إنهاء الجلسة قسرياً
        /// </summary>
        public bool IsForceTerminated { get; set; }

        #endregion

        #region معلومات النظام

        /// <summary>
        /// تاريخ إنشاء السجل
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime ModifiedDate { get; set; }

        #endregion

        #region العلاقات

        /// <summary>
        /// المستخدم المرتبط بالجلسة
        /// </summary>
        public User User { get; set; }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ جلسة المستخدم مع التهيئة الافتراضية
        /// </summary>
        public UserSession()
        {
            // تهيئة التواريخ
            StartTime = DateTime.Now;
            LastActivity = DateTime.Now;
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;

            // تهيئة القيم الافتراضية
            IsActive = true;
            IsVerified = true;
            IsSuspicious = false;
            IsForceTerminated = false;
            OperationsCount = 0;
            PagesVisited = 0;

            // توليد معرف جلسة فريد
            SessionToken = GenerateSessionToken();

            // تعيين وقت انتهاء الصلاحية (8 ساعات افتراضياً)
            ExpiryTime = DateTime.Now.AddHours(8);
        }

        /// <summary>
        /// منشئ جلسة المستخدم مع معاملات
        /// </summary>
        /// <param name="userID">رقم المستخدم</param>
        /// <param name="ipAddress">عنوان IP</param>
        /// <param name="userAgent">معلومات المتصفح</param>
        /// <param name="sessionDurationHours">مدة الجلسة بالساعات</param>
        public UserSession(int userID, string ipAddress, string userAgent = null, int sessionDurationHours = 8) : this()
        {
            UserID = userID;
            IPAddress = ipAddress;
            UserAgent = userAgent;
            ExpiryTime = DateTime.Now.AddHours(sessionDurationHours);
        }

        #endregion

        #region العمليات والتحقق

        /// <summary>
        /// التحقق من صحة بيانات الجلسة
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return UserID > 0 &&
                   !string.IsNullOrWhiteSpace(SessionToken) &&
                   StartTime <= DateTime.Now &&
                   ExpiryTime > StartTime;
        }

        /// <summary>
        /// التحقق من انتهاء صلاحية الجلسة
        /// </summary>
        /// <returns>true إذا انتهت صلاحية الجلسة</returns>
        public bool IsExpired()
        {
            return DateTime.Now > ExpiryTime || !IsActive;
        }

        /// <summary>
        /// التحقق من نشاط الجلسة
        /// </summary>
        /// <param name="maxInactiveMinutes">الحد الأقصى لعدم النشاط بالدقائق</param>
        /// <returns>true إذا كانت الجلسة نشطة</returns>
        public bool IsActiveSession(int maxInactiveMinutes = 30)
        {
            return IsActive && 
                   !IsExpired() && 
                   (DateTime.Now - LastActivity).TotalMinutes <= maxInactiveMinutes;
        }

        /// <summary>
        /// تحديث آخر نشاط
        /// </summary>
        public void UpdateLastActivity()
        {
            LastActivity = DateTime.Now;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// تسجيل عملية جديدة
        /// </summary>
        /// <param name="operationType">نوع العملية</param>
        public void RecordOperation(string operationType = null)
        {
            OperationsCount++;
            UpdateLastActivity();
        }

        /// <summary>
        /// تسجيل زيارة صفحة جديدة
        /// </summary>
        /// <param name="pageName">اسم الصفحة</param>
        public void RecordPageVisit(string pageName = null)
        {
            PagesVisited++;
            UpdateLastActivity();
        }

        /// <summary>
        /// إنهاء الجلسة
        /// </summary>
        /// <param name="reason">سبب الإنهاء</param>
        /// <param name="details">تفاصيل إضافية</param>
        public void EndSession(string reason = "تسجيل خروج عادي", string details = null)
        {
            IsActive = false;
            EndTime = DateTime.Now;
            EndReason = reason;
            EndDetails = details;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// إنهاء الجلسة قسرياً
        /// </summary>
        /// <param name="reason">سبب الإنهاء القسري</param>
        /// <param name="details">تفاصيل إضافية</param>
        public void ForceEndSession(string reason = "إنهاء قسري", string details = null)
        {
            IsForceTerminated = true;
            EndSession(reason, details);
        }

        /// <summary>
        /// تمديد الجلسة
        /// </summary>
        /// <param name="additionalHours">عدد الساعات الإضافية</param>
        public void ExtendSession(int additionalHours = 2)
        {
            if (IsActive && !IsExpired())
            {
                ExpiryTime = ExpiryTime.AddHours(additionalHours);
                ModifiedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// تجديد الجلسة
        /// </summary>
        /// <param name="newDurationHours">مدة الجلسة الجديدة بالساعات</param>
        public void RenewSession(int newDurationHours = 8)
        {
            if (IsActive)
            {
                ExpiryTime = DateTime.Now.AddHours(newDurationHours);
                UpdateLastActivity();
            }
        }

        /// <summary>
        /// وضع علامة الشك على الجلسة
        /// </summary>
        /// <param name="reason">سبب الشك</param>
        public void MarkAsSuspicious(string reason)
        {
            IsSuspicious = true;
            SuspiciousReason = reason;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// إزالة علامة الشك من الجلسة
        /// </summary>
        public void ClearSuspicious()
        {
            IsSuspicious = false;
            SuspiciousReason = null;
            ModifiedDate = DateTime.Now;
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// توليد معرف جلسة فريد
        /// </summary>
        /// <returns>معرف الجلسة</returns>
        private string GenerateSessionToken()
        {
            return Guid.NewGuid().ToString("N") + DateTime.Now.Ticks.ToString("X");
        }

        /// <summary>
        /// الحصول على مدة الجلسة كنص
        /// </summary>
        /// <returns>مدة الجلسة</returns>
        public string GetSessionDurationText()
        {
            var duration = SessionDurationMinutes;
            if (!duration.HasValue)
                return "غير محدد";

            if (duration.Value < 60)
                return $"{duration.Value} دقيقة";

            var hours = duration.Value / 60;
            var minutes = duration.Value % 60;

            if (minutes == 0)
                return $"{hours} ساعة";

            return $"{hours} ساعة و {minutes} دقيقة";
        }

        /// <summary>
        /// الحصول على حالة الجلسة كنص
        /// </summary>
        /// <returns>حالة الجلسة</returns>
        public string GetStatusText()
        {
            if (IsForceTerminated)
                return "منتهية قسرياً";

            if (!IsActive)
                return "منتهية";

            if (IsExpired())
                return "منتهية الصلاحية";

            if (IsSuspicious)
                return "مشبوهة";

            return "نشطة";
        }

        #endregion

        #region عمليات النسخ والتحويل

        /// <summary>
        /// تحويل الجلسة إلى نص وصفي
        /// </summary>
        /// <returns>وصف نصي للجلسة</returns>
        public override string ToString()
        {
            return $"جلسة {SessionToken.Substring(0, 8)}... - {GetStatusText()} - {GetSessionDurationText()}";
        }

        #endregion
    }

    #region أسباب انتهاء الجلسة

    /// <summary>
    /// أسباب انتهاء الجلسة
    /// </summary>
    public static class SessionEndReasons
    {
        public const string NormalLogout = "تسجيل خروج عادي";
        public const string Timeout = "انتهاء المهلة الزمنية";
        public const string Expired = "انتهاء صلاحية الجلسة";
        public const string ForceLogout = "تسجيل خروج قسري";
        public const string SystemShutdown = "إغلاق النظام";
        public const string SecurityBreach = "خرق أمني";
        public const string DuplicateLogin = "تسجيل دخول مكرر";
        public const string AdminAction = "إجراء إداري";
        public const string ApplicationError = "خطأ في التطبيق";
        public const string NetworkDisconnection = "انقطاع الشبكة";
    }

    /// <summary>
    /// أنواع الأجهزة
    /// </summary>
    public static class DeviceTypes
    {
        public const string Desktop = "كمبيوتر مكتبي";
        public const string Laptop = "كمبيوتر محمول";
        public const string Tablet = "جهاز لوحي";
        public const string Mobile = "هاتف محمول";
        public const string POS = "جهاز نقاط البيع";
        public const string Unknown = "غير معروف";
    }

    #endregion
}
