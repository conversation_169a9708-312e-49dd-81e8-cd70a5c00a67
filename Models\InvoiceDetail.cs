using System;
using System.ComponentModel.DataAnnotations;

namespace AridooPOS.Models
{
    /// <summary>
    /// نموذج تفاصيل الفاتورة
    /// </summary>
    public class InvoiceDetail
    {
        public int InvoiceDetailID { get; set; }
        
        [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
        public int InvoiceID { get; set; }
        
        [Required(ErrorMessage = "رقم المنتج مطلوب")]
        public int ProductID { get; set; }
        
        [StringLength(20, ErrorMessage = "كود المنتج لا يجب أن يتجاوز 20 حرف")]
        public string ProductCode { get; set; }
        
        [Required(ErrorMessage = "اسم المنتج مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المنتج لا يجب أن يتجاوز 100 حرف")]
        public string ProductName { get; set; }
        
        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(0.001, double.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من صفر")]
        public decimal Quantity { get; set; }
        
        [Required(ErrorMessage = "سعر الوحدة مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal UnitPrice { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "مبلغ الخصم يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal DiscountAmount { get; set; }
        
        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public decimal DiscountPercent { get; set; }
        
        [Range(0, 100, ErrorMessage = "معدل الضريبة يجب أن يكون بين 0 و 100")]
        public decimal TaxRate { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "مبلغ الضريبة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal TaxAmount { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "إجمالي السطر يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal LineTotal { get; set; }
        
        public InvoiceDetail()
        {
            Quantity = 1;
            DiscountAmount = 0;
            DiscountPercent = 0;
            TaxRate = 0;
            TaxAmount = 0;
        }
        
        /// <summary>
        /// حساب إجمالي السطر
        /// </summary>
        public void CalculateLineTotal()
        {
            // حساب المبلغ الأساسي
            decimal baseAmount = Quantity * UnitPrice;
            
            // تطبيق الخصم
            decimal discountAmount = DiscountAmount;
            if (DiscountPercent > 0)
            {
                discountAmount = baseAmount * (DiscountPercent / 100);
            }
            
            // المبلغ بعد الخصم
            decimal amountAfterDiscount = baseAmount - discountAmount;
            
            // حساب الضريبة
            TaxAmount = amountAfterDiscount * (TaxRate / 100);
            
            // الإجمالي النهائي
            LineTotal = amountAfterDiscount + TaxAmount;
            
            // تحديث مبلغ الخصم الفعلي
            DiscountAmount = discountAmount;
        }
        
        /// <summary>
        /// التحقق من صحة تفاصيل الفاتورة
        /// </summary>
        /// <returns>true إذا كانت التفاصيل صحيحة</returns>
        public bool IsValid()
        {
            return ProductID > 0 &&
                   !string.IsNullOrEmpty(ProductName) &&
                   Quantity > 0 &&
                   UnitPrice >= 0;
        }
        
        /// <summary>
        /// نسخ تفاصيل الفاتورة
        /// </summary>
        /// <returns>نسخة من تفاصيل الفاتورة</returns>
        public InvoiceDetail Clone()
        {
            return new InvoiceDetail
            {
                InvoiceDetailID = this.InvoiceDetailID,
                InvoiceID = this.InvoiceID,
                ProductID = this.ProductID,
                ProductCode = this.ProductCode,
                ProductName = this.ProductName,
                Quantity = this.Quantity,
                UnitPrice = this.UnitPrice,
                DiscountAmount = this.DiscountAmount,
                DiscountPercent = this.DiscountPercent,
                TaxRate = this.TaxRate,
                TaxAmount = this.TaxAmount,
                LineTotal = this.LineTotal
            };
        }
    }
}
