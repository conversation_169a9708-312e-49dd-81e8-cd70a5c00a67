{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\pos v2\\AredooPOSFull.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\pos v2\\AredooPOSFull.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\pos v2\\AredooPOSFull.csproj", "projectName": "AredooPOSFull", "projectPath": "C:\\Users\\<USER>\\Desktop\\pos v2\\AredooPOSFull.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\pos v2\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"targetAlias": "net472", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net472": {"targetAlias": "net472", "dependencies": {"Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[7.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[7.0.1, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[4.1.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.5, )"}, "System.Drawing.Common": {"target": "Package", "version": "[7.0.0, )"}, "System.IO.Ports": {"target": "Package", "version": "[7.0.0, )"}, "System.Management": {"target": "Package", "version": "[7.0.2, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}}