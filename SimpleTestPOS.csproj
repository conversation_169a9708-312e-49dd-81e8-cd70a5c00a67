<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net472</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <AssemblyTitle>اختبار أريدو POS البسيط</AssemblyTitle>
    <AssemblyDescription>اختبار بسيط لنظام أريدو POS</AssemblyDescription>
    <AssemblyCompany>أريدو</AssemblyCompany>
    <AssemblyProduct>أريدو POS Simple Test</AssemblyProduct>
    <AssemblyCopyright>© 2024 أريدو. جميع الحقوق محفوظة.</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <StartupObject>AredooPOS.SimpleTest.SimpleTest</StartupObject>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <Deterministic>true</Deterministic>
    <LangVersion>latest</LangVersion>
    <Nullable>disable</Nullable>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="Microsoft.CSharp" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="SimpleTest.cs" />
  </ItemGroup>

</Project>
