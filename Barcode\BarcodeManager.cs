using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Barcode
{
    /// <summary>
    /// مدير الباركود
    /// يدعم إنتاج وقراءة أنواع مختلفة من الباركود
    /// متوافق مع Windows 7+
    /// </summary>
    public class BarcodeManager
    {
        #region المتغيرات والخصائص

        private readonly ILogger<BarcodeManager> _logger;
        private readonly BarcodeSettings _settings;

        /// <summary>
        /// أنواع الباركود المدعومة
        /// </summary>
        public static readonly Dictionary<BarcodeType, string> SupportedTypes = new Dictionary<BarcodeType, string>
        {
            { BarcodeType.Code128, "Code 128" },
            { BarcodeType.Code39, "Code 39" },
            { BarcodeType.EAN13, "EAN-13" },
            { BarcodeType.EAN8, "EAN-8" },
            { BarcodeType.UPCA, "UPC-A" },
            { BarcodeType.UPCE, "UPC-E" },
            { BarcodeType.ITF, "Interleaved 2 of 5" },
            { BarcodeType.Codabar, "Codabar" },
            { BarcodeType.QRCode, "QR Code" },
            { BarcodeType.DataMatrix, "Data Matrix" }
        };

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ مدير الباركود
        /// </summary>
        /// <param name="settings">إعدادات الباركود</param>
        /// <param name="logger">مسجل الأحداث</param>
        public BarcodeManager(BarcodeSettings settings = null, ILogger<BarcodeManager> logger = null)
        {
            _settings = settings ?? new BarcodeSettings();
            _logger = logger;
        }

        #endregion

        #region إنتاج الباركود

        /// <summary>
        /// إنتاج باركود
        /// </summary>
        /// <param name="data">البيانات</param>
        /// <param name="type">نوع الباركود</param>
        /// <param name="width">العرض</param>
        /// <param name="height">الارتفاع</param>
        /// <param name="includeText">تضمين النص</param>
        /// <returns>صورة الباركود</returns>
        public Bitmap GenerateBarcode(string data, BarcodeType type, int width = 300, int height = 100, bool includeText = true)
        {
            try
            {
                if (string.IsNullOrEmpty(data))
                    throw new ArgumentException("البيانات مطلوبة لإنتاج الباركود");

                // التحقق من صحة البيانات
                if (!ValidateData(data, type))
                    throw new ArgumentException($"البيانات غير صالحة لنوع الباركود {type}");

                _logger?.LogInformation($"إنتاج باركود من نوع {type} للبيانات: {data}");

                switch (type)
                {
                    case BarcodeType.Code128:
                        return GenerateCode128(data, width, height, includeText);

                    case BarcodeType.Code39:
                        return GenerateCode39(data, width, height, includeText);

                    case BarcodeType.EAN13:
                        return GenerateEAN13(data, width, height, includeText);

                    case BarcodeType.EAN8:
                        return GenerateEAN8(data, width, height, includeText);

                    case BarcodeType.UPCA:
                        return GenerateUPCA(data, width, height, includeText);

                    case BarcodeType.UPCE:
                        return GenerateUPCE(data, width, height, includeText);

                    case BarcodeType.QRCode:
                        return GenerateQRCode(data, width, height);

                    default:
                        throw new NotSupportedException($"نوع الباركود {type} غير مدعوم");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إنتاج الباركود: {data}");
                throw;
            }
        }

        /// <summary>
        /// إنتاج باركود Code 128
        /// </summary>
        private Bitmap GenerateCode128(string data, int width, int height, bool includeText)
        {
            var bitmap = new Bitmap(width, height);
            using var graphics = Graphics.FromImage(bitmap);
            
            // خلفية بيضاء
            graphics.Clear(Color.White);

            // تحويل البيانات إلى نمط الخطوط
            var pattern = EncodeCode128(data);
            
            // رسم الخطوط
            var barWidth = (float)width / pattern.Length;
            var barHeight = includeText ? height - 20 : height;

            for (int i = 0; i < pattern.Length; i++)
            {
                if (pattern[i] == '1')
                {
                    var brush = new SolidBrush(Color.Black);
                    graphics.FillRectangle(brush, i * barWidth, 0, barWidth, barHeight);
                }
            }

            // إضافة النص
            if (includeText)
            {
                var font = new Font("Arial", 8, FontStyle.Regular);
                var textBrush = new SolidBrush(Color.Black);
                var textFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };

                var textRect = new RectangleF(0, barHeight, width, 20);
                graphics.DrawString(data, font, textBrush, textRect, textFormat);
            }

            return bitmap;
        }

        /// <summary>
        /// إنتاج باركود Code 39
        /// </summary>
        private Bitmap GenerateCode39(string data, int width, int height, bool includeText)
        {
            var bitmap = new Bitmap(width, height);
            using var graphics = Graphics.FromImage(bitmap);
            
            graphics.Clear(Color.White);

            // إضافة رموز البداية والنهاية
            var encodedData = "*" + data.ToUpper() + "*";
            var pattern = EncodeCode39(encodedData);
            
            var barWidth = (float)width / pattern.Length;
            var barHeight = includeText ? height - 20 : height;

            for (int i = 0; i < pattern.Length; i++)
            {
                if (pattern[i] == '1')
                {
                    var brush = new SolidBrush(Color.Black);
                    graphics.FillRectangle(brush, i * barWidth, 0, barWidth, barHeight);
                }
            }

            if (includeText)
            {
                var font = new Font("Arial", 8, FontStyle.Regular);
                var textBrush = new SolidBrush(Color.Black);
                var textFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };

                var textRect = new RectangleF(0, barHeight, width, 20);
                graphics.DrawString(data, font, textBrush, textRect, textFormat);
            }

            return bitmap;
        }

        /// <summary>
        /// إنتاج باركود EAN-13
        /// </summary>
        private Bitmap GenerateEAN13(string data, int width, int height, bool includeText)
        {
            // التأكد من أن البيانات 12 رقم (سيتم إضافة رقم التحقق)
            if (data.Length == 12)
            {
                data += CalculateEAN13CheckDigit(data);
            }
            else if (data.Length != 13)
            {
                throw new ArgumentException("EAN-13 يتطلب 12 أو 13 رقم");
            }

            var bitmap = new Bitmap(width, height);
            using var graphics = Graphics.FromImage(bitmap);
            
            graphics.Clear(Color.White);

            var pattern = EncodeEAN13(data);
            var barWidth = (float)width / pattern.Length;
            var barHeight = includeText ? height - 20 : height;

            for (int i = 0; i < pattern.Length; i++)
            {
                if (pattern[i] == '1')
                {
                    var brush = new SolidBrush(Color.Black);
                    graphics.FillRectangle(brush, i * barWidth, 0, barWidth, barHeight);
                }
            }

            if (includeText)
            {
                var font = new Font("Arial", 8, FontStyle.Regular);
                var textBrush = new SolidBrush(Color.Black);
                var textFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };

                var textRect = new RectangleF(0, barHeight, width, 20);
                graphics.DrawString(data, font, textBrush, textRect, textFormat);
            }

            return bitmap;
        }

        /// <summary>
        /// إنتاج باركود EAN-8
        /// </summary>
        private Bitmap GenerateEAN8(string data, int width, int height, bool includeText)
        {
            if (data.Length == 7)
            {
                data += CalculateEAN8CheckDigit(data);
            }
            else if (data.Length != 8)
            {
                throw new ArgumentException("EAN-8 يتطلب 7 أو 8 أرقام");
            }

            var bitmap = new Bitmap(width, height);
            using var graphics = Graphics.FromImage(bitmap);
            
            graphics.Clear(Color.White);

            var pattern = EncodeEAN8(data);
            var barWidth = (float)width / pattern.Length;
            var barHeight = includeText ? height - 20 : height;

            for (int i = 0; i < pattern.Length; i++)
            {
                if (pattern[i] == '1')
                {
                    var brush = new SolidBrush(Color.Black);
                    graphics.FillRectangle(brush, i * barWidth, 0, barWidth, barHeight);
                }
            }

            if (includeText)
            {
                var font = new Font("Arial", 8, FontStyle.Regular);
                var textBrush = new SolidBrush(Color.Black);
                var textFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };

                var textRect = new RectangleF(0, barHeight, width, 20);
                graphics.DrawString(data, font, textBrush, textRect, textFormat);
            }

            return bitmap;
        }

        /// <summary>
        /// إنتاج باركود UPC-A
        /// </summary>
        private Bitmap GenerateUPCA(string data, int width, int height, bool includeText)
        {
            if (data.Length == 11)
            {
                data += CalculateUPCACheckDigit(data);
            }
            else if (data.Length != 12)
            {
                throw new ArgumentException("UPC-A يتطلب 11 أو 12 رقم");
            }

            return GenerateEAN13("0" + data, width, height, includeText);
        }

        /// <summary>
        /// إنتاج باركود UPC-E
        /// </summary>
        private Bitmap GenerateUPCE(string data, int width, int height, bool includeText)
        {
            if (data.Length != 6 && data.Length != 8)
            {
                throw new ArgumentException("UPC-E يتطلب 6 أو 8 أرقام");
            }

            var bitmap = new Bitmap(width, height);
            using var graphics = Graphics.FromImage(bitmap);
            
            graphics.Clear(Color.White);

            var pattern = EncodeUPCE(data);
            var barWidth = (float)width / pattern.Length;
            var barHeight = includeText ? height - 20 : height;

            for (int i = 0; i < pattern.Length; i++)
            {
                if (pattern[i] == '1')
                {
                    var brush = new SolidBrush(Color.Black);
                    graphics.FillRectangle(brush, i * barWidth, 0, barWidth, barHeight);
                }
            }

            if (includeText)
            {
                var font = new Font("Arial", 8, FontStyle.Regular);
                var textBrush = new SolidBrush(Color.Black);
                var textFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };

                var textRect = new RectangleF(0, barHeight, width, 20);
                graphics.DrawString(data, font, textBrush, textRect, textFormat);
            }

            return bitmap;
        }

        /// <summary>
        /// إنتاج QR Code
        /// </summary>
        private Bitmap GenerateQRCode(string data, int width, int height)
        {
            // تنفيذ مبسط لـ QR Code
            // في التطبيق الحقيقي، يُفضل استخدام مكتبة متخصصة مثل ZXing.Net
            
            var bitmap = new Bitmap(width, height);
            using var graphics = Graphics.FromImage(bitmap);
            
            graphics.Clear(Color.White);

            // رسم نمط QR Code مبسط
            var moduleSize = Math.Min(width, height) / 25;
            var qrMatrix = GenerateSimpleQRMatrix(data, 25);

            for (int y = 0; y < qrMatrix.GetLength(0); y++)
            {
                for (int x = 0; x < qrMatrix.GetLength(1); x++)
                {
                    if (qrMatrix[y, x])
                    {
                        var brush = new SolidBrush(Color.Black);
                        graphics.FillRectangle(brush, x * moduleSize, y * moduleSize, moduleSize, moduleSize);
                    }
                }
            }

            return bitmap;
        }

        #endregion

        #region التحقق من صحة البيانات

        /// <summary>
        /// التحقق من صحة البيانات للباركود
        /// </summary>
        /// <param name="data">البيانات</param>
        /// <param name="type">نوع الباركود</param>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool ValidateData(string data, BarcodeType type)
        {
            if (string.IsNullOrEmpty(data))
                return false;

            try
            {
                switch (type)
                {
                    case BarcodeType.Code128:
                        return ValidateCode128(data);

                    case BarcodeType.Code39:
                        return ValidateCode39(data);

                    case BarcodeType.EAN13:
                        return ValidateEAN13(data);

                    case BarcodeType.EAN8:
                        return ValidateEAN8(data);

                    case BarcodeType.UPCA:
                        return ValidateUPCA(data);

                    case BarcodeType.UPCE:
                        return ValidateUPCE(data);

                    case BarcodeType.QRCode:
                        return ValidateQRCode(data);

                    default:
                        return false;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات Code 128
        /// </summary>
        private bool ValidateCode128(string data)
        {
            // Code 128 يدعم جميع أحرف ASCII
            return data.All(c => c >= 0 && c <= 127);
        }

        /// <summary>
        /// التحقق من صحة بيانات Code 39
        /// </summary>
        private bool ValidateCode39(string data)
        {
            // Code 39 يدعم الأرقام والأحرف الكبيرة وبعض الرموز الخاصة
            var validChars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%";
            return data.All(c => validChars.Contains(c));
        }

        /// <summary>
        /// التحقق من صحة بيانات EAN-13
        /// </summary>
        private bool ValidateEAN13(string data)
        {
            return (data.Length == 12 || data.Length == 13) && data.All(char.IsDigit);
        }

        /// <summary>
        /// التحقق من صحة بيانات EAN-8
        /// </summary>
        private bool ValidateEAN8(string data)
        {
            return (data.Length == 7 || data.Length == 8) && data.All(char.IsDigit);
        }

        /// <summary>
        /// التحقق من صحة بيانات UPC-A
        /// </summary>
        private bool ValidateUPCA(string data)
        {
            return (data.Length == 11 || data.Length == 12) && data.All(char.IsDigit);
        }

        /// <summary>
        /// التحقق من صحة بيانات UPC-E
        /// </summary>
        private bool ValidateUPCE(string data)
        {
            return (data.Length == 6 || data.Length == 8) && data.All(char.IsDigit);
        }

        /// <summary>
        /// التحقق من صحة بيانات QR Code
        /// </summary>
        private bool ValidateQRCode(string data)
        {
            // QR Code يدعم معظم أنواع البيانات
            return data.Length <= 4296; // الحد الأقصى للبيانات النصية
        }

        #endregion

        #region حفظ وتصدير الباركود

        /// <summary>
        /// حفظ الباركود كصورة
        /// </summary>
        /// <param name="barcode">صورة الباركود</param>
        /// <param name="filePath">مسار الملف</param>
        /// <param name="format">تنسيق الصورة</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        public bool SaveBarcode(Bitmap barcode, string filePath, ImageFormat format = null)
        {
            try
            {
                format = format ?? ImageFormat.Png;
                barcode.Save(filePath, format);
                
                _logger?.LogInformation($"تم حفظ الباركود في: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حفظ الباركود: {filePath}");
                return false;
            }
        }

        /// <summary>
        /// تحويل الباركود إلى مصفوفة بايتات
        /// </summary>
        /// <param name="barcode">صورة الباركود</param>
        /// <param name="format">تنسيق الصورة</param>
        /// <returns>مصفوفة البايتات</returns>
        public byte[] BarcodeToBytes(Bitmap barcode, ImageFormat format = null)
        {
            try
            {
                format = format ?? ImageFormat.Png;
                using var stream = new MemoryStream();
                barcode.Save(stream, format);
                return stream.ToArray();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحويل الباركود إلى بايتات");
                return null;
            }
        }

        /// <summary>
        /// تحويل مصفوفة البايتات إلى باركود
        /// </summary>
        /// <param name="data">مصفوفة البايتات</param>
        /// <returns>صورة الباركود</returns>
        public Bitmap BytesToBarcode(byte[] data)
        {
            try
            {
                using var stream = new MemoryStream(data);
                return new Bitmap(stream);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحويل البايتات إلى باركود");
                return null;
            }
        }

        #endregion

        #region إنتاج أرقام الباركود

        /// <summary>
        /// إنتاج رقم باركود جديد
        /// </summary>
        /// <param name="type">نوع الباركود</param>
        /// <param name="prefix">البادئة</param>
        /// <returns>رقم الباركود</returns>
        public string GenerateBarcodeNumber(BarcodeType type, string prefix = "")
        {
            try
            {
                switch (type)
                {
                    case BarcodeType.EAN13:
                        return GenerateEAN13Number(prefix);

                    case BarcodeType.EAN8:
                        return GenerateEAN8Number(prefix);

                    case BarcodeType.UPCA:
                        return GenerateUPCANumber(prefix);

                    case BarcodeType.Code128:
                    case BarcodeType.Code39:
                        return GenerateAlphanumericCode(prefix);

                    default:
                        return GenerateNumericCode(prefix);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنتاج رقم الباركود");
                return null;
            }
        }

        /// <summary>
        /// إنتاج رقم EAN-13
        /// </summary>
        private string GenerateEAN13Number(string prefix)
        {
            var random = new Random();
            var code = prefix.PadRight(12, '0');
            
            if (code.Length > 12)
                code = code.Substring(0, 12);
            
            // ملء الأرقام المتبقية عشوائياً
            var sb = new StringBuilder(code);
            for (int i = prefix.Length; i < 12; i++)
            {
                sb[i] = (char)('0' + random.Next(10));
            }
            
            code = sb.ToString();
            return code + CalculateEAN13CheckDigit(code);
        }

        /// <summary>
        /// إنتاج رقم EAN-8
        /// </summary>
        private string GenerateEAN8Number(string prefix)
        {
            var random = new Random();
            var code = prefix.PadRight(7, '0');
            
            if (code.Length > 7)
                code = code.Substring(0, 7);
            
            var sb = new StringBuilder(code);
            for (int i = prefix.Length; i < 7; i++)
            {
                sb[i] = (char)('0' + random.Next(10));
            }
            
            code = sb.ToString();
            return code + CalculateEAN8CheckDigit(code);
        }

        /// <summary>
        /// إنتاج رقم UPC-A
        /// </summary>
        private string GenerateUPCANumber(string prefix)
        {
            var random = new Random();
            var code = prefix.PadRight(11, '0');
            
            if (code.Length > 11)
                code = code.Substring(0, 11);
            
            var sb = new StringBuilder(code);
            for (int i = prefix.Length; i < 11; i++)
            {
                sb[i] = (char)('0' + random.Next(10));
            }
            
            code = sb.ToString();
            return code + CalculateUPCACheckDigit(code);
        }

        /// <summary>
        /// إنتاج كود أبجدي رقمي
        /// </summary>
        private string GenerateAlphanumericCode(string prefix)
        {
            var random = new Random();
            var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            var length = Math.Max(8, prefix.Length + 4);
            
            var sb = new StringBuilder(prefix);
            while (sb.Length < length)
            {
                sb.Append(chars[random.Next(chars.Length)]);
            }
            
            return sb.ToString();
        }

        /// <summary>
        /// إنتاج كود رقمي
        /// </summary>
        private string GenerateNumericCode(string prefix)
        {
            var random = new Random();
            var length = Math.Max(8, prefix.Length + 4);
            
            var sb = new StringBuilder(prefix);
            while (sb.Length < length)
            {
                sb.Append(random.Next(10));
            }
            
            return sb.ToString();
        }

        #endregion

        #region دوال التشفير المساعدة

        /// <summary>
        /// تشفير Code 128
        /// </summary>
        private string EncodeCode128(string data)
        {
            // تنفيذ مبسط لـ Code 128
            // في التطبيق الحقيقي، يجب تنفيذ الخوارزمية الكاملة
            var pattern = new StringBuilder();

            // رمز البداية
            pattern.Append("11010000100");

            // تشفير البيانات
            foreach (char c in data)
            {
                pattern.Append(GetCode128Pattern(c));
            }

            // رمز النهاية
            pattern.Append("1100011101011");

            return pattern.ToString();
        }

        /// <summary>
        /// تشفير Code 39
        /// </summary>
        private string EncodeCode39(string data)
        {
            var patterns = new Dictionary<char, string>
            {
                {'*', "100010111011101"}, // رمز البداية/النهاية
                {'0', "101001101101"}, {'1', "110100101011"}, {'2', "101100101011"},
                {'3', "110110010101"}, {'4', "101001101011"}, {'5', "110100110101"},
                {'6', "101100110101"}, {'7', "101001011011"}, {'8', "110100101101"},
                {'9', "101100101101"}, {'A', "110101001011"}, {'B', "101101001011"},
                {'C', "110110100101"}, {'D', "101011001011"}, {'E', "110101100101"},
                {'F', "101101100101"}, {'G', "101010011011"}, {'H', "110101001101"},
                {'I', "101101001101"}, {'J', "101011001101"}, {'K', "110101010011"},
                {'L', "101101010011"}, {'M', "110110101001"}, {'N', "101011010011"},
                {'O', "110101101001"}, {'P', "101101101001"}, {'Q', "101010110011"},
                {'R', "110101011001"}, {'S', "101101011001"}, {'T', "101011011001"},
                {'U', "110010101011"}, {'V', "100110101011"}, {'W', "110011010101"},
                {'X', "100101101011"}, {'Y', "110010110101"}, {'Z', "100110110101"},
                {'-', "100101011011"}, {'.', "110010101101"}, {' ', "100110101101"},
                {'$', "100100100101"}, {'/', "100100101001"}, {'+', "100101001001"},
                {'%', "101001001001"}
            };

            var pattern = new StringBuilder();
            foreach (char c in data)
            {
                if (patterns.ContainsKey(c))
                {
                    pattern.Append(patterns[c]);
                    pattern.Append("0"); // فاصل
                }
            }

            return pattern.ToString();
        }

        /// <summary>
        /// تشفير EAN-13
        /// </summary>
        private string EncodeEAN13(string data)
        {
            var leftPatterns = new string[]
            {
                "0001101", "0011001", "0010011", "0111101", "0100011",
                "0110001", "0101111", "0111011", "0110111", "0001011"
            };

            var rightPatterns = new string[]
            {
                "1110010", "1100110", "1101100", "1000010", "1011100",
                "1001110", "1010000", "1000100", "1001000", "1110100"
            };

            var pattern = new StringBuilder();

            // رمز البداية
            pattern.Append("101");

            // الجانب الأيسر
            for (int i = 1; i < 7; i++)
            {
                int digit = int.Parse(data[i].ToString());
                pattern.Append(leftPatterns[digit]);
            }

            // الفاصل الأوسط
            pattern.Append("01010");

            // الجانب الأيمن
            for (int i = 7; i < 13; i++)
            {
                int digit = int.Parse(data[i].ToString());
                pattern.Append(rightPatterns[digit]);
            }

            // رمز النهاية
            pattern.Append("101");

            return pattern.ToString();
        }

        /// <summary>
        /// تشفير EAN-8
        /// </summary>
        private string EncodeEAN8(string data)
        {
            var leftPatterns = new string[]
            {
                "0001101", "0011001", "0010011", "0111101", "0100011",
                "0110001", "0101111", "0111011", "0110111", "0001011"
            };

            var rightPatterns = new string[]
            {
                "1110010", "1100110", "1101100", "1000010", "1011100",
                "1001110", "1010000", "1000100", "1001000", "1110100"
            };

            var pattern = new StringBuilder();

            pattern.Append("101"); // البداية

            // الجانب الأيسر
            for (int i = 0; i < 4; i++)
            {
                int digit = int.Parse(data[i].ToString());
                pattern.Append(leftPatterns[digit]);
            }

            pattern.Append("01010"); // الفاصل

            // الجانب الأيمن
            for (int i = 4; i < 8; i++)
            {
                int digit = int.Parse(data[i].ToString());
                pattern.Append(rightPatterns[digit]);
            }

            pattern.Append("101"); // النهاية

            return pattern.ToString();
        }

        /// <summary>
        /// تشفير UPC-E
        /// </summary>
        private string EncodeUPCE(string data)
        {
            // تنفيذ مبسط لـ UPC-E
            var pattern = new StringBuilder();
            pattern.Append("101"); // البداية

            // تشفير البيانات (مبسط)
            foreach (char c in data)
            {
                if (char.IsDigit(c))
                {
                    int digit = int.Parse(c.ToString());
                    pattern.Append("0001101"); // نمط مبسط
                }
            }

            pattern.Append("010101"); // النهاية

            return pattern.ToString();
        }

        /// <summary>
        /// الحصول على نمط Code 128 للحرف
        /// </summary>
        private string GetCode128Pattern(char c)
        {
            // نماذج مبسطة لـ Code 128
            var patterns = new Dictionary<char, string>();

            // إضافة النماذج للأرقام
            for (int i = 0; i <= 9; i++)
            {
                patterns[(char)('0' + i)] = "11011001100";
            }

            // إضافة النماذج للأحرف
            for (int i = 0; i < 26; i++)
            {
                patterns[(char)('A' + i)] = "11001101100";
                patterns[(char)('a' + i)] = "11001100110";
            }

            return patterns.ContainsKey(c) ? patterns[c] : "11011001100";
        }

        /// <summary>
        /// إنتاج مصفوفة QR Code مبسطة
        /// </summary>
        private bool[,] GenerateSimpleQRMatrix(string data, int size)
        {
            var matrix = new bool[size, size];
            var random = new Random(data.GetHashCode());

            // إنتاج نمط عشوائي بناءً على البيانات
            for (int y = 0; y < size; y++)
            {
                for (int x = 0; x < size; x++)
                {
                    matrix[y, x] = random.NextDouble() > 0.5;
                }
            }

            // إضافة نماذج الكشف (مبسطة)
            AddFinderPattern(matrix, 0, 0);
            AddFinderPattern(matrix, 0, size - 7);
            AddFinderPattern(matrix, size - 7, 0);

            return matrix;
        }

        /// <summary>
        /// إضافة نمط الكشف لـ QR Code
        /// </summary>
        private void AddFinderPattern(bool[,] matrix, int x, int y)
        {
            var pattern = new bool[,]
            {
                {true, true, true, true, true, true, true},
                {true, false, false, false, false, false, true},
                {true, false, true, true, true, false, true},
                {true, false, true, true, true, false, true},
                {true, false, true, true, true, false, true},
                {true, false, false, false, false, false, true},
                {true, true, true, true, true, true, true}
            };

            for (int py = 0; py < 7; py++)
            {
                for (int px = 0; px < 7; px++)
                {
                    if (y + py < matrix.GetLength(0) && x + px < matrix.GetLength(1))
                    {
                        matrix[y + py, x + px] = pattern[py, px];
                    }
                }
            }
        }

        #endregion

        #region حساب أرقام التحقق

        /// <summary>
        /// حساب رقم التحقق لـ EAN-13
        /// </summary>
        private char CalculateEAN13CheckDigit(string data)
        {
            int sum = 0;
            for (int i = 0; i < 12; i++)
            {
                int digit = int.Parse(data[i].ToString());
                sum += (i % 2 == 0) ? digit : digit * 3;
            }

            int checkDigit = (10 - (sum % 10)) % 10;
            return (char)('0' + checkDigit);
        }

        /// <summary>
        /// حساب رقم التحقق لـ EAN-8
        /// </summary>
        private char CalculateEAN8CheckDigit(string data)
        {
            int sum = 0;
            for (int i = 0; i < 7; i++)
            {
                int digit = int.Parse(data[i].ToString());
                sum += (i % 2 == 0) ? digit : digit * 3;
            }

            int checkDigit = (10 - (sum % 10)) % 10;
            return (char)('0' + checkDigit);
        }

        /// <summary>
        /// حساب رقم التحقق لـ UPC-A
        /// </summary>
        private char CalculateUPCACheckDigit(string data)
        {
            int sum = 0;
            for (int i = 0; i < 11; i++)
            {
                int digit = int.Parse(data[i].ToString());
                sum += (i % 2 == 0) ? digit * 3 : digit;
            }

            int checkDigit = (10 - (sum % 10)) % 10;
            return (char)('0' + checkDigit);
        }

        #endregion
    }

    #region النماذج والتعدادات

    /// <summary>
    /// أنواع الباركود المدعومة
    /// </summary>
    public enum BarcodeType
    {
        Code128,
        Code39,
        EAN13,
        EAN8,
        UPCA,
        UPCE,
        ITF,
        Codabar,
        QRCode,
        DataMatrix
    }

    /// <summary>
    /// إعدادات الباركود
    /// </summary>
    public class BarcodeSettings
    {
        /// <summary>
        /// العرض الافتراضي
        /// </summary>
        public int DefaultWidth { get; set; } = 300;

        /// <summary>
        /// الارتفاع الافتراضي
        /// </summary>
        public int DefaultHeight { get; set; } = 100;

        /// <summary>
        /// تضمين النص افتراضياً
        /// </summary>
        public bool IncludeTextByDefault { get; set; } = true;

        /// <summary>
        /// نوع الباركود الافتراضي
        /// </summary>
        public BarcodeType DefaultType { get; set; } = BarcodeType.Code128;

        /// <summary>
        /// البادئة الافتراضية للمنتجات
        /// </summary>
        public string ProductPrefix { get; set; } = "PRD";

        /// <summary>
        /// البادئة الافتراضية للعملاء
        /// </summary>
        public string CustomerPrefix { get; set; } = "CUS";

        /// <summary>
        /// البادئة الافتراضية للفواتير
        /// </summary>
        public string InvoicePrefix { get; set; } = "INV";

        /// <summary>
        /// مجلد حفظ الباركود
        /// </summary>
        public string SaveDirectory { get; set; } = "Barcodes";

        /// <summary>
        /// تنسيق الحفظ الافتراضي
        /// </summary>
        public ImageFormat DefaultSaveFormat { get; set; } = ImageFormat.Png;
    }

    /// <summary>
    /// معلومات الباركود
    /// </summary>
    public class BarcodeInfo
    {
        /// <summary>
        /// البيانات
        /// </summary>
        public string Data { get; set; }

        /// <summary>
        /// النوع
        /// </summary>
        public BarcodeType Type { get; set; }

        /// <summary>
        /// العرض
        /// </summary>
        public int Width { get; set; }

        /// <summary>
        /// الارتفاع
        /// </summary>
        public int Height { get; set; }

        /// <summary>
        /// تضمين النص
        /// </summary>
        public bool IncludeText { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// من أنشأه
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// الوصف
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// مسار الملف
        /// </summary>
        public string FilePath { get; set; }
    }

    #endregion
}
