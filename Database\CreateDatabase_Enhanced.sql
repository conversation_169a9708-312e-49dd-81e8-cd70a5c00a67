-- =============================================
-- نظام أريدو POS - إنشاء قاعدة البيانات المحسنة
-- SQL Server Database Creation Script
-- متوافق مع Windows 7+ و SQL Server 2008+
-- =============================================

USE master;
GO

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'AredooPOS')
BEGIN
    CREATE DATABASE AredooPOS
    ON 
    (
        NAME = 'AredooPOS_Data',
        FILENAME = 'C:\AredooPOS\Database\AredooPOS.mdf',
        SIZE = 100MB,
        MAXSIZE = 10GB,
        FILEGROWTH = 10MB
    )
    LOG ON 
    (
        NAME = 'AredooPOS_Log',
        FILENAME = 'C:\AredooPOS\Database\AredooPOS.ldf',
        SIZE = 10MB,
        MAXSIZE = 1GB,
        FILEGROWTH = 10%
    );
END
GO

USE AredooPOS;
GO

-- =============================================
-- إعداد الترتيب العربي والتوافق
-- =============================================
ALTER DATABASE AredooPOS COLLATE Arabic_CI_AS;
GO

-- تفعيل خيارات التوافق مع الإصدارات القديمة
ALTER DATABASE AredooPOS SET COMPATIBILITY_LEVEL = 100; -- SQL Server 2008
GO

-- إعدادات الأداء والموثوقية
ALTER DATABASE AredooPOS SET AUTO_CLOSE OFF;
GO
ALTER DATABASE AredooPOS SET AUTO_SHRINK OFF;
GO
ALTER DATABASE AredooPOS SET AUTO_CREATE_STATISTICS ON;
GO
ALTER DATABASE AredooPOS SET AUTO_UPDATE_STATISTICS ON;
GO

-- =============================================
-- إنشاء المخططات (Schemas)
-- =============================================

-- مخطط الأمان والمستخدمين
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Security')
    EXEC('CREATE SCHEMA Security');
GO

-- مخطط المبيعات
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Sales')
    EXEC('CREATE SCHEMA Sales');
GO

-- مخطط المخزون
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Inventory')
    EXEC('CREATE SCHEMA Inventory');
GO

-- مخطط المالية
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Finance')
    EXEC('CREATE SCHEMA Finance');
GO

-- مخطط التقارير
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Reports')
    EXEC('CREATE SCHEMA Reports');
GO

-- مخطط الإعدادات
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Settings')
    EXEC('CREATE SCHEMA Settings');
GO

-- مخطط المزامنة
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Sync')
    EXEC('CREATE SCHEMA Sync');
GO

-- =============================================
-- جداول المزامنة والعمل أوفلاين
-- =============================================

-- جدول تتبع التغييرات للمزامنة
CREATE TABLE Sync.ChangeTracking (
    ChangeID bigint IDENTITY(1,1) PRIMARY KEY,
    TableName nvarchar(100) NOT NULL,
    RecordID nvarchar(50) NOT NULL,
    OperationType nvarchar(10) NOT NULL, -- INSERT, UPDATE, DELETE
    ChangeData nvarchar(max),
    ChangeDate datetime NOT NULL DEFAULT GETDATE(),
    UserID int,
    DeviceID nvarchar(50),
    IsSynced bit NOT NULL DEFAULT 0,
    SyncDate datetime,
    ConflictResolved bit NOT NULL DEFAULT 0,
    
    INDEX IX_ChangeTracking_TableName NONCLUSTERED (TableName),
    INDEX IX_ChangeTracking_IsSynced NONCLUSTERED (IsSynced),
    INDEX IX_ChangeTracking_ChangeDate NONCLUSTERED (ChangeDate)
);
GO

-- جدول الأجهزة المسجلة
CREATE TABLE Sync.RegisteredDevices (
    DeviceID nvarchar(50) PRIMARY KEY,
    DeviceName nvarchar(100) NOT NULL,
    DeviceType nvarchar(50), -- Desktop, Tablet, Mobile
    LastSyncDate datetime,
    IsActive bit NOT NULL DEFAULT 1,
    RegisteredDate datetime NOT NULL DEFAULT GETDATE(),
    RegisteredBy int,
    
    INDEX IX_RegisteredDevices_IsActive NONCLUSTERED (IsActive),
    INDEX IX_RegisteredDevices_LastSyncDate NONCLUSTERED (LastSyncDate)
);
GO

-- جدول حالة المزامنة
CREATE TABLE Sync.SyncStatus (
    SyncID bigint IDENTITY(1,1) PRIMARY KEY,
    DeviceID nvarchar(50) NOT NULL,
    TableName nvarchar(100) NOT NULL,
    LastSyncDate datetime NOT NULL,
    RecordsCount int DEFAULT 0,
    ConflictsCount int DEFAULT 0,
    Status nvarchar(20) DEFAULT 'Success', -- Success, Failed, InProgress
    ErrorMessage nvarchar(500),
    
    FOREIGN KEY (DeviceID) REFERENCES Sync.RegisteredDevices(DeviceID),
    
    INDEX IX_SyncStatus_DeviceID NONCLUSTERED (DeviceID),
    INDEX IX_SyncStatus_TableName NONCLUSTERED (TableName),
    INDEX IX_SyncStatus_LastSyncDate NONCLUSTERED (LastSyncDate)
);
GO

-- =============================================
-- جداول الأمان والمستخدمين
-- =============================================

-- جدول المستخدمين
CREATE TABLE Security.Users (
    UserID int IDENTITY(1,1) PRIMARY KEY,
    Username nvarchar(50) NOT NULL UNIQUE,
    PasswordHash nvarchar(255) NOT NULL,
    Salt nvarchar(50) NOT NULL,
    FullName nvarchar(100) NOT NULL,
    Email nvarchar(100),
    Phone nvarchar(20),
    IsActive bit NOT NULL DEFAULT 1,
    LastLogin datetime,
    FailedLoginAttempts int DEFAULT 0,
    AccountLocked bit DEFAULT 0,
    LockoutTime datetime,
    PasswordExpiry datetime,
    MustChangePassword bit DEFAULT 0,
    CreatedDate datetime NOT NULL DEFAULT GETDATE(),
    CreatedBy nvarchar(50),
    ModifiedDate datetime,
    ModifiedBy nvarchar(50),
    
    INDEX IX_Users_Username NONCLUSTERED (Username),
    INDEX IX_Users_Email NONCLUSTERED (Email),
    INDEX IX_Users_IsActive NONCLUSTERED (IsActive)
);
GO

-- جدول الأدوار
CREATE TABLE Security.Roles (
    RoleID int IDENTITY(1,1) PRIMARY KEY,
    RoleName nvarchar(50) NOT NULL UNIQUE,
    Description nvarchar(255),
    IsActive bit NOT NULL DEFAULT 1,
    CreatedDate datetime NOT NULL DEFAULT GETDATE(),
    CreatedBy nvarchar(50),
    
    INDEX IX_Roles_RoleName NONCLUSTERED (RoleName),
    INDEX IX_Roles_IsActive NONCLUSTERED (IsActive)
);
GO

-- جدول ربط المستخدمين بالأدوار
CREATE TABLE Security.UserRoles (
    UserRoleID int IDENTITY(1,1) PRIMARY KEY,
    UserID int NOT NULL,
    RoleID int NOT NULL,
    AssignedDate datetime NOT NULL DEFAULT GETDATE(),
    AssignedBy nvarchar(50),
    
    FOREIGN KEY (UserID) REFERENCES Security.Users(UserID) ON DELETE CASCADE,
    FOREIGN KEY (RoleID) REFERENCES Security.Roles(RoleID) ON DELETE CASCADE,
    
    UNIQUE (UserID, RoleID),
    INDEX IX_UserRoles_UserID NONCLUSTERED (UserID),
    INDEX IX_UserRoles_RoleID NONCLUSTERED (RoleID)
);
GO

-- جدول الصلاحيات
CREATE TABLE Security.Permissions (
    PermissionID int IDENTITY(1,1) PRIMARY KEY,
    PermissionName nvarchar(100) NOT NULL UNIQUE,
    Description nvarchar(255),
    Module nvarchar(50) NOT NULL,
    Action nvarchar(50) NOT NULL,
    IsActive bit NOT NULL DEFAULT 1,
    
    INDEX IX_Permissions_Module NONCLUSTERED (Module),
    INDEX IX_Permissions_Action NONCLUSTERED (Action)
);
GO

-- جدول ربط الأدوار بالصلاحيات
CREATE TABLE Security.RolePermissions (
    RolePermissionID int IDENTITY(1,1) PRIMARY KEY,
    RoleID int NOT NULL,
    PermissionID int NOT NULL,
    GrantedDate datetime NOT NULL DEFAULT GETDATE(),
    GrantedBy nvarchar(50),
    
    FOREIGN KEY (RoleID) REFERENCES Security.Roles(RoleID) ON DELETE CASCADE,
    FOREIGN KEY (PermissionID) REFERENCES Security.Permissions(PermissionID) ON DELETE CASCADE,
    
    UNIQUE (RoleID, PermissionID),
    INDEX IX_RolePermissions_RoleID NONCLUSTERED (RoleID),
    INDEX IX_RolePermissions_PermissionID NONCLUSTERED (PermissionID)
);
GO

-- جدول سجل تسجيل الدخول
CREATE TABLE Security.LoginHistory (
    LoginID bigint IDENTITY(1,1) PRIMARY KEY,
    UserID int,
    Username nvarchar(50),
    LoginDate datetime NOT NULL DEFAULT GETDATE(),
    IPAddress nvarchar(45),
    DeviceInfo nvarchar(255),
    LoginStatus nvarchar(20), -- Success, Failed, Locked
    LogoutDate datetime,
    SessionDuration int, -- بالدقائق
    
    FOREIGN KEY (UserID) REFERENCES Security.Users(UserID),
    
    INDEX IX_LoginHistory_UserID NONCLUSTERED (UserID),
    INDEX IX_LoginHistory_LoginDate NONCLUSTERED (LoginDate),
    INDEX IX_LoginHistory_LoginStatus NONCLUSTERED (LoginStatus)
);
GO

-- =============================================
-- جداول الإعدادات المحسنة
-- =============================================

-- جدول إعدادات النظام
CREATE TABLE Settings.SystemSettings (
    SettingID int IDENTITY(1,1) PRIMARY KEY,
    SettingName nvarchar(100) NOT NULL UNIQUE,
    SettingValue nvarchar(max),
    Description nvarchar(255),
    Category nvarchar(50) NOT NULL,
    DataType nvarchar(20) NOT NULL DEFAULT 'String',
    IsRequired bit NOT NULL DEFAULT 0,
    IsEditable bit NOT NULL DEFAULT 1,
    DefaultValue nvarchar(max),
    ValidationRule nvarchar(255),
    LastUpdated datetime NOT NULL DEFAULT GETDATE(),
    UpdatedBy nvarchar(50),
    
    INDEX IX_SystemSettings_Category NONCLUSTERED (Category),
    INDEX IX_SystemSettings_SettingName NONCLUSTERED (SettingName)
);
GO

-- جدول إعدادات الطباعة المتقدمة
CREATE TABLE Settings.PrinterSettings (
    PrinterID int IDENTITY(1,1) PRIMARY KEY,
    PrinterName nvarchar(100) NOT NULL,
    PrinterType nvarchar(50) NOT NULL, -- Thermal, Laser, Inkjet
    ConnectionType nvarchar(20) NOT NULL, -- USB, Network, Serial
    IPAddress nvarchar(15),
    Port int,
    SerialPort nvarchar(10),
    BaudRate int,
    PaperWidth int, -- بالمليمتر
    PaperHeight int,
    CharactersPerLine int,
    LinesPerPage int,
    IsDefault bit NOT NULL DEFAULT 0,
    IsActive bit NOT NULL DEFAULT 1,
    PrinterCommands nvarchar(max), -- أوامر ESC/POS
    CreatedDate datetime NOT NULL DEFAULT GETDATE(),
    
    INDEX IX_PrinterSettings_PrinterType NONCLUSTERED (PrinterType),
    INDEX IX_PrinterSettings_IsDefault NONCLUSTERED (IsDefault),
    INDEX IX_PrinterSettings_IsActive NONCLUSTERED (IsActive)
);
GO
