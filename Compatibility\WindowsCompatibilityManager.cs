using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Management;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Security.Principal;
using System.Windows.Forms;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;

namespace AredooPOS.Compatibility
{
    /// <summary>
    /// مدير التوافق مع Windows
    /// يضمن عمل النظام على Windows 7 وما فوق
    /// </summary>
    public class WindowsCompatibilityManager
    {
        #region المتغيرات والخصائص

        private readonly ILogger<WindowsCompatibilityManager> _logger;
        private static WindowsCompatibilityManager _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// المثيل الوحيد للمدير
        /// </summary>
        public static WindowsCompatibilityManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new WindowsCompatibilityManager();
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// معلومات نظام التشغيل
        /// </summary>
        public WindowsVersionInfo WindowsVersion { get; private set; }

        /// <summary>
        /// هل النظام متوافق
        /// </summary>
        public bool IsCompatible { get; private set; }

        /// <summary>
        /// قائمة المشاكل المكتشفة
        /// </summary>
        public List<CompatibilityIssue> Issues { get; private set; }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ مدير التوافق
        /// </summary>
        private WindowsCompatibilityManager(ILogger<WindowsCompatibilityManager> logger = null)
        {
            _logger = logger;
            Issues = new List<CompatibilityIssue>();

            InitializeCompatibilityCheck();
        }

        /// <summary>
        /// تهيئة فحص التوافق
        /// </summary>
        private void InitializeCompatibilityCheck()
        {
            try
            {
                _logger?.LogInformation("بدء فحص التوافق مع Windows");

                // فحص إصدار Windows
                WindowsVersion = GetWindowsVersionInfo();

                // فحص التوافق
                CheckCompatibility();

                _logger?.LogInformation($"اكتمل فحص التوافق - النتيجة: {(IsCompatible ? "متوافق" : "غير متوافق")}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تهيئة فحص التوافق");
                IsCompatible = false;
            }
        }

        #endregion

        #region فحص إصدار Windows

        /// <summary>
        /// الحصول على معلومات إصدار Windows
        /// </summary>
        /// <returns>معلومات الإصدار</returns>
        private WindowsVersionInfo GetWindowsVersionInfo()
        {
            var versionInfo = new WindowsVersionInfo();

            try
            {
                // الحصول على معلومات النظام
                var osVersion = Environment.OSVersion;
                versionInfo.Version = osVersion.Version;
                versionInfo.Platform = osVersion.Platform;
                versionInfo.ServicePack = osVersion.ServicePack;

                // تحديد اسم النظام
                versionInfo.Name = GetWindowsName(osVersion.Version);

                // الحصول على معلومات إضافية من Registry
                GetAdditionalVersionInfo(versionInfo);

                // فحص البنية (32/64 بت)
                versionInfo.Is64Bit = Environment.Is64BitOperatingSystem;
                versionInfo.ProcessorArchitecture = Environment.Is64BitProcess ? "x64" : "x86";

                // الحصول على معلومات الذاكرة
                GetMemoryInfo(versionInfo);

                _logger?.LogInformation($"تم اكتشاف النظام: {versionInfo.Name} {versionInfo.Version} ({versionInfo.ProcessorArchitecture})");

                return versionInfo;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على معلومات إصدار Windows");
                return versionInfo;
            }
        }

        /// <summary>
        /// تحديد اسم Windows من رقم الإصدار
        /// </summary>
        /// <param name="version">رقم الإصدار</param>
        /// <returns>اسم النظام</returns>
        private string GetWindowsName(Version version)
        {
            switch (version.Major)
            {
                case 6:
                    switch (version.Minor)
                    {
                        case 1:
                            return "Windows 7";
                        case 2:
                            return "Windows 8";
                        case 3:
                            return "Windows 8.1";
                        default:
                            return "Windows Vista";
                    }
                case 10:
                    return version.Build >= 22000 ? "Windows 11" : "Windows 10";
                case 5:
                    return version.Minor == 1 ? "Windows XP" : "Windows 2000";
                default:
                    return $"Windows {version.Major}.{version.Minor}";
            }
        }

        /// <summary>
        /// الحصول على معلومات إضافية من Registry
        /// </summary>
        /// <param name="versionInfo">معلومات الإصدار</param>
        private void GetAdditionalVersionInfo(WindowsVersionInfo versionInfo)
        {
            try
            {
                using var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion");
                if (key != null)
                {
                    versionInfo.ProductName = key.GetValue("ProductName")?.ToString();
                    versionInfo.BuildNumber = key.GetValue("CurrentBuild")?.ToString();
                    versionInfo.ReleaseId = key.GetValue("ReleaseId")?.ToString();
                    versionInfo.Edition = key.GetValue("EditionID")?.ToString();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطأ في الحصول على معلومات إضافية من Registry");
            }
        }

        /// <summary>
        /// الحصول على معلومات الذاكرة
        /// </summary>
        /// <param name="versionInfo">معلومات الإصدار</param>
        private void GetMemoryInfo(WindowsVersionInfo versionInfo)
        {
            try
            {
                var memoryStatus = new MEMORYSTATUSEX();
                memoryStatus.dwLength = (uint)Marshal.SizeOf(memoryStatus);

                if (GlobalMemoryStatusEx(ref memoryStatus))
                {
                    versionInfo.TotalPhysicalMemory = (long)memoryStatus.ullTotalPhys;
                    versionInfo.AvailablePhysicalMemory = (long)memoryStatus.ullAvailPhys;
                    versionInfo.MemoryUsagePercent = (int)memoryStatus.dwMemoryLoad;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطأ في الحصول على معلومات الذاكرة");
            }
        }

        #endregion

        #region فحص التوافق

        /// <summary>
        /// فحص التوافق العام
        /// </summary>
        private void CheckCompatibility()
        {
            Issues.Clear();

            // فحص إصدار Windows
            CheckWindowsVersion();

            // فحص .NET Framework
            CheckDotNetFramework();

            // فحص الذاكرة
            CheckMemoryRequirements();

            // فحص مساحة القرص
            CheckDiskSpace();

            // فحص الصلاحيات
            CheckPermissions();

            // فحص المكونات المطلوبة
            CheckRequiredComponents();

            // تحديد حالة التوافق العامة
            IsCompatible = !Issues.Exists(i => i.Severity == IssueSeverity.Critical);
        }

        /// <summary>
        /// فحص إصدار Windows
        /// </summary>
        private void CheckWindowsVersion()
        {
            try
            {
                var minVersion = new Version(6, 1); // Windows 7

                if (WindowsVersion.Version < minVersion)
                {
                    Issues.Add(new CompatibilityIssue
                    {
                        Type = IssueType.OperatingSystem,
                        Severity = IssueSeverity.Critical,
                        Title = "إصدار Windows غير مدعوم",
                        Description = $"النظام يتطلب Windows 7 أو أحدث. الإصدار الحالي: {WindowsVersion.Name}",
                        Solution = "يرجى ترقية نظام التشغيل إلى Windows 7 أو أحدث"
                    });
                }
                else if (WindowsVersion.Version.Major == 6 && WindowsVersion.Version.Minor == 1)
                {
                    // Windows 7 - فحص Service Pack
                    if (string.IsNullOrEmpty(WindowsVersion.ServicePack) ||
                        !WindowsVersion.ServicePack.Contains("Service Pack 1"))
                    {
                        Issues.Add(new CompatibilityIssue
                        {
                            Type = IssueType.OperatingSystem,
                            Severity = IssueSeverity.Warning,
                            Title = "Service Pack مفقود",
                            Description = "يُنصح بتثبيت Windows 7 Service Pack 1 لضمان الأداء الأمثل",
                            Solution = "قم بتحديث Windows وتثبيت Service Pack 1"
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فحص إصدار Windows");
            }
        }

        /// <summary>
        /// فحص .NET Framework
        /// </summary>
        private void CheckDotNetFramework()
        {
            try
            {
                var currentVersion = Environment.Version;
                var requiredVersion = new Version(4, 7, 2); // .NET Framework 4.7.2

                // فحص إصدار .NET Framework المثبت
                var installedVersions = GetInstalledDotNetVersions();

                bool hasRequiredVersion = installedVersions.Any(v => v >= requiredVersion);

                if (!hasRequiredVersion)
                {
                    Issues.Add(new CompatibilityIssue
                    {
                        Type = IssueType.Framework,
                        Severity = IssueSeverity.Critical,
                        Title = ".NET Framework غير متوفر",
                        Description = $"النظام يتطلب .NET Framework 4.7.2 أو أحدث",
                        Solution = "قم بتحميل وتثبيت .NET Framework 4.7.2 من موقع Microsoft"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فحص .NET Framework");
            }
        }

        /// <summary>
        /// فحص متطلبات الذاكرة
        /// </summary>
        private void CheckMemoryRequirements()
        {
            try
            {
                const long minMemoryGB = 2; // 2 GB كحد أدنى
                const long recommendedMemoryGB = 4; // 4 GB موصى به

                var totalMemoryGB = WindowsVersion.TotalPhysicalMemory / (1024 * 1024 * 1024);

                if (totalMemoryGB < minMemoryGB)
                {
                    Issues.Add(new CompatibilityIssue
                    {
                        Type = IssueType.Memory,
                        Severity = IssueSeverity.Critical,
                        Title = "ذاكرة غير كافية",
                        Description = $"النظام يتطلب {minMemoryGB} GB على الأقل. المتوفر: {totalMemoryGB} GB",
                        Solution = "قم بزيادة ذاكرة النظام"
                    });
                }
                else if (totalMemoryGB < recommendedMemoryGB)
                {
                    Issues.Add(new CompatibilityIssue
                    {
                        Type = IssueType.Memory,
                        Severity = IssueSeverity.Warning,
                        Title = "ذاكرة أقل من الموصى به",
                        Description = $"للأداء الأمثل، يُنصح بـ {recommendedMemoryGB} GB. المتوفر: {totalMemoryGB} GB",
                        Solution = "فكر في زيادة ذاكرة النظام لتحسين الأداء"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فحص متطلبات الذاكرة");
            }
        }

        /// <summary>
        /// فحص مساحة القرص
        /// </summary>
        private void CheckDiskSpace()
        {
            try
            {
                var systemDrive = Path.GetPathRoot(Environment.SystemDirectory);
                var driveInfo = new DriveInfo(systemDrive);

                const long minSpaceGB = 1; // 1 GB كحد أدنى
                const long recommendedSpaceGB = 5; // 5 GB موصى به

                var availableSpaceGB = driveInfo.AvailableFreeSpace / (1024 * 1024 * 1024);

                if (availableSpaceGB < minSpaceGB)
                {
                    Issues.Add(new CompatibilityIssue
                    {
                        Type = IssueType.Storage,
                        Severity = IssueSeverity.Critical,
                        Title = "مساحة قرص غير كافية",
                        Description = $"النظام يتطلب {minSpaceGB} GB مساحة فارغة على الأقل. المتوفر: {availableSpaceGB} GB",
                        Solution = "قم بتحرير مساحة على القرص الصلب"
                    });
                }
                else if (availableSpaceGB < recommendedSpaceGB)
                {
                    Issues.Add(new CompatibilityIssue
                    {
                        Type = IssueType.Storage,
                        Severity = IssueSeverity.Warning,
                        Title = "مساحة قرص أقل من الموصى به",
                        Description = $"للأداء الأمثل، يُنصح بـ {recommendedSpaceGB} GB مساحة فارغة. المتوفر: {availableSpaceGB} GB",
                        Solution = "فكر في تحرير مساحة إضافية على القرص"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فحص مساحة القرص");
            }
        }

        /// <summary>
        /// فحص الصلاحيات
        /// </summary>
        private void CheckPermissions()
        {
            try
            {
                var identity = WindowsIdentity.GetCurrent();
                var principal = new WindowsPrincipal(identity);

                // فحص صلاحيات المدير
                bool isAdmin = principal.IsInRole(WindowsBuiltInRole.Administrator);

                if (!isAdmin)
                {
                    Issues.Add(new CompatibilityIssue
                    {
                        Type = IssueType.Permissions,
                        Severity = IssueSeverity.Warning,
                        Title = "صلاحيات محدودة",
                        Description = "بعض الميزات قد تتطلب صلاحيات المدير",
                        Solution = "قم بتشغيل البرنامج كمدير عند الحاجة"
                    });
                }

                // فحص صلاحيات الكتابة في مجلد البرنامج
                var appPath = Application.StartupPath;
                if (!HasWritePermission(appPath))
                {
                    Issues.Add(new CompatibilityIssue
                    {
                        Type = IssueType.Permissions,
                        Severity = IssueSeverity.Warning,
                        Title = "صلاحيات كتابة محدودة",
                        Description = "لا توجد صلاحيات كتابة في مجلد البرنامج",
                        Solution = "تأكد من وجود صلاحيات كتابة في مجلد البرنامج"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فحص الصلاحيات");
            }
        }

        /// <summary>
        /// فحص المكونات المطلوبة
        /// </summary>
        private void CheckRequiredComponents()
        {
            try
            {
                // فحص SQL Server Compact أو LocalDB
                CheckSQLServerComponents();

                // فحص Visual C++ Redistributables
                CheckVCRedistributables();

                // فحص Windows Installer
                CheckWindowsInstaller();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فحص المكونات المطلوبة");
            }
        }

        #endregion

        #region دوال مساعدة

        /// <summary>
        /// الحصول على إصدارات .NET Framework المثبتة
        /// </summary>
        /// <returns>قائمة الإصدارات</returns>
        private List<Version> GetInstalledDotNetVersions()
        {
            var versions = new List<Version>();

            try
            {
                using var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\NET Framework Setup\NDP");
                if (key != null)
                {
                    foreach (string subKeyName in key.GetSubKeyNames())
                    {
                        if (subKeyName.StartsWith("v"))
                        {
                            using var subKey = key.OpenSubKey(subKeyName);
                            var version = subKey?.GetValue("Version")?.ToString();
                            if (!string.IsNullOrEmpty(version) && Version.TryParse(version, out var parsedVersion))
                            {
                                versions.Add(parsedVersion);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطأ في الحصول على إصدارات .NET Framework");
            }

            return versions;
        }

        /// <summary>
        /// فحص وجود صلاحيات الكتابة
        /// </summary>
        /// <param name="path">المسار</param>
        /// <returns>true إذا كانت الصلاحيات متوفرة</returns>
        private bool HasWritePermission(string path)
        {
            try
            {
                var testFile = Path.Combine(path, "test_write_permission.tmp");
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// فحص مكونات SQL Server
        /// </summary>
        private void CheckSQLServerComponents()
        {
            // فحص SQL Server LocalDB أو Compact
            // يمكن تحسين هذا حسب متطلبات النظام
        }

        /// <summary>
        /// فحص Visual C++ Redistributables
        /// </summary>
        private void CheckVCRedistributables()
        {
            // فحص وجود Visual C++ Redistributables المطلوبة
            // يمكن تحسين هذا حسب متطلبات النظام
        }

        /// <summary>
        /// فحص Windows Installer
        /// </summary>
        private void CheckWindowsInstaller()
        {
            // فحص إصدار Windows Installer
            // يمكن تحسين هذا حسب متطلبات النظام
        }

        #endregion

        #region Windows API

        [StructLayout(LayoutKind.Sequential)]
        private struct MEMORYSTATUSEX
        {
            public uint dwLength;
            public uint dwMemoryLoad;
            public ulong ullTotalPhys;
            public ulong ullAvailPhys;
            public ulong ullTotalPageFile;
            public ulong ullAvailPageFile;
            public ulong ullTotalVirtual;
            public ulong ullAvailVirtual;
            public ulong ullAvailExtendedVirtual;
        }

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool GlobalMemoryStatusEx(ref MEMORYSTATUSEX lpBuffer);

        #endregion

        #region دوال التوافق العامة

        /// <summary>
        /// تطبيق إعدادات التوافق
        /// </summary>
        /// <returns>true إذا تم التطبيق بنجاح</returns>
        public bool ApplyCompatibilitySettings()
        {
            try
            {
                _logger?.LogInformation("تطبيق إعدادات التوافق");

                // تعيين إعدادات DPI للتوافق مع Windows 7
                SetDPIAwareness();

                // تعيين إعدادات الخطوط
                SetFontSettings();

                // تعيين إعدادات الألوان
                SetColorSettings();

                // تعيين إعدادات الأداء
                SetPerformanceSettings();

                _logger?.LogInformation("تم تطبيق إعدادات التوافق بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تطبيق إعدادات التوافق");
                return false;
            }
        }

        /// <summary>
        /// تعيين وعي DPI
        /// </summary>
        private void SetDPIAwareness()
        {
            try
            {
                if (WindowsVersion.Version.Major >= 6)
                {
                    // تعيين وعي DPI للتوافق مع الشاشات عالية الدقة
                    Application.SetCompatibleTextRenderingDefault(false);

                    if (WindowsVersion.Version.Major > 6 ||
                        (WindowsVersion.Version.Major == 6 && WindowsVersion.Version.Minor >= 3))
                    {
                        // Windows 8.1 وما فوق
                        SetProcessDPIAware();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطأ في تعيين وعي DPI");
            }
        }

        /// <summary>
        /// تعيين إعدادات الخطوط
        /// </summary>
        private void SetFontSettings()
        {
            try
            {
                // تعيين خط افتراضي متوافق مع جميع إصدارات Windows
                var defaultFont = new Font("Tahoma", 8.25f, FontStyle.Regular);

                // يمكن تطبيق الخط على النماذج حسب الحاجة
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطأ في تعيين إعدادات الخطوط");
            }
        }

        /// <summary>
        /// تعيين إعدادات الألوان
        /// </summary>
        private void SetColorSettings()
        {
            try
            {
                // استخدام ألوان النظام للتوافق
                var systemColors = new Dictionary<string, Color>
                {
                    { "Window", SystemColors.Window },
                    { "WindowText", SystemColors.WindowText },
                    { "Control", SystemColors.Control },
                    { "ControlText", SystemColors.ControlText },
                    { "Highlight", SystemColors.Highlight },
                    { "HighlightText", SystemColors.HighlightText }
                };
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطأ في تعيين إعدادات الألوان");
            }
        }

        /// <summary>
        /// تعيين إعدادات الأداء
        /// </summary>
        private void SetPerformanceSettings()
        {
            try
            {
                // تحسين الأداء للأنظمة القديمة
                if (WindowsVersion.Version.Major == 6 && WindowsVersion.Version.Minor == 1)
                {
                    // Windows 7 - تطبيق تحسينات خاصة
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطأ في تعيين إعدادات الأداء");
            }
        }

        /// <summary>
        /// إنشاء تقرير التوافق
        /// </summary>
        /// <returns>تقرير التوافق</returns>
        public CompatibilityReport GenerateCompatibilityReport()
        {
            var report = new CompatibilityReport
            {
                GeneratedDate = DateTime.Now,
                WindowsVersion = WindowsVersion,
                IsCompatible = IsCompatible,
                Issues = new List<CompatibilityIssue>(Issues)
            };

            // إضافة معلومات إضافية
            report.SystemInfo = new SystemInfo
            {
                MachineName = Environment.MachineName,
                UserName = Environment.UserName,
                ProcessorCount = Environment.ProcessorCount,
                WorkingSet = Environment.WorkingSet,
                SystemDirectory = Environment.SystemDirectory,
                CurrentDirectory = Environment.CurrentDirectory
            };

            return report;
        }

        #endregion

        #region Windows API للتوافق

        [DllImport("user32.dll")]
        private static extern bool SetProcessDPIAware();

        [DllImport("shcore.dll")]
        private static extern int SetProcessDpiAwareness(int awareness);

        #endregion
    }

    #region النماذج والتعدادات

    /// <summary>
    /// معلومات إصدار Windows
    /// </summary>
    public class WindowsVersionInfo
    {
        /// <summary>
        /// رقم الإصدار
        /// </summary>
        public Version Version { get; set; }

        /// <summary>
        /// اسم النظام
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// اسم المنتج
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// رقم البناء
        /// </summary>
        public string BuildNumber { get; set; }

        /// <summary>
        /// معرف الإصدار
        /// </summary>
        public string ReleaseId { get; set; }

        /// <summary>
        /// الإصدار
        /// </summary>
        public string Edition { get; set; }

        /// <summary>
        /// حزمة الخدمة
        /// </summary>
        public string ServicePack { get; set; }

        /// <summary>
        /// المنصة
        /// </summary>
        public PlatformID Platform { get; set; }

        /// <summary>
        /// هل النظام 64 بت
        /// </summary>
        public bool Is64Bit { get; set; }

        /// <summary>
        /// بنية المعالج
        /// </summary>
        public string ProcessorArchitecture { get; set; }

        /// <summary>
        /// إجمالي الذاكرة الفيزيائية
        /// </summary>
        public long TotalPhysicalMemory { get; set; }

        /// <summary>
        /// الذاكرة الفيزيائية المتاحة
        /// </summary>
        public long AvailablePhysicalMemory { get; set; }

        /// <summary>
        /// نسبة استخدام الذاكرة
        /// </summary>
        public int MemoryUsagePercent { get; set; }
    }

    /// <summary>
    /// مشكلة التوافق
    /// </summary>
    public class CompatibilityIssue
    {
        /// <summary>
        /// نوع المشكلة
        /// </summary>
        public IssueType Type { get; set; }

        /// <summary>
        /// خطورة المشكلة
        /// </summary>
        public IssueSeverity Severity { get; set; }

        /// <summary>
        /// العنوان
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// الوصف
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// الحل المقترح
        /// </summary>
        public string Solution { get; set; }

        /// <summary>
        /// تاريخ الاكتشاف
        /// </summary>
        public DateTime DetectedDate { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// تقرير التوافق
    /// </summary>
    public class CompatibilityReport
    {
        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime GeneratedDate { get; set; }

        /// <summary>
        /// معلومات إصدار Windows
        /// </summary>
        public WindowsVersionInfo WindowsVersion { get; set; }

        /// <summary>
        /// هل النظام متوافق
        /// </summary>
        public bool IsCompatible { get; set; }

        /// <summary>
        /// قائمة المشاكل
        /// </summary>
        public List<CompatibilityIssue> Issues { get; set; }

        /// <summary>
        /// معلومات النظام
        /// </summary>
        public SystemInfo SystemInfo { get; set; }
    }

    /// <summary>
    /// معلومات النظام
    /// </summary>
    public class SystemInfo
    {
        /// <summary>
        /// اسم الجهاز
        /// </summary>
        public string MachineName { get; set; }

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// عدد المعالجات
        /// </summary>
        public int ProcessorCount { get; set; }

        /// <summary>
        /// مجموعة العمل
        /// </summary>
        public long WorkingSet { get; set; }

        /// <summary>
        /// مجلد النظام
        /// </summary>
        public string SystemDirectory { get; set; }

        /// <summary>
        /// المجلد الحالي
        /// </summary>
        public string CurrentDirectory { get; set; }
    }

    /// <summary>
    /// نوع المشكلة
    /// </summary>
    public enum IssueType
    {
        OperatingSystem,
        Framework,
        Memory,
        Storage,
        Permissions,
        Components,
        Performance,
        Security
    }

    /// <summary>
    /// خطورة المشكلة
    /// </summary>
    public enum IssueSeverity
    {
        Info,
        Warning,
        Critical
    }

    #endregion
}