using System;
using System.Drawing;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// نظام التخطيط المتجاوب المتقدم لتطبيق أريدو الكاشير
    /// يدعم جميع أحجام الشاشات من 1366x768 إلى 4K
    /// </summary>
    public static class ResponsiveLayoutSystem
    {
        #region أحجام الشاشات المدعومة

        /// <summary>
        /// أحجام الشاشات المختلفة
        /// </summary>
        public enum ScreenSize
        {
            Small,      // 1366x768 - 1440x900
            Medium,     // 1600x900 - 1920x1080
            Large,      // 2560x1440
            ExtraLarge  // 3840x2160 (4K)
        }

        /// <summary>
        /// نقاط التوقف للشاشات
        /// </summary>
        public static class Breakpoints
        {
            public const int Small = 1366;
            public const int Medium = 1600;
            public const int Large = 2560;
            public const int ExtraLarge = 3840;
        }

        #endregion

        #region كشف حجم الشاشة

        /// <summary>
        /// الحصول على حجم الشاشة الحالي
        /// </summary>
        public static ScreenSize GetCurrentScreenSize()
        {
            var screenWidth = Screen.PrimaryScreen.Bounds.Width;
            
            if (screenWidth >= Breakpoints.ExtraLarge)
                return ScreenSize.ExtraLarge;
            else if (screenWidth >= Breakpoints.Large)
                return ScreenSize.Large;
            else if (screenWidth >= Breakpoints.Medium)
                return ScreenSize.Medium;
            else
                return ScreenSize.Small;
        }

        /// <summary>
        /// الحصول على معامل التكبير بناءً على حجم الشاشة
        /// </summary>
        public static float GetScaleFactor()
        {
            var screenSize = GetCurrentScreenSize();
            
            return screenSize switch
            {
                ScreenSize.Small => 1.0f,
                ScreenSize.Medium => 1.25f,
                ScreenSize.Large => 1.5f,
                ScreenSize.ExtraLarge => 2.0f,
                _ => 1.0f
            };
        }

        /// <summary>
        /// الحصول على معامل DPI
        /// </summary>
        public static float GetDpiScaleFactor()
        {
            try
            {
                using (var g = Graphics.FromHwnd(IntPtr.Zero))
                {
                    return Math.Max(1.0f, g.DpiX / 96f); // 96 DPI هو المعيار
                }
            }
            catch
            {
                return 1.0f; // قيمة افتراضية آمنة
            }
        }

        #endregion

        #region نظام الشبكة المتجاوبة

        /// <summary>
        /// نظام الشبكة المتجاوبة (12 عمود)
        /// </summary>
        public static class Grid
        {
            public const int TotalColumns = 12;

            /// <summary>
            /// حساب عرض العمود بناءً على عدد الأعمدة
            /// </summary>
            public static int GetColumnWidth(int containerWidth, int columns, int gutter = 16)
            {
                var totalGutters = (TotalColumns - 1) * gutter;
                var availableWidth = containerWidth - totalGutters;
                var columnWidth = availableWidth / TotalColumns;
                return (columnWidth * columns) + ((columns - 1) * gutter);
            }

            /// <summary>
            /// حساب موقع العمود
            /// </summary>
            public static int GetColumnOffset(int containerWidth, int startColumn, int gutter = 16)
            {
                var totalGutters = (TotalColumns - 1) * gutter;
                var availableWidth = containerWidth - totalGutters;
                var columnWidth = availableWidth / TotalColumns;
                return (columnWidth * startColumn) + (startColumn * gutter);
            }
        }

        #endregion

        #region المسافات المتجاوبة

        /// <summary>
        /// نظام المسافات المتجاوبة
        /// </summary>
        public static class ResponsiveSpacing
        {
            /// <summary>
            /// الحصول على المسافة الأساسية
            /// </summary>
            public static int GetBaseSpacing()
            {
                var scaleFactor = GetScaleFactor();
                return (int)(8 * scaleFactor);
            }

            /// <summary>
            /// الحصول على مسافة صغيرة
            /// </summary>
            public static int GetSmallSpacing()
            {
                return GetBaseSpacing();
            }

            /// <summary>
            /// الحصول على مسافة متوسطة
            /// </summary>
            public static int GetMediumSpacing()
            {
                return GetBaseSpacing() * 2;
            }

            /// <summary>
            /// الحصول على مسافة كبيرة
            /// </summary>
            public static int GetLargeSpacing()
            {
                return GetBaseSpacing() * 3;
            }

            /// <summary>
            /// الحصول على مسافة كبيرة جداً
            /// </summary>
            public static int GetExtraLargeSpacing()
            {
                return GetBaseSpacing() * 4;
            }
        }

        #endregion

        #region الأبعاد المتجاوبة

        /// <summary>
        /// نظام الأبعاد المتجاوبة
        /// </summary>
        public static class ResponsiveDimensions
        {
            /// <summary>
            /// الحصول على ارتفاع الشريط العلوي
            /// </summary>
            public static int GetTopBarHeight()
            {
                var scaleFactor = GetScaleFactor();
                return (int)(60 * scaleFactor);
            }

            /// <summary>
            /// الحصول على عرض الشريط الجانبي
            /// </summary>
            public static int GetSidebarWidth()
            {
                var scaleFactor = GetScaleFactor();
                return (int)(240 * scaleFactor);
            }

            /// <summary>
            /// الحصول على عرض الشريط الجانبي المطوي
            /// </summary>
            public static int GetCollapsedSidebarWidth()
            {
                var scaleFactor = GetScaleFactor();
                return (int)(60 * scaleFactor);
            }

            /// <summary>
            /// الحصول على ارتفاع الزر
            /// </summary>
            public static int GetButtonHeight()
            {
                var scaleFactor = GetScaleFactor();
                return (int)(36 * scaleFactor);
            }

            /// <summary>
            /// الحصول على ارتفاع الزر الكبير
            /// </summary>
            public static int GetLargeButtonHeight()
            {
                var scaleFactor = GetScaleFactor();
                return (int)(48 * scaleFactor);
            }

            /// <summary>
            /// الحصول على ارتفاع حقل الإدخال
            /// </summary>
            public static int GetInputHeight()
            {
                var scaleFactor = GetScaleFactor();
                return (int)(32 * scaleFactor);
            }

            /// <summary>
            /// الحصول على حجم الأيقونة
            /// </summary>
            public static int GetIconSize()
            {
                var scaleFactor = GetScaleFactor();
                return (int)(24 * scaleFactor);
            }

            /// <summary>
            /// الحصول على حجم الأيقونة الكبيرة
            /// </summary>
            public static int GetLargeIconSize()
            {
                var scaleFactor = GetScaleFactor();
                return (int)(32 * scaleFactor);
            }
        }

        #endregion

        #region تطبيق التخطيط المتجاوب

        /// <summary>
        /// تطبيق التخطيط المتجاوب على عنصر تحكم
        /// </summary>
        public static void ApplyResponsiveLayout(Control control)
        {
            if (control == null) return;

            var scaleFactor = GetScaleFactor();
            var dpiScaleFactor = GetDpiScaleFactor();
            var totalScaleFactor = scaleFactor * dpiScaleFactor;

            // تطبيق التكبير على الخط
            if (control.Font != null)
            {
                var newFontSize = control.Font.Size * totalScaleFactor;
                control.Font = new Font(control.Font.FontFamily, newFontSize, control.Font.Style);
            }

            // تطبيق التكبير على الحجم
            if (control.Size != Size.Empty)
            {
                var newWidth = (int)(control.Width * totalScaleFactor);
                var newHeight = (int)(control.Height * totalScaleFactor);
                control.Size = new Size(newWidth, newHeight);
            }

            // تطبيق التكبير على الموقع
            if (control.Location != Point.Empty)
            {
                var newX = (int)(control.Location.X * totalScaleFactor);
                var newY = (int)(control.Location.Y * totalScaleFactor);
                control.Location = new Point(newX, newY);
            }

            // تطبيق التكبير على الحشو
            if (control.Padding != Padding.Empty)
            {
                var newPadding = new Padding(
                    (int)(control.Padding.Left * totalScaleFactor),
                    (int)(control.Padding.Top * totalScaleFactor),
                    (int)(control.Padding.Right * totalScaleFactor),
                    (int)(control.Padding.Bottom * totalScaleFactor)
                );
                control.Padding = newPadding;
            }

            // تطبيق التكبير على الهامش
            if (control.Margin != Padding.Empty)
            {
                var newMargin = new Padding(
                    (int)(control.Margin.Left * totalScaleFactor),
                    (int)(control.Margin.Top * totalScaleFactor),
                    (int)(control.Margin.Right * totalScaleFactor),
                    (int)(control.Margin.Bottom * totalScaleFactor)
                );
                control.Margin = newMargin;
            }

            // تطبيق التخطيط على العناصر الفرعية
            foreach (Control child in control.Controls)
            {
                ApplyResponsiveLayout(child);
            }
        }

        /// <summary>
        /// إنشاء حاوي متجاوب
        /// </summary>
        public static Panel CreateResponsiveContainer(int columns = 12, bool fluid = true)
        {
            var container = new Panel
            {
                Dock = fluid ? DockStyle.Fill : DockStyle.None,
                BackColor = Color.Transparent,
                Padding = new Padding(ResponsiveSpacing.GetMediumSpacing())
            };

            return container;
        }

        /// <summary>
        /// إنشاء صف متجاوب
        /// </summary>
        public static Panel CreateResponsiveRow()
        {
            var row = new Panel
            {
                Dock = DockStyle.Top,
                BackColor = Color.Transparent,
                Height = ResponsiveDimensions.GetButtonHeight() + ResponsiveSpacing.GetMediumSpacing()
            };

            return row;
        }

        /// <summary>
        /// إنشاء عمود متجاوب
        /// </summary>
        public static Panel CreateResponsiveColumn(int columns, int offset = 0)
        {
            var column = new Panel
            {
                BackColor = Color.Transparent,
                Margin = new Padding(ResponsiveSpacing.GetSmallSpacing())
            };

            // سيتم حساب العرض والموقع عند إضافة العمود للصف
            return column;
        }

        #endregion

        #region مساعدات التخطيط

        /// <summary>
        /// تحديث تخطيط الصف والأعمدة
        /// </summary>
        public static void UpdateRowLayout(Panel row)
        {
            if (row == null || row.Controls.Count == 0) return;

            var containerWidth = row.Width - row.Padding.Horizontal;
            var gutter = ResponsiveSpacing.GetSmallSpacing();
            var currentX = row.Padding.Left;

            foreach (Control control in row.Controls)
            {
                if (control.Tag is int columns)
                {
                    var columnWidth = Grid.GetColumnWidth(containerWidth, columns, gutter);
                    control.Width = columnWidth;
                    control.Location = new Point(currentX, row.Padding.Top);
                    control.Height = row.Height - row.Padding.Vertical;
                    currentX += columnWidth + gutter;
                }
            }
        }

        /// <summary>
        /// تحديث التخطيط عند تغيير حجم النافذة
        /// </summary>
        public static void HandleResize(Form form)
        {
            if (form == null) return;

            // إعادة حساب معامل التكبير
            var newScaleFactor = GetScaleFactor();
            
            // تطبيق التخطيط الجديد
            ApplyResponsiveLayout(form);

            // تحديث تخطيط الصفوف
            UpdateAllRowLayouts(form);
        }

        /// <summary>
        /// تحديث جميع تخطيطات الصفوف
        /// </summary>
        private static void UpdateAllRowLayouts(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                if (control is Panel panel && panel.Tag?.ToString() == "ResponsiveRow")
                {
                    UpdateRowLayout(panel);
                }
                
                UpdateAllRowLayouts(control);
            }
        }

        #endregion
    }
}
