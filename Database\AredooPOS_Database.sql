-- =====================================================
-- أريدوو - نظام نقاط البيع
-- ملف إنشاء قاعدة البيانات الشاملة
-- الإصدار: 1.0.0
-- التاريخ: 2024-12-19
-- =====================================================

-- إنشاء قاعدة البيانات الرئيسية
-- =====================================================
PRAGMA foreign_keys = ON;
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = memory;

-- جدول إعدادات النظام
-- =====================================================
CREATE TABLE IF NOT EXISTS SystemSettings (
    SettingID INTEGER PRIMARY KEY AUTOINCREMENT,
    SettingKey TEXT UNIQUE NOT NULL,           -- مفتاح الإعداد
    SettingValue TEXT NOT NULL,                -- قيمة الإعداد
    SettingDescription TEXT,                   -- وصف الإعداد
    SettingCategory TEXT DEFAULT 'عام',        -- فئة الإعداد
    IsActive INTEGER DEFAULT 1,                -- حالة التفعيل
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate TEXT DEFAULT CURRENT_TIMESTAMP
);

-- جدول المستخدمين والصلاحيات
-- =====================================================
CREATE TABLE IF NOT EXISTS Users (
    UserID INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT UNIQUE NOT NULL,             -- اسم المستخدم
    PasswordHash TEXT NOT NULL,                -- كلمة المرور المشفرة
    FullName TEXT NOT NULL,                    -- الاسم الكامل
    Email TEXT,                                -- البريد الإلكتروني
    Phone TEXT,                                -- رقم الهاتف
    UserRole TEXT DEFAULT 'كاشير',             -- دور المستخدم (مدير، كاشير، مشرف)
    Permissions TEXT,                          -- الصلاحيات (JSON)
    IsActive INTEGER DEFAULT 1,                -- حالة التفعيل
    LastLoginDate TEXT,                        -- تاريخ آخر دخول
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate TEXT DEFAULT CURRENT_TIMESTAMP
);

-- جدول فئات المنتجات
-- =====================================================
CREATE TABLE IF NOT EXISTS Categories (
    CategoryID INTEGER PRIMARY KEY AUTOINCREMENT,
    CategoryCode TEXT UNIQUE NOT NULL,         -- كود الفئة
    CategoryName TEXT NOT NULL,                -- اسم الفئة
    CategoryNameEn TEXT,                       -- الاسم بالإنجليزية
    Description TEXT,                          -- وصف الفئة
    ParentCategoryID INTEGER,                  -- الفئة الأب (للفئات الفرعية)
    SortOrder INTEGER DEFAULT 0,               -- ترتيب العرض
    IsActive INTEGER DEFAULT 1,                -- حالة التفعيل
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    
    -- العلاقات الخارجية
    FOREIGN KEY (ParentCategoryID) REFERENCES Categories(CategoryID)
);

-- جدول الوحدات
-- =====================================================
CREATE TABLE IF NOT EXISTS Units (
    UnitID INTEGER PRIMARY KEY AUTOINCREMENT,
    UnitCode TEXT UNIQUE NOT NULL,             -- كود الوحدة
    UnitName TEXT NOT NULL,                    -- اسم الوحدة (قطعة، كيلو، لتر)
    UnitNameEn TEXT,                           -- الاسم بالإنجليزية
    BaseUnit INTEGER DEFAULT 1,                -- الوحدة الأساسية
    ConversionFactor REAL DEFAULT 1.0,         -- معامل التحويل
    IsActive INTEGER DEFAULT 1,                -- حالة التفعيل
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP
);

-- جدول الموردين
-- =====================================================
CREATE TABLE IF NOT EXISTS Suppliers (
    SupplierID INTEGER PRIMARY KEY AUTOINCREMENT,
    SupplierCode TEXT UNIQUE NOT NULL,         -- كود المورد
    SupplierName TEXT NOT NULL,                -- اسم المورد
    ContactPerson TEXT,                        -- الشخص المسؤول
    Phone TEXT,                                -- رقم الهاتف
    Email TEXT,                                -- البريد الإلكتروني
    Address TEXT,                              -- العنوان
    City TEXT,                                 -- المدينة
    Country TEXT DEFAULT 'السعودية',           -- البلد
    TaxNumber TEXT,                            -- الرقم الضريبي
    PaymentTerms TEXT,                         -- شروط الدفع
    CreditLimit REAL DEFAULT 0,                -- حد الائتمان
    CurrentBalance REAL DEFAULT 0,             -- الرصيد الحالي
    IsActive INTEGER DEFAULT 1,                -- حالة التفعيل
    Notes TEXT,                                -- ملاحظات
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate TEXT DEFAULT CURRENT_TIMESTAMP
);

-- جدول العملاء
-- =====================================================
CREATE TABLE IF NOT EXISTS Customers (
    CustomerID INTEGER PRIMARY KEY AUTOINCREMENT,
    CustomerCode TEXT UNIQUE NOT NULL,         -- كود العميل
    CustomerName TEXT NOT NULL,                -- اسم العميل
    CustomerType TEXT DEFAULT 'فرد',           -- نوع العميل (فرد، شركة)
    Phone TEXT,                                -- رقم الهاتف
    Email TEXT,                                -- البريد الإلكتروني
    Address TEXT,                              -- العنوان
    City TEXT,                                 -- المدينة
    District TEXT,                             -- الحي
    PostalCode TEXT,                           -- الرمز البريدي
    TaxNumber TEXT,                            -- الرقم الضريبي
    CreditLimit REAL DEFAULT 0,                -- حد الائتمان
    CurrentBalance REAL DEFAULT 0,             -- الرصيد الحالي
    DiscountPercent REAL DEFAULT 0,            -- نسبة الخصم الافتراضية
    PriceLevel INTEGER DEFAULT 1,              -- مستوى السعر
    PaymentTerms TEXT DEFAULT 'نقدي',          -- شروط الدفع
    IsActive INTEGER DEFAULT 1,                -- حالة التفعيل
    Notes TEXT,                                -- ملاحظات
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate TEXT DEFAULT CURRENT_TIMESTAMP
);

-- جدول المنتجات
-- =====================================================
CREATE TABLE IF NOT EXISTS Products (
    ProductID INTEGER PRIMARY KEY AUTOINCREMENT,
    ProductCode TEXT UNIQUE NOT NULL,          -- كود المنتج
    ProductName TEXT NOT NULL,                 -- اسم المنتج
    ProductNameEn TEXT,                        -- الاسم بالإنجليزية
    Description TEXT,                          -- وصف المنتج
    CategoryID INTEGER,                        -- فئة المنتج
    SupplierID INTEGER,                        -- المورد الرئيسي
    UnitID INTEGER,                            -- وحدة القياس
    
    -- معلومات التسعير
    CostPrice REAL DEFAULT 0,                  -- سعر التكلفة
    SalePrice REAL NOT NULL,                   -- سعر البيع
    WholesalePrice REAL DEFAULT 0,             -- سعر الجملة
    MinSalePrice REAL DEFAULT 0,               -- أقل سعر بيع
    
    -- معلومات المخزون
    StockQuantity REAL DEFAULT 0,              -- الكمية الحالية
    MinStockLevel REAL DEFAULT 0,              -- الحد الأدنى للمخزون
    MaxStockLevel REAL DEFAULT 0,              -- الحد الأقصى للمخزون
    ReorderLevel REAL DEFAULT 0,               -- نقطة إعادة الطلب
    
    -- معلومات إضافية
    Barcode TEXT,                              -- الباركود
    SKU TEXT,                                  -- رقم المنتج
    Brand TEXT,                                -- العلامة التجارية
    Model TEXT,                                -- الموديل
    Color TEXT,                                -- اللون
    Size TEXT,                                 -- الحجم
    Weight REAL,                               -- الوزن
    
    -- الضرائب والخصومات
    TaxRate REAL DEFAULT 15,                   -- معدل الضريبة
    IsDiscountAllowed INTEGER DEFAULT 1,       -- السماح بالخصم
    MaxDiscountPercent REAL DEFAULT 0,         -- أقصى نسبة خصم
    
    -- إعدادات أخرى
    IsActive INTEGER DEFAULT 1,                -- حالة التفعيل
    IsService INTEGER DEFAULT 0,               -- هل هو خدمة
    HasExpiry INTEGER DEFAULT 0,               -- له تاريخ انتهاء
    TrackSerial INTEGER DEFAULT 0,             -- تتبع الأرقام التسلسلية
    
    -- تواريخ
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    
    -- العلاقات الخارجية
    FOREIGN KEY (CategoryID) REFERENCES Categories(CategoryID),
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
);

-- جدول الفواتير الرئيسي
-- =====================================================
CREATE TABLE IF NOT EXISTS Invoices (
    InvoiceID INTEGER PRIMARY KEY AUTOINCREMENT,
    InvoiceNumber TEXT UNIQUE NOT NULL,        -- رقم الفاتورة
    InvoiceType TEXT DEFAULT 'بيع',            -- نوع الفاتورة (بيع، إرجاع، عرض سعر)
    InvoiceDate TEXT NOT NULL,                 -- تاريخ الفاتورة
    DueDate TEXT,                              -- تاريخ الاستحقاق
    
    -- معلومات العميل
    CustomerID INTEGER,                        -- رقم العميل
    CustomerName TEXT,                         -- اسم العميل
    CustomerPhone TEXT,                        -- هاتف العميل
    CustomerAddress TEXT,                      -- عنوان العميل
    
    -- المبالغ المالية
    SubTotal REAL NOT NULL DEFAULT 0,          -- المجموع الفرعي
    DiscountAmount REAL DEFAULT 0,             -- مبلغ الخصم
    DiscountPercent REAL DEFAULT 0,            -- نسبة الخصم
    TaxAmount REAL DEFAULT 0,                  -- مبلغ الضريبة
    TotalAmount REAL NOT NULL DEFAULT 0,       -- المبلغ الإجمالي
    PaidAmount REAL DEFAULT 0,                 -- المبلغ المدفوع
    RemainingAmount REAL DEFAULT 0,            -- المبلغ المتبقي
    
    -- معلومات الدفع
    PaymentType TEXT NOT NULL DEFAULT 'نقد',   -- نوع الدفع (نقد، بطاقة، آجل، مختلط)
    PaymentStatus TEXT DEFAULT 'مدفوع',        -- حالة الدفع
    
    -- حالة الفاتورة
    InvoiceStatus TEXT DEFAULT 'مكتملة',       -- حالة الفاتورة
    IsReturned INTEGER DEFAULT 0,              -- مرتجعة
    ReturnedDate TEXT,                         -- تاريخ الإرجاع
    
    -- معلومات إضافية
    Notes TEXT,                                -- ملاحظات
    InternalNotes TEXT,                        -- ملاحظات داخلية
    
    -- معلومات المستخدم
    CashierID INTEGER,                         -- رقم الكاشير
    CashierName TEXT,                          -- اسم الكاشير
    CreatedBy TEXT,                            -- منشئ الفاتورة
    
    -- تواريخ النظام
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    
    -- العلاقات الخارجية
    FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
    FOREIGN KEY (CashierID) REFERENCES Users(UserID)
);

-- جدول تفاصيل الفواتير
-- =====================================================
CREATE TABLE IF NOT EXISTS InvoiceDetails (
    InvoiceDetailID INTEGER PRIMARY KEY AUTOINCREMENT,
    InvoiceID INTEGER NOT NULL,                -- رقم الفاتورة
    LineNumber INTEGER NOT NULL,               -- رقم السطر

    -- معلومات المنتج
    ProductID INTEGER NOT NULL,                -- رقم المنتج
    ProductCode TEXT,                          -- كود المنتج
    ProductName TEXT NOT NULL,                 -- اسم المنتج
    ProductDescription TEXT,                   -- وصف المنتج

    -- الكميات والأسعار
    Quantity REAL NOT NULL,                    -- الكمية
    UnitPrice REAL NOT NULL,                   -- سعر الوحدة
    CostPrice REAL DEFAULT 0,                  -- سعر التكلفة

    -- الخصومات والضرائب
    DiscountAmount REAL DEFAULT 0,             -- مبلغ الخصم
    DiscountPercent REAL DEFAULT 0,            -- نسبة الخصم
    TaxRate REAL DEFAULT 0,                    -- معدل الضريبة
    TaxAmount REAL DEFAULT 0,                  -- مبلغ الضريبة

    -- الإجماليات
    LineTotal REAL NOT NULL,                   -- إجمالي السطر
    LineCost REAL DEFAULT 0,                   -- تكلفة السطر
    LineProfit REAL DEFAULT 0,                 -- ربح السطر

    -- معلومات إضافية
    Notes TEXT,                                -- ملاحظات السطر
    SerialNumbers TEXT,                        -- الأرقام التسلسلية
    ExpiryDate TEXT,                           -- تاريخ الانتهاء

    -- العلاقات الخارجية
    FOREIGN KEY (InvoiceID) REFERENCES Invoices(InvoiceID) ON DELETE CASCADE,
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- جدول المدفوعات
-- =====================================================
CREATE TABLE IF NOT EXISTS Payments (
    PaymentID INTEGER PRIMARY KEY AUTOINCREMENT,
    InvoiceID INTEGER NOT NULL,                -- رقم الفاتورة
    PaymentDate TEXT DEFAULT CURRENT_TIMESTAMP, -- تاريخ الدفع
    PaymentTime TEXT DEFAULT CURRENT_TIME,     -- وقت الدفع

    -- معلومات الدفع
    PaymentType TEXT NOT NULL,                 -- نوع الدفع (نقد، بطاقة، شيك، تحويل)
    Amount REAL NOT NULL,                      -- المبلغ
    Currency TEXT DEFAULT 'ريال',              -- العملة
    ExchangeRate REAL DEFAULT 1,               -- سعر الصرف

    -- معلومات البطاقة/الشيك
    CardType TEXT,                             -- نوع البطاقة (فيزا، ماستر)
    CardNumber TEXT,                           -- آخر 4 أرقام من البطاقة
    AuthorizationCode TEXT,                    -- رمز التفويض
    CheckNumber TEXT,                          -- رقم الشيك
    BankName TEXT,                             -- اسم البنك

    -- معلومات إضافية
    Reference TEXT,                            -- المرجع
    Notes TEXT,                                -- ملاحظات

    -- معلومات المستخدم
    ProcessedBy TEXT,                          -- معالج بواسطة
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,

    -- العلاقات الخارجية
    FOREIGN KEY (InvoiceID) REFERENCES Invoices(InvoiceID)
);

-- جدول الأقساط
-- =====================================================
CREATE TABLE IF NOT EXISTS Installments (
    InstallmentID INTEGER PRIMARY KEY AUTOINCREMENT,
    InvoiceID INTEGER NOT NULL,                -- رقم الفاتورة
    InstallmentNumber INTEGER NOT NULL,        -- رقم القسط

    -- معلومات القسط
    DueDate TEXT NOT NULL,                     -- تاريخ الاستحقاق
    Amount REAL NOT NULL,                      -- مبلغ القسط
    PaidAmount REAL DEFAULT 0,                 -- المبلغ المدفوع
    RemainingAmount REAL NOT NULL,             -- المبلغ المتبقي

    -- حالة القسط
    Status TEXT DEFAULT 'مستحق',               -- حالة القسط (مستحق، مدفوع، متأخر، ملغي)
    PaidDate TEXT,                             -- تاريخ الدفع
    LateDays INTEGER DEFAULT 0,                -- أيام التأخير
    LateFees REAL DEFAULT 0,                   -- رسوم التأخير

    -- معلومات إضافية
    Notes TEXT,                                -- ملاحظات
    ReminderSent INTEGER DEFAULT 0,            -- تم إرسال تذكير
    ReminderDate TEXT,                         -- تاريخ التذكير

    -- تواريخ النظام
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate TEXT DEFAULT CURRENT_TIMESTAMP,

    -- العلاقات الخارجية
    FOREIGN KEY (InvoiceID) REFERENCES Invoices(InvoiceID)
);

-- جدول إرجاع الفواتير
-- =====================================================
CREATE TABLE IF NOT EXISTS InvoiceReturns (
    ReturnID INTEGER PRIMARY KEY AUTOINCREMENT,
    OriginalInvoiceID INTEGER NOT NULL,        -- الفاتورة الأصلية
    ReturnInvoiceID INTEGER,                   -- فاتورة الإرجاع
    ReturnDate TEXT DEFAULT CURRENT_TIMESTAMP, -- تاريخ الإرجاع

    -- معلومات الإرجاع
    ReturnType TEXT NOT NULL,                  -- نوع الإرجاع (كامل، جزئي)
    ReturnReason TEXT NOT NULL,                -- سبب الإرجاع
    TotalReturnAmount REAL NOT NULL,           -- إجمالي مبلغ الإرجاع
    RefundAmount REAL DEFAULT 0,               -- مبلغ الاسترداد
    RefundMethod TEXT,                         -- طريقة الاسترداد

    -- معلومات الموافقة
    ApprovedBy TEXT,                           -- موافق بواسطة
    ApprovalDate TEXT,                         -- تاريخ الموافقة
    ApprovalNotes TEXT,                        -- ملاحظات الموافقة

    -- معلومات المعالجة
    ProcessedBy TEXT,                          -- معالج بواسطة
    ProcessingNotes TEXT,                      -- ملاحظات المعالجة

    -- تواريخ النظام
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,

    -- العلاقات الخارجية
    FOREIGN KEY (OriginalInvoiceID) REFERENCES Invoices(InvoiceID),
    FOREIGN KEY (ReturnInvoiceID) REFERENCES Invoices(InvoiceID)
);

-- جدول تفاصيل الإرجاع
-- =====================================================
CREATE TABLE IF NOT EXISTS ReturnDetails (
    ReturnDetailID INTEGER PRIMARY KEY AUTOINCREMENT,
    ReturnID INTEGER NOT NULL,                 -- رقم الإرجاع
    OriginalDetailID INTEGER NOT NULL,         -- تفصيل الفاتورة الأصلية

    -- معلومات المنتج
    ProductID INTEGER NOT NULL,                -- رقم المنتج
    ProductName TEXT NOT NULL,                 -- اسم المنتج

    -- الكميات
    OriginalQuantity REAL NOT NULL,            -- الكمية الأصلية
    ReturnedQuantity REAL NOT NULL,            -- الكمية المرتجعة

    -- الأسعار
    UnitPrice REAL NOT NULL,                   -- سعر الوحدة
    LineTotal REAL NOT NULL,                   -- إجمالي السطر

    -- معلومات الإرجاع
    ReturnReason TEXT,                         -- سبب إرجاع هذا المنتج
    Condition TEXT,                            -- حالة المنتج المرتجع
    RestockQuantity REAL DEFAULT 0,            -- الكمية المعادة للمخزون

    -- العلاقات الخارجية
    FOREIGN KEY (ReturnID) REFERENCES InvoiceReturns(ReturnID) ON DELETE CASCADE,
    FOREIGN KEY (OriginalDetailID) REFERENCES InvoiceDetails(InvoiceDetailID),
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- جدول حركات المخزون
-- =====================================================
CREATE TABLE IF NOT EXISTS StockMovements (
    MovementID INTEGER PRIMARY KEY AUTOINCREMENT,
    ProductID INTEGER NOT NULL,                -- رقم المنتج
    MovementType TEXT NOT NULL,                -- نوع الحركة (دخول، خروج، تسوية، تلف)
    MovementDate TEXT DEFAULT CURRENT_TIMESTAMP, -- تاريخ الحركة

    -- الكميات
    QuantityBefore REAL NOT NULL,              -- الكمية قبل الحركة
    MovementQuantity REAL NOT NULL,            -- كمية الحركة
    QuantityAfter REAL NOT NULL,               -- الكمية بعد الحركة

    -- معلومات الحركة
    UnitCost REAL DEFAULT 0,                   -- تكلفة الوحدة
    TotalCost REAL DEFAULT 0,                  -- إجمالي التكلفة
    Reference TEXT,                            -- المرجع (رقم فاتورة، أمر شراء، إلخ)
    ReferenceType TEXT,                        -- نوع المرجع

    -- معلومات إضافية
    Notes TEXT,                                -- ملاحظات
    ProcessedBy TEXT,                          -- معالج بواسطة

    -- العلاقات الخارجية
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- جدول الصندوق والخزينة
-- =====================================================
CREATE TABLE IF NOT EXISTS CashRegister (
    TransactionID INTEGER PRIMARY KEY AUTOINCREMENT,
    TransactionDate TEXT DEFAULT CURRENT_TIMESTAMP, -- تاريخ العملية
    TransactionTime TEXT DEFAULT CURRENT_TIME,      -- وقت العملية

    -- معلومات العملية
    TransactionType TEXT NOT NULL,             -- نوع العملية (دخول، خروج، افتتاح، إغلاق)
    Amount REAL NOT NULL,                      -- المبلغ
    Currency TEXT DEFAULT 'ريال',              -- العملة

    -- معلومات المرجع
    ReferenceType TEXT,                        -- نوع المرجع (فاتورة، مصروف، إيداع)
    ReferenceID INTEGER,                       -- رقم المرجع
    ReferenceNumber TEXT,                      -- رقم المرجع النصي

    -- معلومات إضافية
    Description TEXT,                          -- وصف العملية
    Notes TEXT,                                -- ملاحظات

    -- الأرصدة
    BalanceBefore REAL DEFAULT 0,              -- الرصيد قبل العملية
    BalanceAfter REAL DEFAULT 0,               -- الرصيد بعد العملية

    -- معلومات المستخدم
    CashierID INTEGER,                         -- رقم الكاشير
    CashierName TEXT,                          -- اسم الكاشير
    ShiftID INTEGER,                           -- رقم الوردية

    -- العلاقات الخارجية
    FOREIGN KEY (CashierID) REFERENCES Users(UserID)
);

-- جدول الورديات
-- =====================================================
CREATE TABLE IF NOT EXISTS Shifts (
    ShiftID INTEGER PRIMARY KEY AUTOINCREMENT,
    ShiftDate TEXT NOT NULL,                   -- تاريخ الوردية
    CashierID INTEGER NOT NULL,                -- رقم الكاشير
    CashierName TEXT NOT NULL,                 -- اسم الكاشير

    -- أوقات الوردية
    StartTime TEXT NOT NULL,                   -- وقت البداية
    EndTime TEXT,                              -- وقت النهاية
    Duration INTEGER,                          -- مدة الوردية بالدقائق

    -- الأرصدة
    OpeningBalance REAL DEFAULT 0,             -- رصيد الافتتاح
    ClosingBalance REAL DEFAULT 0,             -- رصيد الإغلاق
    ExpectedClosing REAL DEFAULT 0,            -- الإغلاق المتوقع
    Difference REAL DEFAULT 0,                 -- الفرق

    -- إحصائيات الوردية
    TotalSales REAL DEFAULT 0,                 -- إجمالي المبيعات
    TotalReturns REAL DEFAULT 0,               -- إجمالي المرتجعات
    TotalDiscount REAL DEFAULT 0,              -- إجمالي الخصومات
    TotalTax REAL DEFAULT 0,                   -- إجمالي الضرائب
    TransactionCount INTEGER DEFAULT 0,        -- عدد المعاملات

    -- حالة الوردية
    Status TEXT DEFAULT 'مفتوحة',              -- حالة الوردية (مفتوحة، مغلقة)
    Notes TEXT,                                -- ملاحظات

    -- تواريخ النظام
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
    ClosedDate TEXT,

    -- العلاقات الخارجية
    FOREIGN KEY (CashierID) REFERENCES Users(UserID)
);

-- جدول المصروفات
-- =====================================================
CREATE TABLE IF NOT EXISTS Expenses (
    ExpenseID INTEGER PRIMARY KEY AUTOINCREMENT,
    ExpenseDate TEXT DEFAULT CURRENT_TIMESTAMP, -- تاريخ المصروف
    ExpenseCategory TEXT NOT NULL,             -- فئة المصروف
    Description TEXT NOT NULL,                 -- وصف المصروف
    Amount REAL NOT NULL,                      -- المبلغ
    PaymentMethod TEXT DEFAULT 'نقد',          -- طريقة الدفع
    Reference TEXT,                            -- المرجع
    ApprovedBy TEXT,                           -- موافق بواسطة
    Notes TEXT,                                -- ملاحظات
    CreatedBy TEXT,                            -- منشئ بواسطة
    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- إنشاء الفهارس لتحسين الأداء
-- =====================================================

-- فهارس جدول المنتجات
CREATE INDEX IF NOT EXISTS idx_products_code ON Products(ProductCode);
CREATE INDEX IF NOT EXISTS idx_products_barcode ON Products(Barcode);
CREATE INDEX IF NOT EXISTS idx_products_category ON Products(CategoryID);
CREATE INDEX IF NOT EXISTS idx_products_active ON Products(IsActive);

-- فهارس جدول العملاء
CREATE INDEX IF NOT EXISTS idx_customers_code ON Customers(CustomerCode);
CREATE INDEX IF NOT EXISTS idx_customers_phone ON Customers(Phone);
CREATE INDEX IF NOT EXISTS idx_customers_active ON Customers(IsActive);

-- فهارس جدول الفواتير
CREATE INDEX IF NOT EXISTS idx_invoices_number ON Invoices(InvoiceNumber);
CREATE INDEX IF NOT EXISTS idx_invoices_date ON Invoices(InvoiceDate);
CREATE INDEX IF NOT EXISTS idx_invoices_customer ON Invoices(CustomerID);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON Invoices(InvoiceStatus);
CREATE INDEX IF NOT EXISTS idx_invoices_cashier ON Invoices(CashierID);

-- فهارس جدول تفاصيل الفواتير
CREATE INDEX IF NOT EXISTS idx_invoice_details_invoice ON InvoiceDetails(InvoiceID);
CREATE INDEX IF NOT EXISTS idx_invoice_details_product ON InvoiceDetails(ProductID);

-- فهارس جدول المدفوعات
CREATE INDEX IF NOT EXISTS idx_payments_invoice ON Payments(InvoiceID);
CREATE INDEX IF NOT EXISTS idx_payments_date ON Payments(PaymentDate);
CREATE INDEX IF NOT EXISTS idx_payments_type ON Payments(PaymentType);

-- فهارس جدول الأقساط
CREATE INDEX IF NOT EXISTS idx_installments_invoice ON Installments(InvoiceID);
CREATE INDEX IF NOT EXISTS idx_installments_due_date ON Installments(DueDate);
CREATE INDEX IF NOT EXISTS idx_installments_status ON Installments(Status);

-- فهارس جدول حركات المخزون
CREATE INDEX IF NOT EXISTS idx_stock_movements_product ON StockMovements(ProductID);
CREATE INDEX IF NOT EXISTS idx_stock_movements_date ON StockMovements(MovementDate);
CREATE INDEX IF NOT EXISTS idx_stock_movements_type ON StockMovements(MovementType);

-- فهارس جدول الصندوق
CREATE INDEX IF NOT EXISTS idx_cash_register_date ON CashRegister(TransactionDate);
CREATE INDEX IF NOT EXISTS idx_cash_register_cashier ON CashRegister(CashierID);
CREATE INDEX IF NOT EXISTS idx_cash_register_type ON CashRegister(TransactionType);

-- =====================================================
-- إدراج البيانات الأولية
-- =====================================================

-- إعدادات النظام الأساسية
INSERT OR IGNORE INTO SystemSettings (SettingKey, SettingValue, SettingDescription, SettingCategory) VALUES
('CompanyName', 'أريدوو', 'اسم الشركة', 'معلومات الشركة'),
('CompanyAddress', 'المملكة العربية السعودية', 'عنوان الشركة', 'معلومات الشركة'),
('CompanyPhone', '+966 XX XXX XXXX', 'هاتف الشركة', 'معلومات الشركة'),
('CompanyEmail', '<EMAIL>', 'بريد الشركة الإلكتروني', 'معلومات الشركة'),
('TaxNumber', '*********', 'الرقم الضريبي', 'معلومات الشركة'),
('DefaultTaxRate', '15', 'معدل الضريبة الافتراضي', 'الضرائب'),
('Currency', 'ريال سعودي', 'العملة الافتراضية', 'عام'),
('CurrencySymbol', 'ر.س', 'رمز العملة', 'عام'),
('InvoicePrefix', 'INV', 'بادئة رقم الفاتورة', 'الفواتير'),
('BackupPath', 'C:\\AredooPOS\\Backup\\', 'مسار النسخ الاحتياطي', 'النسخ الاحتياطي'),
('AutoBackup', '1', 'النسخ الاحتياطي التلقائي', 'النسخ الاحتياطي'),
('PrinterType', 'Thermal', 'نوع الطابعة الافتراضي', 'الطباعة'),
('ReceiptWidth', '80', 'عرض الإيصال بالمليمتر', 'الطباعة');

-- المستخدم الافتراضي (مدير النظام)
INSERT OR IGNORE INTO Users (Username, PasswordHash, FullName, UserRole, IsActive) VALUES
('admin', 'admin123', 'مدير النظام', 'مدير', 1),
('cashier', 'cashier123', 'كاشير', 'كاشير', 1);

-- الوحدات الأساسية
INSERT OR IGNORE INTO Units (UnitCode, UnitName, UnitNameEn, BaseUnit) VALUES
('PCS', 'قطعة', 'Piece', 1),
('KG', 'كيلوجرام', 'Kilogram', 1),
('LTR', 'لتر', 'Liter', 1),
('MTR', 'متر', 'Meter', 1),
('BOX', 'صندوق', 'Box', 1),
('PKT', 'باكيت', 'Packet', 1);

-- فئات المنتجات الأساسية
INSERT OR IGNORE INTO Categories (CategoryCode, CategoryName, Description) VALUES
('FOOD', 'مواد غذائية', 'المواد الغذائية والمشروبات والوجبات الخفيفة'),
('BEVERAGE', 'مشروبات', 'المشروبات الباردة والساخنة'),
('HOUSEHOLD', 'مستلزمات منزلية', 'الأدوات والمستلزمات المنزلية'),
('PERSONAL', 'عناية شخصية', 'منتجات العناية الشخصية والتجميل'),
('ELECTRONICS', 'إلكترونيات', 'الأجهزة الإلكترونية والكهربائية'),
('STATIONERY', 'قرطاسية', 'الأدوات المكتبية والقرطاسية'),
('CLOTHING', 'ملابس', 'الملابس والأزياء'),
('TOYS', 'ألعاب', 'ألعاب الأطفال والترفيه'),
('HEALTH', 'صحة', 'المنتجات الصحية والطبية'),
('MISC', 'متنوعة', 'منتجات متنوعة أخرى');

-- العميل الافتراضي للمبيعات النقدية
INSERT OR IGNORE INTO Customers (CustomerCode, CustomerName, CustomerType, Phone, Address, IsActive) VALUES
('CASH001', 'عميل نقدي', 'فرد', '', 'مبيعات نقدية', 1),
('WALK001', 'عميل عابر', 'فرد', '', 'عميل عابر', 1);

-- منتجات تجريبية للاختبار
INSERT OR IGNORE INTO Products (ProductCode, ProductName, CategoryID, UnitID, CostPrice, SalePrice, StockQuantity, Barcode, TaxRate) VALUES
('PRD001', 'منتج تجريبي 1', 1, 1, 7.50, 10.00, 100, '*********0123', 15),
('PRD002', 'منتج تجريبي 2', 2, 1, 18.00, 25.50, 50, '*********0124', 15),
('PRD003', 'منتج تجريبي 3', 3, 1, 55.00, 75.00, 25, '*********0125', 15),
('PRD004', 'مشروب غازي', 2, 1, 1.50, 2.50, 200, '*********0126', 15),
('PRD005', 'شامبو', 4, 1, 12.00, 18.00, 30, '*********0127', 15);

-- =====================================================
-- إنشاء المشاهد (Views) للتقارير
-- =====================================================

-- مشهد تفاصيل الفواتير مع معلومات المنتجات
CREATE VIEW IF NOT EXISTS vw_InvoiceDetailsReport AS
SELECT
    i.InvoiceID,
    i.InvoiceNumber,
    i.InvoiceDate,
    i.CustomerName,
    i.PaymentType,
    i.InvoiceStatus,
    id.ProductCode,
    id.ProductName,
    id.Quantity,
    id.UnitPrice,
    id.DiscountAmount,
    id.TaxAmount,
    id.LineTotal,
    p.CategoryID,
    c.CategoryName,
    i.CashierName
FROM InvoiceDetails id
JOIN Invoices i ON id.InvoiceID = i.InvoiceID
JOIN Products p ON id.ProductID = p.ProductID
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID;

-- مشهد ملخص المبيعات اليومية
CREATE VIEW IF NOT EXISTS vw_DailySalesSummary AS
SELECT
    DATE(InvoiceDate) as SaleDate,
    COUNT(*) as InvoiceCount,
    SUM(SubTotal) as SubTotal,
    SUM(DiscountAmount) as TotalDiscount,
    SUM(TaxAmount) as TotalTax,
    SUM(TotalAmount) as TotalSales,
    SUM(PaidAmount) as TotalPaid,
    SUM(RemainingAmount) as TotalRemaining,
    CashierName
FROM Invoices
WHERE InvoiceStatus = 'مكتملة' AND IsReturned = 0
GROUP BY DATE(InvoiceDate), CashierName;

-- مشهد المنتجات منخفضة المخزون
CREATE VIEW IF NOT EXISTS vw_LowStockProducts AS
SELECT
    p.ProductID,
    p.ProductCode,
    p.ProductName,
    p.StockQuantity,
    p.MinStockLevel,
    p.ReorderLevel,
    c.CategoryName,
    s.SupplierName,
    (p.MinStockLevel - p.StockQuantity) as ShortageQuantity
FROM Products p
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
LEFT JOIN Suppliers s ON p.SupplierID = s.SupplierID
WHERE p.IsActive = 1 AND p.StockQuantity <= p.MinStockLevel;

-- مشهد الأقساط المستحقة
CREATE VIEW IF NOT EXISTS vw_DueInstallments AS
SELECT
    inst.InstallmentID,
    inst.InvoiceID,
    i.InvoiceNumber,
    i.CustomerName,
    i.CustomerPhone,
    inst.InstallmentNumber,
    inst.DueDate,
    inst.Amount,
    inst.PaidAmount,
    inst.RemainingAmount,
    inst.Status,
    CASE
        WHEN DATE(inst.DueDate) < DATE('now') THEN
            CAST((JULIANDAY('now') - JULIANDAY(inst.DueDate)) AS INTEGER)
        ELSE 0
    END as OverdueDays
FROM Installments inst
JOIN Invoices i ON inst.InvoiceID = i.InvoiceID
WHERE inst.RemainingAmount > 0
ORDER BY inst.DueDate;
