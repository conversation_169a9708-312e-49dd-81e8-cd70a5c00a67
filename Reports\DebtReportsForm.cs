using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Collections.Generic;
using AredooPOS.Models;
using AredooPOS.BLL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Reports
{
    /// <summary>
    /// واجهة تقارير الديون والتحصيل
    /// توفر مجموعة شاملة من التقارير المالية المتعلقة بالديون
    /// </summary>
    public partial class DebtReportsForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly DebtBLL _debtBLL;
        private readonly CustomerBLL _customerBLL;
        private readonly ILogger<DebtReportsForm> _logger;

        // ألوان النظام
        private readonly Color PrimaryColor = Color.FromArgb(41, 128, 185);
        private readonly Color SuccessColor = Color.FromArgb(46, 204, 113);
        private readonly Color WarningColor = Color.FromArgb(241, 196, 15);
        private readonly Color DangerColor = Color.FromArgb(231, 76, 60);
        private readonly Color LightGray = Color.FromArgb(236, 240, 241);

        // أنواع التقارير
        private enum ReportType
        {
            OutstandingDebts,      // الديون المستحقة
            OverdueDebts,          // الديون المتأخرة
            DebtAging,             // تقرير أعمار الديون
            CustomerDebts,         // ديون العملاء
            CollectionReport,      // تقرير التحصيل
            DebtSummary,          // ملخص الديون
            PaymentHistory,       // سجل المدفوعات
            TopDebtors            // أكبر المدينين
        }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ واجهة تقارير الديون
        /// </summary>
        /// <param name="debtBLL">طبقة منطق الأعمال للديون</param>
        /// <param name="customerBLL">طبقة منطق الأعمال للعملاء</param>
        /// <param name="logger">مسجل الأحداث</param>
        public DebtReportsForm(DebtBLL debtBLL, CustomerBLL customerBLL, ILogger<DebtReportsForm> logger = null)
        {
            _debtBLL = debtBLL ?? throw new ArgumentNullException(nameof(debtBLL));
            _customerBLL = customerBLL ?? throw new ArgumentNullException(nameof(customerBLL));
            _logger = logger;

            InitializeComponent();
            InitializeArabicUI();
            LoadInitialData();
            SetupEventHandlers();
        }

        /// <summary>
        /// تهيئة الواجهة العربية
        /// </summary>
        private void InitializeArabicUI()
        {
            // إعدادات النموذج الأساسية
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Text = "تقارير الديون والتحصيل";
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = LightGray;

            // تطبيق الألوان والأنماط
            ApplyThemeColors();
            UpdateUITexts();
        }

        /// <summary>
        /// تطبيق ألوان النظام
        /// </summary>
        private void ApplyThemeColors()
        {
            // شريط العنوان
            pnlHeader.BackColor = PrimaryColor;
            lblTitle.ForeColor = Color.White;

            // أزرار التقارير
            btnOutstandingDebts.BackColor = DangerColor;
            btnOutstandingDebts.ForeColor = Color.White;
            btnOutstandingDebts.FlatStyle = FlatStyle.Flat;

            btnOverdueDebts.BackColor = WarningColor;
            btnOverdueDebts.ForeColor = Color.White;
            btnOverdueDebts.FlatStyle = FlatStyle.Flat;

            btnDebtAging.BackColor = Color.FromArgb(155, 89, 182);
            btnDebtAging.ForeColor = Color.White;
            btnDebtAging.FlatStyle = FlatStyle.Flat;

            btnCustomerDebts.BackColor = PrimaryColor;
            btnCustomerDebts.ForeColor = Color.White;
            btnCustomerDebts.FlatStyle = FlatStyle.Flat;

            btnCollectionReport.BackColor = SuccessColor;
            btnCollectionReport.ForeColor = Color.White;
            btnCollectionReport.FlatStyle = FlatStyle.Flat;

            btnDebtSummary.BackColor = Color.FromArgb(52, 152, 219);
            btnDebtSummary.ForeColor = Color.White;
            btnDebtSummary.FlatStyle = FlatStyle.Flat;

            btnPaymentHistory.BackColor = Color.FromArgb(46, 204, 113);
            btnPaymentHistory.ForeColor = Color.White;
            btnPaymentHistory.FlatStyle = FlatStyle.Flat;

            btnTopDebtors.BackColor = Color.FromArgb(231, 76, 60);
            btnTopDebtors.ForeColor = Color.White;
            btnTopDebtors.FlatStyle = FlatStyle.Flat;

            // أزرار العمليات
            btnGenerateReport.BackColor = PrimaryColor;
            btnGenerateReport.ForeColor = Color.White;
            btnGenerateReport.FlatStyle = FlatStyle.Flat;

            btnPrint.BackColor = Color.FromArgb(52, 152, 219);
            btnPrint.ForeColor = Color.White;
            btnPrint.FlatStyle = FlatStyle.Flat;

            btnExport.BackColor = SuccessColor;
            btnExport.ForeColor = Color.White;
            btnExport.FlatStyle = FlatStyle.Flat;

            // شبكة البيانات
            dgvReport.BackgroundColor = Color.White;
            dgvReport.GridColor = LightGray;
            dgvReport.DefaultCellStyle.Font = new Font("Tahoma", 9F);
            dgvReport.ColumnHeadersDefaultCellStyle.BackColor = PrimaryColor;
            dgvReport.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvReport.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 9F, FontStyle.Bold);

            // لوحات المعلومات
            pnlReportOptions.BackColor = Color.White;
            pnlReportOptions.BorderStyle = BorderStyle.FixedSingle;

            pnlReportData.BackColor = Color.White;
            pnlReportData.BorderStyle = BorderStyle.FixedSingle;

            pnlSummary.BackColor = Color.White;
            pnlSummary.BorderStyle = BorderStyle.FixedSingle;
        }

        /// <summary>
        /// تحديث النصوص في الواجهة
        /// </summary>
        private void UpdateUITexts()
        {
            lblTitle.Text = "تقارير الديون والتحصيل";

            // أزرار التقارير
            btnOutstandingDebts.Text = "الديون المستحقة";
            btnOverdueDebts.Text = "الديون المتأخرة";
            btnDebtAging.Text = "أعمار الديون";
            btnCustomerDebts.Text = "ديون العملاء";
            btnCollectionReport.Text = "تقرير التحصيل";
            btnDebtSummary.Text = "ملخص الديون";
            btnPaymentHistory.Text = "سجل المدفوعات";
            btnTopDebtors.Text = "أكبر المدينين";

            // أزرار العمليات
            btnGenerateReport.Text = "إنشاء التقرير";
            btnPrint.Text = "طباعة";
            btnExport.Text = "تصدير";
            btnClose.Text = "إغلاق";

            // خيارات التقرير
            grpReportOptions.Text = "خيارات التقرير";
            lblFromDate.Text = "من تاريخ:";
            lblToDate.Text = "إلى تاريخ:";
            lblCustomer.Text = "العميل:";
            lblMinAmount.Text = "الحد الأدنى للمبلغ:";
            lblMaxAmount.Text = "الحد الأقصى للمبلغ:";
            chkIncludeFullyPaid.Text = "تضمين الديون المدفوعة";
            chkGroupByCustomer.Text = "تجميع حسب العميل";

            // بيانات التقرير
            grpReportData.Text = "بيانات التقرير";

            // الملخص
            grpSummary.Text = "ملخص التقرير";
        }

        /// <summary>
        /// تحميل البيانات الأولية
        /// </summary>
        private void LoadInitialData()
        {
            try
            {
                // تعيين التواريخ الافتراضية
                dtpFromDate.Value = DateTime.Now.AddMonths(-1);
                dtpToDate.Value = DateTime.Now;

                // تحميل قائمة العملاء
                LoadCustomers();

                // تعيين القيم الافتراضية
                numMinAmount.Value = 0;
                numMaxAmount.Value = 999999;
                chkIncludeFullyPaid.Checked = false;
                chkGroupByCustomer.Checked = false;

                // تعطيل أزرار العمليات
                btnPrint.Enabled = false;
                btnExport.Enabled = false;

                _logger?.LogInformation("تم تحميل البيانات الأولية لواجهة تقارير الديون");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل البيانات الأولية");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل قائمة العملاء
        /// </summary>
        private void LoadCustomers()
        {
            try
            {
                cmbCustomer.Items.Clear();
                cmbCustomer.Items.Add("جميع العملاء");

                var customers = _customerBLL.GetAllCustomers();
                foreach (var customer in customers.Where(c => c.IsActive))
                {
                    cmbCustomer.Items.Add($"{customer.CustomerCode} - {customer.CustomerName}");
                    cmbCustomer.Tag = customer.CustomerID;
                }

                cmbCustomer.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل قائمة العملاء");
            }
        }

        /// <summary>
        /// إعداد معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // أحداث أزرار التقارير
            btnOutstandingDebts.Click += (s, e) => SelectReportType(ReportType.OutstandingDebts);
            btnOverdueDebts.Click += (s, e) => SelectReportType(ReportType.OverdueDebts);
            btnDebtAging.Click += (s, e) => SelectReportType(ReportType.DebtAging);
            btnCustomerDebts.Click += (s, e) => SelectReportType(ReportType.CustomerDebts);
            btnCollectionReport.Click += (s, e) => SelectReportType(ReportType.CollectionReport);
            btnDebtSummary.Click += (s, e) => SelectReportType(ReportType.DebtSummary);
            btnPaymentHistory.Click += (s, e) => SelectReportType(ReportType.PaymentHistory);
            btnTopDebtors.Click += (s, e) => SelectReportType(ReportType.TopDebtors);

            // أحداث أزرار العمليات
            btnGenerateReport.Click += BtnGenerateReport_Click;
            btnPrint.Click += BtnPrint_Click;
            btnExport.Click += BtnExport_Click;
            btnClose.Click += BtnClose_Click;

            // أحداث النموذج
            this.Load += DebtReportsForm_Load;
            this.KeyDown += DebtReportsForm_KeyDown;
            this.KeyPreview = true;
        }

        #endregion

        #region اختيار وإنشاء التقارير

        /// <summary>
        /// اختيار نوع التقرير
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        private void SelectReportType(ReportType reportType)
        {
            // إعادة تعيين ألوان الأزرار
            ResetButtonColors();

            // تمييز الزر المحدد
            var selectedButton = GetReportButton(reportType);
            if (selectedButton != null)
            {
                selectedButton.BackColor = Color.FromArgb(26, 82, 118); // لون أغمق
            }

            // تحديث خيارات التقرير حسب النوع
            UpdateReportOptions(reportType);

            // حفظ نوع التقرير المحدد
            this.Tag = reportType;

            _logger?.LogInformation("تم اختيار نوع التقرير: {ReportType}", reportType);
        }

        /// <summary>
        /// الحصول على زر التقرير
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        /// <returns>زر التقرير</returns>
        private Button GetReportButton(ReportType reportType)
        {
            return reportType switch
            {
                ReportType.OutstandingDebts => btnOutstandingDebts,
                ReportType.OverdueDebts => btnOverdueDebts,
                ReportType.DebtAging => btnDebtAging,
                ReportType.CustomerDebts => btnCustomerDebts,
                ReportType.CollectionReport => btnCollectionReport,
                ReportType.DebtSummary => btnDebtSummary,
                ReportType.PaymentHistory => btnPaymentHistory,
                ReportType.TopDebtors => btnTopDebtors,
                _ => null
            };
        }

        /// <summary>
        /// إعادة تعيين ألوان الأزرار
        /// </summary>
        private void ResetButtonColors()
        {
            btnOutstandingDebts.BackColor = DangerColor;
            btnOverdueDebts.BackColor = WarningColor;
            btnDebtAging.BackColor = Color.FromArgb(155, 89, 182);
            btnCustomerDebts.BackColor = PrimaryColor;
            btnCollectionReport.BackColor = SuccessColor;
            btnDebtSummary.BackColor = Color.FromArgb(52, 152, 219);
            btnPaymentHistory.BackColor = Color.FromArgb(46, 204, 113);
            btnTopDebtors.BackColor = Color.FromArgb(231, 76, 60);
        }

        /// <summary>
        /// تحديث خيارات التقرير حسب النوع
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        private void UpdateReportOptions(ReportType reportType)
        {
            // إظهار/إخفاء الخيارات حسب نوع التقرير
            switch (reportType)
            {
                case ReportType.CustomerDebts:
                    lblCustomer.Visible = true;
                    cmbCustomer.Visible = true;
                    chkGroupByCustomer.Visible = false;
                    break;

                case ReportType.DebtSummary:
                    chkGroupByCustomer.Visible = true;
                    chkIncludeFullyPaid.Visible = false;
                    break;

                case ReportType.PaymentHistory:
                    chkIncludeFullyPaid.Visible = false;
                    break;

                case ReportType.TopDebtors:
                    lblCustomer.Visible = false;
                    cmbCustomer.Visible = false;
                    chkIncludeFullyPaid.Visible = false;
                    break;

                default:
                    lblCustomer.Visible = false;
                    cmbCustomer.Visible = false;
                    chkGroupByCustomer.Visible = true;
                    chkIncludeFullyPaid.Visible = true;
                    break;
            }
        }

        /// <summary>
        /// إنشاء التقرير
        /// </summary>
        private void BtnGenerateReport_Click(object sender, EventArgs e)
        {
            if (this.Tag == null)
            {
                MessageBox.Show("يرجى اختيار نوع التقرير أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var reportType = (ReportType)this.Tag;
                var reportData = GenerateReportData(reportType);

                if (reportData != null && reportData.Any())
                {
                    DisplayReportData(reportData, reportType);
                    UpdateReportSummary(reportData, reportType);

                    // تفعيل أزرار العمليات
                    btnPrint.Enabled = true;
                    btnExport.Enabled = true;

                    _logger?.LogInformation("تم إنشاء التقرير بنجاح. النوع: {ReportType}, عدد السجلات: {Count}",
                        reportType, reportData.Count());
                }
                else
                {
                    MessageBox.Show("لا توجد بيانات للفترة المحددة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    dgvReport.DataSource = null;
                    ClearSummary();

                    btnPrint.Enabled = false;
                    btnExport.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنشاء التقرير");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region توليد بيانات التقارير

        /// <summary>
        /// توليد بيانات التقرير حسب النوع
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        /// <returns>بيانات التقرير</returns>
        private object GenerateReportData(ReportType reportType)
        {
            var criteria = BuildSearchCriteria();

            return reportType switch
            {
                ReportType.OutstandingDebts => GenerateOutstandingDebtsReport(criteria),
                ReportType.OverdueDebts => GenerateOverdueDebtsReport(criteria),
                ReportType.DebtAging => GenerateDebtAgingReport(criteria),
                ReportType.CustomerDebts => GenerateCustomerDebtsReport(criteria),
                ReportType.CollectionReport => GenerateCollectionReport(criteria),
                ReportType.DebtSummary => GenerateDebtSummaryReport(criteria),
                ReportType.PaymentHistory => GeneratePaymentHistoryReport(criteria),
                ReportType.TopDebtors => GenerateTopDebtorsReport(),
                _ => null
            };
        }

        /// <summary>
        /// بناء معايير البحث
        /// </summary>
        /// <returns>معايير البحث</returns>
        private DebtSearchCriteria BuildSearchCriteria()
        {
            var criteria = new DebtSearchCriteria
            {
                FromDate = dtpFromDate.Value.Date,
                ToDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1),
                MinAmount = numMinAmount.Value,
                MaxAmount = numMaxAmount.Value,
                OnlyOutstanding = !chkIncludeFullyPaid.Checked
            };

            // العميل المحدد
            if (cmbCustomer.SelectedIndex > 0 && cmbCustomer.Tag is int customerId)
            {
                criteria.CustomerId = customerId;
            }

            return criteria;
        }

        /// <summary>
        /// تقرير الديون المستحقة
        /// </summary>
        private List<OutstandingDebtReportRow> GenerateOutstandingDebtsReport(DebtSearchCriteria criteria)
        {
            var debts = _debtBLL.SearchDebts(criteria);

            return debts.Select(d => new OutstandingDebtReportRow
            {
                DebtNumber = d.DebtNumber,
                CustomerName = d.CustomerName,
                CustomerPhone = d.CustomerPhone,
                DebtDate = d.DebtDate,
                DueDate = d.DueDate,
                OriginalAmount = d.OriginalAmount,
                PaidAmount = d.PaidAmount,
                RemainingAmount = d.RemainingAmount,
                OverdueDays = d.OverdueDays,
                DebtStatus = d.DebtStatus,
                Priority = d.Priority
            }).ToList();
        }

        /// <summary>
        /// تقرير الديون المتأخرة
        /// </summary>
        private List<OverdueDebtReportRow> GenerateOverdueDebtsReport(DebtSearchCriteria criteria)
        {
            var overdueDebts = _debtBLL.GetOverdueDebts();

            return overdueDebts.Where(d =>
                d.DebtDate >= criteria.FromDate &&
                d.DebtDate <= criteria.ToDate &&
                d.RemainingAmount >= criteria.MinAmount &&
                d.RemainingAmount <= criteria.MaxAmount)
                .Select(d => new OverdueDebtReportRow
                {
                    DebtNumber = d.DebtNumber,
                    CustomerName = d.CustomerName,
                    CustomerPhone = d.CustomerPhone,
                    DebtDate = d.DebtDate,
                    DueDate = d.DueDate.Value,
                    RemainingAmount = d.RemainingAmount,
                    OverdueDays = d.OverdueDays,
                    LateFees = d.LateFees,
                    TotalDueAmount = d.TotalDueAmount,
                    ReminderCount = d.ReminderCount,
                    Priority = d.Priority
                }).ToList();
        }

        /// <summary>
        /// تقرير أعمار الديون
        /// </summary>
        private List<DebtAgingReportRow> GenerateDebtAgingReport(DebtSearchCriteria criteria)
        {
            var debts = _debtBLL.SearchDebts(criteria);

            return debts.GroupBy(d => d.CustomerName)
                .Select(g => new DebtAgingReportRow
                {
                    CustomerName = g.Key,
                    Current = g.Where(d => d.OverdueDays <= 0).Sum(d => d.RemainingAmount),
                    Days1To30 = g.Where(d => d.OverdueDays > 0 && d.OverdueDays <= 30).Sum(d => d.RemainingAmount),
                    Days31To60 = g.Where(d => d.OverdueDays > 30 && d.OverdueDays <= 60).Sum(d => d.RemainingAmount),
                    Days61To90 = g.Where(d => d.OverdueDays > 60 && d.OverdueDays <= 90).Sum(d => d.RemainingAmount),
                    Over90Days = g.Where(d => d.OverdueDays > 90).Sum(d => d.RemainingAmount),
                    TotalAmount = g.Sum(d => d.RemainingAmount)
                }).ToList();
        }

        /// <summary>
        /// تقرير ديون العملاء
        /// </summary>
        private List<CustomerDebtReportRow> GenerateCustomerDebtsReport(DebtSearchCriteria criteria)
        {
            var debts = _debtBLL.SearchDebts(criteria);

            if (chkGroupByCustomer.Checked)
            {
                return debts.GroupBy(d => new { d.CustomerID, d.CustomerName, d.CustomerPhone })
                    .Select(g => new CustomerDebtReportRow
                    {
                        CustomerName = g.Key.CustomerName,
                        CustomerPhone = g.Key.CustomerPhone,
                        TotalDebts = g.Count(),
                        TotalOriginalAmount = g.Sum(d => d.OriginalAmount),
                        TotalPaidAmount = g.Sum(d => d.PaidAmount),
                        TotalRemainingAmount = g.Sum(d => d.RemainingAmount),
                        OverdueDebts = g.Count(d => d.IsOverdue()),
                        OverdueAmount = g.Where(d => d.IsOverdue()).Sum(d => d.RemainingAmount)
                    }).ToList();
            }
            else
            {
                return debts.Select(d => new CustomerDebtReportRow
                {
                    DebtNumber = d.DebtNumber,
                    CustomerName = d.CustomerName,
                    CustomerPhone = d.CustomerPhone,
                    DebtDate = d.DebtDate,
                    DueDate = d.DueDate,
                    TotalOriginalAmount = d.OriginalAmount,
                    TotalPaidAmount = d.PaidAmount,
                    TotalRemainingAmount = d.RemainingAmount,
                    DebtStatus = d.DebtStatus
                }).ToList();
            }
        }

        /// <summary>
        /// تقرير التحصيل
        /// </summary>
        private List<CollectionReportRow> GenerateCollectionReport(DebtSearchCriteria criteria)
        {
            // هذا التقرير يحتاج إلى بيانات المدفوعات
            // سيتم تنفيذه عند إنشاء طبقة المدفوعات
            return new List<CollectionReportRow>();
        }

        /// <summary>
        /// تقرير ملخص الديون
        /// </summary>
        private List<DebtSummaryReportRow> GenerateDebtSummaryReport(DebtSearchCriteria criteria)
        {
            var summary = _debtBLL.GetDebtSummary();

            return new List<DebtSummaryReportRow>
            {
                new DebtSummaryReportRow
                {
                    Category = "إجمالي الديون المستحقة",
                    Count = summary.TotalOutstandingDebts,
                    Amount = summary.TotalOutstandingAmount
                },
                new DebtSummaryReportRow
                {
                    Category = "الديون المتأخرة",
                    Count = summary.TotalOverdueDebts,
                    Amount = summary.TotalOverdueAmount
                },
                new DebtSummaryReportRow
                {
                    Category = "رسوم التأخير",
                    Count = 0,
                    Amount = summary.TotalLateFees
                }
            };
        }

        /// <summary>
        /// تقرير سجل المدفوعات
        /// </summary>
        private List<PaymentHistoryReportRow> GeneratePaymentHistoryReport(DebtSearchCriteria criteria)
        {
            // هذا التقرير يحتاج إلى بيانات المدفوعات
            // سيتم تنفيذه عند إنشاء طبقة المدفوعات
            return new List<PaymentHistoryReportRow>();
        }

        /// <summary>
        /// تقرير أكبر المدينين
        /// </summary>
        private List<CustomerDebtSummary> GenerateTopDebtorsReport()
        {
            return _debtBLL.GetTopDebtorCustomers(20); // أكبر 20 مدين
        }

        #endregion

        #region عرض البيانات والملخص

        /// <summary>
        /// عرض بيانات التقرير
        /// </summary>
        /// <param name="data">البيانات</param>
        /// <param name="reportType">نوع التقرير</param>
        private void DisplayReportData(object data, ReportType reportType)
        {
            dgvReport.DataSource = data;
            SetupReportColumns(reportType);

            // تحديث عنوان التقرير
            lblReportTitle.Text = GetReportTitle(reportType);
            lblRecordCount.Text = $"عدد السجلات: {GetRecordCount(data)}";
        }

        /// <summary>
        /// إعداد أعمدة التقرير
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        private void SetupReportColumns(ReportType reportType)
        {
            // إخفاء جميع الأعمدة أولاً
            foreach (DataGridViewColumn column in dgvReport.Columns)
            {
                column.Visible = false;
            }

            // إظهار الأعمدة المطلوبة حسب نوع التقرير
            switch (reportType)
            {
                case ReportType.OutstandingDebts:
                    SetupOutstandingDebtsColumns();
                    break;
                case ReportType.OverdueDebts:
                    SetupOverdueDebtsColumns();
                    break;
                case ReportType.DebtAging:
                    SetupDebtAgingColumns();
                    break;
                // ... باقي أنواع التقارير
            }
        }

        /// <summary>
        /// إعداد أعمدة تقرير الديون المستحقة
        /// </summary>
        private void SetupOutstandingDebtsColumns()
        {
            var columns = new[]
            {
                ("DebtNumber", "رقم الدين", 120),
                ("CustomerName", "اسم العميل", 200),
                ("DebtDate", "تاريخ الدين", 100),
                ("DueDate", "تاريخ الاستحقاق", 100),
                ("OriginalAmount", "المبلغ الأصلي", 120),
                ("RemainingAmount", "المبلغ المتبقي", 120),
                ("OverdueDays", "أيام التأخير", 100),
                ("DebtStatus", "الحالة", 100)
            };

            foreach (var (name, header, width) in columns)
            {
                if (dgvReport.Columns[name] != null)
                {
                    dgvReport.Columns[name].Visible = true;
                    dgvReport.Columns[name].HeaderText = header;
                    dgvReport.Columns[name].Width = width;
                }
            }
        }

        /// <summary>
        /// إعداد أعمدة تقرير الديون المتأخرة
        /// </summary>
        private void SetupOverdueDebtsColumns()
        {
            var columns = new[]
            {
                ("DebtNumber", "رقم الدين", 120),
                ("CustomerName", "اسم العميل", 200),
                ("DueDate", "تاريخ الاستحقاق", 100),
                ("RemainingAmount", "المبلغ المتبقي", 120),
                ("OverdueDays", "أيام التأخير", 100),
                ("LateFees", "رسوم التأخير", 120),
                ("TotalDueAmount", "إجمالي المستحق", 120),
                ("Priority", "الأولوية", 80)
            };

            foreach (var (name, header, width) in columns)
            {
                if (dgvReport.Columns[name] != null)
                {
                    dgvReport.Columns[name].Visible = true;
                    dgvReport.Columns[name].HeaderText = header;
                    dgvReport.Columns[name].Width = width;
                }
            }
        }

        /// <summary>
        /// إعداد أعمدة تقرير أعمار الديون
        /// </summary>
        private void SetupDebtAgingColumns()
        {
            var columns = new[]
            {
                ("CustomerName", "اسم العميل", 200),
                ("Current", "حالي", 100),
                ("Days1To30", "1-30 يوم", 100),
                ("Days31To60", "31-60 يوم", 100),
                ("Days61To90", "61-90 يوم", 100),
                ("Over90Days", "أكثر من 90 يوم", 120),
                ("TotalAmount", "الإجمالي", 120)
            };

            foreach (var (name, header, width) in columns)
            {
                if (dgvReport.Columns[name] != null)
                {
                    dgvReport.Columns[name].Visible = true;
                    dgvReport.Columns[name].HeaderText = header;
                    dgvReport.Columns[name].Width = width;
                }
            }
        }

        /// <summary>
        /// تحديث ملخص التقرير
        /// </summary>
        /// <param name="data">البيانات</param>
        /// <param name="reportType">نوع التقرير</param>
        private void UpdateReportSummary(object data, ReportType reportType)
        {
            switch (reportType)
            {
                case ReportType.OutstandingDebts:
                    UpdateOutstandingDebtsSummary(data as List<OutstandingDebtReportRow>);
                    break;
                case ReportType.OverdueDebts:
                    UpdateOverdueDebtsSummary(data as List<OverdueDebtReportRow>);
                    break;
                case ReportType.DebtAging:
                    UpdateDebtAgingSummary(data as List<DebtAgingReportRow>);
                    break;
                // ... باقي أنواع التقارير
            }
        }

        /// <summary>
        /// تحديث ملخص الديون المستحقة
        /// </summary>
        private void UpdateOutstandingDebtsSummary(List<OutstandingDebtReportRow> data)
        {
            if (data == null) return;

            var totalCount = data.Count;
            var totalOriginal = data.Sum(d => d.OriginalAmount);
            var totalRemaining = data.Sum(d => d.RemainingAmount);
            var overdueCount = data.Count(d => d.OverdueDays > 0);

            lblSummary1.Text = $"عدد الديون: {totalCount:N0}";
            lblSummary2.Text = $"إجمالي المبلغ الأصلي: {totalOriginal:C}";
            lblSummary3.Text = $"إجمالي المبلغ المتبقي: {totalRemaining:C}";
            lblSummary4.Text = $"الديون المتأخرة: {overdueCount:N0}";
        }

        /// <summary>
        /// تحديث ملخص الديون المتأخرة
        /// </summary>
        private void UpdateOverdueDebtsSummary(List<OverdueDebtReportRow> data)
        {
            if (data == null) return;

            var totalCount = data.Count;
            var totalAmount = data.Sum(d => d.RemainingAmount);
            var totalLateFees = data.Sum(d => d.LateFees);
            var averageOverdueDays = data.Any() ? data.Average(d => d.OverdueDays) : 0;

            lblSummary1.Text = $"عدد الديون المتأخرة: {totalCount:N0}";
            lblSummary2.Text = $"إجمالي المبلغ المتأخر: {totalAmount:C}";
            lblSummary3.Text = $"إجمالي رسوم التأخير: {totalLateFees:C}";
            lblSummary4.Text = $"متوسط أيام التأخير: {averageOverdueDays:F0}";
        }

        /// <summary>
        /// تحديث ملخص أعمار الديون
        /// </summary>
        private void UpdateDebtAgingSummary(List<DebtAgingReportRow> data)
        {
            if (data == null) return;

            var totalCurrent = data.Sum(d => d.Current);
            var total1To30 = data.Sum(d => d.Days1To30);
            var total31To60 = data.Sum(d => d.Days31To60);
            var totalOver90 = data.Sum(d => d.Over90Days);

            lblSummary1.Text = $"الديون الحالية: {totalCurrent:C}";
            lblSummary2.Text = $"1-30 يوم: {total1To30:C}";
            lblSummary3.Text = $"31-60 يوم: {total31To60:C}";
            lblSummary4.Text = $"أكثر من 90 يوم: {totalOver90:C}";
        }

        /// <summary>
        /// مسح الملخص
        /// </summary>
        private void ClearSummary()
        {
            lblSummary1.Text = "";
            lblSummary2.Text = "";
            lblSummary3.Text = "";
            lblSummary4.Text = "";
        }

        #endregion

        #region معالجات الأحداث

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void DebtReportsForm_Load(object sender, EventArgs e)
        {
            _logger?.LogInformation("تم تحميل واجهة تقارير الديون");
        }

        /// <summary>
        /// حدث الضغط على مفاتيح الاختصار
        /// </summary>
        private void DebtReportsForm_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.F5:
                    BtnGenerateReport_Click(sender, e);
                    break;
                case Keys.F6:
                    BtnPrint_Click(sender, e);
                    break;
                case Keys.F7:
                    BtnExport_Click(sender, e);
                    break;
                case Keys.Escape:
                    this.Close();
                    break;
            }
        }

        #endregion

        #region عمليات الأزرار

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private void BtnPrint_Click(object sender, EventArgs e)
        {
            if (dgvReport.DataSource == null)
            {
                MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var reportType = (ReportType)this.Tag;
                var reportTitle = GetReportTitle(reportType);
                var printForm = new ReportPrintForm(dgvReport, reportTitle);
                printForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في طباعة التقرير");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصدير التقرير
        /// </summary>
        private void BtnExport_Click(object sender, EventArgs e)
        {
            if (dgvReport.DataSource == null)
            {
                MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var reportType = (ReportType)this.Tag;
                var reportTitle = GetReportTitle(reportType);

                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx|CSV Files|*.csv|PDF Files|*.pdf",
                    FileName = $"{reportTitle}_{DateTime.Now:yyyyMMdd}.xlsx"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    var exporter = new DataExporter();
                    var data = dgvReport.DataSource;

                    switch (System.IO.Path.GetExtension(saveDialog.FileName).ToLower())
                    {
                        case ".xlsx":
                            exporter.ExportToExcel(data, saveDialog.FileName, reportTitle);
                            break;
                        case ".csv":
                            exporter.ExportToCSV(data, saveDialog.FileName);
                            break;
                        case ".pdf":
                            exporter.ExportToPDF(data, saveDialog.FileName, reportTitle);
                            break;
                    }

                    MessageBox.Show("تم تصدير التقرير بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تصدير التقرير");
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إغلاق النموذج
        /// </summary>
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        #region عمليات مساعدة

        /// <summary>
        /// الحصول على عنوان التقرير
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        /// <returns>عنوان التقرير</returns>
        private string GetReportTitle(ReportType reportType)
        {
            return reportType switch
            {
                ReportType.OutstandingDebts => "تقرير الديون المستحقة",
                ReportType.OverdueDebts => "تقرير الديون المتأخرة",
                ReportType.DebtAging => "تقرير أعمار الديون",
                ReportType.CustomerDebts => "تقرير ديون العملاء",
                ReportType.CollectionReport => "تقرير التحصيل",
                ReportType.DebtSummary => "تقرير ملخص الديون",
                ReportType.PaymentHistory => "تقرير سجل المدفوعات",
                ReportType.TopDebtors => "تقرير أكبر المدينين",
                _ => "تقرير الديون"
            };
        }

        /// <summary>
        /// الحصول على عدد السجلات
        /// </summary>
        /// <param name="data">البيانات</param>
        /// <returns>عدد السجلات</returns>
        private int GetRecordCount(object data)
        {
            return data switch
            {
                System.Collections.ICollection collection => collection.Count,
                System.Collections.IEnumerable enumerable => enumerable.Cast<object>().Count(),
                _ => 0
            };
        }

        #endregion
    }

    #region نماذج بيانات التقارير

    /// <summary>
    /// صف تقرير الديون المستحقة
    /// </summary>
    public class OutstandingDebtReportRow
    {
        public string DebtNumber { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public DateTime DebtDate { get; set; }
        public DateTime? DueDate { get; set; }
        public decimal OriginalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public int OverdueDays { get; set; }
        public string DebtStatus { get; set; }
        public string Priority { get; set; }
    }

    /// <summary>
    /// صف تقرير الديون المتأخرة
    /// </summary>
    public class OverdueDebtReportRow
    {
        public string DebtNumber { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public DateTime DebtDate { get; set; }
        public DateTime DueDate { get; set; }
        public decimal RemainingAmount { get; set; }
        public int OverdueDays { get; set; }
        public decimal LateFees { get; set; }
        public decimal TotalDueAmount { get; set; }
        public int ReminderCount { get; set; }
        public string Priority { get; set; }
    }

    /// <summary>
    /// صف تقرير أعمار الديون
    /// </summary>
    public class DebtAgingReportRow
    {
        public string CustomerName { get; set; }
        public decimal Current { get; set; }
        public decimal Days1To30 { get; set; }
        public decimal Days31To60 { get; set; }
        public decimal Days61To90 { get; set; }
        public decimal Over90Days { get; set; }
        public decimal TotalAmount { get; set; }
    }

    /// <summary>
    /// صف تقرير ديون العملاء
    /// </summary>
    public class CustomerDebtReportRow
    {
        public string DebtNumber { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public DateTime? DebtDate { get; set; }
        public DateTime? DueDate { get; set; }
        public int TotalDebts { get; set; }
        public decimal TotalOriginalAmount { get; set; }
        public decimal TotalPaidAmount { get; set; }
        public decimal TotalRemainingAmount { get; set; }
        public int OverdueDebts { get; set; }
        public decimal OverdueAmount { get; set; }
        public string DebtStatus { get; set; }
    }

    /// <summary>
    /// صف تقرير التحصيل
    /// </summary>
    public class CollectionReportRow
    {
        public DateTime PaymentDate { get; set; }
        public string PaymentNumber { get; set; }
        public string CustomerName { get; set; }
        public string DebtNumber { get; set; }
        public decimal PaymentAmount { get; set; }
        public string PaymentMethod { get; set; }
        public string CollectedBy { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// صف تقرير ملخص الديون
    /// </summary>
    public class DebtSummaryReportRow
    {
        public string Category { get; set; }
        public int Count { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// صف تقرير سجل المدفوعات
    /// </summary>
    public class PaymentHistoryReportRow
    {
        public DateTime PaymentDate { get; set; }
        public string PaymentNumber { get; set; }
        public string CustomerName { get; set; }
        public string DebtNumber { get; set; }
        public decimal PaymentAmount { get; set; }
        public string PaymentMethod { get; set; }
        public string PaymentStatus { get; set; }
        public string Notes { get; set; }
    }

    #endregion
}