using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using AredooPOS.Models;
using Microsoft.Extensions.Logging;

namespace AredooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات للمعاملات النقدية
    /// تحتوي على جميع العمليات المتعلقة بقاعدة البيانات للمعاملات النقدية
    /// </summary>
    public class CashTransactionDAL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly string _connectionString;
        private readonly ILogger<CashTransactionDAL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة الوصول للبيانات للمعاملات النقدية
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public CashTransactionDAL(string connectionString = null, ILogger<CashTransactionDAL> logger = null)
        {
            _connectionString = connectionString ?? DatabaseConfig.GetConnectionString();
            _logger = logger;
        }

        #endregion

        #region العمليات الأساسية

        /// <summary>
        /// إضافة معاملة نقدية جديدة
        /// </summary>
        /// <param name="transaction">بيانات المعاملة</param>
        /// <returns>رقم المعاملة الجديدة</returns>
        public int AddCashTransaction(CashTransaction transaction)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_AddCashTransaction", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@SessionID", transaction.SessionID);
                command.Parameters.AddWithValue("@TransactionType", transaction.TransactionType);
                command.Parameters.AddWithValue("@Amount", transaction.Amount);
                command.Parameters.AddWithValue("@PaymentMethodID", transaction.PaymentMethodID);
                command.Parameters.AddWithValue("@ReferenceNumber", transaction.ReferenceNumber ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Description", transaction.Description ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Category", transaction.Category ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@RelatedDocumentType", transaction.RelatedDocumentType ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@RelatedDocumentID", transaction.RelatedDocumentID ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@CustomerID", transaction.CustomerID ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@SupplierID", transaction.SupplierID ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@UserID", transaction.UserID);
                command.Parameters.AddWithValue("@CreatedBy", transaction.CreatedBy);

                var outputParam = new SqlParameter("@NewTransactionID", SqlDbType.Int)
                {
                    Direction = ParameterDirection.Output
                };
                command.Parameters.Add(outputParam);

                connection.Open();
                command.ExecuteNonQuery();

                var transactionId = Convert.ToInt32(outputParam.Value);
                _logger?.LogInformation($"تم إضافة معاملة نقدية جديدة برقم {transactionId}");
                return transactionId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إضافة المعاملة النقدية");
                throw;
            }
        }

        /// <summary>
        /// إلغاء معاملة نقدية
        /// </summary>
        /// <param name="transactionID">رقم المعاملة</param>
        /// <param name="voidReason">سبب الإلغاء</param>
        /// <param name="voidedBy">من ألغى المعاملة</param>
        /// <returns>true إذا تم الإلغاء بنجاح</returns>
        public bool VoidCashTransaction(int transactionID, string voidReason, string voidedBy)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_VoidCashTransaction", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@TransactionID", transactionID);
                command.Parameters.AddWithValue("@VoidReason", voidReason);
                command.Parameters.AddWithValue("@VoidedBy", voidedBy);

                connection.Open();
                command.ExecuteNonQuery();

                _logger?.LogInformation($"تم إلغاء المعاملة النقدية {transactionID}");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إلغاء المعاملة النقدية {transactionID}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على معاملة نقدية بالرقم
        /// </summary>
        /// <param name="transactionID">رقم المعاملة</param>
        /// <returns>بيانات المعاملة</returns>
        public CashTransaction GetCashTransactionById(int transactionID)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("SELECT * FROM CashTransactions WHERE TransactionID = @TransactionID", connection);

                command.Parameters.AddWithValue("@TransactionID", transactionID);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapCashTransactionFromReader(reader);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على المعاملة النقدية {transactionID}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على معاملات الجلسة
        /// </summary>
        /// <param name="sessionID">رقم الجلسة</param>
        /// <param name="includeVoided">تضمين المعاملات الملغاة</param>
        /// <returns>قائمة معاملات الجلسة</returns>
        public List<CashTransaction> GetSessionTransactions(int sessionID, bool includeVoided = false)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var sql = "SELECT * FROM CashTransactions WHERE SessionID = @SessionID";
                if (!includeVoided)
                    sql += " AND IsVoided = 0";
                sql += " ORDER BY TransactionDate DESC";

                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@SessionID", sessionID);

                connection.Open();
                using var reader = command.ExecuteReader();

                var transactions = new List<CashTransaction>();
                while (reader.Read())
                {
                    transactions.Add(MapCashTransactionFromReader(reader));
                }

                return transactions;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على معاملات الجلسة {sessionID}");
                throw;
            }
        }

        /// <summary>
        /// البحث في المعاملات النقدية
        /// </summary>
        /// <param name="searchCriteria">معايير البحث</param>
        /// <returns>قائمة المعاملات المطابقة</returns>
        public List<CashTransaction> SearchTransactions(TransactionSearchCriteria searchCriteria)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var sql = "SELECT * FROM CashTransactions WHERE 1=1";
                var parameters = new List<SqlParameter>();

                if (searchCriteria.SessionID.HasValue)
                {
                    sql += " AND SessionID = @SessionID";
                    parameters.Add(new SqlParameter("@SessionID", searchCriteria.SessionID.Value));
                }

                if (!string.IsNullOrWhiteSpace(searchCriteria.TransactionType))
                {
                    sql += " AND TransactionType = @TransactionType";
                    parameters.Add(new SqlParameter("@TransactionType", searchCriteria.TransactionType));
                }

                if (searchCriteria.FromDate.HasValue)
                {
                    sql += " AND TransactionDate >= @FromDate";
                    parameters.Add(new SqlParameter("@FromDate", searchCriteria.FromDate.Value));
                }

                if (searchCriteria.ToDate.HasValue)
                {
                    sql += " AND TransactionDate <= @ToDate";
                    parameters.Add(new SqlParameter("@ToDate", searchCriteria.ToDate.Value));
                }

                if (searchCriteria.PaymentMethodID.HasValue)
                {
                    sql += " AND PaymentMethodID = @PaymentMethodID";
                    parameters.Add(new SqlParameter("@PaymentMethodID", searchCriteria.PaymentMethodID.Value));
                }

                if (searchCriteria.UserID.HasValue)
                {
                    sql += " AND UserID = @UserID";
                    parameters.Add(new SqlParameter("@UserID", searchCriteria.UserID.Value));
                }

                if (!string.IsNullOrWhiteSpace(searchCriteria.ReferenceNumber))
                {
                    sql += " AND ReferenceNumber LIKE @ReferenceNumber";
                    parameters.Add(new SqlParameter("@ReferenceNumber", $"%{searchCriteria.ReferenceNumber}%"));
                }

                if (!searchCriteria.IncludeVoided)
                {
                    sql += " AND IsVoided = 0";
                }

                sql += " ORDER BY TransactionDate DESC";

                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddRange(parameters.ToArray());

                connection.Open();
                using var reader = command.ExecuteReader();

                var transactions = new List<CashTransaction>();
                while (reader.Read())
                {
                    transactions.Add(MapCashTransactionFromReader(reader));
                }

                return transactions;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في البحث في المعاملات النقدية");
                throw;
            }
        }

        /// <summary>
        /// الحصول على إحصائيات المعاملات
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="cashRegisterID">رقم الصندوق (اختياري)</param>
        /// <returns>إحصائيات المعاملات</returns>
        public TransactionStatistics GetTransactionStatistics(DateTime? fromDate = null, DateTime? toDate = null, int? cashRegisterID = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                var sql = @"
                    SELECT 
                        COUNT(*) as TotalTransactions,
                        SUM(CASE WHEN TransactionType = 'Sale' AND IsVoided = 0 THEN Amount ELSE 0 END) as TotalSales,
                        SUM(CASE WHEN TransactionType = 'Expense' AND IsVoided = 0 THEN Amount ELSE 0 END) as TotalExpenses,
                        SUM(CASE WHEN TransactionType = 'Withdrawal' AND IsVoided = 0 THEN Amount ELSE 0 END) as TotalWithdrawals,
                        SUM(CASE WHEN TransactionType = 'Deposit' AND IsVoided = 0 THEN Amount ELSE 0 END) as TotalDeposits,
                        COUNT(CASE WHEN IsVoided = 1 THEN 1 END) as VoidedTransactions
                    FROM CashTransactions ct
                    INNER JOIN CashSessions cs ON ct.SessionID = cs.SessionID
                    WHERE 1=1";

                var parameters = new List<SqlParameter>();

                if (fromDate.HasValue)
                {
                    sql += " AND ct.TransactionDate >= @FromDate";
                    parameters.Add(new SqlParameter("@FromDate", fromDate.Value));
                }

                if (toDate.HasValue)
                {
                    sql += " AND ct.TransactionDate <= @ToDate";
                    parameters.Add(new SqlParameter("@ToDate", toDate.Value));
                }

                if (cashRegisterID.HasValue)
                {
                    sql += " AND cs.CashRegisterID = @CashRegisterID";
                    parameters.Add(new SqlParameter("@CashRegisterID", cashRegisterID.Value));
                }

                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddRange(parameters.ToArray());

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return new TransactionStatistics
                    {
                        TotalTransactions = reader.GetInt32("TotalTransactions"),
                        TotalSales = reader.GetDecimal("TotalSales"),
                        TotalExpenses = reader.GetDecimal("TotalExpenses"),
                        TotalWithdrawals = reader.GetDecimal("TotalWithdrawals"),
                        TotalDeposits = reader.GetDecimal("TotalDeposits"),
                        VoidedTransactions = reader.GetInt32("VoidedTransactions"),
                        FromDate = fromDate,
                        ToDate = toDate
                    };
                }

                return new TransactionStatistics();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إحصائيات المعاملات");
                throw;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// تحويل بيانات القارئ إلى كائن معاملة نقدية
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن المعاملة النقدية</returns>
        private CashTransaction MapCashTransactionFromReader(SqlDataReader reader)
        {
            return new CashTransaction
            {
                TransactionID = reader.GetInt32("TransactionID"),
                SessionID = reader.GetInt32("SessionID"),
                TransactionNumber = reader.GetString("TransactionNumber"),
                TransactionType = reader.GetString("TransactionType"),
                TransactionDate = reader.GetDateTime("TransactionDate"),
                Amount = reader.GetDecimal("Amount"),
                PaymentMethodID = reader.GetInt32("PaymentMethodID"),
                ReferenceNumber = reader.IsDBNull("ReferenceNumber") ? null : reader.GetString("ReferenceNumber"),
                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                Category = reader.IsDBNull("Category") ? null : reader.GetString("Category"),
                RelatedDocumentType = reader.IsDBNull("RelatedDocumentType") ? null : reader.GetString("RelatedDocumentType"),
                RelatedDocumentID = reader.IsDBNull("RelatedDocumentID") ? null : reader.GetInt32("RelatedDocumentID"),
                CustomerID = reader.IsDBNull("CustomerID") ? null : reader.GetInt32("CustomerID"),
                SupplierID = reader.IsDBNull("SupplierID") ? null : reader.GetInt32("SupplierID"),
                UserID = reader.GetInt32("UserID"),
                IsVoided = reader.GetBoolean("IsVoided"),
                VoidedBy = reader.IsDBNull("VoidedBy") ? null : reader.GetString("VoidedBy"),
                VoidedDate = reader.IsDBNull("VoidedDate") ? null : reader.GetDateTime("VoidedDate"),
                VoidReason = reader.IsDBNull("VoidReason") ? null : reader.GetString("VoidReason"),
                RequiresApproval = reader.GetBoolean("RequiresApproval"),
                IsApproved = reader.GetBoolean("IsApproved"),
                ApprovedBy = reader.IsDBNull("ApprovedBy") ? null : reader.GetString("ApprovedBy"),
                ApprovalDate = reader.IsDBNull("ApprovalDate") ? null : reader.GetDateTime("ApprovalDate"),
                Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes"),
                CreatedBy = reader.GetString("CreatedBy"),
                CreatedDate = reader.GetDateTime("CreatedDate"),
                ModifiedBy = reader.IsDBNull("ModifiedBy") ? null : reader.GetString("ModifiedBy"),
                ModifiedDate = reader.IsDBNull("ModifiedDate") ? null : reader.GetDateTime("ModifiedDate")
            };
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// معايير البحث في المعاملات
    /// </summary>
    public class TransactionSearchCriteria
    {
        public int? SessionID { get; set; }
        public string TransactionType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? PaymentMethodID { get; set; }
        public int? UserID { get; set; }
        public string ReferenceNumber { get; set; }
        public bool IncludeVoided { get; set; } = false;
    }

    /// <summary>
    /// إحصائيات المعاملات
    /// </summary>
    public class TransactionStatistics
    {
        public int TotalTransactions { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal TotalWithdrawals { get; set; }
        public decimal TotalDeposits { get; set; }
        public int VoidedTransactions { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        
        public decimal NetCashFlow => TotalSales + TotalDeposits - TotalExpenses - TotalWithdrawals;
        public decimal ValidTransactions => TotalTransactions - VoidedTransactions;
    }

    #endregion
}
