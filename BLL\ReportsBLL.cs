using System;
using System.Collections.Generic;
using System.Linq;
using AredooPOS.Models.Reports;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.BLL
{
    /// <summary>
    /// طبقة منطق الأعمال للتقارير
    /// تحتوي على جميع العمليات المتعلقة بإنشاء وتحليل التقارير
    /// </summary>
    public class ReportsBLL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly ReportsDAL _reportsDAL;
        private readonly ILogger<ReportsBLL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة منطق الأعمال للتقارير
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public ReportsBLL(string connectionString = null, ILogger<ReportsBLL> logger = null)
        {
            _reportsDAL = new ReportsDAL(connectionString, null);
            _logger = logger;
        }

        #endregion

        #region تقارير المبيعات

        /// <summary>
        /// إنشاء تقرير المبيعات
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="reportType">نوع التقرير</param>
        /// <param name="generatedBy">من أنشأ التقرير</param>
        /// <returns>تقرير المبيعات</returns>
        public SalesReport GenerateSalesReport(DateTime fromDate, DateTime toDate, string reportType, string generatedBy)
        {
            try
            {
                // التحقق من صحة المدخلات
                ValidateDateRange(fromDate, toDate);
                ValidateReportType(reportType, SalesReportTypes.Daily);

                _logger?.LogInformation($"بدء إنشاء تقرير المبيعات من {fromDate:yyyy-MM-dd} إلى {toDate:yyyy-MM-dd}");

                // الحصول على التقرير من قاعدة البيانات
                var report = _reportsDAL.GetSalesReport(fromDate, toDate, reportType);
                report.GeneratedBy = generatedBy;

                // إضافة التحليلات والحسابات الإضافية
                EnhanceSalesReport(report);

                _logger?.LogInformation($"تم إنشاء تقرير المبيعات بنجاح - إجمالي المبيعات: {report.TotalSales:C}");
                return report;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنشاء تقرير المبيعات");
                throw;
            }
        }

        /// <summary>
        /// تحسين تقرير المبيعات بالتحليلات الإضافية
        /// </summary>
        /// <param name="report">تقرير المبيعات</param>
        private void EnhanceSalesReport(SalesReport report)
        {
            // حساب الإجماليات
            report.CalculateTotals();

            // تحليل الاتجاهات
            if (report.DailySalesDetails?.Count >= 7)
            {
                AnalyzeSalesTrends(report);
            }

            // تصنيف المنتجات حسب الأداء
            if (report.ProductSalesDetails?.Any() == true)
            {
                ClassifyProductPerformance(report);
            }

            // حساب المؤشرات الإضافية
            CalculateAdditionalMetrics(report);
        }

        /// <summary>
        /// تحليل اتجاهات المبيعات
        /// </summary>
        /// <param name="report">تقرير المبيعات</param>
        private void AnalyzeSalesTrends(SalesReport report)
        {
            var dailyDetails = report.DailySalesDetails.OrderBy(d => d.SalesDate).ToList();
            
            // حساب معدل النمو
            for (int i = 1; i < dailyDetails.Count; i++)
            {
                var current = dailyDetails[i];
                var previous = dailyDetails[i - 1];
                
                if (previous.TotalSales > 0)
                {
                    var growthRate = ((current.TotalSales - previous.TotalSales) / previous.TotalSales) * 100;
                    // يمكن إضافة خاصية GrowthRate للـ DailySalesDetail
                }
            }
        }

        /// <summary>
        /// تصنيف أداء المنتجات
        /// </summary>
        /// <param name="report">تقرير المبيعات</param>
        private void ClassifyProductPerformance(SalesReport report)
        {
            var totalSales = report.ProductSalesDetails.Sum(p => p.TotalSales);
            
            foreach (var product in report.ProductSalesDetails)
            {
                var salesPercentage = totalSales > 0 ? (product.TotalSales / totalSales) * 100 : 0;
                
                // تصنيف المنتجات (A, B, C)
                if (salesPercentage >= 20)
                {
                    // منتج من الفئة A - عالي الأداء
                }
                else if (salesPercentage >= 5)
                {
                    // منتج من الفئة B - متوسط الأداء
                }
                else
                {
                    // منتج من الفئة C - منخفض الأداء
                }
            }
        }

        /// <summary>
        /// حساب المؤشرات الإضافية
        /// </summary>
        /// <param name="report">تقرير المبيعات</param>
        private void CalculateAdditionalMetrics(SalesReport report)
        {
            // حساب متوسط قيمة الفاتورة
            if (report.TotalInvoices > 0)
            {
                report.AverageInvoiceValue = report.TotalSales / report.TotalInvoices;
            }

            // حساب معدل التحويل (إذا توفرت بيانات الزوار)
            // يمكن إضافة هذه المؤشرات لاحقاً
        }

        #endregion

        #region تقارير المصاريف

        /// <summary>
        /// إنشاء تقرير المصاريف
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="reportType">نوع التقرير</param>
        /// <param name="generatedBy">من أنشأ التقرير</param>
        /// <returns>تقرير المصاريف</returns>
        public ExpenseReport GenerateExpenseReport(DateTime fromDate, DateTime toDate, string reportType, string generatedBy)
        {
            try
            {
                ValidateDateRange(fromDate, toDate);
                ValidateReportType(reportType, ExpenseReportTypes.Daily);

                _logger?.LogInformation($"بدء إنشاء تقرير المصاريف من {fromDate:yyyy-MM-dd} إلى {toDate:yyyy-MM-dd}");

                var report = _reportsDAL.GetExpenseReport(fromDate, toDate, reportType);
                report.GeneratedBy = generatedBy;

                // تحسين التقرير
                EnhanceExpenseReport(report);

                _logger?.LogInformation($"تم إنشاء تقرير المصاريف بنجاح - إجمالي المصاريف: {report.TotalExpenses:C}");
                return report;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنشاء تقرير المصاريف");
                throw;
            }
        }

        /// <summary>
        /// تحسين تقرير المصاريف
        /// </summary>
        /// <param name="report">تقرير المصاريف</param>
        private void EnhanceExpenseReport(ExpenseReport report)
        {
            report.CalculateTotals();

            // حساب النسب المئوية للفئات
            if (report.CategoryDetails?.Any() == true)
            {
                foreach (var category in report.CategoryDetails)
                {
                    category.Percentage = report.TotalExpenses > 0 ? (category.TotalAmount / report.TotalExpenses) * 100 : 0;
                }
            }

            // حساب المتوسطات
            if (report.ExpenseCount > 0)
            {
                report.AverageExpense = report.TotalExpenses / report.ExpenseCount;
            }
        }

        #endregion

        #region تقارير الأرباح

        /// <summary>
        /// إنشاء تقرير الأرباح
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="reportType">نوع التقرير</param>
        /// <param name="generatedBy">من أنشأ التقرير</param>
        /// <returns>تقرير الأرباح</returns>
        public ProfitReport GenerateProfitReport(DateTime fromDate, DateTime toDate, string reportType, string generatedBy)
        {
            try
            {
                ValidateDateRange(fromDate, toDate);
                ValidateReportType(reportType, ProfitReportTypes.Daily);

                _logger?.LogInformation($"بدء إنشاء تقرير الأرباح من {fromDate:yyyy-MM-dd} إلى {toDate:yyyy-MM-dd}");

                var report = _reportsDAL.GetProfitReport(fromDate, toDate, reportType);
                report.GeneratedBy = generatedBy;

                // تحسين التقرير
                EnhanceProfitReport(report);

                _logger?.LogInformation($"تم إنشاء تقرير الأرباح بنجاح - صافي الربح: {report.NetProfit:C}");
                return report;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنشاء تقرير الأرباح");
                throw;
            }
        }

        /// <summary>
        /// تحسين تقرير الأرباح
        /// </summary>
        /// <param name="report">تقرير الأرباح</param>
        private void EnhanceProfitReport(ProfitReport report)
        {
            // حساب جميع المؤشرات المالية
            // النسب محسوبة تلقائياً في النموذج

            // تحليل الربحية حسب المنتج
            if (report.ProductProfitDetails?.Any() == true)
            {
                AnalyzeProductProfitability(report);
            }
        }

        /// <summary>
        /// تحليل ربحية المنتجات
        /// </summary>
        /// <param name="report">تقرير الأرباح</param>
        private void AnalyzeProductProfitability(ProfitReport report)
        {
            var totalProfit = report.ProductProfitDetails.Sum(p => p.TotalProfit);
            
            // ترتيب المنتجات حسب الربحية
            var sortedProducts = report.ProductProfitDetails.OrderByDescending(p => p.TotalProfit).ToList();
            
            // تحديد المنتجات الأكثر ربحية (80/20 rule)
            var cumulativeProfit = 0m;
            var profitableProductsCount = 0;
            
            foreach (var product in sortedProducts)
            {
                cumulativeProfit += product.TotalProfit;
                profitableProductsCount++;
                
                if (cumulativeProfit >= totalProfit * 0.8m)
                    break;
            }
        }

        #endregion

        #region تقارير الأقساط

        /// <summary>
        /// إنشاء تقرير الأقساط
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="reportType">نوع التقرير</param>
        /// <param name="generatedBy">من أنشأ التقرير</param>
        /// <returns>تقرير الأقساط</returns>
        public InstallmentReport GenerateInstallmentReport(DateTime fromDate, DateTime toDate, string reportType, string generatedBy)
        {
            try
            {
                ValidateDateRange(fromDate, toDate);
                ValidateReportType(reportType, InstallmentReportTypes.Due);

                _logger?.LogInformation($"بدء إنشاء تقرير الأقساط من {fromDate:yyyy-MM-dd} إلى {toDate:yyyy-MM-dd}");

                var report = _reportsDAL.GetInstallmentReport(fromDate, toDate, reportType);
                report.GeneratedBy = generatedBy;

                // تحسين التقرير
                EnhanceInstallmentReport(report);

                _logger?.LogInformation($"تم إنشاء تقرير الأقساط بنجاح - إجمالي المستحق: {report.TotalOutstandingAmount:C}");
                return report;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنشاء تقرير الأقساط");
                throw;
            }
        }

        /// <summary>
        /// تحسين تقرير الأقساط
        /// </summary>
        /// <param name="report">تقرير الأقساط</param>
        private void EnhanceInstallmentReport(InstallmentReport report)
        {
            report.CalculateTotals();

            // تصنيف العملاء حسب المخاطر
            if (report.CustomerDetails?.Any() == true)
            {
                ClassifyCustomerRisk(report);
            }
        }

        /// <summary>
        /// تصنيف مخاطر العملاء
        /// </summary>
        /// <param name="report">تقرير الأقساط</param>
        private void ClassifyCustomerRisk(InstallmentReport report)
        {
            foreach (var customer in report.CustomerDetails)
            {
                // تصنيف المخاطر بناءً على عدة عوامل
                if (customer.DaysOverdue > 90 || customer.OverdueInstallments > 3)
                {
                    customer.RiskLevel = RiskLevels.High;
                }
                else if (customer.DaysOverdue > 30 || customer.OverdueInstallments > 1)
                {
                    customer.RiskLevel = RiskLevels.Medium;
                }
                else
                {
                    customer.RiskLevel = RiskLevels.Low;
                }
            }
        }

        #endregion

        #region تقارير الديون

        /// <summary>
        /// إنشاء تقرير الديون
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="reportType">نوع التقرير</param>
        /// <param name="generatedBy">من أنشأ التقرير</param>
        /// <returns>تقرير الديون</returns>
        public DebtReport GenerateDebtReport(DateTime fromDate, DateTime toDate, string reportType, string generatedBy)
        {
            try
            {
                ValidateDateRange(fromDate, toDate);
                ValidateReportType(reportType, DebtReportTypes.Outstanding);

                _logger?.LogInformation($"بدء إنشاء تقرير الديون من {fromDate:yyyy-MM-dd} إلى {toDate:yyyy-MM-dd}");

                var report = _reportsDAL.GetDebtReport(fromDate, toDate, reportType);
                report.GeneratedBy = generatedBy;

                // تحسين التقرير
                EnhanceDebtReport(report);

                _logger?.LogInformation($"تم إنشاء تقرير الديون بنجاح - إجمالي المستحق: {report.TotalOutstandingAmount:C}");
                return report;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنشاء تقرير الديون");
                throw;
            }
        }

        /// <summary>
        /// تحسين تقرير الديون
        /// </summary>
        /// <param name="report">تقرير الديون</param>
        private void EnhanceDebtReport(DebtReport report)
        {
            report.CalculateTotals();
        }

        #endregion

        #region تقارير حركة الصندوق

        /// <summary>
        /// إنشاء تقرير حركة الصندوق
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        /// <param name="cashRegisterID">رقم الصندوق</param>
        /// <param name="reportType">نوع التقرير</param>
        /// <param name="generatedBy">من أنشأ التقرير</param>
        /// <returns>تقرير حركة الصندوق</returns>
        public CashFlowReport GenerateCashFlowReport(DateTime fromDate, DateTime toDate, int? cashRegisterID, string reportType, string generatedBy)
        {
            try
            {
                ValidateDateRange(fromDate, toDate);
                ValidateReportType(reportType, CashFlowReportTypes.Daily);

                _logger?.LogInformation($"بدء إنشاء تقرير حركة الصندوق من {fromDate:yyyy-MM-dd} إلى {toDate:yyyy-MM-dd}");

                var report = _reportsDAL.GetCashFlowReport(fromDate, toDate, cashRegisterID, reportType);
                report.GeneratedBy = generatedBy;

                // تحسين التقرير
                EnhanceCashFlowReport(report);

                _logger?.LogInformation($"تم إنشاء تقرير حركة الصندوق بنجاح - صافي التدفق: {report.NetCashFlow:C}");
                return report;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنشاء تقرير حركة الصندوق");
                throw;
            }
        }

        /// <summary>
        /// تحسين تقرير حركة الصندوق
        /// </summary>
        /// <param name="report">تقرير حركة الصندوق</param>
        private void EnhanceCashFlowReport(CashFlowReport report)
        {
            report.CalculateTotals();
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// التحقق من صحة نطاق التاريخ
        /// </summary>
        /// <param name="fromDate">من تاريخ</param>
        /// <param name="toDate">إلى تاريخ</param>
        private void ValidateDateRange(DateTime fromDate, DateTime toDate)
        {
            if (fromDate > toDate)
                throw new ArgumentException("تاريخ البداية يجب أن يكون أقل من أو يساوي تاريخ النهاية");

            if (fromDate > DateTime.Now)
                throw new ArgumentException("تاريخ البداية لا يمكن أن يكون في المستقبل");

            var daysDifference = (toDate - fromDate).Days;
            if (daysDifference > 365)
                throw new ArgumentException("نطاق التقرير لا يمكن أن يتجاوز سنة واحدة");
        }

        /// <summary>
        /// التحقق من صحة نوع التقرير
        /// </summary>
        /// <param name="reportType">نوع التقرير</param>
        /// <param name="defaultType">النوع الافتراضي</param>
        private void ValidateReportType(string reportType, string defaultType)
        {
            if (string.IsNullOrWhiteSpace(reportType))
                throw new ArgumentException("نوع التقرير مطلوب");
        }

        #endregion
    }
}
