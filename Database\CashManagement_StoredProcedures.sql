-- =============================================
-- نظام إدارة النقدية والصندوق - الإجراءات المخزنة
-- تاريخ الإنشاء: 2024-07-11
-- الوصف: إجراءات مخزنة شاملة لإدارة الصناديق والجلسات والمعاملات
-- =============================================

USE AredooPOS;
GO

-- =============================================
-- إجراء فتح جلسة صندوق جديدة
-- =============================================
IF OBJECT_ID('sp_OpenCashSession', 'P') IS NOT NULL
    DROP PROCEDURE sp_OpenCashSession;
GO

CREATE PROCEDURE sp_OpenCashSession
    @CashRegisterID INT,
    @UserID INT,
    @OpeningBalance DECIMAL(18,2),
    @CreatedBy NVARCHAR(50),
    @NewSessionID INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- التحقق من عدم وجود جلسة مفتوحة للصندوق
        IF EXISTS (SELECT 1 FROM CashSessions 
                   WHERE CashRegisterID = @CashRegisterID AND Status = 'Open')
        BEGIN
            RAISERROR('يوجد جلسة مفتوحة بالفعل لهذا الصندوق', 16, 1);
            RETURN;
        END
        
        -- التحقق من صحة الصندوق
        IF NOT EXISTS (SELECT 1 FROM CashRegisters 
                       WHERE CashRegisterID = @CashRegisterID AND IsActive = 1)
        BEGIN
            RAISERROR('الصندوق غير موجود أو غير نشط', 16, 1);
            RETURN;
        END
        
        -- إنشاء جلسة جديدة
        INSERT INTO CashSessions (
            CashRegisterID, UserID, SessionDate, OpenTime, Status,
            OpeningBalance, ExpectedClosingBalance, CreatedBy
        )
        VALUES (
            @CashRegisterID, @UserID, CAST(GETDATE() AS DATE), GETDATE(), 'Open',
            @OpeningBalance, @OpeningBalance, @CreatedBy
        );
        
        SET @NewSessionID = SCOPE_IDENTITY();
        
        -- تحديث آخر جلسة في الصندوق
        UPDATE CashRegisters 
        SET LastSessionID = @NewSessionID,
            CurrentBalance = @OpeningBalance,
            LastTransactionDate = GETDATE(),
            ModifiedBy = @CreatedBy,
            ModifiedDate = GETDATE()
        WHERE CashRegisterID = @CashRegisterID;
        
        -- إضافة معاملة الرصيد الافتتاحي
        IF @OpeningBalance > 0
        BEGIN
            INSERT INTO CashTransactions (
                SessionID, TransactionNumber, TransactionType, Amount,
                PaymentMethodID, Description, UserID, CreatedBy
            )
            VALUES (
                @NewSessionID, dbo.GenerateTransactionNumber(), 'Opening', @OpeningBalance,
                (SELECT PaymentMethodID FROM PaymentMethods WHERE MethodCode = 'CASH'),
                N'الرصيد الافتتاحي للجلسة', @UserID, @CreatedBy
            );
        END
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- =============================================
-- إجراء إغلاق جلسة الصندوق
-- =============================================
IF OBJECT_ID('sp_CloseCashSession', 'P') IS NOT NULL
    DROP PROCEDURE sp_CloseCashSession;
GO

CREATE PROCEDURE sp_CloseCashSession
    @SessionID INT,
    @ClosingBalance DECIMAL(18,2),
    @VarianceReason NVARCHAR(255) = NULL,
    @Notes NVARCHAR(500) = NULL,
    @ClosedBy NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        DECLARE @ExpectedBalance DECIMAL(18,2);
        DECLARE @Variance DECIMAL(18,2);
        DECLARE @CashRegisterID INT;
        
        -- التحقق من وجود الجلسة وأنها مفتوحة
        SELECT @ExpectedBalance = ExpectedClosingBalance,
               @CashRegisterID = CashRegisterID
        FROM CashSessions 
        WHERE SessionID = @SessionID AND Status = 'Open';
        
        IF @ExpectedBalance IS NULL
        BEGIN
            RAISERROR('الجلسة غير موجودة أو مغلقة بالفعل', 16, 1);
            RETURN;
        END
        
        -- حساب الفرق
        SET @Variance = @ClosingBalance - @ExpectedBalance;
        
        -- تحديث الجلسة
        UPDATE CashSessions 
        SET CloseTime = GETDATE(),
            Status = 'Closed',
            ClosingBalance = @ClosingBalance,
            Variance = @Variance,
            VarianceReason = @VarianceReason,
            Notes = @Notes,
            ClosedBy = @ClosedBy,
            ModifiedBy = @ClosedBy,
            ModifiedDate = GETDATE()
        WHERE SessionID = @SessionID;
        
        -- تحديث رصيد الصندوق
        UPDATE CashRegisters 
        SET CurrentBalance = @ClosingBalance,
            LastTransactionDate = GETDATE(),
            ModifiedBy = @ClosedBy,
            ModifiedDate = GETDATE()
        WHERE CashRegisterID = @CashRegisterID;
        
        -- إضافة معاملة الرصيد الختامي إذا كان هناك فرق
        IF @Variance <> 0
        BEGIN
            INSERT INTO CashTransactions (
                SessionID, TransactionNumber, TransactionType, Amount,
                PaymentMethodID, Description, UserID, CreatedBy
            )
            VALUES (
                @SessionID, dbo.GenerateTransactionNumber(), 'Adjustment', @Variance,
                (SELECT PaymentMethodID FROM PaymentMethods WHERE MethodCode = 'CASH'),
                N'تسوية الرصيد الختامي - ' + ISNULL(@VarianceReason, N'غير محدد'),
                (SELECT UserID FROM CashSessions WHERE SessionID = @SessionID),
                @ClosedBy
            );
        END
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- =============================================
-- إجراء إضافة معاملة نقدية
-- =============================================
IF OBJECT_ID('sp_AddCashTransaction', 'P') IS NOT NULL
    DROP PROCEDURE sp_AddCashTransaction;
GO

CREATE PROCEDURE sp_AddCashTransaction
    @SessionID INT,
    @TransactionType NVARCHAR(20),
    @Amount DECIMAL(18,2),
    @PaymentMethodID INT,
    @ReferenceNumber NVARCHAR(100) = NULL,
    @Description NVARCHAR(255) = NULL,
    @Category NVARCHAR(50) = NULL,
    @RelatedDocumentType NVARCHAR(20) = NULL,
    @RelatedDocumentID INT = NULL,
    @CustomerID INT = NULL,
    @SupplierID INT = NULL,
    @UserID INT,
    @CreatedBy NVARCHAR(50),
    @NewTransactionID INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        DECLARE @CashRegisterID INT;
        DECLARE @SessionStatus NVARCHAR(20);
        
        -- التحقق من صحة الجلسة
        SELECT @CashRegisterID = CashRegisterID, @SessionStatus = Status
        FROM CashSessions 
        WHERE SessionID = @SessionID;
        
        IF @CashRegisterID IS NULL
        BEGIN
            RAISERROR('الجلسة غير موجودة', 16, 1);
            RETURN;
        END
        
        IF @SessionStatus <> 'Open'
        BEGIN
            RAISERROR('الجلسة غير مفتوحة', 16, 1);
            RETURN;
        END
        
        -- التحقق من صحة طريقة الدفع
        IF NOT EXISTS (SELECT 1 FROM PaymentMethods 
                       WHERE PaymentMethodID = @PaymentMethodID AND IsActive = 1)
        BEGIN
            RAISERROR('طريقة الدفع غير صحيحة أو غير نشطة', 16, 1);
            RETURN;
        END
        
        -- إضافة المعاملة
        INSERT INTO CashTransactions (
            SessionID, TransactionNumber, TransactionType, Amount,
            PaymentMethodID, ReferenceNumber, Description, Category,
            RelatedDocumentType, RelatedDocumentID, CustomerID, SupplierID,
            UserID, CreatedBy
        )
        VALUES (
            @SessionID, dbo.GenerateTransactionNumber(), @TransactionType, @Amount,
            @PaymentMethodID, @ReferenceNumber, @Description, @Category,
            @RelatedDocumentType, @RelatedDocumentID, @CustomerID, @SupplierID,
            @UserID, @CreatedBy
        );
        
        SET @NewTransactionID = SCOPE_IDENTITY();
        
        -- تحديث إجماليات الجلسة
        EXEC sp_UpdateSessionTotals @SessionID;
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- =============================================
-- إجراء تحديث إجماليات الجلسة
-- =============================================
IF OBJECT_ID('sp_UpdateSessionTotals', 'P') IS NOT NULL
    DROP PROCEDURE sp_UpdateSessionTotals;
GO

CREATE PROCEDURE sp_UpdateSessionTotals
    @SessionID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @OpeningBalance DECIMAL(18,2);
    DECLARE @CashSales DECIMAL(18,2) = 0;
    DECLARE @CardSales DECIMAL(18,2) = 0;
    DECLARE @TransferSales DECIMAL(18,2) = 0;
    DECLARE @TotalSales DECIMAL(18,2) = 0;
    DECLARE @CashExpenses DECIMAL(18,2) = 0;
    DECLARE @CashWithdrawals DECIMAL(18,2) = 0;
    DECLARE @CashDeposits DECIMAL(18,2) = 0;
    DECLARE @TotalTransactions INT = 0;
    DECLARE @ExpectedClosingBalance DECIMAL(18,2);
    
    -- الحصول على الرصيد الافتتاحي
    SELECT @OpeningBalance = OpeningBalance
    FROM CashSessions 
    WHERE SessionID = @SessionID;
    
    -- حساب المبيعات النقدية
    SELECT @CashSales = ISNULL(SUM(ct.Amount), 0)
    FROM CashTransactions ct
    INNER JOIN PaymentMethods pm ON ct.PaymentMethodID = pm.PaymentMethodID
    WHERE ct.SessionID = @SessionID 
      AND ct.TransactionType IN ('Sale') 
      AND pm.MethodCode = 'CASH'
      AND ct.IsVoided = 0;
    
    -- حساب المبيعات بالبطاقة
    SELECT @CardSales = ISNULL(SUM(ct.Amount), 0)
    FROM CashTransactions ct
    INNER JOIN PaymentMethods pm ON ct.PaymentMethodID = pm.PaymentMethodID
    WHERE ct.SessionID = @SessionID 
      AND ct.TransactionType IN ('Sale') 
      AND pm.MethodCode = 'CARD'
      AND ct.IsVoided = 0;
    
    -- حساب المبيعات بالتحويل
    SELECT @TransferSales = ISNULL(SUM(ct.Amount), 0)
    FROM CashTransactions ct
    INNER JOIN PaymentMethods pm ON ct.PaymentMethodID = pm.PaymentMethodID
    WHERE ct.SessionID = @SessionID 
      AND ct.TransactionType IN ('Sale') 
      AND pm.MethodCode = 'TRANSFER'
      AND ct.IsVoided = 0;
    
    -- حساب إجمالي المبيعات
    SET @TotalSales = @CashSales + @CardSales + @TransferSales;
    
    -- حساب المصاريف النقدية
    SELECT @CashExpenses = ISNULL(SUM(ABS(ct.Amount)), 0)
    FROM CashTransactions ct
    INNER JOIN PaymentMethods pm ON ct.PaymentMethodID = pm.PaymentMethodID
    WHERE ct.SessionID = @SessionID 
      AND ct.TransactionType = 'Expense' 
      AND pm.MethodCode = 'CASH'
      AND ct.IsVoided = 0;
    
    -- حساب السحوبات النقدية
    SELECT @CashWithdrawals = ISNULL(SUM(ABS(ct.Amount)), 0)
    FROM CashTransactions ct
    INNER JOIN PaymentMethods pm ON ct.PaymentMethodID = pm.PaymentMethodID
    WHERE ct.SessionID = @SessionID 
      AND ct.TransactionType = 'Withdrawal' 
      AND pm.MethodCode = 'CASH'
      AND ct.IsVoided = 0;
    
    -- حساب الإيداعات النقدية
    SELECT @CashDeposits = ISNULL(SUM(ct.Amount), 0)
    FROM CashTransactions ct
    INNER JOIN PaymentMethods pm ON ct.PaymentMethodID = pm.PaymentMethodID
    WHERE ct.SessionID = @SessionID 
      AND ct.TransactionType = 'Deposit' 
      AND pm.MethodCode = 'CASH'
      AND ct.IsVoided = 0;
    
    -- حساب عدد المعاملات
    SELECT @TotalTransactions = COUNT(*)
    FROM CashTransactions 
    WHERE SessionID = @SessionID AND IsVoided = 0;
    
    -- حساب الرصيد المتوقع
    SET @ExpectedClosingBalance = @OpeningBalance + @CashSales - @CashExpenses - @CashWithdrawals + @CashDeposits;
    
    -- تحديث الجلسة
    UPDATE CashSessions 
    SET CashSales = @CashSales,
        CardSales = @CardSales,
        TransferSales = @TransferSales,
        TotalSales = @TotalSales,
        CashExpenses = @CashExpenses,
        CashWithdrawals = @CashWithdrawals,
        CashDeposits = @CashDeposits,
        TotalTransactions = @TotalTransactions,
        ExpectedClosingBalance = @ExpectedClosingBalance,
        ModifiedDate = GETDATE()
    WHERE SessionID = @SessionID;
END
GO

-- =============================================
-- إجراء إلغاء معاملة
-- =============================================
IF OBJECT_ID('sp_VoidCashTransaction', 'P') IS NOT NULL
    DROP PROCEDURE sp_VoidCashTransaction;
GO

CREATE PROCEDURE sp_VoidCashTransaction
    @TransactionID INT,
    @VoidReason NVARCHAR(255),
    @VoidedBy NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        DECLARE @SessionID INT;
        DECLARE @IsVoided BIT;
        
        -- التحقق من وجود المعاملة
        SELECT @SessionID = SessionID, @IsVoided = IsVoided
        FROM CashTransactions 
        WHERE TransactionID = @TransactionID;
        
        IF @SessionID IS NULL
        BEGIN
            RAISERROR('المعاملة غير موجودة', 16, 1);
            RETURN;
        END
        
        IF @IsVoided = 1
        BEGIN
            RAISERROR('المعاملة ملغاة مسبقاً', 16, 1);
            RETURN;
        END
        
        -- التحقق من أن الجلسة مفتوحة
        IF NOT EXISTS (SELECT 1 FROM CashSessions 
                       WHERE SessionID = @SessionID AND Status = 'Open')
        BEGIN
            RAISERROR('لا يمكن إلغاء معاملة في جلسة مغلقة', 16, 1);
            RETURN;
        END
        
        -- إلغاء المعاملة
        UPDATE CashTransactions 
        SET IsVoided = 1,
            VoidedBy = @VoidedBy,
            VoidedDate = GETDATE(),
            VoidReason = @VoidReason,
            ModifiedBy = @VoidedBy,
            ModifiedDate = GETDATE()
        WHERE TransactionID = @TransactionID;
        
        -- تحديث إجماليات الجلسة
        EXEC sp_UpdateSessionTotals @SessionID;
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- =============================================
-- إجراء الحصول على ملخص الجلسة
-- =============================================
IF OBJECT_ID('sp_GetSessionSummary', 'P') IS NOT NULL
    DROP PROCEDURE sp_GetSessionSummary;
GO

CREATE PROCEDURE sp_GetSessionSummary
    @SessionID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    -- معلومات الجلسة الأساسية
    SELECT * FROM vw_CashSessionSummary
    WHERE SessionID = @SessionID;
    
    -- ملخص المعاملات حسب النوع
    SELECT 
        ct.TransactionType,
        pm.MethodNameArabic AS PaymentMethod,
        COUNT(*) AS TransactionCount,
        SUM(ct.Amount) AS TotalAmount
    FROM CashTransactions ct
    INNER JOIN PaymentMethods pm ON ct.PaymentMethodID = pm.PaymentMethodID
    WHERE ct.SessionID = @SessionID AND ct.IsVoided = 0
    GROUP BY ct.TransactionType, pm.MethodNameArabic, pm.SortOrder
    ORDER BY ct.TransactionType, pm.SortOrder;
    
    -- تفاصيل أرصدة العملات
    SELECT 
        DenominationType,
        Value,
        Quantity,
        TotalAmount,
        CountType
    FROM CashDenominations
    WHERE SessionID = @SessionID
    ORDER BY CountType, DenominationType DESC, Value DESC;
END
GO

-- =============================================
-- إجراء الحصول على تقرير يومي
-- =============================================
IF OBJECT_ID('sp_GetDailyReport', 'P') IS NOT NULL
    DROP PROCEDURE sp_GetDailyReport;
GO

CREATE PROCEDURE sp_GetDailyReport
    @ReportDate DATE,
    @CashRegisterID INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    -- ملخص الجلسات اليومية
    SELECT * FROM vw_CashSessionSummary
    WHERE SessionDate = @ReportDate
      AND (@CashRegisterID IS NULL OR CashRegisterID = @CashRegisterID)
    ORDER BY OpenTime;
    
    -- ملخص المبيعات حسب طريقة الدفع
    SELECT 
        pm.MethodNameArabic AS PaymentMethod,
        COUNT(*) AS TransactionCount,
        SUM(ct.Amount) AS TotalAmount
    FROM CashTransactions ct
    INNER JOIN CashSessions cs ON ct.SessionID = cs.SessionID
    INNER JOIN PaymentMethods pm ON ct.PaymentMethodID = pm.PaymentMethodID
    WHERE cs.SessionDate = @ReportDate
      AND ct.TransactionType = 'Sale'
      AND ct.IsVoided = 0
      AND (@CashRegisterID IS NULL OR cs.CashRegisterID = @CashRegisterID)
    GROUP BY pm.MethodNameArabic, pm.SortOrder
    ORDER BY pm.SortOrder;
    
    -- ملخص المصاريف والسحوبات
    SELECT 
        ct.TransactionType,
        ct.Category,
        COUNT(*) AS TransactionCount,
        SUM(ABS(ct.Amount)) AS TotalAmount
    FROM CashTransactions ct
    INNER JOIN CashSessions cs ON ct.SessionID = cs.SessionID
    WHERE cs.SessionDate = @ReportDate
      AND ct.TransactionType IN ('Expense', 'Withdrawal')
      AND ct.IsVoided = 0
      AND (@CashRegisterID IS NULL OR cs.CashRegisterID = @CashRegisterID)
    GROUP BY ct.TransactionType, ct.Category
    ORDER BY ct.TransactionType, ct.Category;
END
GO

PRINT '==============================================';
PRINT 'تم إنشاء جميع الإجراءات المخزنة لنظام إدارة النقدية والصندوق بنجاح';
PRINT 'الإجراءات المُنشأة:';
PRINT '- sp_OpenCashSession: فتح جلسة صندوق';
PRINT '- sp_CloseCashSession: إغلاق جلسة صندوق';
PRINT '- sp_AddCashTransaction: إضافة معاملة نقدية';
PRINT '- sp_UpdateSessionTotals: تحديث إجماليات الجلسة';
PRINT '- sp_VoidCashTransaction: إلغاء معاملة';
PRINT '- sp_GetSessionSummary: ملخص الجلسة';
PRINT '- sp_GetDailyReport: التقرير اليومي';
PRINT '==============================================';
