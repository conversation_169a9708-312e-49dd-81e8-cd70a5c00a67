using System;
using System.ComponentModel.DataAnnotations;

namespace AridooPOS.Models
{
    /// <summary>
    /// نموذج الدفعة
    /// </summary>
    public class Payment
    {
        public int PaymentID { get; set; }
        
        [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
        public int InvoiceID { get; set; }
        
        [Required(ErrorMessage = "تاريخ الدفع مطلوب")]
        public DateTime PaymentDate { get; set; }
        
        [Required(ErrorMessage = "نوع الدفع مطلوب")]
        [StringLength(20, ErrorMessage = "نوع الدفع لا يجب أن يتجاوز 20 حرف")]
        public string PaymentType { get; set; }
        
        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }
        
        [StringLength(50, ErrorMessage = "المرجع لا يجب أن يتجاوز 50 حرف")]
        public string Reference { get; set; }
        
        [StringLength(200, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 200 حرف")]
        public string Notes { get; set; }
        
        [StringLength(50, ErrorMessage = "اسم المنشئ لا يجب أن يتجاوز 50 حرف")]
        public string CreatedBy { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public Payment()
        {
            PaymentDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            PaymentType = PaymentTypes.Cash;
        }
        
        /// <summary>
        /// التحقق من صحة بيانات الدفعة
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return InvoiceID > 0 &&
                   !string.IsNullOrEmpty(PaymentType) &&
                   Amount > 0;
        }
    }
    
    /// <summary>
    /// نموذج القسط
    /// </summary>
    public class Installment
    {
        public int InstallmentID { get; set; }
        
        [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
        public int InvoiceID { get; set; }
        
        [Required(ErrorMessage = "رقم القسط مطلوب")]
        [Range(1, int.MaxValue, ErrorMessage = "رقم القسط يجب أن يكون أكبر من صفر")]
        public int InstallmentNumber { get; set; }
        
        [Required(ErrorMessage = "تاريخ الاستحقاق مطلوب")]
        public DateTime DueDate { get; set; }
        
        [Required(ErrorMessage = "مبلغ القسط مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "مبلغ القسط يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "المبلغ المدفوع يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal PaidAmount { get; set; }
        
        public decimal RemainingAmount { get; set; }
        
        [StringLength(20, ErrorMessage = "حالة القسط لا يجب أن تتجاوز 20 حرف")]
        public string Status { get; set; }
        
        public DateTime? PaidDate { get; set; }
        
        [StringLength(200, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 200 حرف")]
        public string Notes { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public Installment()
        {
            CreatedDate = DateTime.Now;
            Status = InstallmentStatuses.Due;
            PaidAmount = 0;
        }
        
        /// <summary>
        /// تحديث حالة القسط
        /// </summary>
        public void UpdateStatus()
        {
            RemainingAmount = Amount - PaidAmount;
            
            if (RemainingAmount <= 0)
            {
                Status = InstallmentStatuses.Paid;
                if (PaidDate == null)
                    PaidDate = DateTime.Now;
            }
            else if (DateTime.Now > DueDate)
            {
                Status = InstallmentStatuses.Overdue;
            }
            else
            {
                Status = InstallmentStatuses.Due;
            }
        }
        
        /// <summary>
        /// دفع مبلغ من القسط
        /// </summary>
        /// <param name="amount">المبلغ المدفوع</param>
        public void PayAmount(decimal amount)
        {
            PaidAmount += amount;
            UpdateStatus();
        }
        
        /// <summary>
        /// التحقق من صحة بيانات القسط
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return InvoiceID > 0 &&
                   InstallmentNumber > 0 &&
                   Amount > 0 &&
                   DueDate > DateTime.MinValue;
        }
        
        /// <summary>
        /// التحقق من تأخر القسط
        /// </summary>
        /// <returns>true إذا كان القسط متأخر</returns>
        public bool IsOverdue()
        {
            return DateTime.Now > DueDate && RemainingAmount > 0;
        }
        
        /// <summary>
        /// عدد الأيام المتأخرة
        /// </summary>
        /// <returns>عدد الأيام</returns>
        public int GetOverdueDays()
        {
            if (!IsOverdue()) return 0;
            return (DateTime.Now - DueDate).Days;
        }
    }
    
    /// <summary>
    /// حالات الأقساط
    /// </summary>
    public static class InstallmentStatuses
    {
        public const string Due = "مستحق";
        public const string Paid = "مدفوع";
        public const string Overdue = "متأخر";
    }
}
