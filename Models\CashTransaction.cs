using System;
using System.ComponentModel.DataAnnotations;

namespace AredooPOS.Models
{
    /// <summary>
    /// نموذج المعاملة النقدية
    /// يمثل معاملة واحدة في الصندوق
    /// </summary>
    public class CashTransaction
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم المعاملة
        /// </summary>
        public int TransactionID { get; set; }

        /// <summary>
        /// رقم الجلسة
        /// </summary>
        [Required(ErrorMessage = "رقم الجلسة مطلوب")]
        public int SessionID { get; set; }

        /// <summary>
        /// رقم المعاملة المرجعي
        /// </summary>
        [Required(ErrorMessage = "رقم المعاملة مطلوب")]
        [StringLength(50)]
        public string TransactionNumber { get; set; }

        /// <summary>
        /// نوع المعاملة
        /// </summary>
        [Required(ErrorMessage = "نوع المعاملة مطلوب")]
        [StringLength(20)]
        public string TransactionType { get; set; }

        /// <summary>
        /// تاريخ ووقت المعاملة
        /// </summary>
        [Required(ErrorMessage = "تاريخ المعاملة مطلوب")]
        public DateTime TransactionDate { get; set; }

        /// <summary>
        /// مبلغ المعاملة
        /// </summary>
        [Required(ErrorMessage = "مبلغ المعاملة مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "مبلغ المعاملة يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        /// <summary>
        /// رقم طريقة الدفع
        /// </summary>
        [Required(ErrorMessage = "طريقة الدفع مطلوبة")]
        public int PaymentMethodID { get; set; }

        /// <summary>
        /// الرقم المرجعي (للبطاقات والتحويلات)
        /// </summary>
        [StringLength(100)]
        public string ReferenceNumber { get; set; }

        /// <summary>
        /// وصف المعاملة
        /// </summary>
        [StringLength(255)]
        public string Description { get; set; }

        /// <summary>
        /// فئة المعاملة
        /// </summary>
        [StringLength(50)]
        public string Category { get; set; }

        #endregion

        #region المعلومات المرتبطة

        /// <summary>
        /// نوع المستند المرتبط
        /// </summary>
        [StringLength(20)]
        public string RelatedDocumentType { get; set; }

        /// <summary>
        /// رقم المستند المرتبط
        /// </summary>
        public int? RelatedDocumentID { get; set; }

        /// <summary>
        /// رقم العميل
        /// </summary>
        public int? CustomerID { get; set; }

        /// <summary>
        /// رقم المورد
        /// </summary>
        public int? SupplierID { get; set; }

        /// <summary>
        /// رقم المستخدم الذي أجرى المعاملة
        /// </summary>
        [Required(ErrorMessage = "رقم المستخدم مطلوب")]
        public int UserID { get; set; }

        #endregion

        #region معلومات الإلغاء

        /// <summary>
        /// هل المعاملة ملغاة
        /// </summary>
        public bool IsVoided { get; set; } = false;

        /// <summary>
        /// من ألغى المعاملة
        /// </summary>
        [StringLength(50)]
        public string VoidedBy { get; set; }

        /// <summary>
        /// تاريخ الإلغاء
        /// </summary>
        public DateTime? VoidedDate { get; set; }

        /// <summary>
        /// سبب الإلغاء
        /// </summary>
        [StringLength(255)]
        public string VoidReason { get; set; }

        #endregion

        #region معلومات الموافقة

        /// <summary>
        /// هل تتطلب موافقة
        /// </summary>
        public bool RequiresApproval { get; set; } = false;

        /// <summary>
        /// هل تمت الموافقة
        /// </summary>
        public bool IsApproved { get; set; } = true;

        /// <summary>
        /// من وافق على المعاملة
        /// </summary>
        [StringLength(50)]
        public string ApprovedBy { get; set; }

        /// <summary>
        /// تاريخ الموافقة
        /// </summary>
        public DateTime? ApprovalDate { get; set; }

        #endregion

        #region معلومات إضافية

        /// <summary>
        /// ملاحظات
        /// </summary>
        [StringLength(500)]
        public string Notes { get; set; }

        #endregion

        #region معلومات النظام

        /// <summary>
        /// من أنشأ السجل
        /// </summary>
        [Required]
        [StringLength(50)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// من عدل السجل
        /// </summary>
        [StringLength(50)]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// تاريخ التعديل
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// هل المعاملة صالحة (غير ملغاة)
        /// </summary>
        public bool IsValid => !IsVoided;

        /// <summary>
        /// هل المعاملة مبيعات
        /// </summary>
        public bool IsSale => TransactionType == CashTransactionTypes.Sale;

        /// <summary>
        /// هل المعاملة مرتجع
        /// </summary>
        public bool IsReturn => TransactionType == CashTransactionTypes.Return;

        /// <summary>
        /// هل المعاملة مصروف
        /// </summary>
        public bool IsExpense => TransactionType == CashTransactionTypes.Expense;

        /// <summary>
        /// هل المعاملة سحب
        /// </summary>
        public bool IsWithdrawal => TransactionType == CashTransactionTypes.Withdrawal;

        /// <summary>
        /// هل المعاملة إيداع
        /// </summary>
        public bool IsDeposit => TransactionType == CashTransactionTypes.Deposit;

        /// <summary>
        /// هل المعاملة تؤثر على الرصيد النقدي
        /// </summary>
        public bool AffectsCashBalance => IsSale || IsReturn || IsExpense || IsWithdrawal || IsDeposit;

        /// <summary>
        /// المبلغ مع الإشارة (موجب للإيرادات، سالب للمصاريف)
        /// </summary>
        public decimal SignedAmount
        {
            get
            {
                if (IsExpense || IsWithdrawal || IsReturn)
                    return -Math.Abs(Amount);
                return Math.Abs(Amount);
            }
        }

        /// <summary>
        /// نص نوع المعاملة بالعربية
        /// </summary>
        public string TransactionTypeArabic
        {
            get
            {
                return TransactionType switch
                {
                    CashTransactionTypes.Sale => "مبيعات",
                    CashTransactionTypes.Return => "مرتجع",
                    CashTransactionTypes.Expense => "مصروف",
                    CashTransactionTypes.Withdrawal => "سحب",
                    CashTransactionTypes.Deposit => "إيداع",
                    CashTransactionTypes.Opening => "رصيد افتتاحي",
                    CashTransactionTypes.Closing => "رصيد ختامي",
                    CashTransactionTypes.Adjustment => "تسوية",
                    _ => TransactionType
                };
            }
        }

        /// <summary>
        /// هل المعاملة تحتاج موافقة ولم تتم الموافقة عليها
        /// </summary>
        public bool IsPendingApproval => RequiresApproval && !IsApproved;

        /// <summary>
        /// عمر المعاملة بالدقائق
        /// </summary>
        public int AgeInMinutes => (int)(DateTime.Now - TransactionDate).TotalMinutes;

        /// <summary>
        /// هل المعاملة حديثة (أقل من ساعة)
        /// </summary>
        public bool IsRecent => AgeInMinutes < 60;

        /// <summary>
        /// هل يمكن إلغاء المعاملة
        /// </summary>
        public bool CanBeVoided => !IsVoided && IsRecent && IsValid;

        #endregion

        #region الدوال المساعدة

        /// <summary>
        /// منشئ افتراضي
        /// </summary>
        public CashTransaction()
        {
            TransactionDate = DateTime.Now;
        }

        /// <summary>
        /// منشئ مع المعاملات الأساسية
        /// </summary>
        /// <param name="sessionID">رقم الجلسة</param>
        /// <param name="transactionType">نوع المعاملة</param>
        /// <param name="amount">المبلغ</param>
        /// <param name="paymentMethodID">طريقة الدفع</param>
        /// <param name="userID">رقم المستخدم</param>
        /// <param name="createdBy">من أنشأ المعاملة</param>
        public CashTransaction(int sessionID, string transactionType, decimal amount, 
            int paymentMethodID, int userID, string createdBy)
        {
            SessionID = sessionID;
            TransactionType = transactionType;
            Amount = amount;
            PaymentMethodID = paymentMethodID;
            UserID = userID;
            CreatedBy = createdBy;
            TransactionDate = DateTime.Now;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValidData()
        {
            if (SessionID <= 0)
                return false;

            if (string.IsNullOrWhiteSpace(TransactionType))
                return false;

            if (Amount <= 0)
                return false;

            if (PaymentMethodID <= 0)
                return false;

            if (UserID <= 0)
                return false;

            if (string.IsNullOrWhiteSpace(CreatedBy))
                return false;

            return true;
        }

        /// <summary>
        /// إلغاء المعاملة
        /// </summary>
        /// <param name="reason">سبب الإلغاء</param>
        /// <param name="voidedBy">من ألغى المعاملة</param>
        public void Void(string reason, string voidedBy)
        {
            if (IsVoided)
                throw new InvalidOperationException("المعاملة ملغاة مسبقاً");

            IsVoided = true;
            VoidReason = reason;
            VoidedBy = voidedBy;
            VoidedDate = DateTime.Now;
            ModifiedBy = voidedBy;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// الموافقة على المعاملة
        /// </summary>
        /// <param name="approvedBy">من وافق</param>
        public void Approve(string approvedBy)
        {
            if (IsApproved)
                throw new InvalidOperationException("المعاملة موافق عليها مسبقاً");

            IsApproved = true;
            ApprovedBy = approvedBy;
            ApprovalDate = DateTime.Now;
            ModifiedBy = approvedBy;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// ربط المعاملة بمستند
        /// </summary>
        /// <param name="documentType">نوع المستند</param>
        /// <param name="documentID">رقم المستند</param>
        public void LinkToDocument(string documentType, int documentID)
        {
            RelatedDocumentType = documentType;
            RelatedDocumentID = documentID;
        }

        /// <summary>
        /// تعيين العميل
        /// </summary>
        /// <param name="customerID">رقم العميل</param>
        public void SetCustomer(int customerID)
        {
            CustomerID = customerID;
        }

        /// <summary>
        /// تعيين المورد
        /// </summary>
        /// <param name="supplierID">رقم المورد</param>
        public void SetSupplier(int supplierID)
        {
            SupplierID = supplierID;
        }

        /// <summary>
        /// نسخ البيانات من معاملة أخرى
        /// </summary>
        /// <param name="source">المصدر</param>
        public void CopyFrom(CashTransaction source)
        {
            if (source == null)
                return;

            SessionID = source.SessionID;
            TransactionType = source.TransactionType;
            Amount = source.Amount;
            PaymentMethodID = source.PaymentMethodID;
            ReferenceNumber = source.ReferenceNumber;
            Description = source.Description;
            Category = source.Category;
            RelatedDocumentType = source.RelatedDocumentType;
            RelatedDocumentID = source.RelatedDocumentID;
            CustomerID = source.CustomerID;
            SupplierID = source.SupplierID;
            UserID = source.UserID;
            Notes = source.Notes;
        }

        /// <summary>
        /// تحويل إلى نص
        /// </summary>
        /// <returns>تمثيل نصي للكائن</returns>
        public override string ToString()
        {
            return $"{TransactionNumber} - {TransactionTypeArabic} - {Amount:C}";
        }

        /// <summary>
        /// مقارنة مع كائن آخر
        /// </summary>
        /// <param name="obj">الكائن المراد المقارنة معه</param>
        /// <returns>true إذا كانا متساويين</returns>
        public override bool Equals(object obj)
        {
            if (obj is CashTransaction other)
            {
                return TransactionID == other.TransactionID;
            }
            return false;
        }

        /// <summary>
        /// الحصول على رمز التجمع
        /// </summary>
        /// <returns>رمز التجمع</returns>
        public override int GetHashCode()
        {
            return TransactionID.GetHashCode();
        }

        #endregion
    }

    #region التعدادات المساعدة

    /// <summary>
    /// أنواع المعاملات النقدية
    /// </summary>
    public static class CashTransactionTypes
    {
        public const string Sale = "Sale";
        public const string Return = "Return";
        public const string Expense = "Expense";
        public const string Withdrawal = "Withdrawal";
        public const string Deposit = "Deposit";
        public const string Opening = "Opening";
        public const string Closing = "Closing";
        public const string Adjustment = "Adjustment";
    }

    /// <summary>
    /// فئات المعاملات
    /// </summary>
    public static class TransactionCategories
    {
        public const string Sales = "Sales";
        public const string Office = "Office";
        public const string Utilities = "Utilities";
        public const string Maintenance = "Maintenance";
        public const string Marketing = "Marketing";
        public const string Travel = "Travel";
        public const string Other = "Other";
    }

    /// <summary>
    /// أنواع المستندات المرتبطة
    /// </summary>
    public static class RelatedDocumentTypes
    {
        public const string Invoice = "Invoice";
        public const string Receipt = "Receipt";
        public const string PurchaseOrder = "PurchaseOrder";
        public const string Expense = "Expense";
        public const string Adjustment = "Adjustment";
    }

    #endregion
}
