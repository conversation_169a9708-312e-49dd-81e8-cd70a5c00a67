using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using AredooPOS.Models.Settings;
using AredooPOS.Services;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// النموذج الرئيسي لإدارة الإعدادات
    /// يوفر واجهة شاملة لإدارة جميع أنواع الإعدادات في النظام
    /// </summary>
    public partial class SettingsMainForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly SettingsService _settingsService;
        private readonly BackupService _backupService;
        private readonly ILogger<SettingsMainForm> _logger;
        private readonly string _currentUser;

        private Dictionary<string, UserControl> _settingsPanels;
        private string _selectedSettingsCategory;
        private bool _hasUnsavedChanges = false;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ النموذج الرئيسي للإعدادات
        /// </summary>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <param name="logger">مسجل الأحداث</param>
        public SettingsMainForm(string currentUser, ILogger<SettingsMainForm> logger = null)
        {
            InitializeComponent();
            
            _currentUser = currentUser;
            _logger = logger;
            _settingsService = new SettingsService(null, logger);
            _backupService = new BackupService(null, null, logger);

            InitializeForm();
            LoadSettingsCategories();
            SetupEventHandlers();
        }

        /// <summary>
        /// تهيئة النموذج
        /// </summary>
        private void InitializeForm()
        {
            // تعيين النصوص العربية
            this.Text = "إعدادات النظام";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            // تهيئة قاموس لوحات الإعدادات
            _settingsPanels = new Dictionary<string, UserControl>();

            // تعيين الألوان والتنسيق
            SetupStyling();
        }

        /// <summary>
        /// تعيين التنسيق والألوان
        /// </summary>
        private void SetupStyling()
        {
            this.BackColor = Color.FromArgb(240, 240, 240);
            
            // تنسيق الشريط الجانبي
            pnlSidebar.BackColor = Color.FromArgb(45, 45, 48);
            pnlSidebar.Width = 280;

            // تنسيق المنطقة الرئيسية
            pnlMain.BackColor = Color.White;
            pnlMain.Padding = new Padding(20);

            // تنسيق شريط الأدوات
            toolStrip.BackColor = Color.FromArgb(230, 230, 230);
            statusStrip.BackColor = Color.FromArgb(230, 230, 230);
        }

        /// <summary>
        /// تحميل فئات الإعدادات
        /// </summary>
        private void LoadSettingsCategories()
        {
            try
            {
                // مسح القائمة الحالية
                lvSettingsCategories.Items.Clear();

                // إضافة فئات الإعدادات
                var categories = new[]
                {
                    new { Name = "الإعدادات العامة", Category = "General", Icon = "⚙️", Description = "الإعدادات الأساسية للنظام" },
                    new { Name = "إعدادات المتجر", Category = "Store", Icon = "🏪", Description = "معلومات المتجر والشعار والاتصال" },
                    new { Name = "إعدادات العملة", Category = "Currency", Icon = "💱", Description = "العملات وأسعار الصرف" },
                    new { Name = "إعدادات الضريبة", Category = "Tax", Icon = "📊", Description = "نسب الضريبة وطرق الحساب" },
                    new { Name = "إعدادات الطباعة", Category = "Print", Icon = "🖨️", Description = "تخصيص الطباعة والفواتير" },
                    new { Name = "النسخ الاحتياطي", Category = "Backup", Icon = "💾", Description = "إدارة النسخ الاحتياطية والاستعادة" },
                    new { Name = "إعدادات الأمان", Category = "Security", Icon = "🔒", Description = "كلمات المرور والصلاحيات" },
                    new { Name = "إعدادات التنبيهات", Category = "Notifications", Icon = "🔔", Description = "تخصيص التنبيهات والإشعارات" }
                };

                foreach (var category in categories)
                {
                    var item = new ListViewItem(category.Name)
                    {
                        Tag = category.Category,
                        ToolTipText = category.Description,
                        ImageIndex = 0 // يمكن إضافة أيقونات لاحقاً
                    };
                    
                    item.SubItems.Add(category.Description);
                    lvSettingsCategories.Items.Add(item);
                }

                // تحديد الفئة الأولى افتراضياً
                if (lvSettingsCategories.Items.Count > 0)
                {
                    lvSettingsCategories.Items[0].Selected = true;
                    LoadSettingsPanel(categories[0].Category);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل فئات الإعدادات");
                MessageBox.Show($"خطأ في تحميل فئات الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تعيين معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // أحداث قائمة فئات الإعدادات
            lvSettingsCategories.SelectedIndexChanged += LvSettingsCategories_SelectedIndexChanged;
            lvSettingsCategories.DoubleClick += LvSettingsCategories_DoubleClick;

            // أحداث شريط الأدوات
            tsbSave.Click += TsbSave_Click;
            tsbCancel.Click += TsbCancel_Click;
            tsbReset.Click += TsbReset_Click;
            tsbImport.Click += TsbImport_Click;
            tsbExport.Click += TsbExport_Click;
            tsbBackup.Click += TsbBackup_Click;

            // أحداث القوائم
            mnuFileSave.Click += MnuFileSave_Click;
            mnuFileImport.Click += MnuFileImport_Click;
            mnuFileExport.Click += MnuFileExport_Click;
            mnuFileExit.Click += MnuFileExit_Click;
            mnuToolsBackup.Click += MnuToolsBackup_Click;
            mnuToolsRestore.Click += MnuToolsRestore_Click;
            mnuToolsReset.Click += MnuToolsReset_Click;
            mnuHelpAbout.Click += MnuHelpAbout_Click;

            // أحداث خدمة الإعدادات
            _settingsService.SettingChanged += SettingsService_SettingChanged;
            _settingsService.SettingsSaved += SettingsService_SettingsSaved;
            _settingsService.SettingsSaveFailed += SettingsService_SettingsSaveFailed;

            // أحداث النموذج
            this.Load += SettingsMainForm_Load;
            this.FormClosing += SettingsMainForm_FormClosing;
        }

        #endregion

        #region تحميل لوحات الإعدادات

        /// <summary>
        /// تحميل لوحة الإعدادات المحددة
        /// </summary>
        /// <param name="category">فئة الإعدادات</param>
        private void LoadSettingsPanel(string category)
        {
            try
            {
                _selectedSettingsCategory = category;

                // إخفاء اللوحة الحالية
                foreach (Control control in pnlMain.Controls)
                {
                    if (control is UserControl)
                    {
                        control.Visible = false;
                    }
                }

                // الحصول على اللوحة أو إنشاؤها
                if (!_settingsPanels.TryGetValue(category, out var panel))
                {
                    panel = CreateSettingsPanel(category);
                    if (panel != null)
                    {
                        _settingsPanels[category] = panel;
                        pnlMain.Controls.Add(panel);
                        panel.Dock = DockStyle.Fill;
                    }
                }

                // إظهار اللوحة
                if (panel != null)
                {
                    panel.Visible = true;
                    panel.BringToFront();
                }

                // تحديث شريط الحالة
                UpdateStatusBar(category);

                _logger?.LogInformation($"تم تحميل لوحة إعدادات {category}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحميل لوحة الإعدادات {category}");
                MessageBox.Show($"خطأ في تحميل لوحة الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إنشاء لوحة الإعدادات حسب الفئة
        /// </summary>
        /// <param name="category">فئة الإعدادات</param>
        /// <returns>لوحة الإعدادات</returns>
        private UserControl CreateSettingsPanel(string category)
        {
            return category switch
            {
                "General" => new GeneralSettingsPanel(_settingsService, _currentUser, _logger),
                "Store" => new StoreSettingsPanel(_settingsService, _currentUser, _logger),
                "Currency" => new CurrencySettingsPanel(_settingsService, _currentUser, _logger),
                "Tax" => new TaxSettingsPanel(_settingsService, _currentUser, _logger),
                "Print" => new PrintSettingsPanel(_settingsService, _currentUser, _logger),
                "Backup" => new BackupSettingsPanel(_backupService, _currentUser, _logger),
                "Security" => new SecuritySettingsPanel(_settingsService, _currentUser, _logger),
                "Notifications" => new NotificationSettingsPanel(_settingsService, _currentUser, _logger),
                _ => null
            };
        }

        /// <summary>
        /// تحديث شريط الحالة
        /// </summary>
        /// <param name="category">فئة الإعدادات</param>
        private void UpdateStatusBar(string category)
        {
            var categoryNames = new Dictionary<string, string>
            {
                { "General", "الإعدادات العامة" },
                { "Store", "إعدادات المتجر" },
                { "Currency", "إعدادات العملة" },
                { "Tax", "إعدادات الضريبة" },
                { "Print", "إعدادات الطباعة" },
                { "Backup", "النسخ الاحتياطي" },
                { "Security", "إعدادات الأمان" },
                { "Notifications", "إعدادات التنبيهات" }
            };

            tslStatus.Text = $"الفئة الحالية: {categoryNames.GetValueOrDefault(category, "غير محدد")}";
            tslUser.Text = $"المستخدم: {_currentUser}";
            tslDateTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
            
            if (_hasUnsavedChanges)
            {
                tslChanges.Text = "يوجد تغييرات غير محفوظة";
                tslChanges.ForeColor = Color.Orange;
            }
            else
            {
                tslChanges.Text = "جميع التغييرات محفوظة";
                tslChanges.ForeColor = Color.Green;
            }
        }

        #endregion

        #region أحداث واجهة المستخدم

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void SettingsMainForm_Load(object sender, EventArgs e)
        {
            try
            {
                // تحديث شريط الحالة
                tslUser.Text = $"المستخدم: {_currentUser}";
                tslDateTime.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
                tslStatus.Text = "جاهز";

                _logger?.LogInformation("تم تحميل النموذج الرئيسي للإعدادات");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل النموذج");
            }
        }

        /// <summary>
        /// حدث تغيير التحديد في قائمة فئات الإعدادات
        /// </summary>
        private void LvSettingsCategories_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (lvSettingsCategories.SelectedItems.Count > 0)
            {
                var selectedItem = lvSettingsCategories.SelectedItems[0];
                var category = selectedItem.Tag.ToString();
                
                // التحقق من وجود تغييرات غير محفوظة
                if (_hasUnsavedChanges)
                {
                    var result = MessageBox.Show(
                        "يوجد تغييرات غير محفوظة. هل تريد حفظها قبل الانتقال؟",
                        "تغييرات غير محفوظة",
                        MessageBoxButtons.YesNoCancel,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        SaveCurrentSettings();
                    }
                    else if (result == DialogResult.Cancel)
                    {
                        return; // إلغاء التغيير
                    }
                }

                LoadSettingsPanel(category);
            }
        }

        /// <summary>
        /// حدث النقر المزدوج على قائمة فئات الإعدادات
        /// </summary>
        private void LvSettingsCategories_DoubleClick(object sender, EventArgs e)
        {
            // يمكن إضافة عمليات إضافية هنا
        }

        /// <summary>
        /// حدث زر الحفظ
        /// </summary>
        private async void TsbSave_Click(object sender, EventArgs e)
        {
            await SaveCurrentSettingsAsync();
        }

        /// <summary>
        /// حدث زر الإلغاء
        /// </summary>
        private void TsbCancel_Click(object sender, EventArgs e)
        {
            CancelCurrentChanges();
        }

        /// <summary>
        /// حدث زر الإعادة
        /// </summary>
        private async void TsbReset_Click(object sender, EventArgs e)
        {
            await ResetCurrentSettingsAsync();
        }

        /// <summary>
        /// حدث زر الاستيراد
        /// </summary>
        private async void TsbImport_Click(object sender, EventArgs e)
        {
            await ImportSettingsAsync();
        }

        /// <summary>
        /// حدث زر التصدير
        /// </summary>
        private async void TsbExport_Click(object sender, EventArgs e)
        {
            await ExportSettingsAsync();
        }

        /// <summary>
        /// حدث زر النسخ الاحتياطي
        /// </summary>
        private async void TsbBackup_Click(object sender, EventArgs e)
        {
            await CreateBackupAsync();
        }

        #endregion

        #region أحداث القوائم

        /// <summary>
        /// حدث قائمة ملف - حفظ
        /// </summary>
        private void MnuFileSave_Click(object sender, EventArgs e)
        {
            TsbSave_Click(sender, e);
        }

        /// <summary>
        /// حدث قائمة ملف - استيراد
        /// </summary>
        private void MnuFileImport_Click(object sender, EventArgs e)
        {
            TsbImport_Click(sender, e);
        }

        /// <summary>
        /// حدث قائمة ملف - تصدير
        /// </summary>
        private void MnuFileExport_Click(object sender, EventArgs e)
        {
            TsbExport_Click(sender, e);
        }

        /// <summary>
        /// حدث قائمة ملف - خروج
        /// </summary>
        private void MnuFileExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// حدث قائمة أدوات - نسخ احتياطي
        /// </summary>
        private void MnuToolsBackup_Click(object sender, EventArgs e)
        {
            TsbBackup_Click(sender, e);
        }

        /// <summary>
        /// حدث قائمة أدوات - استعادة
        /// </summary>
        private async void MnuToolsRestore_Click(object sender, EventArgs e)
        {
            await RestoreBackupAsync();
        }

        /// <summary>
        /// حدث قائمة أدوات - إعادة تعيين
        /// </summary>
        private void MnuToolsReset_Click(object sender, EventArgs e)
        {
            TsbReset_Click(sender, e);
        }

        /// <summary>
        /// حدث قائمة مساعدة - حول
        /// </summary>
        private void MnuHelpAbout_Click(object sender, EventArgs e)
        {
            var aboutForm = new AboutSettingsForm();
            aboutForm.ShowDialog();
        }

        #endregion

        #region أحداث خدمة الإعدادات

        /// <summary>
        /// حدث تغيير إعداد
        /// </summary>
        private void SettingsService_SettingChanged(object sender, SettingChangedEventArgs e)
        {
            _hasUnsavedChanges = true;
            UpdateStatusBar(_selectedSettingsCategory);
        }

        /// <summary>
        /// حدث حفظ الإعدادات
        /// </summary>
        private void SettingsService_SettingsSaved(object sender, SettingsSavedEventArgs e)
        {
            _hasUnsavedChanges = false;
            UpdateStatusBar(_selectedSettingsCategory);

            tslStatus.Text = $"تم حفظ {e.SavedCount} إعداد بنجاح";
            _logger?.LogInformation($"تم حفظ {e.SavedCount} إعداد بواسطة {e.UpdatedBy}");
        }

        /// <summary>
        /// حدث فشل حفظ الإعدادات
        /// </summary>
        private void SettingsService_SettingsSaveFailed(object sender, SettingsSaveFailedEventArgs e)
        {
            tslStatus.Text = $"فشل في حفظ الإعداد: {e.SettingName}";
            MessageBox.Show($"فشل في حفظ الإعداد {e.SettingName}: {e.ErrorMessage}", "خطأ",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        #endregion

        #region العمليات الأساسية

        /// <summary>
        /// حفظ الإعدادات الحالية
        /// </summary>
        private async Task SaveCurrentSettingsAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(_selectedSettingsCategory))
                    return;

                // الحصول على اللوحة الحالية
                if (_settingsPanels.TryGetValue(_selectedSettingsCategory, out var panel))
                {
                    // التحقق من وجود دالة حفظ في اللوحة
                    if (panel is ISettingsPanel settingsPanel)
                    {
                        var result = await settingsPanel.SaveSettingsAsync();
                        if (result)
                        {
                            _hasUnsavedChanges = false;
                            UpdateStatusBar(_selectedSettingsCategory);
                            MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في حفظ الإعدادات الحالية");
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// حفظ الإعدادات الحالية (متزامن)
        /// </summary>
        private void SaveCurrentSettings()
        {
            Task.Run(async () => await SaveCurrentSettingsAsync());
        }

        /// <summary>
        /// إلغاء التغييرات الحالية
        /// </summary>
        private void CancelCurrentChanges()
        {
            try
            {
                if (string.IsNullOrEmpty(_selectedSettingsCategory))
                    return;

                var result = MessageBox.Show(
                    "هل تريد إلغاء جميع التغييرات غير المحفوظة؟",
                    "تأكيد الإلغاء",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // إعادة تحميل اللوحة
                    if (_settingsPanels.TryGetValue(_selectedSettingsCategory, out var panel))
                    {
                        if (panel is ISettingsPanel settingsPanel)
                        {
                            settingsPanel.RefreshSettings();
                        }
                    }

                    _hasUnsavedChanges = false;
                    UpdateStatusBar(_selectedSettingsCategory);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إلغاء التغييرات");
                MessageBox.Show($"خطأ في إلغاء التغييرات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعادة تعيين الإعدادات الحالية
        /// </summary>
        private async Task ResetCurrentSettingsAsync()
        {
            try
            {
                if (string.IsNullOrEmpty(_selectedSettingsCategory))
                    return;

                var result = MessageBox.Show(
                    "هل تريد إعادة تعيين جميع الإعدادات في هذه الفئة إلى القيم الافتراضية؟",
                    "تأكيد الإعادة",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    var resetResult = await _settingsService.ResetSettingsToDefaultAsync(_selectedSettingsCategory, _currentUser);

                    if (resetResult)
                    {
                        // إعادة تحميل اللوحة
                        if (_settingsPanels.TryGetValue(_selectedSettingsCategory, out var panel))
                        {
                            if (panel is ISettingsPanel settingsPanel)
                            {
                                settingsPanel.RefreshSettings();
                            }
                        }

                        _hasUnsavedChanges = false;
                        UpdateStatusBar(_selectedSettingsCategory);

                        MessageBox.Show("تم إعادة تعيين الإعدادات إلى القيم الافتراضية", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إعادة تعيين الإعدادات");
                MessageBox.Show($"خطأ في إعادة تعيين الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// استيراد الإعدادات
        /// </summary>
        private async Task ImportSettingsAsync()
        {
            try
            {
                var openDialog = new OpenFileDialog
                {
                    Title = "استيراد الإعدادات",
                    Filter = "ملفات الإعدادات (*.json)|*.json|جميع الملفات (*.*)|*.*",
                    FilterIndex = 1
                };

                if (openDialog.ShowDialog() == DialogResult.OK)
                {
                    var result = MessageBox.Show(
                        "هل تريد استيراد الإعدادات؟ سيتم استبدال الإعدادات الحالية.",
                        "تأكيد الاستيراد",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Warning);

                    if (result == DialogResult.Yes)
                    {
                        var fileContent = await System.IO.File.ReadAllTextAsync(openDialog.FileName);
                        var importResult = await _settingsService.ImportSettingsAsync(fileContent, _currentUser);

                        if (importResult)
                        {
                            // إعادة تحميل جميع اللوحات
                            RefreshAllPanels();

                            MessageBox.Show("تم استيراد الإعدادات بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("فشل في استيراد الإعدادات", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في استيراد الإعدادات");
                MessageBox.Show($"خطأ في استيراد الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصدير الإعدادات
        /// </summary>
        private async Task ExportSettingsAsync()
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Title = "تصدير الإعدادات",
                    Filter = "ملفات الإعدادات (*.json)|*.json|جميع الملفات (*.*)|*.*",
                    FilterIndex = 1,
                    FileName = $"AredooPOS_Settings_{DateTime.Now:yyyyMMdd_HHmmss}.json"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    var settingsData = await _settingsService.ExportAllSettingsAsync();
                    await System.IO.File.WriteAllTextAsync(saveDialog.FileName, settingsData);

                    MessageBox.Show("تم تصدير الإعدادات بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تصدير الإعدادات");
                MessageBox.Show($"خطأ في تصدير الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية
        /// </summary>
        private async Task CreateBackupAsync()
        {
            try
            {
                var backupForm = new BackupCreateForm(_backupService, _logger);
                backupForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إنشاء النسخة الاحتياطية");
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// استعادة نسخة احتياطية
        /// </summary>
        private async Task RestoreBackupAsync()
        {
            try
            {
                var restoreForm = new BackupRestoreForm(_backupService, _logger);
                restoreForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في استعادة النسخة الاحتياطية");
                MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// تحديث جميع اللوحات
        /// </summary>
        private void RefreshAllPanels()
        {
            foreach (var panel in _settingsPanels.Values)
            {
                if (panel is ISettingsPanel settingsPanel)
                {
                    settingsPanel.RefreshSettings();
                }
            }
        }

        /// <summary>
        /// حدث إغلاق النموذج
        /// </summary>
        private void SettingsMainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // التحقق من وجود تغييرات غير محفوظة
                if (_hasUnsavedChanges)
                {
                    var result = MessageBox.Show(
                        "يوجد تغييرات غير محفوظة. هل تريد حفظها قبل الإغلاق؟",
                        "تغييرات غير محفوظة",
                        MessageBoxButtons.YesNoCancel,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        SaveCurrentSettings();
                    }
                    else if (result == DialogResult.Cancel)
                    {
                        e.Cancel = true;
                        return;
                    }
                }

                // تنظيف الموارد
                foreach (var panel in _settingsPanels.Values)
                {
                    panel?.Dispose();
                }
                _settingsPanels.Clear();

                _logger?.LogInformation("تم إغلاق النموذج الرئيسي للإعدادات");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إغلاق النموذج");
            }
        }

        #endregion
    }

    #region واجهة لوحة الإعدادات

    /// <summary>
    /// واجهة لوحة الإعدادات
    /// </summary>
    public interface ISettingsPanel
    {
        Task<bool> SaveSettingsAsync();
        void RefreshSettings();
        bool HasUnsavedChanges { get; }
    }

    #endregion

    #region النماذج المساعدة

    /// <summary>
    /// نموذج حول الإعدادات
    /// </summary>
    public partial class AboutSettingsForm : Form
    {
        public AboutSettingsForm()
        {
            InitializeComponent();
            this.Text = "حول إعدادات النظام";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
        }
    }

    #endregion
}
