using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using AredooPOS.Models;
using Microsoft.Extensions.Logging;

namespace AredooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات لجلسات المستخدمين
    /// تحتوي على جميع العمليات المتعلقة بقاعدة البيانات لجلسات المستخدمين
    /// </summary>
    public class UserSessionDAL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly string _connectionString;
        private readonly ILogger<UserSessionDAL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة الوصول للبيانات لجلسات المستخدمين
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public UserSessionDAL(string connectionString = null, ILogger<UserSessionDAL> logger = null)
        {
            _connectionString = connectionString ?? DatabaseConfig.GetConnectionString();
            _logger = logger;
        }

        #endregion

        #region العمليات الأساسية

        /// <summary>
        /// إضافة جلسة مستخدم جديدة
        /// </summary>
        /// <param name="session">بيانات الجلسة</param>
        /// <returns>رقم الجلسة الجديدة</returns>
        public int AddUserSession(UserSession session)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_AddUserSession", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                // إضافة المعاملات
                command.Parameters.AddWithValue("@UserID", session.UserID);
                command.Parameters.AddWithValue("@SessionToken", session.SessionToken);
                command.Parameters.AddWithValue("@StartTime", session.StartTime);
                command.Parameters.AddWithValue("@ExpiryTime", session.ExpiryTime);
                command.Parameters.AddWithValue("@IPAddress", session.IPAddress ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@UserAgent", session.UserAgent ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@DeviceName", session.DeviceName ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@DeviceType", session.DeviceType ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@OperatingSystem", session.OperatingSystem ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Location", session.Location ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@IsActive", session.IsActive);
                command.Parameters.AddWithValue("@IsVerified", session.IsVerified);

                // معامل الإخراج
                var outputParam = new SqlParameter("@NewSessionID", SqlDbType.Int)
                {
                    Direction = ParameterDirection.Output
                };
                command.Parameters.Add(outputParam);

                connection.Open();
                command.ExecuteNonQuery();

                var sessionId = Convert.ToInt32(outputParam.Value);
                _logger?.LogInformation($"تم إضافة جلسة جديدة برقم {sessionId} للمستخدم {session.UserID}");
                return sessionId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إضافة جلسة المستخدم");
                throw;
            }
        }

        /// <summary>
        /// تحديث بيانات الجلسة
        /// </summary>
        /// <param name="session">بيانات الجلسة المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateUserSession(UserSession session)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_UpdateUserSession", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                // إضافة المعاملات
                command.Parameters.AddWithValue("@SessionID", session.SessionID);
                command.Parameters.AddWithValue("@EndTime", session.EndTime ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ExpiryTime", session.ExpiryTime);
                command.Parameters.AddWithValue("@LastActivity", session.LastActivity);
                command.Parameters.AddWithValue("@OperationsCount", session.OperationsCount);
                command.Parameters.AddWithValue("@PagesVisited", session.PagesVisited);
                command.Parameters.AddWithValue("@IsActive", session.IsActive);
                command.Parameters.AddWithValue("@EndReason", session.EndReason ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@EndDetails", session.EndDetails ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@IsSuspicious", session.IsSuspicious);
                command.Parameters.AddWithValue("@SuspiciousReason", session.SuspiciousReason ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@IsForceTerminated", session.IsForceTerminated);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم تحديث الجلسة {session.SessionID}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث الجلسة {session.SessionID}");
                throw;
            }
        }

        /// <summary>
        /// إنهاء جلسة المستخدم
        /// </summary>
        /// <param name="sessionId">رقم الجلسة</param>
        /// <param name="endReason">سبب الإنهاء</param>
        /// <param name="endDetails">تفاصيل الإنهاء</param>
        /// <returns>true إذا تم الإنهاء بنجاح</returns>
        public bool EndUserSession(int sessionId, string endReason = "تسجيل خروج عادي", string endDetails = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_EndUserSession", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@SessionID", sessionId);
                command.Parameters.AddWithValue("@EndReason", endReason);
                command.Parameters.AddWithValue("@EndDetails", endDetails ?? (object)DBNull.Value);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم إنهاء الجلسة {sessionId}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إنهاء الجلسة {sessionId}");
                throw;
            }
        }

        /// <summary>
        /// إنهاء جلسة المستخدم بالرمز المميز
        /// </summary>
        /// <param name="sessionToken">رمز الجلسة</param>
        /// <param name="endReason">سبب الإنهاء</param>
        /// <param name="endDetails">تفاصيل الإنهاء</param>
        /// <returns>true إذا تم الإنهاء بنجاح</returns>
        public bool EndUserSessionByToken(string sessionToken, string endReason = "تسجيل خروج عادي", string endDetails = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_EndUserSessionByToken", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@SessionToken", sessionToken);
                command.Parameters.AddWithValue("@EndReason", endReason);
                command.Parameters.AddWithValue("@EndDetails", endDetails ?? (object)DBNull.Value);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم إنهاء الجلسة بالرمز {sessionToken.Substring(0, 8)}...");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إنهاء الجلسة بالرمز {sessionToken}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جلسة بالرقم
        /// </summary>
        /// <param name="sessionId">رقم الجلسة</param>
        /// <returns>بيانات الجلسة</returns>
        public UserSession GetSessionById(int sessionId)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetSessionById", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@SessionID", sessionId);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapSessionFromReader(reader);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الجلسة {sessionId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جلسة بالرمز المميز
        /// </summary>
        /// <param name="sessionToken">رمز الجلسة</param>
        /// <returns>بيانات الجلسة</returns>
        public UserSession GetSessionByToken(string sessionToken)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetSessionByToken", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@SessionToken", sessionToken);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapSessionFromReader(reader);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الجلسة بالرمز {sessionToken}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جلسات المستخدم
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="activeOnly">الجلسات النشطة فقط</param>
        /// <returns>قائمة جلسات المستخدم</returns>
        public List<UserSession> GetUserSessions(int userId, bool activeOnly = false)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetUserSessions", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@UserID", userId);
                command.Parameters.AddWithValue("@ActiveOnly", activeOnly);

                connection.Open();
                using var reader = command.ExecuteReader();

                var sessions = new List<UserSession>();
                while (reader.Read())
                {
                    sessions.Add(MapSessionFromReader(reader));
                }

                return sessions;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على جلسات المستخدم {userId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على الجلسات النشطة
        /// </summary>
        /// <returns>قائمة الجلسات النشطة</returns>
        public List<UserSession> GetActiveSessions()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetActiveSessions", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                connection.Open();
                using var reader = command.ExecuteReader();

                var sessions = new List<UserSession>();
                while (reader.Read())
                {
                    sessions.Add(MapSessionFromReader(reader));
                }

                return sessions;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على الجلسات النشطة");
                throw;
            }
        }

        #endregion

        #region عمليات الصيانة

        /// <summary>
        /// تنظيف الجلسات المنتهية الصلاحية
        /// </summary>
        /// <param name="olderThanDays">أقدم من عدد الأيام</param>
        /// <returns>عدد الجلسات المحذوفة</returns>
        public int CleanupExpiredSessions(int olderThanDays = 30)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_CleanupExpiredSessions", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@OlderThanDays", olderThanDays);

                // معامل الإخراج
                var outputParam = new SqlParameter("@DeletedCount", SqlDbType.Int)
                {
                    Direction = ParameterDirection.Output
                };
                command.Parameters.Add(outputParam);

                connection.Open();
                command.ExecuteNonQuery();

                var deletedCount = Convert.ToInt32(outputParam.Value);
                _logger?.LogInformation($"تم حذف {deletedCount} جلسة منتهية الصلاحية");
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تنظيف الجلسات المنتهية الصلاحية");
                throw;
            }
        }

        /// <summary>
        /// إنهاء جميع جلسات المستخدم
        /// </summary>
        /// <param name="userId">رقم المستخدم</param>
        /// <param name="endReason">سبب الإنهاء</param>
        /// <param name="excludeSessionId">استثناء جلسة معينة</param>
        /// <returns>عدد الجلسات المنتهية</returns>
        public int EndAllUserSessions(int userId, string endReason = "إنهاء جميع الجلسات", int? excludeSessionId = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_EndAllUserSessions", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@UserID", userId);
                command.Parameters.AddWithValue("@EndReason", endReason);
                command.Parameters.AddWithValue("@ExcludeSessionID", excludeSessionId ?? (object)DBNull.Value);

                // معامل الإخراج
                var outputParam = new SqlParameter("@EndedCount", SqlDbType.Int)
                {
                    Direction = ParameterDirection.Output
                };
                command.Parameters.Add(outputParam);

                connection.Open();
                command.ExecuteNonQuery();

                var endedCount = Convert.ToInt32(outputParam.Value);
                _logger?.LogInformation($"تم إنهاء {endedCount} جلسة للمستخدم {userId}");
                return endedCount;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إنهاء جلسات المستخدم {userId}");
                throw;
            }
        }

        /// <summary>
        /// تحديث آخر نشاط للجلسة
        /// </summary>
        /// <param name="sessionToken">رمز الجلسة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateLastActivity(string sessionToken)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_UpdateSessionLastActivity", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@SessionToken", sessionToken);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث آخر نشاط للجلسة {sessionToken}");
                throw;
            }
        }

        #endregion

        #region الإحصائيات

        /// <summary>
        /// الحصول على إحصائيات الجلسات
        /// </summary>
        /// <returns>إحصائيات الجلسات</returns>
        public SessionStatistics GetSessionStatistics()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetSessionStatistics", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return new SessionStatistics
                    {
                        TotalSessions = reader.GetInt32("TotalSessions"),
                        ActiveSessions = reader.GetInt32("ActiveSessions"),
                        ExpiredSessions = reader.GetInt32("ExpiredSessions"),
                        SuspiciousSessions = reader.GetInt32("SuspiciousSessions"),
                        AverageSessionDuration = reader.GetDouble("AverageSessionDuration"),
                        TodaySessions = reader.GetInt32("TodaySessions")
                    };
                }

                return new SessionStatistics();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على إحصائيات الجلسات");
                throw;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// تحويل بيانات القارئ إلى كائن جلسة
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن الجلسة</returns>
        private UserSession MapSessionFromReader(SqlDataReader reader)
        {
            return new UserSession
            {
                SessionID = reader.GetInt32("SessionID"),
                UserID = reader.GetInt32("UserID"),
                SessionToken = reader.GetString("SessionToken"),
                StartTime = reader.GetDateTime("StartTime"),
                EndTime = reader.IsDBNull("EndTime") ? null : reader.GetDateTime("EndTime"),
                ExpiryTime = reader.GetDateTime("ExpiryTime"),
                IsActive = reader.GetBoolean("IsActive"),
                IPAddress = reader.IsDBNull("IPAddress") ? null : reader.GetString("IPAddress"),
                UserAgent = reader.IsDBNull("UserAgent") ? null : reader.GetString("UserAgent"),
                DeviceName = reader.IsDBNull("DeviceName") ? null : reader.GetString("DeviceName"),
                DeviceType = reader.IsDBNull("DeviceType") ? null : reader.GetString("DeviceType"),
                OperatingSystem = reader.IsDBNull("OperatingSystem") ? null : reader.GetString("OperatingSystem"),
                Location = reader.IsDBNull("Location") ? null : reader.GetString("Location"),
                LastActivity = reader.GetDateTime("LastActivity"),
                OperationsCount = reader.GetInt32("OperationsCount"),
                PagesVisited = reader.GetInt32("PagesVisited"),
                EndReason = reader.IsDBNull("EndReason") ? null : reader.GetString("EndReason"),
                EndDetails = reader.IsDBNull("EndDetails") ? null : reader.GetString("EndDetails"),
                IsVerified = reader.GetBoolean("IsVerified"),
                IsSuspicious = reader.GetBoolean("IsSuspicious"),
                SuspiciousReason = reader.IsDBNull("SuspiciousReason") ? null : reader.GetString("SuspiciousReason"),
                IsForceTerminated = reader.GetBoolean("IsForceTerminated"),
                CreatedDate = reader.GetDateTime("CreatedDate"),
                ModifiedDate = reader.GetDateTime("ModifiedDate")
            };
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// إحصائيات الجلسات
    /// </summary>
    public class SessionStatistics
    {
        public int TotalSessions { get; set; }
        public int ActiveSessions { get; set; }
        public int ExpiredSessions { get; set; }
        public int SuspiciousSessions { get; set; }
        public double AverageSessionDuration { get; set; }
        public int TodaySessions { get; set; }
    }

    #endregion
}
