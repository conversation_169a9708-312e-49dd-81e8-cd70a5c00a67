using System;
using System.Collections.Generic;
using System.Transactions;
using AridooPOS.Models;
using AridooPOS.DAL;

namespace AridooPOS.BLL
{
    /// <summary>
    /// طبقة منطق الأعمال للفواتير
    /// </summary>
    public class InvoiceBLL
    {
        private readonly InvoiceDAL _invoiceDAL;
        private readonly InvoiceDetailDAL _invoiceDetailDAL;
        private readonly ProductDAL _productDAL;
        private readonly CustomerDAL _customerDAL;

        public InvoiceBLL()
        {
            _invoiceDAL = new InvoiceDAL();
            _invoiceDetailDAL = new InvoiceDetailDAL();
            _productDAL = new ProductDAL();
            _customerDAL = new CustomerDAL();
        }

        /// <summary>
        /// إنشاء فاتورة جديدة
        /// </summary>
        /// <param name="invoice">بيانات الفاتورة</param>
        /// <returns>رقم الفاتورة المنشأة</returns>
        public int CreateInvoice(Invoice invoice)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateInvoice(invoice);

                using (var scope = new TransactionScope())
                {
                    // الحصول على رقم فاتورة جديد إذا لم يكن محدد
                    if (string.IsNullOrEmpty(invoice.InvoiceNumber))
                    {
                        invoice.InvoiceNumber = _invoiceDAL.GetNextInvoiceNumber();
                    }

                    // حساب إجماليات الفاتورة
                    CalculateInvoiceTotals(invoice);

                    // إدراج الفاتورة
                    int invoiceId = _invoiceDAL.InsertInvoice(invoice);
                    invoice.InvoiceID = invoiceId;

                    // إدراج تفاصيل الفاتورة
                    foreach (var detail in invoice.InvoiceDetails)
                    {
                        detail.InvoiceID = invoiceId;
                        detail.CalculateLineTotal();
                    }
                    _invoiceDetailDAL.InsertInvoiceDetails(invoice.InvoiceDetails);

                    // تحديث المخزون
                    UpdateInventoryForSale(invoice.InvoiceDetails);

                    // تحديث رصيد العميل إذا كان البيع بالآجل
                    if (invoice.PaymentType == PaymentTypes.Installment && invoice.CustomerID.HasValue)
                    {
                        var customer = _customerDAL.GetCustomerById(invoice.CustomerID.Value);
                        if (customer != null)
                        {
                            customer.AddToBalance(invoice.RemainingAmount);
                            _customerDAL.UpdateCustomer(customer);
                        }
                    }

                    scope.Complete();
                    return invoiceId;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء الفاتورة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث فاتورة موجودة
        /// </summary>
        /// <param name="invoice">بيانات الفاتورة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateInvoice(Invoice invoice)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateInvoice(invoice);

                using (var scope = new TransactionScope())
                {
                    // الحصول على الفاتورة الأصلية لاسترجاع المخزون
                    var originalInvoice = GetInvoiceById(invoice.InvoiceID);
                    if (originalInvoice == null)
                    {
                        throw new Exception("الفاتورة غير موجودة");
                    }

                    // استرجاع المخزون من الفاتورة الأصلية
                    RestoreInventoryFromSale(originalInvoice.InvoiceDetails);

                    // حساب إجماليات الفاتورة الجديدة
                    CalculateInvoiceTotals(invoice);

                    // تحديث الفاتورة
                    _invoiceDAL.UpdateInvoice(invoice);

                    // حفظ تفاصيل الفاتورة الجديدة
                    foreach (var detail in invoice.InvoiceDetails)
                    {
                        detail.CalculateLineTotal();
                    }
                    _invoiceDetailDAL.SaveInvoiceDetails(invoice.InvoiceID, invoice.InvoiceDetails);

                    // تحديث المخزون بالكميات الجديدة
                    UpdateInventoryForSale(invoice.InvoiceDetails);

                    scope.Complete();
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث الفاتورة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إرجاع فاتورة
        /// </summary>
        /// <param name="invoiceId">رقم الفاتورة</param>
        /// <param name="returnReason">سبب الإرجاع</param>
        /// <returns>true إذا تم الإرجاع بنجاح</returns>
        public bool ReturnInvoice(int invoiceId, string returnReason)
        {
            try
            {
                using (var scope = new TransactionScope())
                {
                    // الحصول على الفاتورة
                    var invoice = GetInvoiceById(invoiceId);
                    if (invoice == null)
                    {
                        throw new Exception("الفاتورة غير موجودة");
                    }

                    if (invoice.IsReturned)
                    {
                        throw new Exception("الفاتورة مرتجعة مسبقاً");
                    }

                    // تحديث حالة الفاتورة
                    invoice.IsReturned = true;
                    invoice.ReturnedDate = DateTime.Now;
                    invoice.InvoiceStatus = InvoiceStatuses.Returned;
                    invoice.Notes += $"\nتم الإرجاع في {DateTime.Now:yyyy-MM-dd HH:mm} - السبب: {returnReason}";

                    _invoiceDAL.UpdateInvoice(invoice);

                    // استرجاع المخزون
                    RestoreInventoryFromSale(invoice.InvoiceDetails);

                    // تحديث رصيد العميل إذا كان البيع بالآجل
                    if (invoice.PaymentType == PaymentTypes.Installment && invoice.CustomerID.HasValue)
                    {
                        var customer = _customerDAL.GetCustomerById(invoice.CustomerID.Value);
                        if (customer != null)
                        {
                            customer.DeductFromBalance(invoice.RemainingAmount);
                            _customerDAL.UpdateCustomer(customer);
                        }
                    }

                    scope.Complete();
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إرجاع الفاتورة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على فاتورة بالرقم مع التفاصيل
        /// </summary>
        /// <param name="invoiceId">رقم الفاتورة</param>
        /// <returns>بيانات الفاتورة</returns>
        public Invoice GetInvoiceById(int invoiceId)
        {
            try
            {
                var invoice = _invoiceDAL.GetInvoiceById(invoiceId);
                if (invoice != null)
                {
                    invoice.InvoiceDetails = _invoiceDetailDAL.GetInvoiceDetails(invoiceId);
                }
                return invoice;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على الفاتورة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// البحث عن الفواتير
        /// </summary>
        /// <param name="searchCriteria">معايير البحث</param>
        /// <returns>قائمة الفواتير</returns>
        public List<Invoice> SearchInvoices(InvoiceSearchCriteria searchCriteria)
        {
            try
            {
                return _invoiceDAL.SearchInvoices(searchCriteria);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث عن الفواتير: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات الفاتورة
        /// </summary>
        /// <param name="invoice">بيانات الفاتورة</param>
        private void ValidateInvoice(Invoice invoice)
        {
            if (invoice == null)
                throw new ArgumentException("بيانات الفاتورة مطلوبة");

            if (invoice.InvoiceDetails == null || invoice.InvoiceDetails.Count == 0)
                throw new ArgumentException("تفاصيل الفاتورة مطلوبة");

            if (invoice.TotalAmount <= 0)
                throw new ArgumentException("إجمالي الفاتورة يجب أن يكون أكبر من صفر");

            // التحقق من توفر المنتجات في المخزون
            foreach (var detail in invoice.InvoiceDetails)
            {
                var product = _productDAL.GetProductById(detail.ProductID);
                if (product == null)
                    throw new ArgumentException($"المنتج غير موجود: {detail.ProductName}");

                if (!product.IsAvailable(detail.Quantity))
                    throw new ArgumentException($"الكمية المطلوبة غير متوفرة للمنتج: {detail.ProductName}");
            }

            // التحقق من حد الائتمان للعميل في حالة البيع بالآجل
            if (invoice.PaymentType == PaymentTypes.Installment && invoice.CustomerID.HasValue)
            {
                var customer = _customerDAL.GetCustomerById(invoice.CustomerID.Value);
                if (customer != null && !customer.CanGrantCredit(invoice.RemainingAmount))
                {
                    throw new ArgumentException("تجاوز حد الائتمان المسموح للعميل");
                }
            }
        }

        /// <summary>
        /// حساب إجماليات الفاتورة
        /// </summary>
        /// <param name="invoice">بيانات الفاتورة</param>
        private void CalculateInvoiceTotals(Invoice invoice)
        {
            // حساب إجماليات التفاصيل
            foreach (var detail in invoice.InvoiceDetails)
            {
                detail.CalculateLineTotal();
            }

            // حساب إجماليات الفاتورة
            invoice.CalculateTotal();
        }

        /// <summary>
        /// تحديث المخزون عند البيع
        /// </summary>
        /// <param name="details">تفاصيل الفاتورة</param>
        private void UpdateInventoryForSale(List<InvoiceDetail> details)
        {
            foreach (var detail in details)
            {
                var product = _productDAL.GetProductById(detail.ProductID);
                if (product != null)
                {
                    product.UpdateStock(-detail.Quantity);
                    _productDAL.UpdateProduct(product);
                }
            }
        }

        /// <summary>
        /// استرجاع المخزون عند الإرجاع
        /// </summary>
        /// <param name="details">تفاصيل الفاتورة</param>
        private void RestoreInventoryFromSale(List<InvoiceDetail> details)
        {
            foreach (var detail in details)
            {
                var product = _productDAL.GetProductById(detail.ProductID);
                if (product != null)
                {
                    product.UpdateStock(detail.Quantity);
                    _productDAL.UpdateProduct(product);
                }
            }
        }
    }
}
