using System;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using AredooPOS.BLL;
using AredooPOS.Models;
using AredooPOS.DAL;

namespace AredooPOS.Tests
{
    /// <summary>
    /// اختبارات وحدة طبقة منطق الأعمال للمستخدمين
    /// </summary>
    [TestClass]
    public class UserBLLTests
    {
        #region المتغيرات والإعداد

        private UserBLL _userBLL;
        private string _testConnectionString;
        private const string TestCurrentUser = "test_admin";

        [TestInitialize]
        public void Setup()
        {
            // استخدام قاعدة بيانات اختبار
            _testConnectionString = "Server=.;Database=AredooPOS_Test;Integrated Security=true;";
            _userBLL = new UserBLL(_testConnectionString);
            
            // تنظيف البيانات السابقة
            CleanupTestData();
        }

        [TestCleanup]
        public void Cleanup()
        {
            CleanupTestData();
            _userBLL = null;
        }

        private void CleanupTestData()
        {
            try
            {
                // حذف المستخدمين التجريبيين
                var testUsers = _userBLL.SearchUsers("test_");
                foreach (var user in testUsers)
                {
                    if (user.Username.StartsWith("test_"))
                    {
                        _userBLL.DeleteUser(user.UserID, TestCurrentUser);
                    }
                }
            }
            catch
            {
                // تجاهل الأخطاء في التنظيف
            }
        }

        #endregion

        #region اختبارات إضافة المستخدمين

        [TestMethod]
        public void AddUser_ValidUser_ShouldReturnUserId()
        {
            // Arrange
            var user = CreateTestUser("test_user1");

            // Act
            var userId = _userBLL.AddUser(user, TestCurrentUser);

            // Assert
            Assert.IsTrue(userId > 0, "يجب أن يعيد رقم مستخدم صحيح");
            
            var addedUser = _userBLL.GetUserById(userId);
            Assert.IsNotNull(addedUser, "يجب أن يتم حفظ المستخدم في قاعدة البيانات");
            Assert.AreEqual(user.Username, addedUser.Username, "اسم المستخدم يجب أن يطابق");
            Assert.AreEqual(user.FirstName, addedUser.FirstName, "الاسم الأول يجب أن يطابق");
            Assert.AreEqual(user.LastName, addedUser.LastName, "الاسم الأخير يجب أن يطابق");
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void AddUser_NullUser_ShouldThrowException()
        {
            // Act & Assert
            _userBLL.AddUser(null, TestCurrentUser);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void AddUser_EmptyUsername_ShouldThrowException()
        {
            // Arrange
            var user = CreateTestUser("");

            // Act & Assert
            _userBLL.AddUser(user, TestCurrentUser);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void AddUser_ShortUsername_ShouldThrowException()
        {
            // Arrange
            var user = CreateTestUser("ab"); // أقل من 3 أحرف

            // Act & Assert
            _userBLL.AddUser(user, TestCurrentUser);
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void AddUser_DuplicateUsername_ShouldThrowException()
        {
            // Arrange
            var user1 = CreateTestUser("test_duplicate");
            var user2 = CreateTestUser("test_duplicate");

            // Act
            _userBLL.AddUser(user1, TestCurrentUser);
            
            // Assert
            _userBLL.AddUser(user2, TestCurrentUser); // يجب أن يرمي استثناء
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void AddUser_DuplicateEmail_ShouldThrowException()
        {
            // Arrange
            var user1 = CreateTestUser("test_user1");
            user1.Email = "<EMAIL>";
            
            var user2 = CreateTestUser("test_user2");
            user2.Email = "<EMAIL>";

            // Act
            _userBLL.AddUser(user1, TestCurrentUser);
            
            // Assert
            _userBLL.AddUser(user2, TestCurrentUser); // يجب أن يرمي استثناء
        }

        #endregion

        #region اختبارات تحديث المستخدمين

        [TestMethod]
        public void UpdateUser_ValidUser_ShouldReturnTrue()
        {
            // Arrange
            var user = CreateTestUser("test_update");
            var userId = _userBLL.AddUser(user, TestCurrentUser);
            
            var updatedUser = _userBLL.GetUserById(userId);
            updatedUser.FirstName = "اسم محدث";
            updatedUser.LastName = "عائلة محدثة";
            updatedUser.Email = "<EMAIL>";

            // Act
            var result = _userBLL.UpdateUser(updatedUser, TestCurrentUser);

            // Assert
            Assert.IsTrue(result, "يجب أن يعيد true عند نجاح التحديث");
            
            var retrievedUser = _userBLL.GetUserById(userId);
            Assert.AreEqual("اسم محدث", retrievedUser.FirstName, "الاسم الأول يجب أن يتم تحديثه");
            Assert.AreEqual("عائلة محدثة", retrievedUser.LastName, "الاسم الأخير يجب أن يتم تحديثه");
            Assert.AreEqual("<EMAIL>", retrievedUser.Email, "البريد الإلكتروني يجب أن يتم تحديثه");
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void UpdateUser_NonExistentUser_ShouldThrowException()
        {
            // Arrange
            var user = CreateTestUser("test_nonexistent");
            user.UserID = 99999; // رقم غير موجود

            // Act & Assert
            _userBLL.UpdateUser(user, TestCurrentUser);
        }

        #endregion

        #region اختبارات حذف المستخدمين

        [TestMethod]
        public void DeleteUser_ValidUser_ShouldReturnTrue()
        {
            // Arrange
            var user = CreateTestUser("test_delete");
            var userId = _userBLL.AddUser(user, TestCurrentUser);

            // Act
            var result = _userBLL.DeleteUser(userId, TestCurrentUser);

            // Assert
            Assert.IsTrue(result, "يجب أن يعيد true عند نجاح الحذف");
            
            var deletedUser = _userBLL.GetUserById(userId);
            Assert.IsNull(deletedUser, "المستخدم يجب أن يتم حذفه من قاعدة البيانات");
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void DeleteUser_NonExistentUser_ShouldThrowException()
        {
            // Act & Assert
            _userBLL.DeleteUser(99999, TestCurrentUser); // رقم غير موجود
        }

        #endregion

        #region اختبارات البحث والاستعلام

        [TestMethod]
        public void GetUserByUsername_ExistingUser_ShouldReturnUser()
        {
            // Arrange
            var user = CreateTestUser("test_search");
            var userId = _userBLL.AddUser(user, TestCurrentUser);

            // Act
            var foundUser = _userBLL.GetUserByUsername("test_search");

            // Assert
            Assert.IsNotNull(foundUser, "يجب العثور على المستخدم");
            Assert.AreEqual(userId, foundUser.UserID, "رقم المستخدم يجب أن يطابق");
            Assert.AreEqual("test_search", foundUser.Username, "اسم المستخدم يجب أن يطابق");
        }

        [TestMethod]
        public void GetUserByUsername_NonExistentUser_ShouldReturnNull()
        {
            // Act
            var foundUser = _userBLL.GetUserByUsername("nonexistent_user");

            // Assert
            Assert.IsNull(foundUser, "يجب أن يعيد null للمستخدم غير الموجود");
        }

        [TestMethod]
        public void SearchUsers_WithSearchTerm_ShouldReturnMatchingUsers()
        {
            // Arrange
            var user1 = CreateTestUser("test_search1");
            var user2 = CreateTestUser("test_search2");
            var user3 = CreateTestUser("other_user");
            
            _userBLL.AddUser(user1, TestCurrentUser);
            _userBLL.AddUser(user2, TestCurrentUser);
            _userBLL.AddUser(user3, TestCurrentUser);

            // Act
            var searchResults = _userBLL.SearchUsers("test_search");

            // Assert
            Assert.AreEqual(2, searchResults.Count, "يجب العثور على مستخدمين فقط");
            Assert.IsTrue(searchResults.All(u => u.Username.Contains("test_search")), 
                "جميع النتائج يجب أن تحتوي على مصطلح البحث");
        }

        [TestMethod]
        public void GetAllUsers_IncludeInactive_ShouldReturnAllUsers()
        {
            // Arrange
            var activeUser = CreateTestUser("test_active");
            var inactiveUser = CreateTestUser("test_inactive");
            inactiveUser.IsActive = false;
            
            _userBLL.AddUser(activeUser, TestCurrentUser);
            _userBLL.AddUser(inactiveUser, TestCurrentUser);

            // Act
            var allUsers = _userBLL.GetAllUsers(true);
            var activeUsers = _userBLL.GetAllUsers(false);

            // Assert
            var testUsers = allUsers.Where(u => u.Username.StartsWith("test_")).ToList();
            var testActiveUsers = activeUsers.Where(u => u.Username.StartsWith("test_")).ToList();
            
            Assert.AreEqual(2, testUsers.Count, "يجب أن يعيد جميع المستخدمين التجريبيين");
            Assert.AreEqual(1, testActiveUsers.Count, "يجب أن يعيد المستخدمين النشطين فقط");
        }

        #endregion

        #region اختبارات كلمات المرور

        [TestMethod]
        public void ChangePassword_ValidOldPassword_ShouldReturnTrue()
        {
            // Arrange
            var user = CreateTestUser("test_password");
            var originalPassword = "TestPassword123!";
            user.PasswordHash = UserBLL.PasswordHelper.HashPassword(originalPassword, user.PasswordSalt);
            
            var userId = _userBLL.AddUser(user, TestCurrentUser);
            var newPassword = "NewPassword456!";

            // Act
            var result = _userBLL.ChangePassword(userId, originalPassword, newPassword, TestCurrentUser);

            // Assert
            Assert.IsTrue(result, "يجب أن يعيد true عند نجاح تغيير كلمة المرور");
            
            // التحقق من أن كلمة المرور الجديدة تعمل
            var updatedUser = _userBLL.GetUserById(userId);
            var isNewPasswordValid = UserBLL.PasswordHelper.VerifyPassword(newPassword, 
                updatedUser.PasswordHash, updatedUser.PasswordSalt);
            Assert.IsTrue(isNewPasswordValid, "كلمة المرور الجديدة يجب أن تعمل");
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void ChangePassword_InvalidOldPassword_ShouldThrowException()
        {
            // Arrange
            var user = CreateTestUser("test_password_fail");
            var originalPassword = "TestPassword123!";
            user.PasswordHash = UserBLL.PasswordHelper.HashPassword(originalPassword, user.PasswordSalt);
            
            var userId = _userBLL.AddUser(user, TestCurrentUser);
            var wrongOldPassword = "WrongPassword";
            var newPassword = "NewPassword456!";

            // Act & Assert
            _userBLL.ChangePassword(userId, wrongOldPassword, newPassword, TestCurrentUser);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void ChangePassword_WeakNewPassword_ShouldThrowException()
        {
            // Arrange
            var user = CreateTestUser("test_weak_password");
            var originalPassword = "TestPassword123!";
            user.PasswordHash = UserBLL.PasswordHelper.HashPassword(originalPassword, user.PasswordSalt);
            
            var userId = _userBLL.AddUser(user, TestCurrentUser);
            var weakPassword = "123"; // كلمة مرور ضعيفة

            // Act & Assert
            _userBLL.ChangePassword(userId, originalPassword, weakPassword, TestCurrentUser);
        }

        #endregion

        #region اختبارات الأمان

        [TestMethod]
        public void LockUser_ValidUser_ShouldReturnTrue()
        {
            // Arrange
            var user = CreateTestUser("test_lock");
            var userId = _userBLL.AddUser(user, TestCurrentUser);
            var lockReason = "اختبار القفل";

            // Act
            var result = _userBLL.LockUser(userId, lockReason, TestCurrentUser);

            // Assert
            Assert.IsTrue(result, "يجب أن يعيد true عند نجاح القفل");
            
            var lockedUser = _userBLL.GetUserById(userId);
            Assert.IsTrue(lockedUser.IsLocked, "المستخدم يجب أن يكون مقفل");
            Assert.AreEqual(lockReason, lockedUser.LockReason, "سبب القفل يجب أن يطابق");
        }

        [TestMethod]
        public void UnlockUser_LockedUser_ShouldReturnTrue()
        {
            // Arrange
            var user = CreateTestUser("test_unlock");
            var userId = _userBLL.AddUser(user, TestCurrentUser);
            
            // قفل المستخدم أولاً
            _userBLL.LockUser(userId, "اختبار القفل", TestCurrentUser);

            // Act
            var result = _userBLL.UnlockUser(userId, TestCurrentUser);

            // Assert
            Assert.IsTrue(result, "يجب أن يعيد true عند نجاح إلغاء القفل");
            
            var unlockedUser = _userBLL.GetUserById(userId);
            Assert.IsFalse(unlockedUser.IsLocked, "المستخدم يجب أن يكون غير مقفل");
        }

        #endregion

        #region اختبارات الإحصائيات

        [TestMethod]
        public void GetUserStatistics_ShouldReturnValidStatistics()
        {
            // Arrange
            var user1 = CreateTestUser("test_stats1");
            var user2 = CreateTestUser("test_stats2");
            user2.IsActive = false;
            
            _userBLL.AddUser(user1, TestCurrentUser);
            _userBLL.AddUser(user2, TestCurrentUser);

            // Act
            var statistics = _userBLL.GetUserStatistics();

            // Assert
            Assert.IsNotNull(statistics, "الإحصائيات يجب أن تكون غير null");
            Assert.IsTrue(statistics.TotalUsers >= 2, "إجمالي المستخدمين يجب أن يكون على الأقل 2");
            Assert.IsTrue(statistics.ActiveUsers >= 1, "المستخدمين النشطين يجب أن يكون على الأقل 1");
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// إنشاء مستخدم تجريبي
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>المستخدم التجريبي</returns>
        private User CreateTestUser(string username)
        {
            var salt = UserBLL.PasswordHelper.GenerateSalt();
            var password = "TestPassword123!";
            var hash = UserBLL.PasswordHelper.HashPassword(password, salt);

            return new User
            {
                Username = username,
                FirstName = "اسم تجريبي",
                LastName = "عائلة تجريبية",
                Email = $"{username}@test.com",
                PhoneNumber = "1234567890",
                RoleID = 1, // افتراض وجود دور بالرقم 1
                PasswordHash = hash,
                PasswordSalt = salt,
                IsActive = true,
                CanAccessSystem = true,
                PermissionLevel = 1
            };
        }

        #endregion
    }
}
