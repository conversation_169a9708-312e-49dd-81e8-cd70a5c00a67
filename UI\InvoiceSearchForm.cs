using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using AridooPOS.Models;
using AridooPOS.BLL;
using AridooPOS.DAL;

namespace AridooPOS.UI
{
    public partial class InvoiceSearchForm : Form
    {
        private readonly InvoiceBLL _invoiceBLL;
        private List<Invoice> _invoices;

        public InvoiceSearchForm()
        {
            InitializeComponent();
            InitializeArabicUI();
            InitializeData();
        }

        private void InitializeComponent()
        {
            this.panel1 = new Panel();
            this.lblTitle = new Label();
            this.panel2 = new Panel();
            this.lblInvoiceNumber = new Label();
            this.txtInvoiceNumber = new TextBox();
            this.lblCustomerName = new Label();
            this.txtCustomerName = new TextBox();
            this.lblFromDate = new Label();
            this.dtpFromDate = new DateTimePicker();
            this.lblToDate = new Label();
            this.dtpToDate = new DateTimePicker();
            this.lblPaymentType = new Label();
            this.cmbPaymentType = new ComboBox();
            this.lblStatus = new Label();
            this.cmbStatus = new ComboBox();
            this.btnSearch = new Button();
            this.btnClear = new Button();
            this.dgvInvoices = new DataGridView();
            this.panel3 = new Panel();
            this.btnView = new Button();
            this.btnEdit = new Button();
            this.btnReturn = new Button();
            this.btnPrint = new Button();
            this.btnClose = new Button();

            this.panel1.SuspendLayout();
            this.panel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvInvoices)).BeginInit();
            this.panel3.SuspendLayout();
            this.SuspendLayout();

            // panel1 - Header
            this.panel1.Controls.Add(this.lblTitle);
            this.panel1.Dock = DockStyle.Top;
            this.panel1.Location = new Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new Size(1200, 50);
            this.panel1.TabIndex = 0;
            this.panel1.BackColor = Color.FromArgb(52, 152, 219);

            // lblTitle
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Tahoma", 16F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(500, 15);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(200, 27);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "البحث عن الفواتير";

            // panel2 - Search Criteria
            this.panel2.Controls.Add(this.lblInvoiceNumber);
            this.panel2.Controls.Add(this.txtInvoiceNumber);
            this.panel2.Controls.Add(this.lblCustomerName);
            this.panel2.Controls.Add(this.txtCustomerName);
            this.panel2.Controls.Add(this.lblFromDate);
            this.panel2.Controls.Add(this.dtpFromDate);
            this.panel2.Controls.Add(this.lblToDate);
            this.panel2.Controls.Add(this.dtpToDate);
            this.panel2.Controls.Add(this.lblPaymentType);
            this.panel2.Controls.Add(this.cmbPaymentType);
            this.panel2.Controls.Add(this.lblStatus);
            this.panel2.Controls.Add(this.cmbStatus);
            this.panel2.Controls.Add(this.btnSearch);
            this.panel2.Controls.Add(this.btnClear);
            this.panel2.Dock = DockStyle.Top;
            this.panel2.Location = new Point(0, 50);
            this.panel2.Name = "panel2";
            this.panel2.Size = new Size(1200, 100);
            this.panel2.TabIndex = 1;
            this.panel2.BackColor = Color.FromArgb(236, 240, 241);

            // Search controls - First row
            this.lblInvoiceNumber.AutoSize = true;
            this.lblInvoiceNumber.Location = new Point(1100, 15);
            this.lblInvoiceNumber.Name = "lblInvoiceNumber";
            this.lblInvoiceNumber.Size = new Size(70, 13);
            this.lblInvoiceNumber.TabIndex = 0;
            this.lblInvoiceNumber.Text = "رقم الفاتورة:";

            this.txtInvoiceNumber.Location = new Point(950, 12);
            this.txtInvoiceNumber.Name = "txtInvoiceNumber";
            this.txtInvoiceNumber.Size = new Size(140, 20);
            this.txtInvoiceNumber.TabIndex = 1;

            this.lblCustomerName.AutoSize = true;
            this.lblCustomerName.Location = new Point(850, 15);
            this.lblCustomerName.Name = "lblCustomerName";
            this.lblCustomerName.Size = new Size(70, 13);
            this.lblCustomerName.TabIndex = 2;
            this.lblCustomerName.Text = "اسم العميل:";

            this.txtCustomerName.Location = new Point(700, 12);
            this.txtCustomerName.Name = "txtCustomerName";
            this.txtCustomerName.Size = new Size(140, 20);
            this.txtCustomerName.TabIndex = 3;

            this.lblFromDate.AutoSize = true;
            this.lblFromDate.Location = new Point(600, 15);
            this.lblFromDate.Name = "lblFromDate";
            this.lblFromDate.Size = new Size(50, 13);
            this.lblFromDate.TabIndex = 4;
            this.lblFromDate.Text = "من تاريخ:";

            this.dtpFromDate.Location = new Point(450, 12);
            this.dtpFromDate.Name = "dtpFromDate";
            this.dtpFromDate.Size = new Size(140, 20);
            this.dtpFromDate.TabIndex = 5;

            this.lblToDate.AutoSize = true;
            this.lblToDate.Location = new Point(350, 15);
            this.lblToDate.Name = "lblToDate";
            this.lblToDate.Size = new Size(50, 13);
            this.lblToDate.TabIndex = 6;
            this.lblToDate.Text = "إلى تاريخ:";

            this.dtpToDate.Location = new Point(200, 12);
            this.dtpToDate.Name = "dtpToDate";
            this.dtpToDate.Size = new Size(140, 20);
            this.dtpToDate.TabIndex = 7;

            // Search controls - Second row
            this.lblPaymentType.AutoSize = true;
            this.lblPaymentType.Location = new Point(1100, 50);
            this.lblPaymentType.Name = "lblPaymentType";
            this.lblPaymentType.Size = new Size(60, 13);
            this.lblPaymentType.TabIndex = 8;
            this.lblPaymentType.Text = "نوع الدفع:";

            this.cmbPaymentType.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbPaymentType.Location = new Point(950, 47);
            this.cmbPaymentType.Name = "cmbPaymentType";
            this.cmbPaymentType.Size = new Size(140, 21);
            this.cmbPaymentType.TabIndex = 9;

            this.lblStatus.AutoSize = true;
            this.lblStatus.Location = new Point(850, 50);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new Size(40, 13);
            this.lblStatus.TabIndex = 10;
            this.lblStatus.Text = "الحالة:";

            this.cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cmbStatus.Location = new Point(700, 47);
            this.cmbStatus.Name = "cmbStatus";
            this.cmbStatus.Size = new Size(140, 21);
            this.cmbStatus.TabIndex = 11;

            this.btnSearch.BackColor = Color.FromArgb(46, 204, 113);
            this.btnSearch.FlatStyle = FlatStyle.Flat;
            this.btnSearch.ForeColor = Color.White;
            this.btnSearch.Location = new Point(500, 45);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new Size(80, 25);
            this.btnSearch.TabIndex = 12;
            this.btnSearch.Text = "بحث";
            this.btnSearch.UseVisualStyleBackColor = false;
            this.btnSearch.Click += new EventHandler(this.btnSearch_Click);

            this.btnClear.BackColor = Color.FromArgb(149, 165, 166);
            this.btnClear.FlatStyle = FlatStyle.Flat;
            this.btnClear.ForeColor = Color.White;
            this.btnClear.Location = new Point(400, 45);
            this.btnClear.Name = "btnClear";
            this.btnClear.Size = new Size(80, 25);
            this.btnClear.TabIndex = 13;
            this.btnClear.Text = "مسح";
            this.btnClear.UseVisualStyleBackColor = false;
            this.btnClear.Click += new EventHandler(this.btnClear_Click);

            // dgvInvoices
            this.dgvInvoices.AllowUserToAddRows = false;
            this.dgvInvoices.AllowUserToDeleteRows = false;
            this.dgvInvoices.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvInvoices.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvInvoices.Dock = DockStyle.Fill;
            this.dgvInvoices.Location = new Point(0, 150);
            this.dgvInvoices.MultiSelect = false;
            this.dgvInvoices.Name = "dgvInvoices";
            this.dgvInvoices.ReadOnly = true;
            this.dgvInvoices.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvInvoices.Size = new Size(1200, 450);
            this.dgvInvoices.TabIndex = 2;
            this.dgvInvoices.DoubleClick += new EventHandler(this.dgvInvoices_DoubleClick);

            // panel3 - Action buttons
            this.panel3.Controls.Add(this.btnView);
            this.panel3.Controls.Add(this.btnEdit);
            this.panel3.Controls.Add(this.btnReturn);
            this.panel3.Controls.Add(this.btnPrint);
            this.panel3.Controls.Add(this.btnClose);
            this.panel3.Dock = DockStyle.Bottom;
            this.panel3.Location = new Point(0, 600);
            this.panel3.Name = "panel3";
            this.panel3.Size = new Size(1200, 50);
            this.panel3.TabIndex = 3;

            this.btnView.BackColor = Color.FromArgb(52, 152, 219);
            this.btnView.FlatStyle = FlatStyle.Flat;
            this.btnView.ForeColor = Color.White;
            this.btnView.Location = new Point(950, 10);
            this.btnView.Name = "btnView";
            this.btnView.Size = new Size(80, 30);
            this.btnView.TabIndex = 0;
            this.btnView.Text = "عرض";
            this.btnView.UseVisualStyleBackColor = false;
            this.btnView.Click += new EventHandler(this.btnView_Click);

            this.btnEdit.BackColor = Color.FromArgb(241, 196, 15);
            this.btnEdit.FlatStyle = FlatStyle.Flat;
            this.btnEdit.ForeColor = Color.White;
            this.btnEdit.Location = new Point(850, 10);
            this.btnEdit.Name = "btnEdit";
            this.btnEdit.Size = new Size(80, 30);
            this.btnEdit.TabIndex = 1;
            this.btnEdit.Text = "تعديل";
            this.btnEdit.UseVisualStyleBackColor = false;
            this.btnEdit.Click += new EventHandler(this.btnEdit_Click);

            this.btnReturn.BackColor = Color.FromArgb(231, 76, 60);
            this.btnReturn.FlatStyle = FlatStyle.Flat;
            this.btnReturn.ForeColor = Color.White;
            this.btnReturn.Location = new Point(750, 10);
            this.btnReturn.Name = "btnReturn";
            this.btnReturn.Size = new Size(80, 30);
            this.btnReturn.TabIndex = 2;
            this.btnReturn.Text = "إرجاع";
            this.btnReturn.UseVisualStyleBackColor = false;
            this.btnReturn.Click += new EventHandler(this.btnReturn_Click);

            this.btnPrint.BackColor = Color.FromArgb(155, 89, 182);
            this.btnPrint.FlatStyle = FlatStyle.Flat;
            this.btnPrint.ForeColor = Color.White;
            this.btnPrint.Location = new Point(650, 10);
            this.btnPrint.Name = "btnPrint";
            this.btnPrint.Size = new Size(80, 30);
            this.btnPrint.TabIndex = 3;
            this.btnPrint.Text = "طباعة";
            this.btnPrint.UseVisualStyleBackColor = false;
            this.btnPrint.Click += new EventHandler(this.btnPrint_Click);

            this.btnClose.BackColor = Color.FromArgb(149, 165, 166);
            this.btnClose.FlatStyle = FlatStyle.Flat;
            this.btnClose.ForeColor = Color.White;
            this.btnClose.Location = new Point(50, 10);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new Size(80, 30);
            this.btnClose.TabIndex = 4;
            this.btnClose.Text = "إغلاق";
            this.btnClose.UseVisualStyleBackColor = false;
            this.btnClose.Click += new EventHandler(this.btnClose_Click);

            // InvoiceSearchForm
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 650);
            this.Controls.Add(this.dgvInvoices);
            this.Controls.Add(this.panel3);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel1);
            this.Name = "InvoiceSearchForm";
            this.Text = "البحث عن الفواتير - أريدوو";
            this.WindowState = FormWindowState.Maximized;

            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvInvoices)).EndInit();
            this.panel3.ResumeLayout(false);
            this.ResumeLayout(false);
        }

        private void InitializeArabicUI()
        {
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void InitializeData()
        {
            _invoiceBLL = new InvoiceBLL();
            _invoices = new List<Invoice>();

            SetupComboBoxes();
            SetupDataGridView();
            SetDefaultDates();
            LoadAllInvoices();
        }

        private void SetupComboBoxes()
        {
            // إعداد أنواع الدفع
            cmbPaymentType.Items.Add("الكل");
            cmbPaymentType.Items.Add(PaymentTypes.Cash);
            cmbPaymentType.Items.Add(PaymentTypes.Card);
            cmbPaymentType.Items.Add(PaymentTypes.Installment);
            cmbPaymentType.Items.Add(PaymentTypes.Mixed);
            cmbPaymentType.SelectedIndex = 0;

            // إعداد حالات الفاتورة
            cmbStatus.Items.Add("الكل");
            cmbStatus.Items.Add(InvoiceStatuses.Completed);
            cmbStatus.Items.Add(InvoiceStatuses.Cancelled);
            cmbStatus.Items.Add(InvoiceStatuses.Returned);
            cmbStatus.Items.Add(InvoiceStatuses.Pending);
            cmbStatus.SelectedIndex = 0;
        }

        private void SetupDataGridView()
        {
            dgvInvoices.Columns.Clear();
            dgvInvoices.Columns.Add("InvoiceNumber", "رقم الفاتورة");
            dgvInvoices.Columns.Add("InvoiceDate", "التاريخ");
            dgvInvoices.Columns.Add("CustomerName", "العميل");
            dgvInvoices.Columns.Add("TotalAmount", "المبلغ الإجمالي");
            dgvInvoices.Columns.Add("PaymentType", "نوع الدفع");
            dgvInvoices.Columns.Add("InvoiceStatus", "الحالة");
            dgvInvoices.Columns.Add("CashierName", "الكاشير");

            dgvInvoices.Columns["InvoiceDate"].DefaultCellStyle.Format = "yyyy-MM-dd HH:mm";
            dgvInvoices.Columns["TotalAmount"].DefaultCellStyle.Format = "N2";
            dgvInvoices.Columns["TotalAmount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        }

        private void SetDefaultDates()
        {
            dtpFromDate.Value = DateTime.Today.AddDays(-30);
            dtpToDate.Value = DateTime.Today;
        }

        private void LoadAllInvoices()
        {
            btnSearch_Click(null, null);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                var searchCriteria = new InvoiceSearchCriteria
                {
                    InvoiceNumber = txtInvoiceNumber.Text.Trim(),
                    CustomerName = txtCustomerName.Text.Trim(),
                    FromDate = dtpFromDate.Value.Date,
                    ToDate = dtpToDate.Value.Date,
                    PaymentType = cmbPaymentType.SelectedIndex > 0 ? cmbPaymentType.Text : null,
                    InvoiceStatus = cmbStatus.SelectedIndex > 0 ? cmbStatus.Text : null
                };

                _invoices = _invoiceBLL.SearchInvoices(searchCriteria);
                LoadInvoicesToGrid();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadInvoicesToGrid()
        {
            dgvInvoices.Rows.Clear();

            foreach (var invoice in _invoices)
            {
                dgvInvoices.Rows.Add(
                    invoice.InvoiceNumber,
                    invoice.InvoiceDate,
                    invoice.CustomerName,
                    invoice.TotalAmount,
                    invoice.PaymentType,
                    invoice.InvoiceStatus,
                    invoice.CashierName
                );
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            txtInvoiceNumber.Clear();
            txtCustomerName.Clear();
            dtpFromDate.Value = DateTime.Today.AddDays(-30);
            dtpToDate.Value = DateTime.Today;
            cmbPaymentType.SelectedIndex = 0;
            cmbStatus.SelectedIndex = 0;
        }

        private void dgvInvoices_DoubleClick(object sender, EventArgs e)
        {
            btnView_Click(sender, e);
        }

        private void btnView_Click(object sender, EventArgs e)
        {
            var selectedInvoice = GetSelectedInvoice();
            if (selectedInvoice != null)
            {
                // عرض تفاصيل الفاتورة
                InvoiceViewForm viewForm = new InvoiceViewForm(selectedInvoice.InvoiceID);
                viewForm.ShowDialog();
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            var selectedInvoice = GetSelectedInvoice();
            if (selectedInvoice != null)
            {
                if (selectedInvoice.IsReturned)
                {
                    MessageBox.Show("لا يمكن تعديل فاتورة مرتجعة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // فتح نموذج التعديل
                InvoiceForm editForm = new InvoiceForm(selectedInvoice.InvoiceID);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadAllInvoices(); // إعادة تحميل القائمة
                }
            }
        }

        private void btnReturn_Click(object sender, EventArgs e)
        {
            var selectedInvoice = GetSelectedInvoice();
            if (selectedInvoice != null)
            {
                if (selectedInvoice.IsReturned)
                {
                    MessageBox.Show("الفاتورة مرتجعة مسبقاً", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show($"هل تريد إرجاع الفاتورة رقم {selectedInvoice.InvoiceNumber}؟",
                    "تأكيد الإرجاع", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        string reason = Microsoft.VisualBasic.Interaction.InputBox(
                            "يرجى إدخال سبب الإرجاع:", "سبب الإرجاع", "");

                        if (!string.IsNullOrEmpty(reason))
                        {
                            _invoiceBLL.ReturnInvoice(selectedInvoice.InvoiceID, reason);
                            MessageBox.Show("تم إرجاع الفاتورة بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadAllInvoices();
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في إرجاع الفاتورة: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            var selectedInvoice = GetSelectedInvoice();
            if (selectedInvoice != null)
            {
                try
                {
                    // طباعة الفاتورة
                    InvoicePrintHelper.PrintInvoice(selectedInvoice.InvoiceID);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private Invoice GetSelectedInvoice()
        {
            if (dgvInvoices.SelectedRows.Count > 0)
            {
                int selectedIndex = dgvInvoices.SelectedRows[0].Index;
                if (selectedIndex >= 0 && selectedIndex < _invoices.Count)
                {
                    return _invoices[selectedIndex];
                }
            }

            MessageBox.Show("يرجى اختيار فاتورة", "تنبيه",
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return null;
        }

        #region Designer Variables
        private Panel panel1;
        private Label lblTitle;
        private Panel panel2;
        private Label lblInvoiceNumber;
        private TextBox txtInvoiceNumber;
        private Label lblCustomerName;
        private TextBox txtCustomerName;
        private Label lblFromDate;
        private DateTimePicker dtpFromDate;
        private Label lblToDate;
        private DateTimePicker dtpToDate;
        private Label lblPaymentType;
        private ComboBox cmbPaymentType;
        private Label lblStatus;
        private ComboBox cmbStatus;
        private Button btnSearch;
        private Button btnClear;
        private DataGridView dgvInvoices;
        private Panel panel3;
        private Button btnView;
        private Button btnEdit;
        private Button btnReturn;
        private Button btnPrint;
        private Button btnClose;
        #endregion
    }
}
