using System;
using System.Globalization;
using System.Threading;
using System.Windows.Forms;
using System.Drawing;

namespace AredooPOS.SimpleTest
{
    /// <summary>
    /// اختبار بسيط لنظام أريدو POS
    /// </summary>
    internal static class SimpleTest
    {
        /// <summary>
        /// نقطة الدخول للاختبار البسيط
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // تهيئة التطبيق
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // تعيين الثقافة العربية
                SetArabicCulture();

                // إنشاء نموذج اختبار
                var testForm = CreateTestForm();

                // تطبيق التخطيط العربي البسيط
                ApplySimpleArabicLayout(testForm);

                // تشغيل التطبيق
                Application.Run(testForm);

                MessageBox.Show("تم تشغيل الاختبار بنجاح!", "نجح الاختبار", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تعيين الثقافة العربية
        /// </summary>
        private static void SetArabicCulture()
        {
            try
            {
                var arabicCulture = new CultureInfo("ar-SA");
                Thread.CurrentThread.CurrentCulture = arabicCulture;
                Thread.CurrentThread.CurrentUICulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentCulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentUICulture = arabicCulture;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"تحذير: فشل في تعيين الثقافة العربية - {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق التخطيط العربي البسيط
        /// </summary>
        /// <param name="form">النموذج</param>
        private static void ApplySimpleArabicLayout(Form form)
        {
            try
            {
                // تعيين الاتجاه من اليمين لليسار
                form.RightToLeft = RightToLeft.Yes;
                form.RightToLeftLayout = true;

                // تعيين خط عربي
                form.Font = new Font("Tahoma", 9, FontStyle.Regular);

                // تطبيق على جميع العناصر الفرعية
                ApplyToChildren(form);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تطبيق التخطيط العربي: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق التخطيط على العناصر الفرعية
        /// </summary>
        /// <param name="parent">العنصر الأب</param>
        private static void ApplyToChildren(Control parent)
        {
            foreach (Control child in parent.Controls)
            {
                child.RightToLeft = RightToLeft.Yes;
                child.Font = new Font("Tahoma", 9, FontStyle.Regular);
                ApplyToChildren(child);
            }
        }

        /// <summary>
        /// إنشاء نموذج اختبار
        /// </summary>
        /// <returns>نموذج الاختبار</returns>
        private static Form CreateTestForm()
        {
            var form = new Form
            {
                Text = "اختبار نظام أريدو POS - نسخة بسيطة",
                Size = new Size(800, 600),
                StartPosition = FormStartPosition.CenterScreen,
                WindowState = FormWindowState.Normal,
                BackColor = Color.White
            };

            // إضافة شريط قوائم
            var menuStrip = new MenuStrip();
            
            // قائمة اختبار
            var testMenu = new ToolStripMenuItem("اختبار");
            testMenu.DropDownItems.Add("اختبار النظام", null, OnTestSystem);
            testMenu.DropDownItems.Add("معلومات النظام", null, OnSystemInfo);
            testMenu.DropDownItems.Add(new ToolStripSeparator());
            testMenu.DropDownItems.Add("خروج", null, (s, e) => Application.Exit());
            
            // قائمة المساعدة
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add("حول البرنامج", null, OnAbout);
            
            menuStrip.Items.Add(testMenu);
            menuStrip.Items.Add(helpMenu);
            
            // إضافة محتوى
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(20)
            };

            var welcomeLabel = new Label
            {
                Text = "🎉 مرحباً بك في اختبار نظام أريدو POS 🎉\n\n" +
                       "✅ تم تشغيل النظام بنجاح\n" +
                       "✅ الواجهة العربية تعمل بشكل صحيح\n" +
                       "✅ التخطيط من اليمين لليسار مفعل\n" +
                       "✅ الخطوط العربية محملة\n\n" +
                       "🔧 المكونات المطورة:\n" +
                       "• نظام إدارة قاعدة البيانات\n" +
                       "• نظام نقاط البيع والفواتير\n" +
                       "• إدارة العملاء والديون\n" +
                       "• نظام الطباعة الحرارية\n" +
                       "• نظام الباركود\n" +
                       "• الواجهة العربية الكاملة\n" +
                       "• التوافق مع Windows 7+\n\n" +
                       "📋 اختر من القائمة أعلاه لاختبار المكونات",
                Font = new Font("Tahoma", 11, FontStyle.Regular),
                TextAlign = ContentAlignment.TopRight,
                Dock = DockStyle.Fill,
                ForeColor = Color.FromArgb(44, 62, 80),
                AutoSize = false
            };

            // إضافة أزرار اختبار
            var buttonPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 60,
                BackColor = Color.FromArgb(240, 240, 240),
                Padding = new Padding(10)
            };

            var testButton = new Button
            {
                Text = "اختبار سريع",
                Size = new Size(120, 40),
                Location = new Point(10, 10),
                BackColor = Color.FromArgb(41, 128, 185),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            testButton.FlatAppearance.BorderSize = 0;
            testButton.Click += OnQuickTest;

            var infoButton = new Button
            {
                Text = "معلومات النظام",
                Size = new Size(120, 40),
                Location = new Point(140, 10),
                BackColor = Color.FromArgb(39, 174, 96),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            infoButton.FlatAppearance.BorderSize = 0;
            infoButton.Click += OnSystemInfo;

            var exitButton = new Button
            {
                Text = "خروج",
                Size = new Size(120, 40),
                Location = new Point(270, 10),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += (s, e) => Application.Exit();

            buttonPanel.Controls.Add(testButton);
            buttonPanel.Controls.Add(infoButton);
            buttonPanel.Controls.Add(exitButton);

            panel.Controls.Add(welcomeLabel);
            
            form.MainMenuStrip = menuStrip;
            form.Controls.Add(menuStrip);
            form.Controls.Add(panel);
            form.Controls.Add(buttonPanel);

            return form;
        }

        /// <summary>
        /// اختبار سريع للنظام
        /// </summary>
        private static void OnQuickTest(object sender, EventArgs e)
        {
            try
            {
                var results = new System.Text.StringBuilder();
                results.AppendLine("🔍 نتائج الاختبار السريع:\n");

                // اختبار الثقافة العربية
                var culture = Thread.CurrentThread.CurrentCulture;
                results.AppendLine($"✅ الثقافة: {culture.DisplayName} ({culture.Name})");

                // اختبار التاريخ العربي
                var arabicDate = DateTime.Now.ToString("dd/MM/yyyy", culture);
                results.AppendLine($"✅ التاريخ العربي: {arabicDate}");

                // اختبار الأرقام العربية
                var arabicNumber = (12345.67m).ToString("N2", culture);
                results.AppendLine($"✅ الأرقام العربية: {arabicNumber}");

                // اختبار نظام التشغيل
                var osVersion = Environment.OSVersion;
                results.AppendLine($"✅ نظام التشغيل: {osVersion.VersionString}");

                // اختبار البنية
                var architecture = Environment.Is64BitOperatingSystem ? "64-bit" : "32-bit";
                results.AppendLine($"✅ البنية: {architecture}");

                // اختبار .NET Framework
                var netVersion = Environment.Version;
                results.AppendLine($"✅ .NET Framework: {netVersion}");

                results.AppendLine("\n🎉 جميع الاختبارات نجحت!");

                MessageBox.Show(results.ToString(), "نتائج الاختبار السريع", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الاختبار: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض معلومات النظام
        /// </summary>
        private static void OnSystemInfo(object sender, EventArgs e)
        {
            try
            {
                var info = new System.Text.StringBuilder();
                info.AppendLine("💻 معلومات النظام:\n");

                info.AppendLine($"اسم الجهاز: {Environment.MachineName}");
                info.AppendLine($"اسم المستخدم: {Environment.UserName}");
                info.AppendLine($"نظام التشغيل: {Environment.OSVersion}");
                info.AppendLine($"البنية: {(Environment.Is64BitOperatingSystem ? "64-bit" : "32-bit")}");
                info.AppendLine($"عدد المعالجات: {Environment.ProcessorCount}");
                info.AppendLine($"الذاكرة المستخدمة: {Environment.WorkingSet / (1024 * 1024)} MB");
                info.AppendLine($"مجلد النظام: {Environment.SystemDirectory}");
                info.AppendLine($"مجلد التطبيق: {Application.StartupPath}");
                info.AppendLine($".NET Framework: {Environment.Version}");

                MessageBox.Show(info.ToString(), "معلومات النظام", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض معلومات النظام: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار النظام
        /// </summary>
        private static void OnTestSystem(object sender, EventArgs e)
        {
            OnQuickTest(sender, e);
        }

        /// <summary>
        /// عرض معلومات حول البرنامج
        /// </summary>
        private static void OnAbout(object sender, EventArgs e)
        {
            MessageBox.Show(
                "🏪 نظام أريدو POS\n" +
                "📅 الإصدار 1.0.0\n\n" +
                "📝 وصف النظام:\n" +
                "نظام شامل لإدارة نقاط البيع يدعم اللغة العربية\n" +
                "والطابعات الحرارية مع جميع الميزات المطلوبة\n\n" +
                "🔧 الميزات المطورة:\n" +
                "• إدارة المبيعات والفواتير\n" +
                "• إدارة المخزون والمنتجات\n" +
                "• إدارة العملاء والديون\n" +
                "• نظام الدفع والأقساط\n" +
                "• إدارة الصندوق والجلسات\n" +
                "• التقارير المتقدمة\n" +
                "• دعم الطابعات الحرارية\n" +
                "• نظام الباركود المتكامل\n" +
                "• واجهة عربية كاملة مع RTL\n" +
                "• التوافق مع Windows 7+\n" +
                "• قاعدة بيانات SQL Server\n" +
                "• نظام النسخ الاحتياطي\n" +
                "• المزامنة والعمل دون اتصال\n\n" +
                "© 2024 أريدو. جميع الحقوق محفوظة.",
                "حول نظام أريدو POS",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }
    }
}
