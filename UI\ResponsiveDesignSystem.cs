using System;
using System.Drawing;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// نظام التصميم المتجاوب المتقدم مع ألوان وخطوط متكيفة
    /// </summary>
    public static class ResponsiveDesignSystem
    {
        #region نظام الألوان المتجاوب

        /// <summary>
        /// مجموعة الألوان المتجاوبة
        /// </summary>
        public static class Colors
        {
            // الألوان الأساسية - متوافقة مع جميع الشاشات
            public static readonly Color Primary = Color.FromArgb(0, 120, 215);        // أزرق Microsoft
            public static readonly Color PrimaryHover = Color.FromArgb(16, 110, 190);  // أزرق عند التمرير
            public static readonly Color PrimaryPressed = Color.FromArgb(0, 90, 158);  // أزرق عند الضغط
            public static readonly Color PrimaryLight = Color.FromArgb(173, 216, 255); // أزرق فاتح
            public static readonly Color PrimaryDark = Color.FromArgb(0, 78, 140);     // أزرق داكن

            // الألوان الثانوية
            public static readonly Color Secondary = Color.FromArgb(16, 124, 16);      // أخضر
            public static readonly Color SecondaryHover = Color.FromArgb(28, 140, 28); // أخضر عند التمرير
            public static readonly Color SecondaryPressed = Color.FromArgb(12, 94, 12); // أخضر عند الضغط
            public static readonly Color SecondaryLight = Color.FromArgb(198, 239, 206); // أخضر فاتح
            public static readonly Color SecondaryDark = Color.FromArgb(10, 80, 10);   // أخضر داكن

            // ألوان الخلفية المتدرجة
            public static readonly Color Background = Color.FromArgb(248, 249, 250);   // رمادي فاتح جداً
            public static readonly Color Surface = Color.White;                        // أبيض نقي
            public static readonly Color SurfaceVariant = Color.FromArgb(245, 245, 245); // رمادي فاتح
            public static readonly Color SurfaceHover = Color.FromArgb(243, 242, 241); // سطح عند التمرير
            public static readonly Color SurfacePressed = Color.FromArgb(237, 235, 233); // سطح عند الضغط

            // ألوان النص المتكيفة
            public static readonly Color TextPrimary = Color.FromArgb(32, 31, 30);     // أسود ناعم
            public static readonly Color TextSecondary = Color.FromArgb(96, 94, 92);   // رمادي متوسط
            public static readonly Color TextTertiary = Color.FromArgb(161, 159, 157); // رمادي فاتح
            public static readonly Color TextOnPrimary = Color.White;                  // أبيض على الأزرق
            public static readonly Color TextOnSecondary = Color.White;                // أبيض على الأخضر

            // ألوان الحالة
            public static readonly Color Success = Color.FromArgb(16, 124, 16);        // أخضر نجاح
            public static readonly Color Warning = Color.FromArgb(255, 185, 0);        // أصفر تحذير
            public static readonly Color Error = Color.FromArgb(196, 43, 28);          // أحمر خطأ
            public static readonly Color Info = Color.FromArgb(0, 120, 215);           // أزرق معلومات

            // ألوان الحدود والظلال
            public static readonly Color Border = Color.FromArgb(225, 223, 221);       // حدود فاتحة
            public static readonly Color BorderHover = Color.FromArgb(200, 198, 196);  // حدود عند التمرير
            public static readonly Color BorderFocus = Color.FromArgb(0, 120, 215);    // حدود عند التركيز
            public static readonly Color Shadow = Color.FromArgb(50, 0, 0, 0);         // ظل شفاف

            /// <summary>
            /// الحصول على لون متكيف بناءً على حجم الشاشة
            /// </summary>
            public static Color GetAdaptiveColor(Color baseColor, float intensity = 1.0f)
            {
                var screenSize = ResponsiveLayoutSystem.GetCurrentScreenSize();
                
                // تعديل كثافة اللون بناءً على حجم الشاشة
                var adjustedIntensity = screenSize switch
                {
                    ResponsiveLayoutSystem.ScreenSize.Small => intensity * 0.9f,
                    ResponsiveLayoutSystem.ScreenSize.Medium => intensity,
                    ResponsiveLayoutSystem.ScreenSize.Large => intensity * 1.1f,
                    ResponsiveLayoutSystem.ScreenSize.ExtraLarge => intensity * 1.2f,
                    _ => intensity
                };

                return AdjustColorIntensity(baseColor, adjustedIntensity);
            }

            /// <summary>
            /// تعديل كثافة اللون
            /// </summary>
            private static Color AdjustColorIntensity(Color color, float intensity)
            {
                var r = Math.Min(255, (int)(color.R * intensity));
                var g = Math.Min(255, (int)(color.G * intensity));
                var b = Math.Min(255, (int)(color.B * intensity));
                return Color.FromArgb(color.A, r, g, b);
            }
        }

        #endregion

        #region نظام الخطوط المتجاوب

        /// <summary>
        /// مجموعة الخطوط المتجاوبة
        /// </summary>
        public static class Fonts
        {
            /// <summary>
            /// الحصول على خط متكيف
            /// </summary>
            public static Font GetResponsiveFont(string fontFamily, float baseSize, FontStyle style = FontStyle.Regular)
            {
                var scaleFactor = ResponsiveLayoutSystem.GetScaleFactor();
                var dpiScaleFactor = ResponsiveLayoutSystem.GetDpiScaleFactor();
                var adjustedSize = baseSize * scaleFactor * dpiScaleFactor;

                // تأكد من أن حجم الخط لا يقل عن الحد الأدنى
                adjustedSize = Math.Max(adjustedSize, 8f);
                
                // تأكد من أن حجم الخط لا يزيد عن الحد الأقصى
                adjustedSize = Math.Min(adjustedSize, 72f);

                return new Font(fontFamily, adjustedSize, style);
            }

            /// <summary>
            /// خطوط العناوين المتجاوبة
            /// </summary>
            public static Font GetHeading1() => GetResponsiveFont("Segoe UI", 28f, FontStyle.Bold);
            public static Font GetHeading2() => GetResponsiveFont("Segoe UI", 24f, FontStyle.Bold);
            public static Font GetHeading3() => GetResponsiveFont("Segoe UI", 20f, FontStyle.Bold);
            public static Font GetHeading4() => GetResponsiveFont("Segoe UI", 16f, FontStyle.Bold);
            public static Font GetHeading5() => GetResponsiveFont("Segoe UI", 14f, FontStyle.Bold);
            public static Font GetHeading6() => GetResponsiveFont("Segoe UI", 12f, FontStyle.Bold);

            /// <summary>
            /// خطوط النص المتجاوبة
            /// </summary>
            public static Font GetBodyLarge() => GetResponsiveFont("Segoe UI", 16f);
            public static Font GetBody() => GetResponsiveFont("Segoe UI", 14f);
            public static Font GetBodySmall() => GetResponsiveFont("Segoe UI", 12f);
            public static Font GetCaption() => GetResponsiveFont("Segoe UI", 11f);
            public static Font GetOverline() => GetResponsiveFont("Segoe UI", 10f);

            /// <summary>
            /// خطوط الأزرار المتجاوبة
            /// </summary>
            public static Font GetButton() => GetResponsiveFont("Segoe UI", 14f, FontStyle.Bold);
            public static Font GetButtonLarge() => GetResponsiveFont("Segoe UI", 16f, FontStyle.Bold);
            public static Font GetButtonSmall() => GetResponsiveFont("Segoe UI", 12f, FontStyle.Bold);

            /// <summary>
            /// خطوط الأرقام المتجاوبة
            /// </summary>
            public static Font GetNumbers() => GetResponsiveFont("Segoe UI", 14f, FontStyle.Bold);
            public static Font GetNumbersLarge() => GetResponsiveFont("Segoe UI", 18f, FontStyle.Bold);
            public static Font GetNumbersSmall() => GetResponsiveFont("Segoe UI", 12f, FontStyle.Bold);

            /// <summary>
            /// خطوط خاصة للشاشات عالية الدقة
            /// </summary>
            public static Font GetHighDpiFont(float baseSize, FontStyle style = FontStyle.Regular)
            {
                var dpiScaleFactor = ResponsiveLayoutSystem.GetDpiScaleFactor();
                
                // تطبيق تكبير إضافي للشاشات عالية الدقة
                if (dpiScaleFactor > 1.5f)
                {
                    baseSize *= 1.1f;
                }

                return GetResponsiveFont("Segoe UI", baseSize, style);
            }
        }

        #endregion

        #region نظام الأيقونات المتجاوب

        /// <summary>
        /// نظام الأيقونات المتجاوب
        /// </summary>
        public static class Icons
        {
            /// <summary>
            /// الحصول على حجم الأيقونة المتكيف
            /// </summary>
            public static int GetResponsiveIconSize(int baseSize)
            {
                var scaleFactor = ResponsiveLayoutSystem.GetScaleFactor();
                return (int)(baseSize * scaleFactor);
            }

            /// <summary>
            /// أحجام الأيقونات المختلفة
            /// </summary>
            public static int GetSmallIconSize() => GetResponsiveIconSize(16);
            public static int GetMediumIconSize() => GetResponsiveIconSize(24);
            public static int GetLargeIconSize() => GetResponsiveIconSize(32);
            public static int GetExtraLargeIconSize() => GetResponsiveIconSize(48);

            /// <summary>
            /// أيقونات الوحدات الرئيسية
            /// </summary>
            public const string Dashboard = "📊";
            public const string Sales = "💰";
            public const string Invoices = "📄";
            public const string Customers = "👥";
            public const string Products = "📦";
            public const string Installments = "📅";
            public const string Debts = "💳";
            public const string Reports = "📈";
            public const string Settings = "⚙️";

            /// <summary>
            /// أيقونات الإجراءات
            /// </summary>
            public const string Add = "➕";
            public const string Edit = "✏️";
            public const string Delete = "🗑️";
            public const string Search = "🔍";
            public const string Print = "🖨️";
            public const string Save = "💾";
            public const string Cancel = "❌";
            public const string Confirm = "✅";

            /// <summary>
            /// أيقونات التنبيهات
            /// </summary>
            public const string Notification = "🔔";
            public const string Warning = "⚠️";
            public const string Error = "❌";
            public const string Success = "✅";
            public const string Info = "ℹ️";
        }

        #endregion

        #region نظام الظلال المتجاوب

        /// <summary>
        /// نظام الظلال المتجاوب
        /// </summary>
        public static class Shadows
        {
            /// <summary>
            /// رسم ظل متكيف
            /// </summary>
            public static void DrawResponsiveShadow(Graphics g, Rectangle bounds, int elevation = 1)
            {
                var scaleFactor = ResponsiveLayoutSystem.GetScaleFactor();
                var shadowOffset = (int)(elevation * 2 * scaleFactor);
                var shadowOpacity = Math.Min(50, elevation * 10);

                using (var shadowBrush = new SolidBrush(Color.FromArgb(shadowOpacity, 0, 0, 0)))
                {
                    var shadowBounds = new Rectangle(
                        bounds.X + shadowOffset,
                        bounds.Y + shadowOffset,
                        bounds.Width,
                        bounds.Height
                    );
                    g.FillRectangle(shadowBrush, shadowBounds);
                }
            }

            /// <summary>
            /// ظل خفيف للبطاقات
            /// </summary>
            public static void DrawCardShadow(Graphics g, Rectangle bounds)
            {
                DrawResponsiveShadow(g, bounds, 1);
            }

            /// <summary>
            /// ظل متوسط للعناصر المرفوعة
            /// </summary>
            public static void DrawElevatedShadow(Graphics g, Rectangle bounds)
            {
                DrawResponsiveShadow(g, bounds, 2);
            }

            /// <summary>
            /// ظل قوي للنوافذ المنبثقة
            /// </summary>
            public static void DrawModalShadow(Graphics g, Rectangle bounds)
            {
                DrawResponsiveShadow(g, bounds, 4);
            }
        }

        #endregion

        #region مساعدات الرسم المتجاوب

        /// <summary>
        /// رسم بطاقة متجاوبة
        /// </summary>
        public static void DrawResponsiveCard(Graphics g, Rectangle bounds, Color backgroundColor, int cornerRadius = 0)
        {
            var scaleFactor = ResponsiveLayoutSystem.GetScaleFactor();
            var adjustedCornerRadius = cornerRadius > 0 ? (int)(cornerRadius * scaleFactor) : 8;

            // رسم الظل
            Shadows.DrawCardShadow(g, bounds);

            // رسم البطاقة
            using (var cardBrush = new SolidBrush(backgroundColor))
            {
                if (adjustedCornerRadius > 0)
                {
                    using (var cardPath = CreateRoundedRectangle(bounds, adjustedCornerRadius))
                    {
                        g.FillPath(cardBrush, cardPath);
                    }
                }
                else
                {
                    g.FillRectangle(cardBrush, bounds);
                }
            }

            // رسم الحدود
            using (var borderPen = new Pen(Colors.Border, 1))
            {
                if (adjustedCornerRadius > 0)
                {
                    using (var borderPath = CreateRoundedRectangle(bounds, adjustedCornerRadius))
                    {
                        g.DrawPath(borderPen, borderPath);
                    }
                }
                else
                {
                    g.DrawRectangle(borderPen, bounds);
                }
            }
        }

        /// <summary>
        /// رسم زر متجاوب
        /// </summary>
        public static void DrawResponsiveButton(Graphics g, Rectangle bounds, Color baseColor, string text, Font font, bool isHovered = false, bool isPressed = false)
        {
            var buttonColor = baseColor;
            if (isPressed)
                buttonColor = DarkenColor(baseColor, 0.2f);
            else if (isHovered)
                buttonColor = LightenColor(baseColor, 0.1f);

            var scaleFactor = ResponsiveLayoutSystem.GetScaleFactor();
            var cornerRadius = (int)(6 * scaleFactor);

            // رسم الزر
            using (var buttonBrush = new SolidBrush(buttonColor))
            using (var buttonPath = CreateRoundedRectangle(bounds, cornerRadius))
            {
                g.FillPath(buttonBrush, buttonPath);
            }

            // رسم النص
            var textColor = IsLightColor(buttonColor) ? Colors.TextPrimary : Colors.TextOnPrimary;
            using (var textBrush = new SolidBrush(textColor))
            {
                var textFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center,
                    FormatFlags = StringFormatFlags.DirectionRightToLeft
                };
                g.DrawString(text, font, textBrush, bounds, textFormat);
            }
        }

        /// <summary>
        /// إنشاء مستطيل بحواف مدورة
        /// </summary>
        public static System.Drawing.Drawing2D.GraphicsPath CreateRoundedRectangle(Rectangle bounds, int cornerRadius)
        {
            var path = new System.Drawing.Drawing2D.GraphicsPath();
            var diameter = cornerRadius * 2;

            path.AddArc(bounds.X, bounds.Y, diameter, diameter, 180, 90);
            path.AddArc(bounds.Right - diameter, bounds.Y, diameter, diameter, 270, 90);
            path.AddArc(bounds.Right - diameter, bounds.Bottom - diameter, diameter, diameter, 0, 90);
            path.AddArc(bounds.X, bounds.Bottom - diameter, diameter, diameter, 90, 90);
            path.CloseFigure();

            return path;
        }

        /// <summary>
        /// تفتيح لون
        /// </summary>
        public static Color LightenColor(Color color, float factor)
        {
            var r = (int)(color.R + (255 - color.R) * factor);
            var g = (int)(color.G + (255 - color.G) * factor);
            var b = (int)(color.B + (255 - color.B) * factor);
            return Color.FromArgb(color.A, Math.Min(255, r), Math.Min(255, g), Math.Min(255, b));
        }

        /// <summary>
        /// تغميق لون
        /// </summary>
        public static Color DarkenColor(Color color, float factor)
        {
            var r = (int)(color.R * (1 - factor));
            var g = (int)(color.G * (1 - factor));
            var b = (int)(color.B * (1 - factor));
            return Color.FromArgb(color.A, Math.Max(0, r), Math.Max(0, g), Math.Max(0, b));
        }

        /// <summary>
        /// تحديد ما إذا كان اللون فاتحاً
        /// </summary>
        public static bool IsLightColor(Color color)
        {
            var brightness = (color.R * 0.299 + color.G * 0.587 + color.B * 0.114) / 255;
            return brightness > 0.5;
        }

        #endregion
    }
}
