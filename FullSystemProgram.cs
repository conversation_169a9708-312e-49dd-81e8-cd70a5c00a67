using System;
using System.Globalization;
using System.Threading;
using System.Windows.Forms;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using AredooPOS.Forms;
using AredooPOS.Database;
using AredooPOS.Core;
using AredooPOS.UI;
using AredooPOS.Compatibility;
using AredooPOS.Printing;
using AredooPOS.Barcode;

namespace AredooPOS.FullSystem
{
    /// <summary>
    /// البرنامج الرئيسي للنظام الكامل لأريدو POS
    /// </summary>
    internal static class FullSystemProgram
    {
        #region المتغيرات العامة

        private static IServiceProvider _serviceProvider;
        private static Microsoft.Extensions.Logging.ILogger _logger;
        private static IConfiguration _configuration;

        #endregion

        #region نقطة الدخول الرئيسية

        /// <summary>
        /// نقطة الدخول الرئيسية للنظام الكامل
        /// </summary>
        [STAThread]
        static void Main(string[] args)
        {
            try
            {
                // تهيئة التطبيق
                InitializeApplication();

                // تحميل التكوين
                LoadConfiguration();

                // تهيئة الخدمات
                ConfigureServices();

                // تهيئة نظام التسجيل
                InitializeLogging();

                // فحص التوافق
                if (!CheckSystemCompatibility())
                {
                    ShowCompatibilityError();
                    return;
                }

                // تهيئة قاعدة البيانات
                if (!InitializeDatabase())
                {
                    ShowDatabaseError();
                    return;
                }

                // تشغيل النظام الكامل
                RunFullSystem();
            }
            catch (Exception ex)
            {
                HandleStartupError(ex);
            }
            finally
            {
                // تنظيف الموارد
                CleanupResources();
            }
        }

        #endregion

        #region تهيئة التطبيق

        /// <summary>
        /// تهيئة التطبيق الأساسية
        /// </summary>
        private static void InitializeApplication()
        {
            // تعيين إعدادات Windows Forms
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // تعيين الثقافة العربية
            SetArabicCulture();

            // تعيين معالج الأخطاء العام
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            Application.ThreadException += OnThreadException;
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
        }

        /// <summary>
        /// تعيين الثقافة العربية
        /// </summary>
        private static void SetArabicCulture()
        {
            try
            {
                var arabicCulture = new CultureInfo("ar-SA");
                Thread.CurrentThread.CurrentCulture = arabicCulture;
                Thread.CurrentThread.CurrentUICulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentCulture = arabicCulture;
                CultureInfo.DefaultThreadCurrentUICulture = arabicCulture;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"تحذير: فشل في تعيين الثقافة العربية - {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل التكوين
        /// </summary>
        private static void LoadConfiguration()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Application.StartupPath)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddEnvironmentVariables();

            _configuration = builder.Build();
        }

        /// <summary>
        /// تكوين الخدمات
        /// </summary>
        private static void ConfigureServices()
        {
            var services = new ServiceCollection();

            // إضافة التكوين
            services.AddSingleton(_configuration);

            // إضافة نظام التسجيل
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddSerilog();
            });

            // إضافة خدمات قاعدة البيانات
            services.AddSingleton<DatabaseManager>(provider =>
            {
                var logger = provider.GetService<ILogger<DatabaseManager>>();
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                return new DatabaseManager(connectionString, logger);
            });

            services.AddSingleton<SyncManager>();

            // إضافة خدمات نقاط البيع
            services.AddSingleton<POSManager>();
            services.AddSingleton<CashRegisterManager>();
            services.AddSingleton<DebtManager>();

            // إضافة خدمات الطباعة
            services.AddSingleton<ThermalPrintManager>(provider =>
            {
                var logger = provider.GetService<ILogger<ThermalPrintManager>>();
                var settings = CreateThermalPrinterSettings();
                return new ThermalPrintManager(settings, logger);
            });

            // إضافة خدمات الباركود
            services.AddSingleton<BarcodeManager>(provider =>
            {
                var logger = provider.GetService<ILogger<BarcodeManager>>();
                var settings = CreateBarcodeSettings();
                return new BarcodeManager(settings, logger);
            });

            // إضافة خدمات التوافق
            services.AddSingleton(provider =>
            {
                var logger = provider.GetService<ILogger<WindowsCompatibilityManager>>();
                return WindowsCompatibilityManager.Instance;
            });

            // إضافة النماذج
            services.AddTransient<MainFormAdvanced>();

            // بناء مزود الخدمات
            _serviceProvider = services.BuildServiceProvider();
        }

        /// <summary>
        /// تهيئة نظام التسجيل
        /// </summary>
        private static void InitializeLogging()
        {
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(_configuration)
                .CreateLogger();

            _logger = _serviceProvider.GetService<Microsoft.Extensions.Logging.ILogger<FullSystemProgram>>();
            _logger?.LogInformation("تم بدء تشغيل نظام أريدو POS الكامل");
        }

        /// <summary>
        /// إنشاء إعدادات الطابعة الحرارية
        /// </summary>
        private static ThermalPrinterSettings CreateThermalPrinterSettings()
        {
            var printerSection = _configuration.GetSection("PrinterSettings:ThermalPrinter");
            
            return new ThermalPrinterSettings
            {
                ConnectionType = Enum.Parse<PrinterConnectionType>(
                    printerSection["ConnectionType"] ?? "USB"),
                PrinterName = printerSection["PrinterName"] ?? "",
                IPAddress = printerSection["IPAddress"] ?? "*************",
                Port = int.Parse(printerSection["Port"] ?? "9100"),
                SerialPort = printerSection["SerialPort"] ?? "COM1",
                BaudRate = int.Parse(printerSection["BaudRate"] ?? "9600"),
                PaperWidth = int.Parse(printerSection["PaperWidth"] ?? "80"),
                CharactersPerLine = int.Parse(printerSection["CharactersPerLine"] ?? "48"),
                PrintStoreLogo = bool.Parse(printerSection["PrintStoreLogo"] ?? "true"),
                PrintStoreInfo = bool.Parse(printerSection["PrintStoreInfo"] ?? "true"),
                PrintCustomerInfo = bool.Parse(printerSection["PrintCustomerInfo"] ?? "true"),
                PrintBarcode = bool.Parse(printerSection["PrintBarcode"] ?? "true"),
                PrintThankYouMessage = bool.Parse(printerSection["PrintThankYouMessage"] ?? "true"),
                PrintPaymentMethod = bool.Parse(printerSection["PrintPaymentMethod"] ?? "true"),
                AutoCutPaper = bool.Parse(printerSection["AutoCutPaper"] ?? "true"),
                OpenCashDrawer = bool.Parse(printerSection["OpenCashDrawer"] ?? "true"),
                CustomThankYouMessage = printerSection["CustomThankYouMessage"] ?? "شكراً لزيارتكم - أريدو"
            };
        }

        /// <summary>
        /// إنشاء إعدادات الباركود
        /// </summary>
        private static BarcodeSettings CreateBarcodeSettings()
        {
            var barcodeSection = _configuration.GetSection("BarcodeSettings");
            
            return new BarcodeSettings
            {
                DefaultType = Enum.Parse<BarcodeType>(
                    barcodeSection["DefaultType"] ?? "Code128"),
                DefaultWidth = int.Parse(barcodeSection["DefaultWidth"] ?? "300"),
                DefaultHeight = int.Parse(barcodeSection["DefaultHeight"] ?? "100"),
                IncludeTextByDefault = bool.Parse(barcodeSection["IncludeTextByDefault"] ?? "true"),
                ProductPrefix = barcodeSection["ProductPrefix"] ?? "PRD",
                CustomerPrefix = barcodeSection["CustomerPrefix"] ?? "CUS",
                InvoicePrefix = barcodeSection["InvoicePrefix"] ?? "INV",
                SaveDirectory = barcodeSection["SaveDirectory"] ?? "Barcodes"
            };
        }

        #endregion

        #region فحص التوافق وقاعدة البيانات

        /// <summary>
        /// فحص توافق النظام
        /// </summary>
        /// <returns>true إذا كان النظام متوافق</returns>
        private static bool CheckSystemCompatibility()
        {
            try
            {
                var compatibilityManager = _serviceProvider.GetService<WindowsCompatibilityManager>();
                
                if (!compatibilityManager.IsCompatible)
                {
                    var criticalIssues = compatibilityManager.Issues.FindAll(i => i.Severity == IssueSeverity.Critical);
                    if (criticalIssues.Count > 0)
                    {
                        _logger?.LogError("النظام غير متوافق - مشاكل حرجة مكتشفة");
                        return false;
                    }

                    // عرض تحذيرات التوافق
                    ShowCompatibilityWarnings(compatibilityManager);
                }

                _logger?.LogInformation("فحص التوافق مكتمل - النظام متوافق");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فحص التوافق");
                return false;
            }
        }

        /// <summary>
        /// تهيئة قاعدة البيانات
        /// </summary>
        /// <returns>true إذا تم التهيئة بنجاح</returns>
        private static bool InitializeDatabase()
        {
            try
            {
                _logger?.LogInformation("بدء تهيئة قاعدة البيانات");

                var databaseManager = _serviceProvider.GetService<DatabaseManager>();
                
                // اختبار الاتصال
                var connectionResult = databaseManager.TestConnectionAsync().Result;
                if (!connectionResult)
                {
                    _logger?.LogError("فشل في الاتصال بقاعدة البيانات");
                    return false;
                }

                // تهيئة قاعدة البيانات
                var initResult = databaseManager.InitializeDatabaseAsync().Result;
                if (!initResult)
                {
                    _logger?.LogError("فشل في تهيئة قاعدة البيانات");
                    return false;
                }

                _logger?.LogInformation("تم تهيئة قاعدة البيانات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تهيئة قاعدة البيانات");
                return false;
            }
        }

        #endregion

        #region تشغيل النظام

        /// <summary>
        /// تشغيل النظام الكامل
        /// </summary>
        private static void RunFullSystem()
        {
            try
            {
                _logger?.LogInformation("بدء تشغيل النظام الكامل");

                // إنشاء النموذج الرئيسي المتقدم
                var mainForm = _serviceProvider.GetService<MainFormAdvanced>();
                
                if (mainForm == null)
                {
                    throw new Exception("فشل في إنشاء النموذج الرئيسي");
                }

                // تطبيق التخطيط العربي
                ArabicLayoutManager.ApplyArabicLayout(mainForm);
                ArabicResourceManager.ApplyArabicTexts(mainForm);

                // تشغيل حلقة الرسائل
                Application.Run(mainForm);

                _logger?.LogInformation("تم إغلاق النظام بنجاح");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تشغيل النظام");
                HandleRuntimeError(ex);
            }
        }

        #endregion

        #region معالجة الأخطاء

        /// <summary>
        /// معالج أخطاء الخيوط
        /// </summary>
        private static void OnThreadException(object sender, ThreadExceptionEventArgs e)
        {
            _logger?.LogError(e.Exception, "خطأ في خيط التطبيق");
            HandleRuntimeError(e.Exception);
        }

        /// <summary>
        /// معالج الأخطاء غير المعالجة
        /// </summary>
        private static void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                _logger?.LogCritical(ex, "خطأ حرج غير معالج");
                HandleRuntimeError(ex);
            }
        }

        /// <summary>
        /// عرض تحذيرات التوافق
        /// </summary>
        private static void ShowCompatibilityWarnings(WindowsCompatibilityManager compatibilityManager)
        {
            var warnings = compatibilityManager.Issues.FindAll(i => i.Severity == IssueSeverity.Warning);
            
            if (warnings.Count > 0)
            {
                var message = "تم اكتشاف بعض مشاكل التوافق:\n\n";
                foreach (var warning in warnings)
                {
                    message += $"• {warning.Title}: {warning.Description}\n";
                }
                message += "\nهل تريد المتابعة؟";

                var result = MessageBox.Show(message, "تحذيرات التوافق", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                if (result == DialogResult.No)
                {
                    Environment.Exit(0);
                }
            }
        }

        /// <summary>
        /// عرض خطأ التوافق
        /// </summary>
        private static void ShowCompatibilityError()
        {
            MessageBox.Show(
                "❌ النظام غير متوافق مع هذا الإصدار من Windows\n\n" +
                "📋 المتطلبات:\n" +
                "• Windows 7 أو أحدث\n" +
                "• .NET Framework 4.7.2 أو أحدث\n" +
                "• 2 GB ذاكرة على الأقل\n" +
                "• 1 GB مساحة فارغة على القرص\n\n" +
                "🔧 الحلول:\n" +
                "• ترقية نظام التشغيل\n" +
                "• تثبيت .NET Framework المطلوب\n" +
                "• الاتصال بالدعم الفني\n\n" +
                "📞 الدعم الفني: +966 XX XXX XXXX",
                "خطأ في التوافق",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }

        /// <summary>
        /// عرض خطأ قاعدة البيانات
        /// </summary>
        private static void ShowDatabaseError()
        {
            var result = MessageBox.Show(
                "❌ فشل في الاتصال بقاعدة البيانات\n\n" +
                "🔍 تأكد من:\n" +
                "• تثبيت SQL Server أو SQL Server Express\n" +
                "• صحة سلسلة الاتصال في ملف الإعدادات\n" +
                "• وجود صلاحيات الوصول لقاعدة البيانات\n" +
                "• تشغيل خدمة SQL Server\n\n" +
                "❓ هل تريد فتح إعدادات قاعدة البيانات؟",
                "خطأ في قاعدة البيانات",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Error);

            if (result == DialogResult.Yes)
            {
                ShowDatabaseSettings();
            }
        }

        /// <summary>
        /// عرض إعدادات قاعدة البيانات
        /// </summary>
        private static void ShowDatabaseSettings()
        {
            MessageBox.Show(
                "⚙️ إعدادات قاعدة البيانات\n\n" +
                "📁 ملف الإعدادات: appsettings.json\n" +
                "🔗 سلسلة الاتصال الحالية:\n" +
                $"{_configuration?.GetConnectionString("DefaultConnection")}\n\n" +
                "📝 لتعديل الإعدادات:\n" +
                "1. افتح ملف appsettings.json\n" +
                "2. عدل قسم ConnectionStrings\n" +
                "3. أعد تشغيل البرنامج\n\n" +
                "📞 للمساعدة: +966 XX XXX XXXX",
                "إعدادات قاعدة البيانات",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        /// <summary>
        /// معالجة أخطاء بدء التشغيل
        /// </summary>
        private static void HandleStartupError(Exception ex)
        {
            var message = $"❌ حدث خطأ أثناء بدء تشغيل النظام:\n\n{ex.Message}";
            
            if (ex.InnerException != null)
            {
                message += $"\n\n🔍 تفاصيل إضافية:\n{ex.InnerException.Message}";
            }

            message += "\n\n📞 للدعم الفني: +966 XX XXX XXXX";

            MessageBox.Show(message, "خطأ في بدء التشغيل", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);

            _logger?.LogCritical(ex, "خطأ حرج في بدء التشغيل");
        }

        /// <summary>
        /// معالجة أخطاء وقت التشغيل
        /// </summary>
        private static void HandleRuntimeError(Exception ex)
        {
            var message = $"❌ حدث خطأ أثناء تشغيل النظام:\n\n{ex.Message}\n\n" +
                         "💡 يمكنك المتابعة أو إعادة تشغيل البرنامج\n" +
                         "📞 للدعم الفني: +966 XX XXX XXXX";
            
            MessageBox.Show(message, "خطأ في التشغيل", 
                MessageBoxButtons.OK, MessageBoxIcon.Warning);

            _logger?.LogError(ex, "خطأ في وقت التشغيل");
        }

        #endregion

        #region تنظيف الموارد

        /// <summary>
        /// تنظيف موارد التطبيق
        /// </summary>
        private static void CleanupResources()
        {
            try
            {
                _logger?.LogInformation("بدء تنظيف موارد النظام");

                // تنظيف مزود الخدمات
                if (_serviceProvider is IDisposable disposableProvider)
                {
                    disposableProvider.Dispose();
                }

                // إغلاق نظام التسجيل
                Log.CloseAndFlush();

                _logger?.LogInformation("تم تنظيف الموارد بنجاح");
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ دون إيقاف التطبيق
                Console.WriteLine($"خطأ في تنظيف الموارد: {ex.Message}");
            }
        }

        #endregion
    }


}
