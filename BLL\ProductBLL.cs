using System;
using System.Collections.Generic;
using System.Linq;
using AredooPOS.Models;
using AredooPOS.DAL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.BLL
{
    /// <summary>
    /// طبقة منطق الأعمال للمنتجات
    /// تحتوي على جميع العمليات التجارية المتعلقة بالمنتجات
    /// </summary>
    public class ProductBLL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly ProductDAL _productDAL;
        private readonly CategoryDAL _categoryDAL;
        private readonly StockMovementDAL _stockMovementDAL;
        private readonly ILogger<ProductBLL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة منطق الأعمال للمنتجات
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public ProductBLL(string connectionString = null, ILogger<ProductBLL> logger = null)
        {
            _productDAL = new ProductDAL(connectionString, null);
            _categoryDAL = new CategoryDAL(connectionString, null);
            _stockMovementDAL = new StockMovementDAL(connectionString, null);
            _logger = logger;
        }

        #endregion

        #region إدارة المنتجات

        /// <summary>
        /// إضافة منتج جديد
        /// </summary>
        /// <param name="product">بيانات المنتج</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>رقم المنتج الجديد</returns>
        public int AddProduct(Product product, string currentUser)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateProduct(product);

                // التحقق من عدم تكرار الكود
                if (IsProductCodeExists(product.ProductCode))
                {
                    throw new InvalidOperationException($"كود المنتج '{product.ProductCode}' موجود مسبقاً");
                }

                // التحقق من عدم تكرار الباركود
                if (!string.IsNullOrWhiteSpace(product.Barcode) && IsProductBarcodeExists(product.Barcode))
                {
                    throw new InvalidOperationException($"الباركود '{product.Barcode}' موجود مسبقاً");
                }

                // تعيين معلومات النظام
                product.CreatedBy = currentUser;
                product.ModifiedBy = currentUser;
                product.CreatedDate = DateTime.Now;
                product.ModifiedDate = DateTime.Now;

                // حساب نسبة الربح إذا لم تكن محددة
                if (product.ProfitMargin == 0 && product.CostPrice > 0 && product.UnitPrice > product.CostPrice)
                {
                    product.CalculateProfitMargin();
                }

                // إضافة المنتج
                var productId = _productDAL.AddProduct(product);
                product.ProductID = productId;

                // إضافة حركة مخزون أولية إذا كانت الكمية أكبر من صفر
                if (product.StockQuantity > 0)
                {
                    var initialMovement = new StockMovement
                    {
                        ProductID = productId,
                        ProductCode = product.ProductCode,
                        ProductName = product.ProductName,
                        MovementDate = DateTime.Now,
                        MovementType = StockMovementTypes.StockIn,
                        Quantity = product.StockQuantity,
                        StockBefore = 0,
                        StockAfter = product.StockQuantity,
                        UnitCost = product.CostPrice,
                        Reason = "رصيد افتتاحي",
                        CreatedBy = currentUser,
                        CreatedDate = DateTime.Now,
                        ModifiedDate = DateTime.Now
                    };

                    initialMovement.GenerateReferenceNumber();
                    _stockMovementDAL.AddStockMovement(initialMovement);
                }

                _logger?.LogInformation($"تم إضافة منتج جديد: {product.ProductCode} - {product.ProductName}");
                return productId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إضافة المنتج: {product?.ProductCode}");
                throw;
            }
        }

        /// <summary>
        /// تحديث بيانات منتج
        /// </summary>
        /// <param name="product">بيانات المنتج المحدثة</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateProduct(Product product, string currentUser)
        {
            try
            {
                // التحقق من وجود المنتج
                var existingProduct = _productDAL.GetProductById(product.ProductID);
                if (existingProduct == null)
                {
                    throw new InvalidOperationException("المنتج غير موجود");
                }

                // التحقق من صحة البيانات
                ValidateProduct(product);

                // التحقق من عدم تكرار الكود (إذا تم تغييره)
                if (product.ProductCode != existingProduct.ProductCode && IsProductCodeExists(product.ProductCode))
                {
                    throw new InvalidOperationException($"كود المنتج '{product.ProductCode}' موجود مسبقاً");
                }

                // التحقق من عدم تكرار الباركود (إذا تم تغييره)
                if (!string.IsNullOrWhiteSpace(product.Barcode) && 
                    product.Barcode != existingProduct.Barcode && 
                    IsProductBarcodeExists(product.Barcode))
                {
                    throw new InvalidOperationException($"الباركود '{product.Barcode}' موجود مسبقاً");
                }

                // تعيين معلومات التعديل
                product.ModifiedBy = currentUser;
                product.ModifiedDate = DateTime.Now;
                product.CreatedBy = existingProduct.CreatedBy;
                product.CreatedDate = existingProduct.CreatedDate;

                // حساب نسبة الربح إذا تم تغيير الأسعار
                if (product.CostPrice != existingProduct.CostPrice || product.UnitPrice != existingProduct.UnitPrice)
                {
                    product.CalculateProfitMargin();
                }

                // تحديث المنتج
                var success = _productDAL.UpdateProduct(product);

                if (success)
                {
                    _logger?.LogInformation($"تم تحديث المنتج: {product.ProductCode} - {product.ProductName}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث المنتج: {product?.ProductCode}");
                throw;
            }
        }

        /// <summary>
        /// حذف منتج
        /// </summary>
        /// <param name="productId">رقم المنتج</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public bool DeleteProduct(int productId, string currentUser)
        {
            try
            {
                var product = _productDAL.GetProductById(productId);
                if (product == null)
                {
                    throw new InvalidOperationException("المنتج غير موجود");
                }

                var success = _productDAL.DeleteProduct(productId);

                if (success)
                {
                    _logger?.LogInformation($"تم حذف المنتج: {product.ProductCode} - {product.ProductName} بواسطة {currentUser}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حذف المنتج {productId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على منتج بالرقم
        /// </summary>
        /// <param name="productId">رقم المنتج</param>
        /// <returns>بيانات المنتج</returns>
        public Product GetProductById(int productId)
        {
            try
            {
                return _productDAL.GetProductById(productId);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على المنتج {productId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على منتج بالكود
        /// </summary>
        /// <param name="productCode">كود المنتج</param>
        /// <returns>بيانات المنتج</returns>
        public Product GetProductByCode(string productCode)
        {
            try
            {
                return _productDAL.GetProductByCode(productCode);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على المنتج بالكود {productCode}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على منتج بالباركود
        /// </summary>
        /// <param name="barcode">الباركود</param>
        /// <returns>بيانات المنتج</returns>
        public Product GetProductByBarcode(string barcode)
        {
            try
            {
                return _productDAL.GetProductByBarcode(barcode);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على المنتج بالباركود {barcode}");
                throw;
            }
        }

        /// <summary>
        /// البحث في المنتجات
        /// </summary>
        /// <param name="searchTerm">كلمة البحث</param>
        /// <param name="categoryId">رقم الفئة (اختياري)</param>
        /// <param name="includeInactive">تضمين المنتجات غير النشطة</param>
        /// <returns>قائمة المنتجات المطابقة</returns>
        public List<Product> SearchProducts(string searchTerm, int? categoryId = null, bool includeInactive = false)
        {
            try
            {
                return _productDAL.SearchProducts(searchTerm, categoryId, includeInactive);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في البحث عن المنتجات: {searchTerm}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع المنتجات
        /// </summary>
        /// <param name="includeInactive">تضمين المنتجات غير النشطة</param>
        /// <returns>قائمة المنتجات</returns>
        public List<Product> GetAllProducts(bool includeInactive = false)
        {
            try
            {
                return _productDAL.GetAllProducts(includeInactive);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على جميع المنتجات");
                throw;
            }
        }

        #endregion

        #region إدارة المخزون

        /// <summary>
        /// تحديث كمية المخزون
        /// </summary>
        /// <param name="productId">رقم المنتج</param>
        /// <param name="newQuantity">الكمية الجديدة</param>
        /// <param name="reason">سبب التحديث</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateStockQuantity(int productId, decimal newQuantity, string reason, string currentUser)
        {
            try
            {
                var product = _productDAL.GetProductById(productId);
                if (product == null)
                {
                    throw new InvalidOperationException("المنتج غير موجود");
                }

                if (!product.TrackStock)
                {
                    throw new InvalidOperationException("هذا المنتج لا يتم تتبع مخزونه");
                }

                var oldQuantity = product.StockQuantity;
                var quantityDifference = newQuantity - oldQuantity;

                // تحديث كمية المخزون
                var success = _productDAL.UpdateStockQuantity(productId, newQuantity, currentUser);

                if (success && quantityDifference != 0)
                {
                    // إضافة حركة مخزون
                    var movement = new StockMovement
                    {
                        ProductID = productId,
                        ProductCode = product.ProductCode,
                        ProductName = product.ProductName,
                        MovementDate = DateTime.Now,
                        MovementType = StockMovementTypes.Adjustment,
                        Quantity = quantityDifference,
                        StockBefore = oldQuantity,
                        StockAfter = newQuantity,
                        UnitCost = product.CostPrice,
                        Reason = reason,
                        CreatedBy = currentUser,
                        CreatedDate = DateTime.Now,
                        ModifiedDate = DateTime.Now
                    };

                    movement.GenerateReferenceNumber();
                    _stockMovementDAL.AddStockMovement(movement);

                    _logger?.LogInformation($"تم تحديث مخزون المنتج {product.ProductCode} من {oldQuantity} إلى {newQuantity}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث مخزون المنتج {productId}");
                throw;
            }
        }

        /// <summary>
        /// إضافة كمية للمخزون
        /// </summary>
        /// <param name="productId">رقم المنتج</param>
        /// <param name="quantity">الكمية المضافة</param>
        /// <param name="unitCost">تكلفة الوحدة</param>
        /// <param name="reason">سبب الإضافة</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <param name="documentNumber">رقم المستند</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool AddStock(int productId, decimal quantity, decimal unitCost, string reason, string currentUser, string documentNumber = null)
        {
            try
            {
                var product = _productDAL.GetProductById(productId);
                if (product == null)
                {
                    throw new InvalidOperationException("المنتج غير موجود");
                }

                if (!product.TrackStock)
                {
                    throw new InvalidOperationException("هذا المنتج لا يتم تتبع مخزونه");
                }

                if (quantity <= 0)
                {
                    throw new ArgumentException("الكمية يجب أن تكون أكبر من صفر");
                }

                var oldQuantity = product.StockQuantity;
                var newQuantity = oldQuantity + quantity;

                // تحديث كمية المخزون
                var success = _productDAL.UpdateStockQuantity(productId, newQuantity, currentUser);

                if (success)
                {
                    // إضافة حركة مخزون
                    var movement = new StockMovement
                    {
                        ProductID = productId,
                        ProductCode = product.ProductCode,
                        ProductName = product.ProductName,
                        MovementDate = DateTime.Now,
                        MovementType = StockMovementTypes.StockIn,
                        Quantity = quantity,
                        StockBefore = oldQuantity,
                        StockAfter = newQuantity,
                        UnitCost = unitCost,
                        Reason = reason,
                        DocumentNumber = documentNumber,
                        CreatedBy = currentUser,
                        CreatedDate = DateTime.Now,
                        ModifiedDate = DateTime.Now
                    };

                    movement.GenerateReferenceNumber();
                    _stockMovementDAL.AddStockMovement(movement);

                    _logger?.LogInformation($"تم إضافة {quantity} وحدة للمنتج {product.ProductCode}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في إضافة مخزون للمنتج {productId}");
                throw;
            }
        }

        /// <summary>
        /// خصم كمية من المخزون
        /// </summary>
        /// <param name="productId">رقم المنتج</param>
        /// <param name="quantity">الكمية المخصومة</param>
        /// <param name="reason">سبب الخصم</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <param name="documentNumber">رقم المستند</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool DeductStock(int productId, decimal quantity, string reason, string currentUser, string documentNumber = null)
        {
            try
            {
                var product = _productDAL.GetProductById(productId);
                if (product == null)
                {
                    throw new InvalidOperationException("المنتج غير موجود");
                }

                if (!product.TrackStock)
                {
                    return true; // إذا لم يكن يتم تتبع المخزون، فالعملية ناجحة
                }

                if (quantity <= 0)
                {
                    throw new ArgumentException("الكمية يجب أن تكون أكبر من صفر");
                }

                if (product.AvailableStock < quantity)
                {
                    throw new InvalidOperationException($"الكمية المتاحة ({product.AvailableStock}) أقل من الكمية المطلوبة ({quantity})");
                }

                var oldQuantity = product.StockQuantity;
                var newQuantity = oldQuantity - quantity;

                // تحديث كمية المخزون
                var success = _productDAL.UpdateStockQuantity(productId, newQuantity, currentUser);

                if (success)
                {
                    // إضافة حركة مخزون
                    var movement = new StockMovement
                    {
                        ProductID = productId,
                        ProductCode = product.ProductCode,
                        ProductName = product.ProductName,
                        MovementDate = DateTime.Now,
                        MovementType = StockMovementTypes.StockOut,
                        Quantity = -quantity,
                        StockBefore = oldQuantity,
                        StockAfter = newQuantity,
                        UnitCost = product.CostPrice,
                        Reason = reason,
                        DocumentNumber = documentNumber,
                        CreatedBy = currentUser,
                        CreatedDate = DateTime.Now,
                        ModifiedDate = DateTime.Now
                    };

                    movement.GenerateReferenceNumber();
                    _stockMovementDAL.AddStockMovement(movement);

                    _logger?.LogInformation($"تم خصم {quantity} وحدة من المنتج {product.ProductCode}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في خصم مخزون المنتج {productId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على المنتجات منخفضة المخزون
        /// </summary>
        /// <returns>قائمة المنتجات منخفضة المخزون</returns>
        public List<Product> GetLowStockProducts()
        {
            try
            {
                return _productDAL.GetLowStockProducts();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على المنتجات منخفضة المخزون");
                throw;
            }
        }

        /// <summary>
        /// الحصول على المنتجات نافدة المخزون
        /// </summary>
        /// <returns>قائمة المنتجات نافدة المخزون</returns>
        public List<Product> GetOutOfStockProducts()
        {
            try
            {
                return _productDAL.GetOutOfStockProducts();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على المنتجات نافدة المخزون");
                throw;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// التحقق من صحة بيانات المنتج
        /// </summary>
        /// <param name="product">بيانات المنتج</param>
        private void ValidateProduct(Product product)
        {
            if (product == null)
                throw new ArgumentNullException(nameof(product));

            if (string.IsNullOrWhiteSpace(product.ProductCode))
                throw new ArgumentException("كود المنتج مطلوب");

            if (string.IsNullOrWhiteSpace(product.ProductName))
                throw new ArgumentException("اسم المنتج مطلوب");

            if (product.CategoryID <= 0)
                throw new ArgumentException("فئة المنتج مطلوبة");

            if (product.UnitPrice < 0)
                throw new ArgumentException("سعر البيع لا يمكن أن يكون سالباً");

            if (product.CostPrice < 0)
                throw new ArgumentException("سعر التكلفة لا يمكن أن يكون سالباً");

            if (product.StockQuantity < 0)
                throw new ArgumentException("كمية المخزون لا يمكن أن تكون سالبة");

            if (product.MinStockLevel < 0)
                throw new ArgumentException("الحد الأدنى للمخزون لا يمكن أن يكون سالباً");

            // التحقق من وجود الفئة
            var category = _categoryDAL.GetCategoryById(product.CategoryID);
            if (category == null)
                throw new ArgumentException("الفئة المحددة غير موجودة");

            if (!category.IsActive)
                throw new ArgumentException("الفئة المحددة غير نشطة");
        }

        /// <summary>
        /// التحقق من وجود كود المنتج
        /// </summary>
        /// <param name="productCode">كود المنتج</param>
        /// <returns>true إذا كان الكود موجود</returns>
        private bool IsProductCodeExists(string productCode)
        {
            var existingProduct = _productDAL.GetProductByCode(productCode);
            return existingProduct != null;
        }

        /// <summary>
        /// التحقق من وجود باركود المنتج
        /// </summary>
        /// <param name="barcode">الباركود</param>
        /// <returns>true إذا كان الباركود موجود</returns>
        private bool IsProductBarcodeExists(string barcode)
        {
            var existingProduct = _productDAL.GetProductByBarcode(barcode);
            return existingProduct != null;
        }

        #endregion
    }
}
