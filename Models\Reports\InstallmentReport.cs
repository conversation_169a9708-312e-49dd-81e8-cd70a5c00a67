using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace AredooPOS.Models.Reports
{
    /// <summary>
    /// نموذج تقرير الأقساط
    /// يحتوي على جميع البيانات المتعلقة بتقارير الأقساط المستحقة والمدفوعة
    /// </summary>
    public class InstallmentReport
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم التقرير
        /// </summary>
        public int ReportID { get; set; }

        /// <summary>
        /// نوع التقرير
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ReportType { get; set; }

        /// <summary>
        /// تاريخ بداية التقرير
        /// </summary>
        [Required]
        public DateTime FromDate { get; set; }

        /// <summary>
        /// تاريخ نهاية التقرير
        /// </summary>
        [Required]
        public DateTime ToDate { get; set; }

        /// <summary>
        /// تاريخ إنشاء التقرير
        /// </summary>
        public DateTime GeneratedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// من أنشأ التقرير
        /// </summary>
        [Required]
        [StringLength(50)]
        public string GeneratedBy { get; set; }

        #endregion

        #region الإجماليات العامة

        /// <summary>
        /// إجمالي قيمة الأقساط
        /// </summary>
        public decimal TotalInstallmentValue { get; set; }

        /// <summary>
        /// إجمالي المبلغ المدفوع
        /// </summary>
        public decimal TotalPaidAmount { get; set; }

        /// <summary>
        /// إجمالي المبلغ المستحق
        /// </summary>
        public decimal TotalOutstandingAmount { get; set; }

        /// <summary>
        /// إجمالي عدد الأقساط
        /// </summary>
        public int TotalInstallments { get; set; }

        /// <summary>
        /// عدد الأقساط المدفوعة
        /// </summary>
        public int PaidInstallments { get; set; }

        /// <summary>
        /// عدد الأقساط المستحقة
        /// </summary>
        public int OutstandingInstallments { get; set; }

        /// <summary>
        /// عدد الأقساط المتأخرة
        /// </summary>
        public int OverdueInstallments { get; set; }

        #endregion

        #region تصنيف الأقساط حسب الحالة

        /// <summary>
        /// الأقساط المدفوعة
        /// </summary>
        public decimal PaidInstallmentAmount { get; set; }

        /// <summary>
        /// الأقساط المستحقة (لم تحن بعد)
        /// </summary>
        public decimal PendingInstallmentAmount { get; set; }

        /// <summary>
        /// الأقساط المتأخرة
        /// </summary>
        public decimal OverdueInstallmentAmount { get; set; }

        /// <summary>
        /// الأقساط المستحقة اليوم
        /// </summary>
        public decimal TodayDueAmount { get; set; }

        /// <summary>
        /// الأقساط المستحقة هذا الأسبوع
        /// </summary>
        public decimal WeekDueAmount { get; set; }

        /// <summary>
        /// الأقساط المستحقة هذا الشهر
        /// </summary>
        public decimal MonthDueAmount { get; set; }

        #endregion

        #region تصنيف الأقساط حسب المدة

        /// <summary>
        /// الأقساط المتأخرة أقل من 30 يوم
        /// </summary>
        public decimal Overdue30Days { get; set; }

        /// <summary>
        /// الأقساط المتأخرة 30-60 يوم
        /// </summary>
        public decimal Overdue60Days { get; set; }

        /// <summary>
        /// الأقساط المتأخرة 60-90 يوم
        /// </summary>
        public decimal Overdue90Days { get; set; }

        /// <summary>
        /// الأقساط المتأخرة أكثر من 90 يوم
        /// </summary>
        public decimal OverdueOver90Days { get; set; }

        #endregion

        #region قوائم التفاصيل

        /// <summary>
        /// تفاصيل الأقساط حسب العميل
        /// </summary>
        public List<CustomerInstallmentDetail> CustomerDetails { get; set; } = new List<CustomerInstallmentDetail>();

        /// <summary>
        /// تفاصيل الأقساط حسب المنتج
        /// </summary>
        public List<ProductInstallmentDetail> ProductDetails { get; set; } = new List<ProductInstallmentDetail>();

        /// <summary>
        /// تفاصيل الأقساط اليومية
        /// </summary>
        public List<DailyInstallmentDetail> DailyDetails { get; set; } = new List<DailyInstallmentDetail>();

        /// <summary>
        /// تفاصيل الأقساط المستحقة
        /// </summary>
        public List<DueInstallmentDetail> DueInstallments { get; set; } = new List<DueInstallmentDetail>();

        /// <summary>
        /// تفاصيل الأقساط المتأخرة
        /// </summary>
        public List<OverdueInstallmentDetail> OverdueInstallmentDetails { get; set; } = new List<OverdueInstallmentDetail>();

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// نسبة التحصيل
        /// </summary>
        public decimal CollectionPercentage => TotalInstallmentValue > 0 ? (TotalPaidAmount / TotalInstallmentValue) * 100 : 0;

        /// <summary>
        /// نسبة التأخير
        /// </summary>
        public decimal OverduePercentage => TotalInstallmentValue > 0 ? (OverdueInstallmentAmount / TotalInstallmentValue) * 100 : 0;

        /// <summary>
        /// متوسط قيمة القسط
        /// </summary>
        public decimal AverageInstallmentValue => TotalInstallments > 0 ? TotalInstallmentValue / TotalInstallments : 0;

        /// <summary>
        /// أكبر عميل مديونية
        /// </summary>
        public CustomerInstallmentDetail TopDebtorCustomer => CustomerDetails?.OrderByDescending(c => c.OutstandingAmount).FirstOrDefault();

        /// <summary>
        /// أكثر منتج أقساط
        /// </summary>
        public ProductInstallmentDetail TopInstallmentProduct => ProductDetails?.OrderByDescending(p => p.TotalInstallmentValue).FirstOrDefault();

        /// <summary>
        /// متوسط فترة التأخير
        /// </summary>
        public double AverageOverdueDays
        {
            get
            {
                var overdueDetails = OverdueInstallmentDetails?.Where(o => o.DaysOverdue > 0);
                return overdueDetails?.Any() == true ? overdueDetails.Average(o => o.DaysOverdue) : 0;
            }
        }

        #endregion

        #region الدوال المساعدة

        /// <summary>
        /// حساب الإجماليات من التفاصيل
        /// </summary>
        public void CalculateTotals()
        {
            if (CustomerDetails?.Any() == true)
            {
                TotalInstallmentValue = CustomerDetails.Sum(c => c.TotalInstallmentValue);
                TotalPaidAmount = CustomerDetails.Sum(c => c.PaidAmount);
                TotalOutstandingAmount = CustomerDetails.Sum(c => c.OutstandingAmount);
                TotalInstallments = CustomerDetails.Sum(c => c.TotalInstallments);
                PaidInstallments = CustomerDetails.Sum(c => c.PaidInstallments);
                OutstandingInstallments = CustomerDetails.Sum(c => c.OutstandingInstallments);
                OverdueInstallments = CustomerDetails.Sum(c => c.OverdueInstallments);
            }

            if (OverdueInstallmentDetails?.Any() == true)
            {
                OverdueInstallmentAmount = OverdueInstallmentDetails.Sum(o => o.Amount);
            }

            // حساب الأقساط حسب فترة التأخير
            CalculateOverdueByPeriod();
        }

        /// <summary>
        /// حساب الأقساط المتأخرة حسب الفترة
        /// </summary>
        private void CalculateOverdueByPeriod()
        {
            if (OverdueInstallmentDetails?.Any() == true)
            {
                Overdue30Days = OverdueInstallmentDetails.Where(o => o.DaysOverdue <= 30).Sum(o => o.Amount);
                Overdue60Days = OverdueInstallmentDetails.Where(o => o.DaysOverdue > 30 && o.DaysOverdue <= 60).Sum(o => o.Amount);
                Overdue90Days = OverdueInstallmentDetails.Where(o => o.DaysOverdue > 60 && o.DaysOverdue <= 90).Sum(o => o.Amount);
                OverdueOver90Days = OverdueInstallmentDetails.Where(o => o.DaysOverdue > 90).Sum(o => o.Amount);
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            if (FromDate > ToDate)
                return false;

            if (string.IsNullOrWhiteSpace(ReportType))
                return false;

            if (string.IsNullOrWhiteSpace(GeneratedBy))
                return false;

            return true;
        }

        /// <summary>
        /// الحصول على ملخص التقرير
        /// </summary>
        /// <returns>ملخص التقرير</returns>
        public string GetSummary()
        {
            return $"تقرير الأقساط من {FromDate:dd/MM/yyyy} إلى {ToDate:dd/MM/yyyy}\n" +
                   $"إجمالي قيمة الأقساط: {TotalInstallmentValue:C}\n" +
                   $"المبلغ المدفوع: {TotalPaidAmount:C}\n" +
                   $"المبلغ المستحق: {TotalOutstandingAmount:C}\n" +
                   $"نسبة التحصيل: {CollectionPercentage:F2}%";
        }

        /// <summary>
        /// الحصول على تحليل المخاطر
        /// </summary>
        /// <returns>تحليل المخاطر</returns>
        public string GetRiskAnalysis()
        {
            var riskLevel = "منخفض";
            
            if (OverduePercentage > 30)
                riskLevel = "عالي";
            else if (OverduePercentage > 15)
                riskLevel = "متوسط";
            
            return $"مستوى المخاطر: {riskLevel}\n" +
                   $"نسبة التأخير: {OverduePercentage:F2}%\n" +
                   $"متوسط فترة التأخير: {AverageOverdueDays:F0} يوم";
        }

        #endregion
    }

    #region النماذج المساعدة

    /// <summary>
    /// تفاصيل أقساط العميل
    /// </summary>
    public class CustomerInstallmentDetail
    {
        public int CustomerID { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public decimal TotalInstallmentValue { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public int TotalInstallments { get; set; }
        public int PaidInstallments { get; set; }
        public int OutstandingInstallments { get; set; }
        public int OverdueInstallments { get; set; }
        public DateTime LastPaymentDate { get; set; }
        public DateTime NextDueDate { get; set; }
        public decimal NextDueAmount { get; set; }
        public int DaysOverdue { get; set; }
        public string RiskLevel { get; set; }
    }

    /// <summary>
    /// تفاصيل أقساط المنتج
    /// </summary>
    public class ProductInstallmentDetail
    {
        public int ProductID { get; set; }
        public string ProductName { get; set; }
        public string ProductCode { get; set; }
        public decimal TotalInstallmentValue { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public int TotalInstallments { get; set; }
        public int ActiveContracts { get; set; }
        public decimal AverageInstallmentValue { get; set; }
    }

    /// <summary>
    /// تفاصيل الأقساط اليومية
    /// </summary>
    public class DailyInstallmentDetail
    {
        public DateTime InstallmentDate { get; set; }
        public decimal DueAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal OutstandingAmount { get; set; }
        public int DueCount { get; set; }
        public int PaidCount { get; set; }
        public int OverdueCount { get; set; }
        public decimal CollectionRate => DueAmount > 0 ? (PaidAmount / DueAmount) * 100 : 0;
        public string DayName => InstallmentDate.ToString("dddd");
    }

    /// <summary>
    /// تفاصيل القسط المستحق
    /// </summary>
    public class DueInstallmentDetail
    {
        public int InstallmentID { get; set; }
        public int CustomerID { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public decimal Amount { get; set; }
        public DateTime DueDate { get; set; }
        public int InstallmentNumber { get; set; }
        public int TotalInstallments { get; set; }
        public string ProductName { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
    }

    /// <summary>
    /// تفاصيل القسط المتأخر
    /// </summary>
    public class OverdueInstallmentDetail
    {
        public int InstallmentID { get; set; }
        public int CustomerID { get; set; }
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public decimal Amount { get; set; }
        public DateTime DueDate { get; set; }
        public int DaysOverdue { get; set; }
        public decimal LateFee { get; set; }
        public decimal TotalAmountDue { get; set; }
        public string ProductName { get; set; }
        public string RiskLevel { get; set; }
        public DateTime LastContactDate { get; set; }
        public string LastContactResult { get; set; }
    }

    #endregion

    #region التعدادات

    /// <summary>
    /// أنواع تقارير الأقساط
    /// </summary>
    public static class InstallmentReportTypes
    {
        public const string Due = "Due";
        public const string Overdue = "Overdue";
        public const string Paid = "Paid";
        public const string ByCustomer = "ByCustomer";
        public const string ByProduct = "ByProduct";
        public const string Daily = "Daily";
        public const string Weekly = "Weekly";
        public const string Monthly = "Monthly";
        public const string Collection = "Collection";
        public const string Risk = "Risk";
    }

    /// <summary>
    /// حالات الأقساط
    /// </summary>
    public static class InstallmentStatus
    {
        public const string Pending = "Pending";
        public const string Paid = "Paid";
        public const string Overdue = "Overdue";
        public const string Cancelled = "Cancelled";
        public const string Rescheduled = "Rescheduled";
    }

    /// <summary>
    /// مستويات المخاطر
    /// </summary>
    public static class RiskLevels
    {
        public const string Low = "Low";
        public const string Medium = "Medium";
        public const string High = "High";
        public const string Critical = "Critical";
    }

    #endregion
}
