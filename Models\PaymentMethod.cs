using System;
using System.ComponentModel.DataAnnotations;

namespace AredooPOS.Models
{
    /// <summary>
    /// نموذج طريقة الدفع
    /// يمثل طرق الدفع المختلفة المتاحة في النظام
    /// </summary>
    public class PaymentMethod
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم طريقة الدفع
        /// </summary>
        public int PaymentMethodID { get; set; }

        /// <summary>
        /// اسم طريقة الدفع بالإنجليزية
        /// </summary>
        [Required(ErrorMessage = "اسم طريقة الدفع مطلوب")]
        [StringLength(50, ErrorMessage = "اسم طريقة الدفع يجب أن يكون أقل من 50 حرف")]
        public string MethodName { get; set; }

        /// <summary>
        /// اسم طريقة الدفع بالعربية
        /// </summary>
        [Required(ErrorMessage = "اسم طريقة الدفع بالعربية مطلوب")]
        [StringLength(50, ErrorMessage = "اسم طريقة الدفع بالعربية يجب أن يكون أقل من 50 حرف")]
        public string MethodNameArabic { get; set; }

        /// <summary>
        /// رمز طريقة الدفع
        /// </summary>
        [Required(ErrorMessage = "رمز طريقة الدفع مطلوب")]
        [StringLength(10, ErrorMessage = "رمز طريقة الدفع يجب أن يكون أقل من 10 أحرف")]
        public string MethodCode { get; set; }

        /// <summary>
        /// هل طريقة الدفع نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل تتطلب رقم مرجعي
        /// </summary>
        public bool RequiresReference { get; set; } = false;

        /// <summary>
        /// هل تسمح بالدفع الجزئي
        /// </summary>
        public bool AllowPartialPayment { get; set; } = true;

        /// <summary>
        /// نسبة رسوم المعالجة
        /// </summary>
        [Range(0, 100, ErrorMessage = "نسبة رسوم المعالجة يجب أن تكون بين 0 و 100")]
        public decimal ProcessingFeePercentage { get; set; } = 0;

        /// <summary>
        /// الحد الأدنى للمبلغ
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الحد الأدنى للمبلغ يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal MinimumAmount { get; set; } = 0;

        /// <summary>
        /// الحد الأقصى للمبلغ
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "الحد الأقصى للمبلغ يجب أن يكون أكبر من صفر")]
        public decimal? MaximumAmount { get; set; }

        /// <summary>
        /// وصف طريقة الدفع
        /// </summary>
        [StringLength(255, ErrorMessage = "الوصف يجب أن يكون أقل من 255 حرف")]
        public string Description { get; set; }

        /// <summary>
        /// ترتيب العرض
        /// </summary>
        public int SortOrder { get; set; } = 0;

        #endregion

        #region معلومات النظام

        /// <summary>
        /// من أنشأ السجل
        /// </summary>
        [Required]
        [StringLength(50)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// من عدل السجل
        /// </summary>
        [StringLength(50)]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// تاريخ التعديل
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// هل السجل محذوف
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// من حذف السجل
        /// </summary>
        [StringLength(50)]
        public string DeletedBy { get; set; }

        /// <summary>
        /// تاريخ الحذف
        /// </summary>
        public DateTime? DeletedDate { get; set; }

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// اسم العرض (العربي أو الإنجليزي حسب اللغة)
        /// </summary>
        public string DisplayName => string.IsNullOrWhiteSpace(MethodNameArabic) ? MethodName : MethodNameArabic;

        /// <summary>
        /// هل طريقة الدفع نقدية
        /// </summary>
        public bool IsCash => MethodCode?.ToUpper() == "CASH";

        /// <summary>
        /// هل طريقة الدفع بطاقة
        /// </summary>
        public bool IsCard => MethodCode?.ToUpper() == "CARD";

        /// <summary>
        /// هل طريقة الدفع تحويل بنكي
        /// </summary>
        public bool IsTransfer => MethodCode?.ToUpper() == "TRANSFER";

        /// <summary>
        /// هل لها رسوم معالجة
        /// </summary>
        public bool HasProcessingFee => ProcessingFeePercentage > 0;

        /// <summary>
        /// هل لها حد أقصى للمبلغ
        /// </summary>
        public bool HasMaximumLimit => MaximumAmount.HasValue && MaximumAmount.Value > 0;

        #endregion

        #region الدوال المساعدة

        /// <summary>
        /// التحقق من صحة المبلغ لطريقة الدفع
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>true إذا كان المبلغ صحيح</returns>
        public bool IsAmountValid(decimal amount)
        {
            if (amount <= 0)
                return false;

            if (amount < MinimumAmount)
                return false;

            if (HasMaximumLimit && amount > MaximumAmount.Value)
                return false;

            return true;
        }

        /// <summary>
        /// حساب رسوم المعالجة
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>رسوم المعالجة</returns>
        public decimal CalculateProcessingFee(decimal amount)
        {
            if (!HasProcessingFee || amount <= 0)
                return 0;

            return Math.Round(amount * ProcessingFeePercentage / 100, 2);
        }

        /// <summary>
        /// حساب المبلغ الإجمالي مع الرسوم
        /// </summary>
        /// <param name="amount">المبلغ الأساسي</param>
        /// <returns>المبلغ الإجمالي</returns>
        public decimal CalculateTotalAmount(decimal amount)
        {
            return amount + CalculateProcessingFee(amount);
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            if (string.IsNullOrWhiteSpace(MethodName))
                return false;

            if (string.IsNullOrWhiteSpace(MethodNameArabic))
                return false;

            if (string.IsNullOrWhiteSpace(MethodCode))
                return false;

            if (ProcessingFeePercentage < 0 || ProcessingFeePercentage > 100)
                return false;

            if (MinimumAmount < 0)
                return false;

            if (HasMaximumLimit && MaximumAmount.Value <= MinimumAmount)
                return false;

            return true;
        }

        /// <summary>
        /// نسخ البيانات من طريقة دفع أخرى
        /// </summary>
        /// <param name="source">المصدر</param>
        public void CopyFrom(PaymentMethod source)
        {
            if (source == null)
                return;

            MethodName = source.MethodName;
            MethodNameArabic = source.MethodNameArabic;
            MethodCode = source.MethodCode;
            IsActive = source.IsActive;
            RequiresReference = source.RequiresReference;
            AllowPartialPayment = source.AllowPartialPayment;
            ProcessingFeePercentage = source.ProcessingFeePercentage;
            MinimumAmount = source.MinimumAmount;
            MaximumAmount = source.MaximumAmount;
            Description = source.Description;
            SortOrder = source.SortOrder;
        }

        /// <summary>
        /// تحويل إلى نص
        /// </summary>
        /// <returns>تمثيل نصي للكائن</returns>
        public override string ToString()
        {
            return $"{DisplayName} ({MethodCode})";
        }

        /// <summary>
        /// مقارنة مع كائن آخر
        /// </summary>
        /// <param name="obj">الكائن المراد المقارنة معه</param>
        /// <returns>true إذا كانا متساويين</returns>
        public override bool Equals(object obj)
        {
            if (obj is PaymentMethod other)
            {
                return PaymentMethodID == other.PaymentMethodID;
            }
            return false;
        }

        /// <summary>
        /// الحصول على رمز التجمع
        /// </summary>
        /// <returns>رمز التجمع</returns>
        public override int GetHashCode()
        {
            return PaymentMethodID.GetHashCode();
        }

        #endregion
    }

    #region التعدادات المساعدة

    /// <summary>
    /// أنواع طرق الدفع الافتراضية
    /// </summary>
    public static class PaymentMethodTypes
    {
        public const string Cash = "CASH";
        public const string Card = "CARD";
        public const string Transfer = "TRANSFER";
        public const string Check = "CHECK";
        public const string GiftCard = "GIFT";
    }

    /// <summary>
    /// أسماء طرق الدفع بالعربية
    /// </summary>
    public static class PaymentMethodNames
    {
        public const string Cash = "نقدي";
        public const string Card = "بطاقة ائتمان";
        public const string Transfer = "تحويل بنكي";
        public const string Check = "شيك";
        public const string GiftCard = "بطاقة هدايا";
    }

    #endregion
}
