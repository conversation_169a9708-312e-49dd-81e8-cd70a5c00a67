using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// نموذج الفاتورة الحديث
    /// </summary>
    public class ModernInvoiceForm : UserControl
    {
        #region المتغيرات

        private Panel _headerPanel;
        private Panel _customerPanel;
        private Panel _productsPanel;
        private Panel _totalsPanel;
        private Panel _paymentPanel;
        private Panel _actionsPanel;

        // معلومات العميل
        private ComboBox _customerComboBox;
        private Button _addCustomerButton;
        private Label _customerInfoLabel;

        // جدول المنتجات
        private DataGridView _productsGrid;
        private Button _addProductButton;
        private TextBox _barcodeTextBox;

        // الإجماليات
        private Label _subtotalLabel;
        private Label _taxLabel;
        private Label _discountLabel;
        private Label _totalLabel;
        private NumericUpDown _discountNumeric;

        // طرق الدفع
        private RadioButton _cashRadio;
        private RadioButton _cardRadio;
        private RadioButton _installmentRadio;
        private Panel _installmentPanel;
        private NumericUpDown _installmentMonthsNumeric;

        // الأزرار
        private Button _saveButton;
        private Button _printButton;
        private Button _clearButton;

        // البيانات
        private List<InvoiceItem> _invoiceItems;
        private decimal _subtotal;
        private decimal _tax;
        private decimal _discount;
        private decimal _total;

        #endregion

        #region الأحداث

        public event EventHandler InvoiceSaved;
        public event EventHandler InvoicePrinted;
        public event EventHandler InvoiceCleared;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ نموذج الفاتورة
        /// </summary>
        public ModernInvoiceForm()
        {
            _invoiceItems = new List<InvoiceItem>();
            InitializeComponent();
            SetupDesign();
            SetupEvents();
        }

        /// <summary>
        /// تهيئة المكونات
        /// </summary>
        private void InitializeComponent()
        {
            // إعدادات النموذج
            BackColor = ModernDesignSystem.Colors.Background;
            Dock = DockStyle.Fill;
            AutoScroll = true;
            Padding = new Padding(ModernDesignSystem.Spacing.Large);

            CreateHeaderPanel();
            CreateCustomerPanel();
            CreateProductsPanel();
            CreateTotalsPanel();
            CreatePaymentPanel();
            CreateActionsPanel();

            // ترتيب اللوحات
            Controls.AddRange(new Control[] 
            { 
                _headerPanel, _customerPanel, _productsPanel, 
                _totalsPanel, _paymentPanel, _actionsPanel 
            });
        }

        /// <summary>
        /// إنشاء لوحة الرأس
        /// </summary>
        private void CreateHeaderPanel()
        {
            _headerPanel = new Panel
            {
                Size = new Size(Width - 48, 80),
                Location = new Point(24, 24),
                BackColor = ModernDesignSystem.Colors.Surface,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            var titleLabel = new Label
            {
                Text = "💰 إنشاء فاتورة جديدة",
                Font = ModernDesignSystem.Fonts.Heading2,
                ForeColor = ModernDesignSystem.Colors.Primary,
                Size = new Size(_headerPanel.Width - 40, 40),
                Location = new Point(20, 20),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            var invoiceNumberLabel = new Label
            {
                Text = $"رقم الفاتورة: INV-{DateTime.Now:yyyyMMdd}-001",
                Font = ModernDesignSystem.Fonts.Body,
                ForeColor = ModernDesignSystem.Colors.TextSecondary,
                Size = new Size(_headerPanel.Width - 40, 20),
                Location = new Point(20, 50),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            _headerPanel.Controls.AddRange(new Control[] { titleLabel, invoiceNumberLabel });
            _headerPanel.Paint += (s, e) => ModernDesignSystem.DrawModernCard(e.Graphics, _headerPanel.ClientRectangle, ModernDesignSystem.Colors.Surface);
        }

        /// <summary>
        /// إنشاء لوحة العميل
        /// </summary>
        private void CreateCustomerPanel()
        {
            _customerPanel = new Panel
            {
                Size = new Size(Width - 48, 120),
                Location = new Point(24, 120),
                BackColor = ModernDesignSystem.Colors.Surface,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            var customerLabel = new Label
            {
                Text = "👤 معلومات العميل",
                Font = ModernDesignSystem.Fonts.Heading4,
                ForeColor = ModernDesignSystem.Colors.TextPrimary,
                Size = new Size(150, 30),
                Location = new Point(_customerPanel.Width - 170, 15),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            _customerComboBox = new ComboBox
            {
                Size = new Size(300, 32),
                Location = new Point(_customerPanel.Width - 320, 50),
                Font = ModernDesignSystem.Fonts.Body,
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };

            _addCustomerButton = new Button
            {
                Text = "➕ إضافة عميل",
                Font = ModernDesignSystem.Fonts.Button,
                Size = new Size(120, 32),
                Location = new Point(_customerPanel.Width - 450, 50),
                FlatStyle = FlatStyle.Flat,
                BackColor = ModernDesignSystem.Colors.Secondary,
                ForeColor = ModernDesignSystem.Colors.TextOnPrimary,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            _addCustomerButton.FlatAppearance.BorderSize = 0;

            _customerInfoLabel = new Label
            {
                Text = "لم يتم تحديد عميل",
                Font = ModernDesignSystem.Fonts.Caption,
                ForeColor = ModernDesignSystem.Colors.TextSecondary,
                Size = new Size(400, 20),
                Location = new Point(_customerPanel.Width - 420, 85),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };

            _customerPanel.Controls.AddRange(new Control[] { customerLabel, _customerComboBox, _addCustomerButton, _customerInfoLabel });
            _customerPanel.Paint += (s, e) => ModernDesignSystem.DrawModernCard(e.Graphics, _customerPanel.ClientRectangle, ModernDesignSystem.Colors.Surface);
        }

        /// <summary>
        /// إنشاء لوحة المنتجات
        /// </summary>
        private void CreateProductsPanel()
        {
            _productsPanel = new Panel
            {
                Size = new Size(Width - 48, 350),
                Location = new Point(24, 260),
                BackColor = ModernDesignSystem.Colors.Surface,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            var productsLabel = new Label
            {
                Text = "📦 المنتجات",
                Font = ModernDesignSystem.Fonts.Heading4,
                ForeColor = ModernDesignSystem.Colors.TextPrimary,
                Size = new Size(100, 30),
                Location = new Point(_productsPanel.Width - 120, 15),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // حقل الباركود
            _barcodeTextBox = new TextBox
            {
                Text = "امسح الباركود أو أدخل كود المنتج",
                Size = new Size(250, 32),
                Location = new Point(_productsPanel.Width - 270, 50),
                Font = ModernDesignSystem.Fonts.Body,
                RightToLeft = RightToLeft.Yes,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };

            _addProductButton = new Button
            {
                Text = "➕ إضافة منتج",
                Font = ModernDesignSystem.Fonts.Button,
                Size = new Size(120, 32),
                Location = new Point(_productsPanel.Width - 400, 50),
                FlatStyle = FlatStyle.Flat,
                BackColor = ModernDesignSystem.Colors.Primary,
                ForeColor = ModernDesignSystem.Colors.TextOnPrimary,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            _addProductButton.FlatAppearance.BorderSize = 0;

            // جدول المنتجات
            _productsGrid = new DataGridView
            {
                Size = new Size(_productsPanel.Width - 40, 250),
                Location = new Point(20, 90),
                BackgroundColor = ModernDesignSystem.Colors.Surface,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                EnableHeadersVisualStyles = false,
                RowHeadersVisible = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                Font = ModernDesignSystem.Fonts.Body,
                RightToLeft = RightToLeft.Yes,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };

            SetupProductsGrid();

            _productsPanel.Controls.AddRange(new Control[] { productsLabel, _barcodeTextBox, _addProductButton, _productsGrid });
            _productsPanel.Paint += (s, e) => ModernDesignSystem.DrawModernCard(e.Graphics, _productsPanel.ClientRectangle, ModernDesignSystem.Colors.Surface);
        }

        /// <summary>
        /// إعداد جدول المنتجات
        /// </summary>
        private void SetupProductsGrid()
        {
            _productsGrid.Columns.Clear();
            
            _productsGrid.Columns.Add("Name", "اسم المنتج");
            _productsGrid.Columns.Add("Price", "السعر");
            _productsGrid.Columns.Add("Quantity", "الكمية");
            _productsGrid.Columns.Add("Discount", "الخصم %");
            _productsGrid.Columns.Add("Total", "الإجمالي");

            // تنسيق الأعمدة
            _productsGrid.Columns["Name"].Width = 200;
            _productsGrid.Columns["Price"].Width = 100;
            _productsGrid.Columns["Quantity"].Width = 80;
            _productsGrid.Columns["Discount"].Width = 80;
            _productsGrid.Columns["Total"].Width = 120;

            // تنسيق الرأس
            _productsGrid.ColumnHeadersDefaultCellStyle.BackColor = ModernDesignSystem.Colors.Primary;
            _productsGrid.ColumnHeadersDefaultCellStyle.ForeColor = ModernDesignSystem.Colors.TextOnPrimary;
            _productsGrid.ColumnHeadersDefaultCellStyle.Font = ModernDesignSystem.Fonts.BodyBold;
            _productsGrid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

            // تنسيق الصفوف
            _productsGrid.DefaultCellStyle.BackColor = ModernDesignSystem.Colors.Surface;
            _productsGrid.DefaultCellStyle.ForeColor = ModernDesignSystem.Colors.TextPrimary;
            _productsGrid.DefaultCellStyle.SelectionBackColor = ModernDesignSystem.Colors.PrimaryLight;
            _productsGrid.DefaultCellStyle.SelectionForeColor = ModernDesignSystem.Colors.TextPrimary;
            _productsGrid.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

            // جعل عمود الكمية قابل للتحرير
            _productsGrid.Columns["Quantity"].ReadOnly = false;
            _productsGrid.Columns["Discount"].ReadOnly = false;
        }

        /// <summary>
        /// إنشاء لوحة الإجماليات
        /// </summary>
        private void CreateTotalsPanel()
        {
            _totalsPanel = new Panel
            {
                Size = new Size((Width - 72) / 2, 200),
                Location = new Point(Width / 2 + 12, 630),
                BackColor = ModernDesignSystem.Colors.Surface,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };

            var totalsLabel = new Label
            {
                Text = "💰 الإجماليات",
                Font = ModernDesignSystem.Fonts.Heading4,
                ForeColor = ModernDesignSystem.Colors.TextPrimary,
                Size = new Size(_totalsPanel.Width - 40, 30),
                Location = new Point(20, 15),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // المجموع الفرعي
            _subtotalLabel = CreateTotalLabel("المجموع الفرعي:", "0.00 ر.س", 50);

            // الخصم
            var discountPanel = new Panel
            {
                Size = new Size(_totalsPanel.Width - 40, 30),
                Location = new Point(20, 80),
                BackColor = Color.Transparent
            };

            var discountLabel = new Label
            {
                Text = "الخصم:",
                Font = ModernDesignSystem.Fonts.Body,
                ForeColor = ModernDesignSystem.Colors.TextPrimary,
                Size = new Size(80, 30),
                Location = new Point(discountPanel.Width - 80, 0),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            _discountNumeric = new NumericUpDown
            {
                Size = new Size(80, 30),
                Location = new Point(discountPanel.Width - 170, 0),
                Font = ModernDesignSystem.Fonts.Numbers,
                Maximum = 100,
                DecimalPlaces = 2
            };

            _discountLabel = new Label
            {
                Text = "0.00 ر.س",
                Font = ModernDesignSystem.Fonts.NumbersLarge,
                ForeColor = ModernDesignSystem.Colors.Warning,
                Size = new Size(80, 30),
                Location = new Point(0, 0),
                TextAlign = ContentAlignment.MiddleLeft
            };

            discountPanel.Controls.AddRange(new Control[] { discountLabel, _discountNumeric, _discountLabel });

            // الضريبة
            _taxLabel = CreateTotalLabel("الضريبة (15%):", "0.00 ر.س", 110);

            // الإجمالي النهائي
            _totalLabel = new Label
            {
                Text = "الإجمالي النهائي: 0.00 ر.س",
                Font = ModernDesignSystem.Fonts.Heading3,
                ForeColor = ModernDesignSystem.Colors.Primary,
                Size = new Size(_totalsPanel.Width - 40, 40),
                Location = new Point(20, 140),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes,
                BackColor = ModernDesignSystem.Colors.PrimaryLight,
                BorderStyle = BorderStyle.FixedSingle
            };

            _totalsPanel.Controls.AddRange(new Control[] { totalsLabel, _subtotalLabel, discountPanel, _taxLabel, _totalLabel });
            _totalsPanel.Paint += (s, e) => ModernDesignSystem.DrawModernCard(e.Graphics, _totalsPanel.ClientRectangle, ModernDesignSystem.Colors.Surface);
        }

        /// <summary>
        /// إنشاء لوحة طرق الدفع
        /// </summary>
        private void CreatePaymentPanel()
        {
            _paymentPanel = new Panel
            {
                Size = new Size((Width - 72) / 2, 200),
                Location = new Point(24, 630),
                BackColor = ModernDesignSystem.Colors.Surface,
                Anchor = AnchorStyles.Top | AnchorStyles.Left
            };

            var paymentLabel = new Label
            {
                Text = "💳 طريقة الدفع",
                Font = ModernDesignSystem.Fonts.Heading4,
                ForeColor = ModernDesignSystem.Colors.TextPrimary,
                Size = new Size(_paymentPanel.Width - 40, 30),
                Location = new Point(20, 15),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            _cashRadio = new RadioButton
            {
                Text = "💵 نقدي",
                Font = ModernDesignSystem.Fonts.Body,
                Size = new Size(100, 25),
                Location = new Point(_paymentPanel.Width - 120, 55),
                Checked = true,
                RightToLeft = RightToLeft.Yes
            };

            _cardRadio = new RadioButton
            {
                Text = "💳 بطاقة",
                Font = ModernDesignSystem.Fonts.Body,
                Size = new Size(100, 25),
                Location = new Point(_paymentPanel.Width - 120, 85),
                RightToLeft = RightToLeft.Yes
            };

            _installmentRadio = new RadioButton
            {
                Text = "📅 أقساط",
                Font = ModernDesignSystem.Fonts.Body,
                Size = new Size(100, 25),
                Location = new Point(_paymentPanel.Width - 120, 115),
                RightToLeft = RightToLeft.Yes
            };

            // لوحة الأقساط
            _installmentPanel = new Panel
            {
                Size = new Size(_paymentPanel.Width - 40, 30),
                Location = new Point(20, 145),
                BackColor = Color.Transparent,
                Visible = false
            };

            var monthsLabel = new Label
            {
                Text = "عدد الأشهر:",
                Font = ModernDesignSystem.Fonts.Body,
                Size = new Size(80, 30),
                Location = new Point(_installmentPanel.Width - 80, 0),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            _installmentMonthsNumeric = new NumericUpDown
            {
                Size = new Size(80, 30),
                Location = new Point(_installmentPanel.Width - 170, 0),
                Font = ModernDesignSystem.Fonts.Numbers,
                Minimum = 2,
                Maximum = 24,
                Value = 3
            };

            _installmentPanel.Controls.AddRange(new Control[] { monthsLabel, _installmentMonthsNumeric });

            _paymentPanel.Controls.AddRange(new Control[] { paymentLabel, _cashRadio, _cardRadio, _installmentRadio, _installmentPanel });
            _paymentPanel.Paint += (s, e) => ModernDesignSystem.DrawModernCard(e.Graphics, _paymentPanel.ClientRectangle, ModernDesignSystem.Colors.Surface);
        }

        /// <summary>
        /// إنشاء لوحة الأزرار
        /// </summary>
        private void CreateActionsPanel()
        {
            _actionsPanel = new Panel
            {
                Size = new Size(Width - 48, 80),
                Location = new Point(24, 850),
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            _saveButton = new Button
            {
                Text = "💾 حفظ الفاتورة",
                Font = ModernDesignSystem.Fonts.ButtonLarge,
                Size = new Size(150, 50),
                Location = new Point(_actionsPanel.Width - 170, 15),
                FlatStyle = FlatStyle.Flat,
                BackColor = ModernDesignSystem.Colors.Success,
                ForeColor = ModernDesignSystem.Colors.TextOnPrimary,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            _saveButton.FlatAppearance.BorderSize = 0;

            _printButton = new Button
            {
                Text = "🖨️ طباعة",
                Font = ModernDesignSystem.Fonts.ButtonLarge,
                Size = new Size(120, 50),
                Location = new Point(_actionsPanel.Width - 300, 15),
                FlatStyle = FlatStyle.Flat,
                BackColor = ModernDesignSystem.Colors.Primary,
                ForeColor = ModernDesignSystem.Colors.TextOnPrimary,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            _printButton.FlatAppearance.BorderSize = 0;

            _clearButton = new Button
            {
                Text = "🗑️ مسح",
                Font = ModernDesignSystem.Fonts.ButtonLarge,
                Size = new Size(100, 50),
                Location = new Point(_actionsPanel.Width - 410, 15),
                FlatStyle = FlatStyle.Flat,
                BackColor = ModernDesignSystem.Colors.Error,
                ForeColor = ModernDesignSystem.Colors.TextOnPrimary,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            _clearButton.FlatAppearance.BorderSize = 0;

            _actionsPanel.Controls.AddRange(new Control[] { _saveButton, _printButton, _clearButton });
        }

        /// <summary>
        /// إنشاء تسمية إجمالي
        /// </summary>
        private Label CreateTotalLabel(string title, string value, int yPosition)
        {
            var panel = new Panel
            {
                Size = new Size(_totalsPanel.Width - 40, 25),
                Location = new Point(20, yPosition),
                BackColor = Color.Transparent
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = ModernDesignSystem.Fonts.Body,
                ForeColor = ModernDesignSystem.Colors.TextPrimary,
                Size = new Size(120, 25),
                Location = new Point(panel.Width - 120, 0),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            var valueLabel = new Label
            {
                Text = value,
                Font = ModernDesignSystem.Fonts.Numbers,
                ForeColor = ModernDesignSystem.Colors.TextPrimary,
                Size = new Size(100, 25),
                Location = new Point(0, 0),
                TextAlign = ContentAlignment.MiddleLeft
            };

            panel.Controls.AddRange(new Control[] { titleLabel, valueLabel });
            _totalsPanel.Controls.Add(panel);

            return valueLabel;
        }

        /// <summary>
        /// إعداد التصميم
        /// </summary>
        private void SetupDesign()
        {
            RightToLeft = RightToLeft.Yes;
        }

        /// <summary>
        /// إعداد الأحداث
        /// </summary>
        private void SetupEvents()
        {
            _installmentRadio.CheckedChanged += (s, e) => _installmentPanel.Visible = _installmentRadio.Checked;
            _discountNumeric.ValueChanged += CalculateTotals;
            _productsGrid.CellValueChanged += (s, e) => CalculateTotals(s, e);

            _saveButton.Click += (s, e) => InvoiceSaved?.Invoke(this, e);
            _printButton.Click += (s, e) => InvoicePrinted?.Invoke(this, e);
            _clearButton.Click += (s, e) => InvoiceCleared?.Invoke(this, e);
        }

        /// <summary>
        /// حساب الإجماليات
        /// </summary>
        private void CalculateTotals(object sender, EventArgs e)
        {
            _subtotal = 0;
            foreach (DataGridViewRow row in _productsGrid.Rows)
            {
                if (row.Cells["Total"].Value != null)
                {
                    if (decimal.TryParse(row.Cells["Total"].Value.ToString(), out decimal total))
                    {
                        _subtotal += total;
                    }
                }
            }

            _discount = _subtotal * (_discountNumeric.Value / 100);
            _tax = (_subtotal - _discount) * 0.15m;
            _total = _subtotal - _discount + _tax;

            _subtotalLabel.Text = $"{_subtotal:N2} ر.س";
            _discountLabel.Text = $"{_discount:N2} ر.س";
            _taxLabel.Text = $"{_tax:N2} ر.س";
            _totalLabel.Text = $"الإجمالي النهائي: {_total:N2} ر.س";
        }

        #endregion
    }

    #region الكلاسات المساعدة

    /// <summary>
    /// عنصر الفاتورة
    /// </summary>
    public class InvoiceItem
    {
        public string ProductName { get; set; }
        public decimal Price { get; set; }
        public int Quantity { get; set; }
        public decimal Discount { get; set; }
        public decimal Total => (Price * Quantity) * (1 - Discount / 100);
    }

    #endregion
}
