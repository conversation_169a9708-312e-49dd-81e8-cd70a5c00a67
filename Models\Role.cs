using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace AredooPOS.Models
{
    /// <summary>
    /// نموذج الدور والصلاحيات
    /// يمثل دور المستخدم في النظام مع الصلاحيات المرتبطة
    /// </summary>
    public class Role
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم الدور في قاعدة البيانات (مفتاح أساسي)
        /// </summary>
        public int RoleID { get; set; }

        /// <summary>
        /// اسم الدور
        /// </summary>
        [Required(ErrorMessage = "اسم الدور مطلوب")]
        [StringLength(50, ErrorMessage = "اسم الدور لا يجب أن يتجاوز 50 حرف")]
        public string RoleName { get; set; }

        /// <summary>
        /// اسم الدور بالإنجليزية
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم الدور بالإنجليزية لا يجب أن يتجاوز 50 حرف")]
        public string RoleNameEn { get; set; }

        /// <summary>
        /// وصف الدور
        /// </summary>
        [StringLength(200, ErrorMessage = "وصف الدور لا يجب أن يتجاوز 200 حرف")]
        public string Description { get; set; }

        /// <summary>
        /// مستوى الصلاحية (1-10)
        /// </summary>
        [Range(1, 10, ErrorMessage = "مستوى الصلاحية يجب أن يكون بين 1 و 10")]
        public int PermissionLevel { get; set; }

        /// <summary>
        /// هل هذا دور إداري
        /// </summary>
        public bool IsAdminRole { get; set; }

        /// <summary>
        /// هل هذا دور افتراضي (لا يمكن حذفه)
        /// </summary>
        public bool IsSystemRole { get; set; }

        /// <summary>
        /// هل الدور نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        #endregion

        #region صلاحيات النظام العامة

        /// <summary>
        /// الوصول للنظام
        /// </summary>
        public bool CanAccessSystem { get; set; } = true;

        /// <summary>
        /// إدارة المستخدمين
        /// </summary>
        public bool CanManageUsers { get; set; }

        /// <summary>
        /// إدارة الأدوار والصلاحيات
        /// </summary>
        public bool CanManageRoles { get; set; }

        /// <summary>
        /// عرض التقارير
        /// </summary>
        public bool CanViewReports { get; set; }

        /// <summary>
        /// إدارة إعدادات النظام
        /// </summary>
        public bool CanManageSystemSettings { get; set; }

        /// <summary>
        /// عرض سجلات النظام
        /// </summary>
        public bool CanViewSystemLogs { get; set; }

        #endregion

        #region صلاحيات المنتجات والمخزون

        /// <summary>
        /// إدارة المنتجات
        /// </summary>
        public bool CanManageProducts { get; set; }

        /// <summary>
        /// إدارة فئات المنتجات
        /// </summary>
        public bool CanManageCategories { get; set; }

        /// <summary>
        /// إدارة المخزون
        /// </summary>
        public bool CanManageInventory { get; set; }

        /// <summary>
        /// تسوية المخزون
        /// </summary>
        public bool CanAdjustInventory { get; set; }

        /// <summary>
        /// عرض تقارير المخزون
        /// </summary>
        public bool CanViewInventoryReports { get; set; }

        #endregion

        #region صلاحيات المبيعات

        /// <summary>
        /// إجراء المبيعات
        /// </summary>
        public bool CanProcessSales { get; set; } = true;

        /// <summary>
        /// إلغاء المبيعات
        /// </summary>
        public bool CanCancelSales { get; set; }

        /// <summary>
        /// تطبيق خصومات
        /// </summary>
        public bool CanApplyDiscounts { get; set; }

        /// <summary>
        /// الحد الأقصى للخصم المسموح (%)
        /// </summary>
        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public decimal MaxDiscountPercentage { get; set; }

        /// <summary>
        /// إرجاع المنتجات
        /// </summary>
        public bool CanProcessReturns { get; set; }

        /// <summary>
        /// عرض تقارير المبيعات
        /// </summary>
        public bool CanViewSalesReports { get; set; }

        #endregion

        #region صلاحيات العملاء

        /// <summary>
        /// إدارة العملاء
        /// </summary>
        public bool CanManageCustomers { get; set; }

        /// <summary>
        /// عرض بيانات العملاء
        /// </summary>
        public bool CanViewCustomerData { get; set; }

        /// <summary>
        /// إدارة ديون العملاء
        /// </summary>
        public bool CanManageCustomerDebts { get; set; }

        /// <summary>
        /// عرض تقارير العملاء
        /// </summary>
        public bool CanViewCustomerReports { get; set; }

        #endregion

        #region صلاحيات المالية

        /// <summary>
        /// إدارة الصندوق
        /// </summary>
        public bool CanManageCashRegister { get; set; }

        /// <summary>
        /// عرض التقارير المالية
        /// </summary>
        public bool CanViewFinancialReports { get; set; }

        /// <summary>
        /// إدارة طرق الدفع
        /// </summary>
        public bool CanManagePaymentMethods { get; set; }

        /// <summary>
        /// عرض الأرباح والخسائر
        /// </summary>
        public bool CanViewProfitLoss { get; set; }

        #endregion

        #region معلومات النظام

        /// <summary>
        /// منشئ السجل
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المنشئ لا يجب أن يتجاوز 50 حرف")]
        public string CreatedBy { get; set; }

        /// <summary>
        /// تاريخ إنشاء السجل
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// آخر من عدل السجل
        /// </summary>
        [StringLength(50, ErrorMessage = "اسم المعدل لا يجب أن يتجاوز 50 حرف")]
        public string ModifiedBy { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime ModifiedDate { get; set; }

        #endregion

        #region القوائم المرتبطة

        /// <summary>
        /// المستخدمون المرتبطون بهذا الدور
        /// </summary>
        public List<User> Users { get; set; }

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ الدور مع التهيئة الافتراضية
        /// </summary>
        public Role()
        {
            // تهيئة القوائم
            Users = new List<User>();

            // تهيئة التواريخ
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;

            // تهيئة القيم الافتراضية
            IsActive = true;
            IsAdminRole = false;
            IsSystemRole = false;
            PermissionLevel = 1;
            CanAccessSystem = true;
            CanProcessSales = true;
            MaxDiscountPercentage = 0;
        }

        #endregion

        #region العمليات والتحقق

        /// <summary>
        /// التحقق من صحة بيانات الدور
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(RoleName) &&
                   PermissionLevel >= 1 && PermissionLevel <= 10;
        }

        /// <summary>
        /// التحقق من صلاحية معينة
        /// </summary>
        /// <param name="permission">اسم الصلاحية</param>
        /// <returns>true إذا كان الدور يملك الصلاحية</returns>
        public bool HasPermission(string permission)
        {
            if (!IsActive)
                return false;

            return permission.ToLower() switch
            {
                "access_system" => CanAccessSystem,
                "manage_users" => CanManageUsers,
                "manage_roles" => CanManageRoles,
                "view_reports" => CanViewReports,
                "manage_system_settings" => CanManageSystemSettings,
                "view_system_logs" => CanViewSystemLogs,
                "manage_products" => CanManageProducts,
                "manage_categories" => CanManageCategories,
                "manage_inventory" => CanManageInventory,
                "adjust_inventory" => CanAdjustInventory,
                "view_inventory_reports" => CanViewInventoryReports,
                "process_sales" => CanProcessSales,
                "cancel_sales" => CanCancelSales,
                "apply_discounts" => CanApplyDiscounts,
                "process_returns" => CanProcessReturns,
                "view_sales_reports" => CanViewSalesReports,
                "manage_customers" => CanManageCustomers,
                "view_customer_data" => CanViewCustomerData,
                "manage_customer_debts" => CanManageCustomerDebts,
                "view_customer_reports" => CanViewCustomerReports,
                "manage_cash_register" => CanManageCashRegister,
                "view_financial_reports" => CanViewFinancialReports,
                "manage_payment_methods" => CanManagePaymentMethods,
                "view_profit_loss" => CanViewProfitLoss,
                _ => false
            };
        }

        /// <summary>
        /// الحصول على قائمة جميع الصلاحيات
        /// </summary>
        /// <returns>قائمة الصلاحيات</returns>
        public List<string> GetAllPermissions()
        {
            var permissions = new List<string>();

            if (CanAccessSystem) permissions.Add("access_system");
            if (CanManageUsers) permissions.Add("manage_users");
            if (CanManageRoles) permissions.Add("manage_roles");
            if (CanViewReports) permissions.Add("view_reports");
            if (CanManageSystemSettings) permissions.Add("manage_system_settings");
            if (CanViewSystemLogs) permissions.Add("view_system_logs");
            if (CanManageProducts) permissions.Add("manage_products");
            if (CanManageCategories) permissions.Add("manage_categories");
            if (CanManageInventory) permissions.Add("manage_inventory");
            if (CanAdjustInventory) permissions.Add("adjust_inventory");
            if (CanViewInventoryReports) permissions.Add("view_inventory_reports");
            if (CanProcessSales) permissions.Add("process_sales");
            if (CanCancelSales) permissions.Add("cancel_sales");
            if (CanApplyDiscounts) permissions.Add("apply_discounts");
            if (CanProcessReturns) permissions.Add("process_returns");
            if (CanViewSalesReports) permissions.Add("view_sales_reports");
            if (CanManageCustomers) permissions.Add("manage_customers");
            if (CanViewCustomerData) permissions.Add("view_customer_data");
            if (CanManageCustomerDebts) permissions.Add("manage_customer_debts");
            if (CanViewCustomerReports) permissions.Add("view_customer_reports");
            if (CanManageCashRegister) permissions.Add("manage_cash_register");
            if (CanViewFinancialReports) permissions.Add("view_financial_reports");
            if (CanManagePaymentMethods) permissions.Add("manage_payment_methods");
            if (CanViewProfitLoss) permissions.Add("view_profit_loss");

            return permissions;
        }

        /// <summary>
        /// نسخ الصلاحيات من دور آخر
        /// </summary>
        /// <param name="sourceRole">الدور المصدر</param>
        public void CopyPermissionsFrom(Role sourceRole)
        {
            if (sourceRole == null)
                return;

            PermissionLevel = sourceRole.PermissionLevel;
            IsAdminRole = sourceRole.IsAdminRole;
            CanAccessSystem = sourceRole.CanAccessSystem;
            CanManageUsers = sourceRole.CanManageUsers;
            CanManageRoles = sourceRole.CanManageRoles;
            CanViewReports = sourceRole.CanViewReports;
            CanManageSystemSettings = sourceRole.CanManageSystemSettings;
            CanViewSystemLogs = sourceRole.CanViewSystemLogs;
            CanManageProducts = sourceRole.CanManageProducts;
            CanManageCategories = sourceRole.CanManageCategories;
            CanManageInventory = sourceRole.CanManageInventory;
            CanAdjustInventory = sourceRole.CanAdjustInventory;
            CanViewInventoryReports = sourceRole.CanViewInventoryReports;
            CanProcessSales = sourceRole.CanProcessSales;
            CanCancelSales = sourceRole.CanCancelSales;
            CanApplyDiscounts = sourceRole.CanApplyDiscounts;
            MaxDiscountPercentage = sourceRole.MaxDiscountPercentage;
            CanProcessReturns = sourceRole.CanProcessReturns;
            CanViewSalesReports = sourceRole.CanViewSalesReports;
            CanManageCustomers = sourceRole.CanManageCustomers;
            CanViewCustomerData = sourceRole.CanViewCustomerData;
            CanManageCustomerDebts = sourceRole.CanManageCustomerDebts;
            CanViewCustomerReports = sourceRole.CanViewCustomerReports;
            CanManageCashRegister = sourceRole.CanManageCashRegister;
            CanViewFinancialReports = sourceRole.CanViewFinancialReports;
            CanManagePaymentMethods = sourceRole.CanManagePaymentMethods;
            CanViewProfitLoss = sourceRole.CanViewProfitLoss;
        }

        #endregion

        #region عمليات النسخ والتحويل

        /// <summary>
        /// تحويل الدور إلى نص وصفي
        /// </summary>
        /// <returns>وصف نصي للدور</returns>
        public override string ToString()
        {
            return $"{RoleName} (مستوى {PermissionLevel})";
        }

        #endregion
    }

    #region الأدوار الافتراضية

    /// <summary>
    /// الأدوار الافتراضية في النظام
    /// </summary>
    public static class DefaultRoles
    {
        /// <summary>
        /// دور مدير النظام
        /// </summary>
        public static Role SystemAdmin => new Role
        {
            RoleName = "مدير النظام",
            RoleNameEn = "System Administrator",
            Description = "مدير النظام مع جميع الصلاحيات",
            PermissionLevel = 10,
            IsAdminRole = true,
            IsSystemRole = true,
            CanAccessSystem = true,
            CanManageUsers = true,
            CanManageRoles = true,
            CanViewReports = true,
            CanManageSystemSettings = true,
            CanViewSystemLogs = true,
            CanManageProducts = true,
            CanManageCategories = true,
            CanManageInventory = true,
            CanAdjustInventory = true,
            CanViewInventoryReports = true,
            CanProcessSales = true,
            CanCancelSales = true,
            CanApplyDiscounts = true,
            MaxDiscountPercentage = 100,
            CanProcessReturns = true,
            CanViewSalesReports = true,
            CanManageCustomers = true,
            CanViewCustomerData = true,
            CanManageCustomerDebts = true,
            CanViewCustomerReports = true,
            CanManageCashRegister = true,
            CanViewFinancialReports = true,
            CanManagePaymentMethods = true,
            CanViewProfitLoss = true
        };

        /// <summary>
        /// دور المدير
        /// </summary>
        public static Role Manager => new Role
        {
            RoleName = "مدير",
            RoleNameEn = "Manager",
            Description = "مدير مع صلاحيات إدارية محدودة",
            PermissionLevel = 8,
            IsAdminRole = true,
            IsSystemRole = true,
            CanAccessSystem = true,
            CanManageUsers = false,
            CanManageRoles = false,
            CanViewReports = true,
            CanManageSystemSettings = false,
            CanViewSystemLogs = true,
            CanManageProducts = true,
            CanManageCategories = true,
            CanManageInventory = true,
            CanAdjustInventory = true,
            CanViewInventoryReports = true,
            CanProcessSales = true,
            CanCancelSales = true,
            CanApplyDiscounts = true,
            MaxDiscountPercentage = 50,
            CanProcessReturns = true,
            CanViewSalesReports = true,
            CanManageCustomers = true,
            CanViewCustomerData = true,
            CanManageCustomerDebts = true,
            CanViewCustomerReports = true,
            CanManageCashRegister = true,
            CanViewFinancialReports = true,
            CanManagePaymentMethods = false,
            CanViewProfitLoss = true
        };

        /// <summary>
        /// دور المشرف
        /// </summary>
        public static Role Supervisor => new Role
        {
            RoleName = "مشرف",
            RoleNameEn = "Supervisor",
            Description = "مشرف مع صلاحيات محدودة",
            PermissionLevel = 5,
            IsAdminRole = false,
            IsSystemRole = true,
            CanAccessSystem = true,
            CanManageUsers = false,
            CanManageRoles = false,
            CanViewReports = true,
            CanManageSystemSettings = false,
            CanViewSystemLogs = false,
            CanManageProducts = true,
            CanManageCategories = false,
            CanManageInventory = true,
            CanAdjustInventory = false,
            CanViewInventoryReports = true,
            CanProcessSales = true,
            CanCancelSales = true,
            CanApplyDiscounts = true,
            MaxDiscountPercentage = 20,
            CanProcessReturns = true,
            CanViewSalesReports = true,
            CanManageCustomers = true,
            CanViewCustomerData = true,
            CanManageCustomerDebts = false,
            CanViewCustomerReports = false,
            CanManageCashRegister = true,
            CanViewFinancialReports = false,
            CanManagePaymentMethods = false,
            CanViewProfitLoss = false
        };

        /// <summary>
        /// دور الكاشير
        /// </summary>
        public static Role Cashier => new Role
        {
            RoleName = "كاشير",
            RoleNameEn = "Cashier",
            Description = "كاشير مع صلاحيات المبيعات الأساسية",
            PermissionLevel = 2,
            IsAdminRole = false,
            IsSystemRole = true,
            CanAccessSystem = true,
            CanManageUsers = false,
            CanManageRoles = false,
            CanViewReports = false,
            CanManageSystemSettings = false,
            CanViewSystemLogs = false,
            CanManageProducts = false,
            CanManageCategories = false,
            CanManageInventory = false,
            CanAdjustInventory = false,
            CanViewInventoryReports = false,
            CanProcessSales = true,
            CanCancelSales = false,
            CanApplyDiscounts = true,
            MaxDiscountPercentage = 10,
            CanProcessReturns = false,
            CanViewSalesReports = false,
            CanManageCustomers = false,
            CanViewCustomerData = true,
            CanManageCustomerDebts = false,
            CanViewCustomerReports = false,
            CanManageCashRegister = true,
            CanViewFinancialReports = false,
            CanManagePaymentMethods = false,
            CanViewProfitLoss = false
        };

        /// <summary>
        /// الحصول على جميع الأدوار الافتراضية
        /// </summary>
        /// <returns>قائمة الأدوار الافتراضية</returns>
        public static List<Role> GetAllDefaultRoles()
        {
            return new List<Role>
            {
                SystemAdmin,
                Manager,
                Supervisor,
                Cashier
            };
        }
    }

    #endregion
}
