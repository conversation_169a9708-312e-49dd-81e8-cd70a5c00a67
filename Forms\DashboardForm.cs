using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using AredooPOS.Models.Reports;
using AredooPOS.Services;
using AredooPOS.BLL;
using Microsoft.Extensions.Logging;

namespace AredooPOS.Forms
{
    /// <summary>
    /// لوحة التحكم التنفيذية
    /// تعرض ملخص شامل لجميع التقارير والمؤشرات الرئيسية
    /// </summary>
    public partial class DashboardForm : Form
    {
        #region المتغيرات والخصائص الخاصة

        private readonly ReportsService _reportsService;
        private readonly ReportsBLL _reportsBLL;
        private readonly ILogger<DashboardForm> _logger;
        private readonly string _currentUser;

        private Timer _refreshTimer;
        private DashboardData _dashboardData;
        private bool _isLoading = false;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ لوحة التحكم التنفيذية
        /// </summary>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <param name="logger">مسجل الأحداث</param>
        public DashboardForm(string currentUser, ILogger<DashboardForm> logger = null)
        {
            InitializeComponent();
            
            _currentUser = currentUser;
            _logger = logger;
            _reportsService = new ReportsService(null, logger);
            _reportsBLL = new ReportsBLL(null, logger);

            InitializeForm();
            SetupRefreshTimer();
            LoadDashboardData();
        }

        /// <summary>
        /// تهيئة النموذج
        /// </summary>
        private void InitializeForm()
        {
            // تعيين النصوص العربية
            this.Text = "لوحة التحكم التنفيذية";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            // تعيين الألوان والتنسيق
            SetupStyling();

            // تهيئة البطاقات
            InitializeCards();

            // تهيئة الرسوم البيانية
            InitializeCharts();

            // تعيين معالجات الأحداث
            SetupEventHandlers();
        }

        /// <summary>
        /// تعيين التنسيق والألوان
        /// </summary>
        private void SetupStyling()
        {
            this.BackColor = Color.FromArgb(245, 245, 245);

            // تنسيق الشريط العلوي
            pnlHeader.BackColor = Color.FromArgb(41, 128, 185);
            pnlHeader.Height = 80;
            lblTitle.ForeColor = Color.White;
            lblTitle.Font = new Font("Segoe UI", 18, FontStyle.Bold);

            // تنسيق منطقة البطاقات
            pnlCards.BackColor = Color.Transparent;
            pnlCards.Padding = new Padding(20);

            // تنسيق منطقة الرسوم البيانية
            pnlCharts.BackColor = Color.White;
            pnlCharts.Padding = new Padding(20);
        }

        /// <summary>
        /// تهيئة البطاقات
        /// </summary>
        private void InitializeCards()
        {
            // بطاقة المبيعات اليومية
            CreateSummaryCard(pnlSalesCard, "المبيعات اليومية", "0.00 ر.س", Color.FromArgb(52, 152, 219), "📊");

            // بطاقة الأرباح
            CreateSummaryCard(pnlProfitCard, "صافي الربح", "0.00 ر.س", Color.FromArgb(46, 204, 113), "💰");

            // بطاقة المصاريف
            CreateSummaryCard(pnlExpenseCard, "المصاريف", "0.00 ر.س", Color.FromArgb(231, 76, 60), "💸");

            // بطاقة الديون
            CreateSummaryCard(pnlDebtCard, "الديون المستحقة", "0.00 ر.س", Color.FromArgb(230, 126, 34), "📋");

            // بطاقة الأقساط
            CreateSummaryCard(pnlInstallmentCard, "الأقساط المستحقة", "0.00 ر.س", Color.FromArgb(155, 89, 182), "📅");

            // بطاقة النقدية
            CreateSummaryCard(pnlCashCard, "رصيد الصندوق", "0.00 ر.س", Color.FromArgb(52, 73, 94), "🏦");
        }

        /// <summary>
        /// إنشاء بطاقة ملخص
        /// </summary>
        /// <param name="panel">اللوحة</param>
        /// <param name="title">العنوان</param>
        /// <param name="value">القيمة</param>
        /// <param name="color">اللون</param>
        /// <param name="icon">الأيقونة</param>
        private void CreateSummaryCard(Panel panel, string title, string value, Color color, string icon)
        {
            panel.BackColor = color;
            panel.Size = new Size(200, 120);
            panel.Margin = new Padding(10);

            // إضافة الأيقونة
            var lblIcon = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", 24),
                ForeColor = Color.White,
                Location = new Point(10, 10),
                Size = new Size(50, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };
            panel.Controls.Add(lblIcon);

            // إضافة العنوان
            var lblTitle = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(70, 15),
                Size = new Size(120, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };
            panel.Controls.Add(lblTitle);

            // إضافة القيمة
            var lblValue = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(70, 40),
                Size = new Size(120, 30),
                TextAlign = ContentAlignment.MiddleLeft,
                Tag = "value" // للتعرف عليها لاحقاً
            };
            panel.Controls.Add(lblValue);

            // إضافة نسبة التغيير
            var lblChange = new Label
            {
                Text = "0.0%",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.White,
                Location = new Point(70, 75),
                Size = new Size(120, 20),
                TextAlign = ContentAlignment.MiddleLeft,
                Tag = "change"
            };
            panel.Controls.Add(lblChange);
        }

        /// <summary>
        /// تهيئة الرسوم البيانية
        /// </summary>
        private void InitializeCharts()
        {
            // TODO: تهيئة الرسوم البيانية باستخدام مكتبة مثل Chart.js أو DevExpress
            // يمكن استخدام WebBrowser control لعرض رسوم بيانية HTML/JavaScript
            
            // رسم بياني للمبيعات الشهرية
            InitializeSalesChart();

            // رسم بياني للأرباح
            InitializeProfitChart();

            // رسم بياني دائري للمصاريف
            InitializeExpenseChart();

            // رسم بياني لحركة النقدية
            InitializeCashFlowChart();
        }

        /// <summary>
        /// تهيئة رسم المبيعات البياني
        /// </summary>
        private void InitializeSalesChart()
        {
            // TODO: تنفيذ الرسم البياني للمبيعات
        }

        /// <summary>
        /// تهيئة رسم الأرباح البياني
        /// </summary>
        private void InitializeProfitChart()
        {
            // TODO: تنفيذ الرسم البياني للأرباح
        }

        /// <summary>
        /// تهيئة رسم المصاريف البياني
        /// </summary>
        private void InitializeExpenseChart()
        {
            // TODO: تنفيذ الرسم البياني الدائري للمصاريف
        }

        /// <summary>
        /// تهيئة رسم حركة النقدية البياني
        /// </summary>
        private void InitializeCashFlowChart()
        {
            // TODO: تنفيذ الرسم البياني لحركة النقدية
        }

        /// <summary>
        /// تعيين معالجات الأحداث
        /// </summary>
        private void SetupEventHandlers()
        {
            // أحداث الأزرار
            btnRefresh.Click += BtnRefresh_Click;
            btnSettings.Click += BtnSettings_Click;
            btnExport.Click += BtnExport_Click;

            // أحداث البطاقات (النقر للتفاصيل)
            pnlSalesCard.Click += (s, e) => ShowSalesDetails();
            pnlProfitCard.Click += (s, e) => ShowProfitDetails();
            pnlExpenseCard.Click += (s, e) => ShowExpenseDetails();
            pnlDebtCard.Click += (s, e) => ShowDebtDetails();
            pnlInstallmentCard.Click += (s, e) => ShowInstallmentDetails();
            pnlCashCard.Click += (s, e) => ShowCashFlowDetails();

            // أحداث النموذج
            this.Load += DashboardForm_Load;
            this.FormClosing += DashboardForm_FormClosing;
        }

        /// <summary>
        /// تعيين مؤقت التحديث
        /// </summary>
        private void SetupRefreshTimer()
        {
            _refreshTimer = new Timer
            {
                Interval = 300000 // 5 دقائق
            };
            _refreshTimer.Tick += RefreshTimer_Tick;
            _refreshTimer.Start();
        }

        #endregion

        #region تحميل البيانات

        /// <summary>
        /// تحميل بيانات لوحة التحكم
        /// </summary>
        private async void LoadDashboardData()
        {
            try
            {
                if (_isLoading) return;

                _isLoading = true;
                ShowLoadingIndicator(true);

                // تحميل البيانات بشكل متوازي
                var tasks = new List<Task>
                {
                    LoadSalesDataAsync(),
                    LoadProfitDataAsync(),
                    LoadExpenseDataAsync(),
                    LoadDebtDataAsync(),
                    LoadInstallmentDataAsync(),
                    LoadCashFlowDataAsync()
                };

                await Task.WhenAll(tasks);

                // تحديث واجهة المستخدم
                UpdateDashboardUI();

                _logger?.LogInformation("تم تحميل بيانات لوحة التحكم بنجاح");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل بيانات لوحة التحكم");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
                ShowLoadingIndicator(false);
            }
        }

        /// <summary>
        /// تحميل بيانات المبيعات
        /// </summary>
        private async Task LoadSalesDataAsync()
        {
            try
            {
                var today = DateTime.Now.Date;
                var salesReport = await Task.Run(() => 
                    _reportsBLL.GenerateSalesReport(today, today, SalesReportTypes.Daily, _currentUser));

                if (_dashboardData == null)
                    _dashboardData = new DashboardData();

                _dashboardData.TodaySales = salesReport.TotalSales;
                _dashboardData.TodayInvoices = salesReport.TotalInvoices;
                _dashboardData.AverageInvoiceValue = salesReport.AverageInvoiceValue;

                // حساب نسبة التغيير مقارنة بالأمس
                var yesterday = today.AddDays(-1);
                var yesterdayReport = await Task.Run(() => 
                    _reportsBLL.GenerateSalesReport(yesterday, yesterday, SalesReportTypes.Daily, _currentUser));

                _dashboardData.SalesChangePercentage = CalculateChangePercentage(
                    salesReport.TotalSales, yesterdayReport.TotalSales);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل بيانات المبيعات");
            }
        }

        /// <summary>
        /// تحميل بيانات الأرباح
        /// </summary>
        private async Task LoadProfitDataAsync()
        {
            try
            {
                var today = DateTime.Now.Date;
                var profitReport = await Task.Run(() => 
                    _reportsBLL.GenerateProfitReport(today, today, ProfitReportTypes.Daily, _currentUser));

                if (_dashboardData == null)
                    _dashboardData = new DashboardData();

                _dashboardData.TodayProfit = profitReport.NetProfit;
                _dashboardData.ProfitMargin = profitReport.NetProfitMargin;

                // حساب نسبة التغيير
                var yesterday = today.AddDays(-1);
                var yesterdayReport = await Task.Run(() => 
                    _reportsBLL.GenerateProfitReport(yesterday, yesterday, ProfitReportTypes.Daily, _currentUser));

                _dashboardData.ProfitChangePercentage = CalculateChangePercentage(
                    profitReport.NetProfit, yesterdayReport.NetProfit);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل بيانات الأرباح");
            }
        }

        /// <summary>
        /// تحميل بيانات المصاريف
        /// </summary>
        private async Task LoadExpenseDataAsync()
        {
            try
            {
                var today = DateTime.Now.Date;
                var expenseReport = await Task.Run(() => 
                    _reportsBLL.GenerateExpenseReport(today, today, ExpenseReportTypes.Daily, _currentUser));

                if (_dashboardData == null)
                    _dashboardData = new DashboardData();

                _dashboardData.TodayExpenses = expenseReport.TotalExpenses;

                // حساب نسبة التغيير
                var yesterday = today.AddDays(-1);
                var yesterdayReport = await Task.Run(() => 
                    _reportsBLL.GenerateExpenseReport(yesterday, yesterday, ExpenseReportTypes.Daily, _currentUser));

                _dashboardData.ExpenseChangePercentage = CalculateChangePercentage(
                    expenseReport.TotalExpenses, yesterdayReport.TotalExpenses);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل بيانات المصاريف");
            }
        }

        /// <summary>
        /// تحميل بيانات الديون
        /// </summary>
        private async Task LoadDebtDataAsync()
        {
            try
            {
                var today = DateTime.Now.Date;
                var debtReport = await Task.Run(() => 
                    _reportsBLL.GenerateDebtReport(today, today, DebtReportTypes.Outstanding, _currentUser));

                if (_dashboardData == null)
                    _dashboardData = new DashboardData();

                _dashboardData.OutstandingDebt = debtReport.TotalOutstandingAmount;
                _dashboardData.DebtorCount = debtReport.DebtorCustomersCount;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل بيانات الديون");
            }
        }

        /// <summary>
        /// تحميل بيانات الأقساط
        /// </summary>
        private async Task LoadInstallmentDataAsync()
        {
            try
            {
                var today = DateTime.Now.Date;
                var installmentReport = await Task.Run(() => 
                    _reportsBLL.GenerateInstallmentReport(today, today, InstallmentReportTypes.Due, _currentUser));

                if (_dashboardData == null)
                    _dashboardData = new DashboardData();

                _dashboardData.DueInstallments = installmentReport.TotalOutstandingAmount;
                _dashboardData.OverdueInstallments = installmentReport.OverdueInstallmentAmount;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل بيانات الأقساط");
            }
        }

        /// <summary>
        /// تحميل بيانات حركة النقدية
        /// </summary>
        private async Task LoadCashFlowDataAsync()
        {
            try
            {
                var today = DateTime.Now.Date;
                var cashFlowReport = await Task.Run(() => 
                    _reportsBLL.GenerateCashFlowReport(today, today, null, CashFlowReportTypes.Daily, _currentUser));

                if (_dashboardData == null)
                    _dashboardData = new DashboardData();

                _dashboardData.CashBalance = cashFlowReport.ClosingBalance;
                _dashboardData.NetCashFlow = cashFlowReport.NetCashFlow;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل بيانات حركة النقدية");
            }
        }

        #endregion

        #region تحديث واجهة المستخدم

        /// <summary>
        /// تحديث واجهة المستخدم
        /// </summary>
        private void UpdateDashboardUI()
        {
            if (_dashboardData == null) return;

            try
            {
                // تحديث بطاقة المبيعات
                UpdateCard(pnlSalesCard, _dashboardData.TodaySales, _dashboardData.SalesChangePercentage);

                // تحديث بطاقة الأرباح
                UpdateCard(pnlProfitCard, _dashboardData.TodayProfit, _dashboardData.ProfitChangePercentage);

                // تحديث بطاقة المصاريف
                UpdateCard(pnlExpenseCard, _dashboardData.TodayExpenses, _dashboardData.ExpenseChangePercentage);

                // تحديث بطاقة الديون
                UpdateCard(pnlDebtCard, _dashboardData.OutstandingDebt, 0);

                // تحديث بطاقة الأقساط
                UpdateCard(pnlInstallmentCard, _dashboardData.DueInstallments, 0);

                // تحديث بطاقة النقدية
                UpdateCard(pnlCashCard, _dashboardData.CashBalance, 0);

                // تحديث الرسوم البيانية
                UpdateCharts();

                // تحديث شريط الحالة
                UpdateStatusBar();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحديث واجهة المستخدم");
            }
        }

        /// <summary>
        /// تحديث بطاقة
        /// </summary>
        /// <param name="panel">اللوحة</param>
        /// <param name="value">القيمة</param>
        /// <param name="changePercentage">نسبة التغيير</param>
        private void UpdateCard(Panel panel, decimal value, decimal changePercentage)
        {
            // تحديث القيمة
            var lblValue = panel.Controls.OfType<Label>().FirstOrDefault(l => l.Tag?.ToString() == "value");
            if (lblValue != null)
            {
                lblValue.Text = $"{value:C}";
            }

            // تحديث نسبة التغيير
            var lblChange = panel.Controls.OfType<Label>().FirstOrDefault(l => l.Tag?.ToString() == "change");
            if (lblChange != null)
            {
                lblChange.Text = $"{changePercentage:+0.0;-0.0;0.0}%";
                lblChange.ForeColor = changePercentage >= 0 ? Color.LightGreen : Color.LightCoral;
            }
        }

        /// <summary>
        /// تحديث الرسوم البيانية
        /// </summary>
        private void UpdateCharts()
        {
            // TODO: تحديث الرسوم البيانية بالبيانات الجديدة
        }

        /// <summary>
        /// تحديث شريط الحالة
        /// </summary>
        private void UpdateStatusBar()
        {
            lblLastUpdate.Text = $"آخر تحديث: {DateTime.Now:HH:mm:ss}";
            lblUser.Text = $"المستخدم: {_currentUser}";
        }

        /// <summary>
        /// إظهار مؤشر التحميل
        /// </summary>
        /// <param name="show">إظهار أم إخفاء</param>
        private void ShowLoadingIndicator(bool show)
        {
            progressBar.Visible = show;
            if (show)
            {
                progressBar.Style = ProgressBarStyle.Marquee;
                lblStatus.Text = "جاري تحميل البيانات...";
            }
            else
            {
                progressBar.Style = ProgressBarStyle.Continuous;
                lblStatus.Text = "جاهز";
            }
        }

        #endregion

        #region الأحداث

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void DashboardForm_Load(object sender, EventArgs e)
        {
            try
            {
                lblTitle.Text = "لوحة التحكم التنفيذية";
                UpdateStatusBar();
                _logger?.LogInformation("تم تحميل لوحة التحكم التنفيذية");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تحميل لوحة التحكم");
            }
        }

        /// <summary>
        /// حدث إغلاق النموذج
        /// </summary>
        private void DashboardForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                _refreshTimer?.Stop();
                _refreshTimer?.Dispose();
                _logger?.LogInformation("تم إغلاق لوحة التحكم التنفيذية");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إغلاق لوحة التحكم");
            }
        }

        /// <summary>
        /// حدث مؤقت التحديث
        /// </summary>
        private void RefreshTimer_Tick(object sender, EventArgs e)
        {
            if (!_isLoading)
            {
                LoadDashboardData();
            }
        }

        /// <summary>
        /// حدث زر التحديث
        /// </summary>
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadDashboardData();
        }

        /// <summary>
        /// حدث زر الإعدادات
        /// </summary>
        private void BtnSettings_Click(object sender, EventArgs e)
        {
            try
            {
                var settingsForm = new DashboardSettingsForm();
                settingsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فتح إعدادات لوحة التحكم");
            }
        }

        /// <summary>
        /// حدث زر التصدير
        /// </summary>
        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                ExportDashboard();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تصدير لوحة التحكم");
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region عرض التفاصيل

        /// <summary>
        /// عرض تفاصيل المبيعات
        /// </summary>
        private void ShowSalesDetails()
        {
            try
            {
                var salesForm = new ReportsMainForm(_currentUser, _logger);
                salesForm.Show();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في عرض تفاصيل المبيعات");
            }
        }

        /// <summary>
        /// عرض تفاصيل الأرباح
        /// </summary>
        private void ShowProfitDetails()
        {
            try
            {
                var profitForm = new ReportsMainForm(_currentUser, _logger);
                profitForm.Show();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في عرض تفاصيل الأرباح");
            }
        }

        /// <summary>
        /// عرض تفاصيل المصاريف
        /// </summary>
        private void ShowExpenseDetails()
        {
            try
            {
                var expenseForm = new ReportsMainForm(_currentUser, _logger);
                expenseForm.Show();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في عرض تفاصيل المصاريف");
            }
        }

        /// <summary>
        /// عرض تفاصيل الديون
        /// </summary>
        private void ShowDebtDetails()
        {
            try
            {
                var debtForm = new ReportsMainForm(_currentUser, _logger);
                debtForm.Show();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في عرض تفاصيل الديون");
            }
        }

        /// <summary>
        /// عرض تفاصيل الأقساط
        /// </summary>
        private void ShowInstallmentDetails()
        {
            try
            {
                var installmentForm = new ReportsMainForm(_currentUser, _logger);
                installmentForm.Show();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في عرض تفاصيل الأقساط");
            }
        }

        /// <summary>
        /// عرض تفاصيل حركة النقدية
        /// </summary>
        private void ShowCashFlowDetails()
        {
            try
            {
                var cashFlowForm = new ReportsMainForm(_currentUser, _logger);
                cashFlowForm.Show();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في عرض تفاصيل حركة النقدية");
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// حساب نسبة التغيير
        /// </summary>
        /// <param name="current">القيمة الحالية</param>
        /// <param name="previous">القيمة السابقة</param>
        /// <returns>نسبة التغيير</returns>
        private decimal CalculateChangePercentage(decimal current, decimal previous)
        {
            if (previous == 0) return 0;
            return ((current - previous) / previous) * 100;
        }

        /// <summary>
        /// تصدير لوحة التحكم
        /// </summary>
        private void ExportDashboard()
        {
            var saveDialog = new SaveFileDialog
            {
                Title = "تصدير لوحة التحكم",
                Filter = "PDF Files|*.pdf|Excel Files|*.xlsx|HTML Files|*.html",
                FileName = $"Dashboard_{DateTime.Now:yyyyMMdd_HHmmss}"
            };

            if (saveDialog.ShowDialog() == DialogResult.OK)
            {
                // TODO: تنفيذ التصدير الفعلي
                MessageBox.Show("تم تصدير لوحة التحكم بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        #endregion
    }

    #region نماذج البيانات

    /// <summary>
    /// بيانات لوحة التحكم
    /// </summary>
    public class DashboardData
    {
        // بيانات المبيعات
        public decimal TodaySales { get; set; }
        public int TodayInvoices { get; set; }
        public decimal AverageInvoiceValue { get; set; }
        public decimal SalesChangePercentage { get; set; }

        // بيانات الأرباح
        public decimal TodayProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public decimal ProfitChangePercentage { get; set; }

        // بيانات المصاريف
        public decimal TodayExpenses { get; set; }
        public decimal ExpenseChangePercentage { get; set; }

        // بيانات الديون
        public decimal OutstandingDebt { get; set; }
        public int DebtorCount { get; set; }

        // بيانات الأقساط
        public decimal DueInstallments { get; set; }
        public decimal OverdueInstallments { get; set; }

        // بيانات النقدية
        public decimal CashBalance { get; set; }
        public decimal NetCashFlow { get; set; }
    }

    /// <summary>
    /// نموذج إعدادات لوحة التحكم
    /// </summary>
    public partial class DashboardSettingsForm : Form
    {
        public DashboardSettingsForm()
        {
            InitializeComponent();
            this.Text = "إعدادات لوحة التحكم";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
        }
    }

    #endregion
}
