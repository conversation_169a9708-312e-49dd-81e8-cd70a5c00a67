using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// لوحة المعلومات المتجاوبة مع مخططات متكيفة
    /// </summary>
    public class ResponsiveDashboard : UserControl
    {
        #region المتغيرات

        private Panel _statsContainer;
        private Panel _chartsContainer;
        private Panel _alertsContainer;
        private Panel _actionsContainer;

        // بطاقات الإحصائيات المتجاوبة
        private List<ResponsiveDashboardCard> _statsCards;

        // الرسوم البيانية المتجاوبة
        private ResponsiveChart _salesChart;
        private ResponsiveChart _productsChart;

        // قائمة التنبيهات
        private ListView _alertsList;

        // الأزرار السريعة
        private List<Button> _quickActionButtons;

        #endregion

        #region الأحداث

        public event EventHandler NewInvoiceClicked;
        public event EventHandler AddCustomerClicked;
        public event EventHandler AddProductClicked;
        public event EventHandler OpenDrawerClicked;
        public event EventHandler CloseSessionClicked;

        #endregion

        #region البناء والتهيئة

        public ResponsiveDashboard()
        {
            InitializeComponent();
            SetupResponsiveDesign();
            LoadSampleData();
        }

        private void InitializeComponent()
        {
            // إعدادات لوحة المعلومات المتجاوبة
            BackColor = ResponsiveDesignSystem.Colors.Background;
            Dock = DockStyle.Fill;
            AutoScroll = true;
            Padding = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetLargeSpacing());

            CreateResponsiveContainers();
            CreateStatsCards();
            CreateCharts();
            CreateAlertsList();
            CreateQuickActions();
        }

        private void CreateResponsiveContainers()
        {
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetLargeSpacing();
            var cardHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight() * 4;

            // حاوي الإحصائيات
            _statsContainer = ResponsiveLayoutSystem.CreateResponsiveRow();
            _statsContainer.Height = cardHeight;
            _statsContainer.Tag = "ResponsiveRow";

            // حاوي الرسوم البيانية
            _chartsContainer = ResponsiveLayoutSystem.CreateResponsiveRow();
            _chartsContainer.Height = cardHeight * 2;
            _chartsContainer.Location = new Point(0, cardHeight + spacing);
            _chartsContainer.Tag = "ResponsiveRow";

            // حاوي التنبيهات
            _alertsContainer = ResponsiveLayoutSystem.CreateResponsiveColumn(6);
            _alertsContainer.Height = cardHeight;
            _alertsContainer.Location = new Point(0, (cardHeight * 3) + (spacing * 2));
            _alertsContainer.Tag = 6; // عدد الأعمدة

            // حاوي الإجراءات السريعة
            _actionsContainer = ResponsiveLayoutSystem.CreateResponsiveColumn(6);
            _actionsContainer.Height = cardHeight;
            _actionsContainer.Location = new Point(0, (cardHeight * 3) + (spacing * 2));
            _actionsContainer.Tag = 6; // عدد الأعمدة

            Controls.AddRange(new Control[] { _statsContainer, _chartsContainer, _alertsContainer, _actionsContainer });
        }

        private void CreateStatsCards()
        {
            _statsCards = new List<ResponsiveDashboardCard>
            {
                new ResponsiveDashboardCard
                {
                    Title = "مبيعات اليوم",
                    Value = "15,750 ر.س",
                    Icon = ResponsiveDesignSystem.Icons.Sales,
                    Color = ResponsiveDesignSystem.Colors.Success,
                    Tag = 3 // عدد الأعمدة
                },
                new ResponsiveDashboardCard
                {
                    Title = "إجمالي العملاء",
                    Value = "1,234",
                    Icon = ResponsiveDesignSystem.Icons.Customers,
                    Color = ResponsiveDesignSystem.Colors.Primary,
                    Tag = 3 // عدد الأعمدة
                },
                new ResponsiveDashboardCard
                {
                    Title = "منتجات منخفضة",
                    Value = "23",
                    Icon = ResponsiveDesignSystem.Icons.Products,
                    Color = ResponsiveDesignSystem.Colors.Warning,
                    Tag = 3 // عدد الأعمدة
                },
                new ResponsiveDashboardCard
                {
                    Title = "أقساط معلقة",
                    Value = "8,500 ر.س",
                    Icon = ResponsiveDesignSystem.Icons.Installments,
                    Color = ResponsiveDesignSystem.Colors.Error,
                    Tag = 3 // عدد الأعمدة
                }
            };

            foreach (var card in _statsCards)
            {
                _statsContainer.Controls.Add(card);
            }
        }

        private void CreateCharts()
        {
            // رسم بياني للمبيعات
            _salesChart = new ResponsiveChart
            {
                Title = "مبيعات الأسبوع",
                ChartType = ResponsiveChartType.Line,
                BackColor = ResponsiveDesignSystem.Colors.Surface,
                Tag = 6 // عدد الأعمدة
            };

            // رسم بياني للمنتجات
            _productsChart = new ResponsiveChart
            {
                Title = "أفضل المنتجات",
                ChartType = ResponsiveChartType.Bar,
                BackColor = ResponsiveDesignSystem.Colors.Surface,
                Tag = 6 // عدد الأعمدة
            };

            _chartsContainer.Controls.AddRange(new Control[] { _salesChart, _productsChart });
        }

        private void CreateAlertsList()
        {
            var alertsCard = new Panel
            {
                BackColor = ResponsiveDesignSystem.Colors.Surface,
                Dock = DockStyle.Fill
            };

            // عنوان التنبيهات
            var alertsTitle = new Label
            {
                Text = "🔔 التنبيهات والإشعارات",
                Font = ResponsiveDesignSystem.Fonts.GetHeading5(),
                ForeColor = ResponsiveDesignSystem.Colors.TextPrimary,
                Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight(),
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes,
                Padding = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing())
            };

            // قائمة التنبيهات المتجاوبة
            _alertsList = new ListView
            {
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = false,
                HeaderStyle = ColumnHeaderStyle.None,
                BorderStyle = BorderStyle.None,
                BackColor = ResponsiveDesignSystem.Colors.Surface,
                Font = ResponsiveDesignSystem.Fonts.GetBody(),
                RightToLeft = RightToLeft.Yes,
                Margin = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing())
            };

            _alertsList.Columns.Add("", -2); // عرض تلقائي

            alertsCard.Controls.AddRange(new Control[] { alertsTitle, _alertsList });
            alertsCard.Paint += (s, e) => ResponsiveDesignSystem.DrawResponsiveCard(e.Graphics, alertsCard.ClientRectangle, ResponsiveDesignSystem.Colors.Surface);

            _alertsContainer.Controls.Add(alertsCard);
        }

        private void CreateQuickActions()
        {
            var actionsCard = new Panel
            {
                BackColor = ResponsiveDesignSystem.Colors.Surface,
                Dock = DockStyle.Fill
            };

            // عنوان الإجراءات السريعة
            var actionsTitle = new Label
            {
                Text = "⚡ الإجراءات السريعة",
                Font = ResponsiveDesignSystem.Fonts.GetHeading5(),
                ForeColor = ResponsiveDesignSystem.Colors.TextPrimary,
                Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight(),
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes,
                Padding = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing())
            };

            // لوحة الأزرار
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent,
                Padding = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing())
            };

            // إنشاء الأزرار السريعة
            _quickActionButtons = new List<Button>
            {
                CreateQuickActionButton("💰 إنشاء فاتورة", ResponsiveDesignSystem.Colors.Primary, (s, e) => NewInvoiceClicked?.Invoke(this, e)),
                CreateQuickActionButton("👤 إضافة عميل", ResponsiveDesignSystem.Colors.Success, (s, e) => AddCustomerClicked?.Invoke(this, e)),
                CreateQuickActionButton("📦 إضافة منتج", ResponsiveDesignSystem.Colors.Info, (s, e) => AddProductClicked?.Invoke(this, e)),
                CreateQuickActionButton("💵 فتح الدرج", ResponsiveDesignSystem.Colors.Warning, (s, e) => OpenDrawerClicked?.Invoke(this, e)),
                CreateQuickActionButton("🔒 إغلاق الجلسة", ResponsiveDesignSystem.Colors.Error, (s, e) => CloseSessionClicked?.Invoke(this, e))
            };

            ArrangeQuickActionButtons(buttonsPanel);

            actionsCard.Controls.AddRange(new Control[] { actionsTitle, buttonsPanel });
            actionsCard.Paint += (s, e) => ResponsiveDesignSystem.DrawResponsiveCard(e.Graphics, actionsCard.ClientRectangle, ResponsiveDesignSystem.Colors.Surface);

            _actionsContainer.Controls.Add(actionsCard);
        }

        private Button CreateQuickActionButton(string text, Color color, EventHandler clickHandler)
        {
            var button = new Button
            {
                Text = text,
                Font = ResponsiveDesignSystem.Fonts.GetButton(),
                FlatStyle = FlatStyle.Flat,
                BackColor = color,
                ForeColor = ResponsiveDesignSystem.Colors.TextOnPrimary,
                TextAlign = ContentAlignment.MiddleCenter,
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes,
                Height = ResponsiveLayoutSystem.ResponsiveDimensions.GetLargeButtonHeight()
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ResponsiveDesignSystem.LightenColor(color, 0.1f);
            button.FlatAppearance.MouseDownBackColor = ResponsiveDesignSystem.DarkenColor(color, 0.1f);

            if (clickHandler != null)
                button.Click += clickHandler;

            return button;
        }

        private void ArrangeQuickActionButtons(Panel container)
        {
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();
            var buttonHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetLargeButtonHeight();
            var availableHeight = container.Height - container.Padding.Vertical;
            var buttonCount = _quickActionButtons.Count;
            var totalSpacing = (buttonCount - 1) * spacing;
            var availableButtonHeight = availableHeight - totalSpacing;

            // إذا كانت الأزرار لا تتسع عمودياً، رتبها في شبكة
            if (buttonCount * buttonHeight > availableButtonHeight)
            {
                ArrangeButtonsInGrid(container);
            }
            else
            {
                ArrangeButtonsVertically(container);
            }
        }

        private void ArrangeButtonsVertically(Panel container)
        {
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();
            var y = container.Padding.Top;

            foreach (var button in _quickActionButtons)
            {
                button.Size = new Size(container.Width - container.Padding.Horizontal, button.Height);
                button.Location = new Point(container.Padding.Left, y);
                container.Controls.Add(button);
                y += button.Height + spacing;
            }
        }

        private void ArrangeButtonsInGrid(Panel container)
        {
            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();
            var columns = 2;
            var rows = (int)Math.Ceiling((double)_quickActionButtons.Count / columns);
            
            var buttonWidth = (container.Width - container.Padding.Horizontal - spacing) / columns;
            var buttonHeight = (container.Height - container.Padding.Vertical - ((rows - 1) * spacing)) / rows;

            for (int i = 0; i < _quickActionButtons.Count; i++)
            {
                var button = _quickActionButtons[i];
                var row = i / columns;
                var col = i % columns;

                var x = container.Padding.Left + (col * (buttonWidth + spacing));
                var y = container.Padding.Top + (row * (buttonHeight + spacing));

                button.Size = new Size(buttonWidth, buttonHeight);
                button.Location = new Point(x, y);
                container.Controls.Add(button);
            }
        }

        private void SetupResponsiveDesign()
        {
            RightToLeft = RightToLeft.Yes;
            Resize += OnResize;
        }

        private void LoadSampleData()
        {
            // إضافة تنبيهات تجريبية
            var alerts = new[]
            {
                "⚠️ منتج 'شامبو الأطفال' وصل للحد الأدنى (5 قطع متبقية)",
                "💳 قسط العميل 'أحمد محمد' مستحق اليوم (500 ر.س)",
                "📦 وصول شحنة جديدة من المنتجات (50 صنف)",
                "💰 تم تحقيق هدف المبيعات اليومي (15,000 ر.س)",
                "🔔 تذكير: إغلاق الصندوق في 6:00 مساءً",
                "👥 عميل جديد 'فاطمة علي' تم تسجيله بنجاح",
                "📈 زيادة في المبيعات بنسبة 15% مقارنة بالأمس"
            };

            foreach (var alert in alerts)
            {
                var item = new ListViewItem(alert)
                {
                    Font = ResponsiveDesignSystem.Fonts.GetBodySmall(),
                    ForeColor = ResponsiveDesignSystem.Colors.TextPrimary
                };
                _alertsList.Items.Add(item);
            }

            // تحديث بيانات الرسوم البيانية
            _salesChart.UpdateData(new[] { 1200.0, 1800.0, 1500.0, 2200.0, 1900.0, 2500.0, 2100.0 });
            _productsChart.UpdateData(new[] { 45.0, 38.0, 32.0, 28.0, 25.0 });
        }

        #endregion

        #region الأحداث

        private void OnResize(object sender, EventArgs e)
        {
            ResponsiveLayoutSystem.HandleResize(this.FindForm());
            UpdateResponsiveLayout();
        }

        private void UpdateResponsiveLayout()
        {
            // تحديث تخطيط الحاويات
            ResponsiveLayoutSystem.UpdateRowLayout(_statsContainer);
            ResponsiveLayoutSystem.UpdateRowLayout(_chartsContainer);

            // تحديث ترتيب الأزرار السريعة
            if (_actionsContainer.Controls.Count > 0 && _actionsContainer.Controls[0].Controls.Count > 1)
            {
                var buttonsPanel = _actionsContainer.Controls[0].Controls[1] as Panel;
                if (buttonsPanel != null)
                {
                    buttonsPanel.Controls.Clear();
                    ArrangeQuickActionButtons(buttonsPanel);
                }
            }
        }

        #endregion
    }

    #region المكونات المساعدة المتجاوبة

    /// <summary>
    /// بطاقة لوحة المعلومات المتجاوبة
    /// </summary>
    public class ResponsiveDashboardCard : UserControl
    {
        public string Title { get; set; }
        public string Value { get; set; }
        public string Icon { get; set; }
        public Color Color { get; set; }

        public ResponsiveDashboardCard()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
            BackColor = ResponsiveDesignSystem.Colors.Surface;
            Dock = DockStyle.Fill;
            Margin = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetSmallSpacing());
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // رسم البطاقة المتجاوبة
            ResponsiveDesignSystem.DrawResponsiveCard(g, ClientRectangle, BackColor);

            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();
            var iconSize = ResponsiveDesignSystem.Icons.GetLargeIconSize();

            // رسم الأيقونة
            var iconRect = new Rectangle(Width - iconSize - spacing, spacing, iconSize, iconSize);
            using (var iconBrush = new SolidBrush(Color))
            {
                var iconFont = ResponsiveDesignSystem.Fonts.GetHighDpiFont(iconSize * 0.8f);
                var iconFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };
                g.DrawString(Icon, iconFont, iconBrush, iconRect, iconFormat);
            }

            // رسم القيمة
            var valueRect = new Rectangle(spacing, spacing, Width - iconSize - (spacing * 3), iconSize);
            using (var valueBrush = new SolidBrush(ResponsiveDesignSystem.Colors.TextPrimary))
            {
                var valueFormat = new StringFormat
                {
                    Alignment = StringAlignment.Far,
                    LineAlignment = StringAlignment.Center,
                    FormatFlags = StringFormatFlags.DirectionRightToLeft
                };
                g.DrawString(Value, ResponsiveDesignSystem.Fonts.GetNumbersLarge(), valueBrush, valueRect, valueFormat);
            }

            // رسم العنوان
            var titleRect = new Rectangle(spacing, iconSize + spacing, Width - iconSize - (spacing * 3), Height - iconSize - (spacing * 2));
            using (var titleBrush = new SolidBrush(ResponsiveDesignSystem.Colors.TextSecondary))
            {
                var titleFormat = new StringFormat
                {
                    Alignment = StringAlignment.Far,
                    LineAlignment = StringAlignment.Near,
                    FormatFlags = StringFormatFlags.DirectionRightToLeft
                };
                g.DrawString(Title, ResponsiveDesignSystem.Fonts.GetBody(), titleBrush, titleRect, titleFormat);
            }
        }
    }

    /// <summary>
    /// رسم بياني متجاوب
    /// </summary>
    public class ResponsiveChart : UserControl
    {
        public string Title { get; set; }
        public ResponsiveChartType ChartType { get; set; }
        private double[] _data;

        public ResponsiveChart()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
            BackColor = ResponsiveDesignSystem.Colors.Surface;
            Dock = DockStyle.Fill;
            Margin = new Padding(ResponsiveLayoutSystem.ResponsiveSpacing.GetSmallSpacing());
        }

        public void UpdateData(double[] data)
        {
            _data = data;
            Invalidate();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // رسم البطاقة المتجاوبة
            ResponsiveDesignSystem.DrawResponsiveCard(g, ClientRectangle, BackColor);

            var spacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetMediumSpacing();
            var titleHeight = ResponsiveLayoutSystem.ResponsiveDimensions.GetButtonHeight();

            // رسم العنوان
            var titleRect = new Rectangle(spacing, spacing, Width - (spacing * 2), titleHeight);
            using (var titleBrush = new SolidBrush(ResponsiveDesignSystem.Colors.TextPrimary))
            {
                var titleFormat = new StringFormat
                {
                    Alignment = StringAlignment.Far,
                    LineAlignment = StringAlignment.Center,
                    FormatFlags = StringFormatFlags.DirectionRightToLeft
                };
                g.DrawString(Title, ResponsiveDesignSystem.Fonts.GetHeading5(), titleBrush, titleRect, titleFormat);
            }

            // رسم البيانات
            if (_data != null && _data.Length > 0)
            {
                var chartRect = new Rectangle(
                    spacing * 2,
                    titleHeight + (spacing * 2),
                    Width - (spacing * 4),
                    Height - titleHeight - (spacing * 4)
                );

                if (ChartType == ResponsiveChartType.Line)
                    DrawResponsiveLineChart(g, chartRect);
                else if (ChartType == ResponsiveChartType.Bar)
                    DrawResponsiveBarChart(g, chartRect);
            }
        }

        private void DrawResponsiveLineChart(Graphics g, Rectangle bounds)
        {
            if (_data.Length < 2) return;

            var maxValue = _data.Max();
            var points = new PointF[_data.Length];
            var lineWidth = Math.Max(2, ResponsiveLayoutSystem.ResponsiveSpacing.GetSmallSpacing() / 4);

            for (int i = 0; i < _data.Length; i++)
            {
                var x = bounds.X + (i * bounds.Width / (_data.Length - 1));
                var y = bounds.Bottom - (_data[i] / maxValue * bounds.Height);
                points[i] = new PointF(x, (float)y);
            }

            // رسم الخط
            using (var pen = new Pen(ResponsiveDesignSystem.Colors.Primary, lineWidth))
            {
                g.DrawLines(pen, points);
            }

            // رسم النقاط
            var pointSize = ResponsiveLayoutSystem.ResponsiveSpacing.GetSmallSpacing();
            foreach (var point in points)
            {
                using (var brush = new SolidBrush(ResponsiveDesignSystem.Colors.Primary))
                {
                    g.FillEllipse(brush, point.X - pointSize / 2, point.Y - pointSize / 2, pointSize, pointSize);
                }
            }
        }

        private void DrawResponsiveBarChart(Graphics g, Rectangle bounds)
        {
            var maxValue = _data.Max();
            var barSpacing = ResponsiveLayoutSystem.ResponsiveSpacing.GetSmallSpacing();
            var barWidth = (bounds.Width - ((_data.Length - 1) * barSpacing)) / _data.Length;

            for (int i = 0; i < _data.Length; i++)
            {
                var barHeight = (int)(_data[i] / maxValue * bounds.Height);
                var barRect = new Rectangle(
                    bounds.X + (i * (barWidth + barSpacing)),
                    bounds.Bottom - barHeight,
                    barWidth,
                    barHeight
                );

                var color = i % 2 == 0 ? ResponsiveDesignSystem.Colors.Primary : ResponsiveDesignSystem.Colors.Secondary;
                using (var brush = new SolidBrush(color))
                {
                    g.FillRectangle(brush, barRect);
                }
            }
        }
    }

    /// <summary>
    /// أنواع الرسوم البيانية المتجاوبة
    /// </summary>
    public enum ResponsiveChartType
    {
        Line,
        Bar,
        Pie,
        Area
    }

    #endregion
}
