using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace AredooPOS.Models.Settings
{
    /// <summary>
    /// نموذج الإعدادات العامة للنظام
    /// يحتوي على جميع الإعدادات الأساسية والتكوينات العامة
    /// </summary>
    public class SystemSettings
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم الإعداد
        /// </summary>
        public int SettingID { get; set; }

        /// <summary>
        /// اسم الإعداد
        /// </summary>
        [Required]
        [StringLength(100)]
        public string SettingName { get; set; }

        /// <summary>
        /// قيمة الإعداد
        /// </summary>
        [Required]
        public string SettingValue { get; set; }

        /// <summary>
        /// وصف الإعداد
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// فئة الإعداد
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Category { get; set; }

        /// <summary>
        /// نوع البيانات
        /// </summary>
        [Required]
        [StringLength(20)]
        public string DataType { get; set; }

        /// <summary>
        /// هل الإعداد مطلوب
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// هل يمكن تعديل الإعداد
        /// </summary>
        public bool IsEditable { get; set; } = true;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// من قام بآخر تحديث
        /// </summary>
        [StringLength(50)]
        public string UpdatedBy { get; set; }

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// الحصول على القيمة كرقم صحيح
        /// </summary>
        public int IntValue
        {
            get
            {
                if (int.TryParse(SettingValue, out int result))
                    return result;
                return 0;
            }
        }

        /// <summary>
        /// الحصول على القيمة كرقم عشري
        /// </summary>
        public decimal DecimalValue
        {
            get
            {
                if (decimal.TryParse(SettingValue, out decimal result))
                    return result;
                return 0;
            }
        }

        /// <summary>
        /// الحصول على القيمة كقيمة منطقية
        /// </summary>
        public bool BoolValue
        {
            get
            {
                if (bool.TryParse(SettingValue, out bool result))
                    return result;
                return false;
            }
        }

        /// <summary>
        /// الحصول على القيمة كتاريخ
        /// </summary>
        public DateTime DateTimeValue
        {
            get
            {
                if (DateTime.TryParse(SettingValue, out DateTime result))
                    return result;
                return DateTime.MinValue;
            }
        }

        #endregion

        #region الدوال المساعدة

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            if (string.IsNullOrWhiteSpace(SettingName))
                return false;

            if (string.IsNullOrWhiteSpace(Category))
                return false;

            if (IsRequired && string.IsNullOrWhiteSpace(SettingValue))
                return false;

            return ValidateDataType();
        }

        /// <summary>
        /// التحقق من صحة نوع البيانات
        /// </summary>
        /// <returns>true إذا كان نوع البيانات صحيح</returns>
        private bool ValidateDataType()
        {
            if (string.IsNullOrWhiteSpace(SettingValue))
                return !IsRequired;

            return DataType?.ToLower() switch
            {
                "int" => int.TryParse(SettingValue, out _),
                "decimal" => decimal.TryParse(SettingValue, out _),
                "bool" => bool.TryParse(SettingValue, out _),
                "datetime" => DateTime.TryParse(SettingValue, out _),
                "string" => true,
                _ => true
            };
        }

        /// <summary>
        /// تحديث قيمة الإعداد
        /// </summary>
        /// <param name="newValue">القيمة الجديدة</param>
        /// <param name="updatedBy">من قام بالتحديث</param>
        public void UpdateValue(string newValue, string updatedBy)
        {
            SettingValue = newValue;
            UpdatedBy = updatedBy;
            LastUpdated = DateTime.Now;
        }

        #endregion
    }

    /// <summary>
    /// نموذج إعدادات الضريبة
    /// </summary>
    public class TaxSettings
    {
        #region الخصائص الأساسية

        /// <summary>
        /// رقم إعداد الضريبة
        /// </summary>
        public int TaxSettingID { get; set; }

        /// <summary>
        /// هل الضريبة مفعلة
        /// </summary>
        public bool IsTaxEnabled { get; set; } = true;

        /// <summary>
        /// نسبة الضريبة الافتراضية
        /// </summary>
        [Range(0, 100)]
        public decimal DefaultTaxRate { get; set; } = 0;

        /// <summary>
        /// اسم الضريبة
        /// </summary>
        [Required]
        [StringLength(100)]
        public string TaxName { get; set; } = "ضريبة القيمة المضافة";

        /// <summary>
        /// رقم التسجيل الضريبي
        /// </summary>
        [StringLength(50)]
        public string TaxRegistrationNumber { get; set; }

        /// <summary>
        /// طريقة حساب الضريبة
        /// </summary>
        [Required]
        [StringLength(20)]
        public string TaxCalculationMethod { get; set; } = "Inclusive"; // Inclusive, Exclusive

        /// <summary>
        /// هل تطبق الضريبة على جميع المنتجات
        /// </summary>
        public bool ApplyToAllProducts { get; set; } = true;

        /// <summary>
        /// الحد الأدنى لتطبيق الضريبة
        /// </summary>
        public decimal MinimumTaxableAmount { get; set; } = 0;

        /// <summary>
        /// عدد الأرقام العشرية للضريبة
        /// </summary>
        [Range(0, 4)]
        public int TaxDecimalPlaces { get; set; } = 2;

        /// <summary>
        /// طريقة التقريب
        /// </summary>
        [StringLength(20)]
        public string RoundingMethod { get; set; } = "Normal"; // Normal, Up, Down

        #endregion

        #region قوائم الضرائب المتعددة

        /// <summary>
        /// قائمة الضرائب المتعددة
        /// </summary>
        public List<TaxRate> TaxRates { get; set; } = new List<TaxRate>();

        #endregion

        #region الخصائص المحسوبة

        /// <summary>
        /// هل الضريبة شاملة
        /// </summary>
        public bool IsInclusive => TaxCalculationMethod == "Inclusive";

        /// <summary>
        /// معامل الضريبة للحساب
        /// </summary>
        public decimal TaxFactor => IsInclusive ? (DefaultTaxRate / (100 + DefaultTaxRate)) : (DefaultTaxRate / 100);

        #endregion

        #region الدوال المساعدة

        /// <summary>
        /// حساب الضريبة
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="taxRate">نسبة الضريبة (اختيارية)</param>
        /// <returns>مبلغ الضريبة</returns>
        public decimal CalculateTax(decimal amount, decimal? taxRate = null)
        {
            if (!IsTaxEnabled || amount < MinimumTaxableAmount)
                return 0;

            var rate = taxRate ?? DefaultTaxRate;
            if (rate <= 0)
                return 0;

            decimal taxAmount;
            if (IsInclusive)
            {
                taxAmount = amount * (rate / (100 + rate));
            }
            else
            {
                taxAmount = amount * (rate / 100);
            }

            return RoundTaxAmount(taxAmount);
        }

        /// <summary>
        /// تقريب مبلغ الضريبة
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ مقرب</returns>
        public decimal RoundTaxAmount(decimal amount)
        {
            return RoundingMethod switch
            {
                "Up" => Math.Ceiling(amount * (decimal)Math.Pow(10, TaxDecimalPlaces)) / (decimal)Math.Pow(10, TaxDecimalPlaces),
                "Down" => Math.Floor(amount * (decimal)Math.Pow(10, TaxDecimalPlaces)) / (decimal)Math.Pow(10, TaxDecimalPlaces),
                _ => Math.Round(amount, TaxDecimalPlaces, MidpointRounding.AwayFromZero)
            };
        }

        /// <summary>
        /// الحصول على نسبة الضريبة للمنتج
        /// </summary>
        /// <param name="productID">رقم المنتج</param>
        /// <param name="categoryID">رقم الفئة</param>
        /// <returns>نسبة الضريبة</returns>
        public decimal GetTaxRateForProduct(int productID, int categoryID)
        {
            if (!IsTaxEnabled)
                return 0;

            // البحث عن ضريبة خاصة بالمنتج
            var productTax = TaxRates.FirstOrDefault(t => t.ProductID == productID);
            if (productTax != null)
                return productTax.Rate;

            // البحث عن ضريبة خاصة بالفئة
            var categoryTax = TaxRates.FirstOrDefault(t => t.CategoryID == categoryID);
            if (categoryTax != null)
                return categoryTax.Rate;

            // إرجاع الضريبة الافتراضية
            return ApplyToAllProducts ? DefaultTaxRate : 0;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            if (DefaultTaxRate < 0 || DefaultTaxRate > 100)
                return false;

            if (string.IsNullOrWhiteSpace(TaxName))
                return false;

            if (!new[] { "Inclusive", "Exclusive" }.Contains(TaxCalculationMethod))
                return false;

            if (TaxDecimalPlaces < 0 || TaxDecimalPlaces > 4)
                return false;

            return true;
        }

        #endregion
    }

    /// <summary>
    /// نموذج معدل الضريبة
    /// </summary>
    public class TaxRate
    {
        public int TaxRateID { get; set; }
        public string Name { get; set; }
        public decimal Rate { get; set; }
        public int? ProductID { get; set; }
        public int? CategoryID { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// فئات الإعدادات
    /// </summary>
    public static class SettingCategories
    {
        public const string General = "General";
        public const string Tax = "Tax";
        public const string Currency = "Currency";
        public const string Store = "Store";
        public const string Print = "Print";
        public const string Backup = "Backup";
        public const string Security = "Security";
        public const string Notification = "Notification";
        public const string Integration = "Integration";
        public const string Appearance = "Appearance";
    }

    /// <summary>
    /// أنواع البيانات للإعدادات
    /// </summary>
    public static class SettingDataTypes
    {
        public const string String = "string";
        public const string Integer = "int";
        public const string Decimal = "decimal";
        public const string Boolean = "bool";
        public const string DateTime = "datetime";
        public const string List = "list";
        public const string Json = "json";
    }

    /// <summary>
    /// الإعدادات الافتراضية
    /// </summary>
    public static class DefaultSettings
    {
        public static readonly Dictionary<string, object> Values = new Dictionary<string, object>
        {
            // إعدادات عامة
            { "System.ApplicationName", "أريدو POS" },
            { "System.Version", "1.0.0" },
            { "System.Language", "ar-IQ" },
            { "System.DateFormat", "dd/MM/yyyy" },
            { "System.TimeFormat", "HH:mm:ss" },
            
            // إعدادات الضريبة
            { "Tax.IsEnabled", true },
            { "Tax.DefaultRate", 0.0m },
            { "Tax.CalculationMethod", "Inclusive" },
            { "Tax.DecimalPlaces", 2 },
            
            // إعدادات العملة
            { "Currency.DefaultCurrency", "IQD" },
            { "Currency.DecimalPlaces", 0 },
            { "Currency.Symbol", "د.ع" },
            
            // إعدادات المتجر
            { "Store.Name", "متجر أريدو" },
            { "Store.Phone", "" },
            { "Store.Address", "" },
            { "Store.Email", "" },
            
            // إعدادات الطباعة
            { "Print.DefaultSize", "Thermal" },
            { "Print.AutoPrint", false },
            { "Print.Copies", 1 }
        };
    }
}
