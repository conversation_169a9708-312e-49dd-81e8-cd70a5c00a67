using System;
using System.Data;
using System.Data.SQLite;

namespace AridooPOS.DAL
{
    /// <summary>
    /// كلاس إدارة الاتصال بقاعدة البيانات
    /// </summary>
    public class DatabaseConnection
    {
        /// <summary>
        /// الحصول على سلسلة الاتصال
        /// </summary>
        public static string ConnectionString => SQLiteConnection.ConnectionString;

        /// <summary>
        /// إنشاء اتصال جديد بقاعدة البيانات
        /// </summary>
        /// <returns>كائن الاتصال</returns>
        public static System.Data.SQLite.SQLiteConnection GetConnection()
        {
            return SQLiteConnection.GetConnection();
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        /// <returns>true إذا كان الاتصال ناجح</returns>
        public static bool TestConnection()
        {
            return SQLiteConnection.TestConnection();
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع DataTable
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>DataTable</returns>
        public static DataTable ExecuteQuery(string query, SQLiteParameter[] parameters = null)
        {
            return SQLiteConnection.ExecuteQuery(query, parameters);
        }

        /// <summary>
        /// تنفيذ أمر وإرجاع عدد الصفوف المتأثرة
        /// </summary>
        /// <param name="query">الأمر</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public static int ExecuteNonQuery(string query, SQLiteParameter[] parameters = null)
        {
            return SQLiteConnection.ExecuteNonQuery(query, parameters);
        }

        /// <summary>
        /// تنفيذ أمر وإرجاع قيمة واحدة
        /// </summary>
        /// <param name="query">الأمر</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>القيمة المرجعة</returns>
        public static object ExecuteScalar(string query, SQLiteParameter[] parameters = null)
        {
            return SQLiteConnection.ExecuteScalar(query, parameters);
        }

        /// <summary>
        /// الحصول على رقم فاتورة جديد
        /// </summary>
        /// <returns>رقم الفاتورة الجديد</returns>
        public static string GetNextInvoiceNumber()
        {
            try
            {
                string query = @"
                    SELECT 'INV' || printf('%06d', COALESCE(MAX(CAST(SUBSTR(InvoiceNumber, 4) AS INTEGER)), 0) + 1)
                    FROM Invoices
                    WHERE InvoiceNumber LIKE 'INV%'";

                object result = ExecuteScalar(query);
                return result?.ToString() ?? "INV000001";
            }
            catch (Exception)
            {
                return "INV000001";
            }
        }
    }
}
