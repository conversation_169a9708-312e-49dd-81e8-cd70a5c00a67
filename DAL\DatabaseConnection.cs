using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

namespace AridooPOS.DAL
{
    /// <summary>
    /// كلاس إدارة الاتصال بقاعدة البيانات
    /// </summary>
    public class DatabaseConnection
    {
        private static string _connectionString;
        
        static DatabaseConnection()
        {
            // سلسلة الاتصال الافتراضية
            _connectionString = @"Data Source=.\SQLEXPRESS;Initial Catalog=AridooPOS;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False";
        }

        /// <summary>
        /// الحصول على سلسلة الاتصال
        /// </summary>
        public static string ConnectionString
        {
            get { return _connectionString; }
            set { _connectionString = value; }
        }

        /// <summary>
        /// إنشاء اتصال جديد بقاعدة البيانات
        /// </summary>
        /// <returns>كائن الاتصال</returns>
        public static SqlConnection GetConnection()
        {
            return new SqlConnection(_connectionString);
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        /// <returns>true إذا كان الاتصال ناجح</returns>
        public static bool TestConnection()
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    return true;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع DataTable
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>DataTable</returns>
        public static DataTable ExecuteQuery(string query, SqlParameter[] parameters = null)
        {
            DataTable dataTable = new DataTable();
            
            try
            {
                using (var connection = GetConnection())
                {
                    using (var command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        connection.Open();
                        using (var adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(dataTable);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الاستعلام: {ex.Message}", ex);
            }
            
            return dataTable;
        }

        /// <summary>
        /// تنفيذ أمر وإرجاع عدد الصفوف المتأثرة
        /// </summary>
        /// <param name="query">الأمر</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public static int ExecuteNonQuery(string query, SqlParameter[] parameters = null)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    using (var command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        connection.Open();
                        return command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الأمر: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ أمر وإرجاع قيمة واحدة
        /// </summary>
        /// <param name="query">الأمر</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>القيمة المرجعة</returns>
        public static object ExecuteScalar(string query, SqlParameter[] parameters = null)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    using (var command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        connection.Open();
                        return command.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الأمر: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ إجراء مخزن
        /// </summary>
        /// <param name="procedureName">اسم الإجراء المخزن</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>DataTable</returns>
        public static DataTable ExecuteStoredProcedure(string procedureName, SqlParameter[] parameters = null)
        {
            DataTable dataTable = new DataTable();
            
            try
            {
                using (var connection = GetConnection())
                {
                    using (var command = new SqlCommand(procedureName, connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        connection.Open();
                        using (var adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(dataTable);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الإجراء المخزن: {ex.Message}", ex);
            }
            
            return dataTable;
        }
    }
}
