using System;
using System.Data;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;

namespace AridooPOS.DAL
{
    /// <summary>
    /// كلاس إدارة الاتصال بقاعدة بيانات SQLite
    /// </summary>
    public class SQLiteConnection
    {
        private static string _connectionString;
        private static string _databasePath;
        
        static SQLiteConnection()
        {
            // مسار قاعدة البيانات
            _databasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AridooPOS.db");
            _connectionString = $"Data Source={_databasePath};Version=3;";
            
            // إنشاء قاعدة البيانات إذا لم تكن موجودة
            InitializeDatabase();
        }

        /// <summary>
        /// الحصول على سلسلة الاتصال
        /// </summary>
        public static string ConnectionString => _connectionString;

        /// <summary>
        /// إنشاء اتصال جديد بقاعدة البيانات
        /// </summary>
        public static System.Data.SQLite.SQLiteConnection GetConnection()
        {
            return new System.Data.SQLite.SQLiteConnection(_connectionString);
        }

        /// <summary>
        /// تهيئة قاعدة البيانات وإنشاء الجداول
        /// </summary>
        private static void InitializeDatabase()
        {
            try
            {
                if (!File.Exists(_databasePath))
                {
                    System.Data.SQLite.SQLiteConnection.CreateFile(_databasePath);
                }

                using (var connection = GetConnection())
                {
                    connection.Open();
                    CreateTables(connection);
                    InsertInitialData(connection);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء الجداول
        /// </summary>
        private static void CreateTables(System.Data.SQLite.SQLiteConnection connection)
        {
            string[] createTableQueries = {
                @"CREATE TABLE IF NOT EXISTS Categories (
                    CategoryID INTEGER PRIMARY KEY AUTOINCREMENT,
                    CategoryName TEXT NOT NULL,
                    Description TEXT,
                    IsActive INTEGER DEFAULT 1
                )",
                
                @"CREATE TABLE IF NOT EXISTS Customers (
                    CustomerID INTEGER PRIMARY KEY AUTOINCREMENT,
                    CustomerCode TEXT UNIQUE NOT NULL,
                    CustomerName TEXT NOT NULL,
                    Phone TEXT,
                    Address TEXT,
                    Email TEXT,
                    CreditLimit REAL DEFAULT 0,
                    CurrentBalance REAL DEFAULT 0,
                    IsActive INTEGER DEFAULT 1,
                    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
                    ModifiedDate TEXT DEFAULT CURRENT_TIMESTAMP
                )",
                
                @"CREATE TABLE IF NOT EXISTS Products (
                    ProductID INTEGER PRIMARY KEY AUTOINCREMENT,
                    ProductCode TEXT UNIQUE NOT NULL,
                    ProductName TEXT NOT NULL,
                    ProductNameEn TEXT,
                    CategoryID INTEGER,
                    UnitPrice REAL NOT NULL,
                    CostPrice REAL DEFAULT 0,
                    StockQuantity INTEGER DEFAULT 0,
                    MinStockLevel INTEGER DEFAULT 0,
                    Barcode TEXT,
                    TaxRate REAL DEFAULT 0,
                    IsActive INTEGER DEFAULT 1,
                    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
                    ModifiedDate TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (CategoryID) REFERENCES Categories(CategoryID)
                )",
                
                @"CREATE TABLE IF NOT EXISTS Invoices (
                    InvoiceID INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceNumber TEXT UNIQUE NOT NULL,
                    InvoiceDate TEXT NOT NULL,
                    CustomerID INTEGER,
                    CustomerName TEXT,
                    SubTotal REAL NOT NULL DEFAULT 0,
                    DiscountAmount REAL DEFAULT 0,
                    DiscountPercent REAL DEFAULT 0,
                    TaxAmount REAL DEFAULT 0,
                    TotalAmount REAL NOT NULL DEFAULT 0,
                    PaidAmount REAL DEFAULT 0,
                    RemainingAmount REAL DEFAULT 0,
                    PaymentType TEXT NOT NULL,
                    InvoiceStatus TEXT DEFAULT 'مكتملة',
                    Notes TEXT,
                    CashierName TEXT,
                    IsReturned INTEGER DEFAULT 0,
                    ReturnedDate TEXT,
                    CreatedBy TEXT,
                    CreatedDate TEXT DEFAULT CURRENT_TIMESTAMP,
                    ModifiedDate TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID)
                )",
                
                @"CREATE TABLE IF NOT EXISTS InvoiceDetails (
                    InvoiceDetailID INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceID INTEGER NOT NULL,
                    ProductID INTEGER NOT NULL,
                    ProductCode TEXT,
                    ProductName TEXT,
                    Quantity REAL NOT NULL,
                    UnitPrice REAL NOT NULL,
                    DiscountAmount REAL DEFAULT 0,
                    DiscountPercent REAL DEFAULT 0,
                    TaxRate REAL DEFAULT 0,
                    TaxAmount REAL DEFAULT 0,
                    LineTotal REAL NOT NULL,
                    FOREIGN KEY (InvoiceID) REFERENCES Invoices(InvoiceID) ON DELETE CASCADE,
                    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
                )"
            };

            foreach (string query in createTableQueries)
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        /// <summary>
        /// إدراج البيانات الأولية
        /// </summary>
        private static void InsertInitialData(System.Data.SQLite.SQLiteConnection connection)
        {
            // التحقق من وجود بيانات
            using (var command = new SQLiteCommand("SELECT COUNT(*) FROM Categories", connection))
            {
                long count = (long)command.ExecuteScalar();
                if (count > 0) return; // البيانات موجودة مسبقاً
            }

            string[] insertQueries = {
                @"INSERT INTO Categories (CategoryName, Description) VALUES 
                ('مواد غذائية', 'المواد الغذائية والمشروبات'),
                ('مستلزمات منزلية', 'الأدوات والمستلزمات المنزلية'),
                ('ملابس', 'الملابس والأزياء'),
                ('إلكترونيات', 'الأجهزة الإلكترونية'),
                ('أدوات مكتبية', 'القرطاسية والأدوات المكتبية'),
                ('متنوعة', 'منتجات متنوعة أخرى')",
                
                @"INSERT INTO Customers (CustomerCode, CustomerName, Phone, Address) VALUES 
                ('CASH001', 'عميل نقدي', '', 'مبيعات نقدية')",
                
                @"INSERT INTO Products (ProductCode, ProductName, CategoryID, UnitPrice, CostPrice, StockQuantity, Barcode) VALUES 
                ('PRD001', 'منتج تجريبي 1', 1, 10.00, 7.50, 100, '1234567890123'),
                ('PRD002', 'منتج تجريبي 2', 2, 25.50, 18.00, 50, '1234567890124'),
                ('PRD003', 'منتج تجريبي 3', 3, 75.00, 55.00, 25, '1234567890125')"
            };

            foreach (string query in insertQueries)
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع DataTable
        /// </summary>
        public static DataTable ExecuteQuery(string query, SQLiteParameter[] parameters = null)
        {
            DataTable dataTable = new DataTable();
            
            try
            {
                using (var connection = GetConnection())
                {
                    using (var command = new SQLiteCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        connection.Open();
                        using (var adapter = new SQLiteDataAdapter(command))
                        {
                            adapter.Fill(dataTable);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الاستعلام: {ex.Message}", ex);
            }
            
            return dataTable;
        }

        /// <summary>
        /// تنفيذ أمر وإرجاع عدد الصفوف المتأثرة
        /// </summary>
        public static int ExecuteNonQuery(string query, SQLiteParameter[] parameters = null)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    using (var command = new SQLiteCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        connection.Open();
                        return command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الأمر: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ أمر وإرجاع قيمة واحدة
        /// </summary>
        public static object ExecuteScalar(string query, SQLiteParameter[] parameters = null)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    using (var command = new SQLiteCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        connection.Open();
                        return command.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الأمر: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        public static bool TestConnection()
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    return true;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
