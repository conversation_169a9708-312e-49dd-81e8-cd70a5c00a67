using System;
using System.Drawing;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// واجهة إدارة العملاء الحديثة
    /// </summary>
    public class ModernCustomersView : UserControl
    {
        #region المتغيرات

        private Panel _headerPanel;
        private Panel _searchPanel;
        private Panel _statsPanel;
        private DataGridView _customersGrid;
        private Panel _actionsPanel;

        private TextBox _searchTextBox;
        private ComboBox _searchTypeCombo;
        private Button _searchButton;
        private Button _clearButton;

        #endregion

        #region البناء والتهيئة

        public ModernCustomersView()
        {
            InitializeComponent();
            CreateContent();
            LoadSampleData();
        }

        private void InitializeComponent()
        {
            BackColor = ModernDesign.Colors.Background;
            Dock = DockStyle.Fill;
            RightToLeft = RightToLeft.Yes;
            AutoScroll = true;
        }

        private void CreateContent()
        {
            var yPosition = ModernDesign.Spacing.Large;

            // رأس الصفحة
            CreateHeader(yPosition);
            yPosition += 80;

            // شريط البحث
            CreateSearchSection(yPosition);
            yPosition += 80;

            // إحصائيات العملاء
            CreateStatsSection(yPosition);
            yPosition += 120;

            // جدول العملاء
            CreateCustomersGrid(yPosition);
            yPosition += 400;

            // أزرار الإجراءات
            CreateActionsSection(yPosition);
        }

        private void CreateHeader(int yPosition)
        {
            _headerPanel = ModernDesign.CreateModernPanel(ModernDesign.Colors.Surface);
            _headerPanel.Size = new Size(Width - 40, 60);
            _headerPanel.Location = new Point(20, yPosition);
            _headerPanel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;

            var titleLabel = ModernDesign.CreateModernLabel(
                "👥 إدارة العملاء",
                ModernDesign.Fonts.Title,
                ModernDesign.Colors.Primary
            );
            titleLabel.Size = new Size(_headerPanel.Width - 40, 30);
            titleLabel.Location = new Point(20, 15);

            var descLabel = ModernDesign.CreateModernLabel(
                "إدارة بيانات العملاء ومتابعة حساباتهم",
                ModernDesign.Fonts.Body,
                ModernDesign.Colors.TextSecondary
            );
            descLabel.Size = new Size(_headerPanel.Width - 40, 20);
            descLabel.Location = new Point(20, 40);

            _headerPanel.Controls.AddRange(new Control[] { titleLabel, descLabel });
            Controls.Add(_headerPanel);
        }

        private void CreateSearchSection(int yPosition)
        {
            _searchPanel = ModernDesign.CreateModernPanel(ModernDesign.Colors.Surface);
            _searchPanel.Size = new Size(Width - 40, 60);
            _searchPanel.Location = new Point(20, yPosition);
            _searchPanel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;

            var searchLabel = ModernDesign.CreateModernLabel(
                "🔍 البحث:",
                ModernDesign.Fonts.BodyBold,
                ModernDesign.Colors.TextPrimary
            );
            searchLabel.Size = new Size(80, 30);
            searchLabel.Location = new Point(_searchPanel.Width - 100, 15);

            _searchTypeCombo = new ComboBox
            {
                Font = ModernDesign.Fonts.Body,
                Size = new Size(120, ModernDesign.Sizes.InputHeight),
                Location = new Point(_searchPanel.Width - 240, 20),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            _searchTypeCombo.Items.AddRange(new[] { "الاسم", "الهاتف", "البريد الإلكتروني" });
            _searchTypeCombo.SelectedIndex = 0;

            _searchTextBox = ModernDesign.CreateModernTextBox("ابحث عن عميل...");
            _searchTextBox.Size = new Size(250, ModernDesign.Sizes.InputHeight);
            _searchTextBox.Location = new Point(_searchPanel.Width - 510, 20);

            _searchButton = ModernDesign.CreateModernButton(
                "🔍 بحث",
                ModernDesign.Colors.Primary,
                Color.White,
                (s, e) => SearchCustomers()
            );
            _searchButton.Size = new Size(80, ModernDesign.Sizes.ButtonHeight);
            _searchButton.Location = new Point(_searchPanel.Width - 610, 15);

            _clearButton = ModernDesign.CreateModernButton(
                "🗑️ مسح",
                ModernDesign.Colors.TextSecondary,
                Color.White,
                (s, e) => ClearSearch()
            );
            _clearButton.Size = new Size(70, ModernDesign.Sizes.ButtonHeight);
            _clearButton.Location = new Point(20, 15);

            _searchPanel.Controls.AddRange(new Control[] { 
                searchLabel, _searchTypeCombo, _searchTextBox, _searchButton, _clearButton 
            });
            Controls.Add(_searchPanel);
        }

        private void CreateStatsSection(int yPosition)
        {
            _statsPanel = new Panel
            {
                Size = new Size(Width - 40, 100),
                Location = new Point(20, yPosition),
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            var cardWidth = (_statsPanel.Width - 60) / 3;
            var cardSpacing = 30;

            var stats = new[]
            {
                new { Title = "إجمالي العملاء", Value = "1,247", Icon = "👥", Color = ModernDesign.Colors.Primary },
                new { Title = "عملاء جدد هذا الشهر", Value = "89", Icon = "🆕", Color = ModernDesign.Colors.Success },
                new { Title = "عملاء نشطون", Value = "892", Icon = "⭐", Color = ModernDesign.Colors.Info }
            };

            for (int i = 0; i < stats.Length; i++)
            {
                var stat = stats[i];
                var card = CreateStatCard(stat.Title, stat.Value, stat.Icon, stat.Color);
                card.Size = new Size(cardWidth, 80);
                card.Location = new Point(i * (cardWidth + cardSpacing), 10);
                _statsPanel.Controls.Add(card);
            }

            Controls.Add(_statsPanel);
        }

        private Panel CreateStatCard(string title, string value, string icon, Color accentColor)
        {
            var card = ModernDesign.CreateModernPanel(ModernDesign.Colors.Surface);
            card.Padding = new Padding(ModernDesign.Spacing.Medium);

            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", 20),
                ForeColor = accentColor,
                Size = new Size(30, 30),
                Location = new Point(card.Width - 40, 10),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var valueLabel = ModernDesign.CreateModernLabel(
                value,
                ModernDesign.Fonts.Heading,
                ModernDesign.Colors.TextPrimary
            );
            valueLabel.Size = new Size(card.Width - 50, 25);
            valueLabel.Location = new Point(10, 10);

            var titleLabel = ModernDesign.CreateModernLabel(
                title,
                ModernDesign.Fonts.Caption,
                ModernDesign.Colors.TextSecondary
            );
            titleLabel.Size = new Size(card.Width - 50, 20);
            titleLabel.Location = new Point(10, 40);

            card.Controls.AddRange(new Control[] { iconLabel, valueLabel, titleLabel });
            return card;
        }

        private void CreateCustomersGrid(int yPosition)
        {
            var gridPanel = ModernDesign.CreateModernPanel(ModernDesign.Colors.Surface);
            gridPanel.Size = new Size(Width - 40, 380);
            gridPanel.Location = new Point(20, yPosition);
            gridPanel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom;

            var gridTitle = ModernDesign.CreateModernLabel(
                "📋 قائمة العملاء:",
                ModernDesign.Fonts.BodyBold,
                ModernDesign.Colors.TextPrimary
            );
            gridTitle.Size = new Size(200, 30);
            gridTitle.Location = new Point(gridPanel.Width - 220, 15);

            _customersGrid = new DataGridView
            {
                Size = new Size(gridPanel.Width - 40, 320),
                Location = new Point(20, 50),
                BackgroundColor = ModernDesign.Colors.Surface,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                EnableHeadersVisualStyles = false,
                RowHeadersVisible = false,
                AllowUserToAddRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                Font = ModernDesign.Fonts.Body,
                RightToLeft = RightToLeft.Yes,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            SetupCustomersGrid();

            gridPanel.Controls.AddRange(new Control[] { gridTitle, _customersGrid });
            Controls.Add(gridPanel);
        }

        private void SetupCustomersGrid()
        {
            _customersGrid.Columns.Clear();

            var columns = new[]
            {
                new { Name = "Name", Header = "اسم العميل", Width = 25 },
                new { Name = "Phone", Header = "رقم الهاتف", Width = 20 },
                new { Name = "Email", Header = "البريد الإلكتروني", Width = 25 },
                new { Name = "TotalPurchases", Header = "إجمالي المشتريات", Width = 15 },
                new { Name = "LastVisit", Header = "آخر زيارة", Width = 15 }
            };

            foreach (var col in columns)
            {
                var column = new DataGridViewTextBoxColumn
                {
                    Name = col.Name,
                    HeaderText = col.Header,
                    FillWeight = col.Width,
                    ReadOnly = true
                };
                _customersGrid.Columns.Add(column);
            }

            // تنسيق الرأس
            _customersGrid.ColumnHeadersDefaultCellStyle.BackColor = ModernDesign.Colors.Primary;
            _customersGrid.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            _customersGrid.ColumnHeadersDefaultCellStyle.Font = ModernDesign.Fonts.BodyBold;
            _customersGrid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _customersGrid.ColumnHeadersHeight = 40;

            // تنسيق الصفوف
            _customersGrid.DefaultCellStyle.BackColor = ModernDesign.Colors.Surface;
            _customersGrid.DefaultCellStyle.ForeColor = ModernDesign.Colors.TextPrimary;
            _customersGrid.DefaultCellStyle.SelectionBackColor = ModernDesign.Colors.Selected;
            _customersGrid.DefaultCellStyle.SelectionForeColor = ModernDesign.Colors.TextPrimary;
            _customersGrid.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _customersGrid.RowTemplate.Height = 35;

            _customersGrid.AlternatingRowsDefaultCellStyle.BackColor = ModernDesign.Colors.Hover;
        }

        private void CreateActionsSection(int yPosition)
        {
            _actionsPanel = new Panel
            {
                Size = new Size(Width - 40, 60),
                Location = new Point(20, yPosition),
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
            };

            var addButton = ModernDesign.CreateModernButton(
                "➕ إضافة عميل جديد",
                ModernDesign.Colors.Success,
                Color.White,
                (s, e) => AddCustomer()
            );
            addButton.Size = new Size(150, ModernDesign.Sizes.ButtonHeight);
            addButton.Location = new Point(_actionsPanel.Width - 170, 10);

            var editButton = ModernDesign.CreateModernButton(
                "✏️ تعديل",
                ModernDesign.Colors.Primary,
                Color.White,
                (s, e) => EditCustomer()
            );
            editButton.Size = new Size(100, ModernDesign.Sizes.ButtonHeight);
            editButton.Location = new Point(_actionsPanel.Width - 290, 10);

            var deleteButton = ModernDesign.CreateModernButton(
                "🗑️ حذف",
                ModernDesign.Colors.Error,
                Color.White,
                (s, e) => DeleteCustomer()
            );
            deleteButton.Size = new Size(80, ModernDesign.Sizes.ButtonHeight);
            deleteButton.Location = new Point(_actionsPanel.Width - 390, 10);

            var exportButton = ModernDesign.CreateModernButton(
                "📤 تصدير",
                ModernDesign.Colors.Info,
                Color.White,
                (s, e) => ExportCustomers()
            );
            exportButton.Size = new Size(100, ModernDesign.Sizes.ButtonHeight);
            exportButton.Location = new Point(20, 10);

            _actionsPanel.Controls.AddRange(new Control[] { addButton, editButton, deleteButton, exportButton });
            Controls.Add(_actionsPanel);
        }

        private void LoadSampleData()
        {
            var sampleCustomers = new[]
            {
                new { Name = "أحمد محمد علي", Phone = "0501234567", Email = "<EMAIL>", Total = "2,450 ر.س", LastVisit = "2024/12/10" },
                new { Name = "فاطمة أحمد", Phone = "0507654321", Email = "<EMAIL>", Total = "1,890 ر.س", LastVisit = "2024/12/09" },
                new { Name = "محمد عبدالله", Phone = "0551122334", Email = "<EMAIL>", Total = "3,200 ر.س", LastVisit = "2024/12/08" },
                new { Name = "عائشة سالم", Phone = "0544556677", Email = "<EMAIL>", Total = "1,650 ر.س", LastVisit = "2024/12/07" },
                new { Name = "عبدالرحمن أحمد", Phone = "0533445566", Email = "<EMAIL>", Total = "2,100 ر.س", LastVisit = "2024/12/06" }
            };

            foreach (var customer in sampleCustomers)
            {
                _customersGrid.Rows.Add(customer.Name, customer.Phone, customer.Email, customer.Total, customer.LastVisit);
            }
        }

        #endregion

        #region معالجة الأحداث

        private void SearchCustomers()
        {
            var searchText = _searchTextBox.Text.Trim();
            var searchType = _searchTypeCombo.SelectedItem?.ToString();

            if (string.IsNullOrEmpty(searchText) || searchText == "ابحث عن عميل...")
            {
                MessageBox.Show("يرجى إدخال نص البحث", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show($"البحث عن '{searchText}' في '{searchType}'", "البحث", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ClearSearch()
        {
            _searchTextBox.Text = "ابحث عن عميل...";
            _searchTextBox.ForeColor = ModernDesign.Colors.TextHint;
            _searchTypeCombo.SelectedIndex = 0;
            LoadSampleData(); // إعادة تحميل جميع البيانات
        }

        private void AddCustomer()
        {
            MessageBox.Show("سيتم فتح نموذج إضافة عميل جديد", "إضافة عميل", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void EditCustomer()
        {
            if (_customersGrid.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار عميل للتعديل", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var customerName = _customersGrid.SelectedRows[0].Cells["Name"].Value?.ToString();
            MessageBox.Show($"سيتم فتح نموذج تعديل بيانات العميل: {customerName}", "تعديل عميل", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteCustomer()
        {
            if (_customersGrid.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار عميل للحذف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var customerName = _customersGrid.SelectedRows[0].Cells["Name"].Value?.ToString();
            var result = MessageBox.Show($"هل تريد حذف العميل: {customerName}؟", "تأكيد الحذف", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                _customersGrid.Rows.RemoveAt(_customersGrid.SelectedRows[0].Index);
                MessageBox.Show("تم حذف العميل بنجاح", "حذف عميل", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void ExportCustomers()
        {
            MessageBox.Show("سيتم تصدير قائمة العملاء إلى ملف Excel", "تصدير البيانات", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            
            // إعادة ترتيب العناصر عند تغيير الحجم
            if (_headerPanel != null)
            {
                _headerPanel.Width = Width - 40;
            }

            if (_searchPanel != null)
            {
                _searchPanel.Width = Width - 40;
            }

            if (_statsPanel != null)
            {
                _statsPanel.Width = Width - 40;
                var cardWidth = (_statsPanel.Width - 60) / 3;
                var cardSpacing = 30;
                
                for (int i = 0; i < _statsPanel.Controls.Count; i++)
                {
                    var card = _statsPanel.Controls[i];
                    card.Size = new Size(cardWidth, 80);
                    card.Location = new Point(i * (cardWidth + cardSpacing), 10);
                }
            }

            if (_actionsPanel != null)
            {
                _actionsPanel.Width = Width - 40;
            }
        }

        #endregion
    }
}
