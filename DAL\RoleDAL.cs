using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using AredooPOS.Models;
using Microsoft.Extensions.Logging;

namespace AredooPOS.DAL
{
    /// <summary>
    /// طبقة الوصول للبيانات للأدوار والصلاحيات
    /// تحتوي على جميع العمليات المتعلقة بقاعدة البيانات للأدوار
    /// </summary>
    public class RoleDAL
    {
        #region المتغيرات والخصائص الخاصة

        private readonly string _connectionString;
        private readonly ILogger<RoleDAL> _logger;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ طبقة الوصول للبيانات للأدوار
        /// </summary>
        /// <param name="connectionString">نص الاتصال بقاعدة البيانات</param>
        /// <param name="logger">مسجل الأحداث</param>
        public RoleDAL(string connectionString = null, ILogger<RoleDAL> logger = null)
        {
            _connectionString = connectionString ?? DatabaseConfig.GetConnectionString();
            _logger = logger;
        }

        #endregion

        #region العمليات الأساسية

        /// <summary>
        /// إضافة دور جديد
        /// </summary>
        /// <param name="role">بيانات الدور</param>
        /// <returns>رقم الدور الجديد</returns>
        public int AddRole(Role role)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_AddRole", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                // إضافة المعاملات
                command.Parameters.AddWithValue("@RoleName", role.RoleName);
                command.Parameters.AddWithValue("@RoleNameEn", role.RoleNameEn ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Description", role.Description ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PermissionLevel", role.PermissionLevel);
                command.Parameters.AddWithValue("@IsAdminRole", role.IsAdminRole);
                command.Parameters.AddWithValue("@IsSystemRole", role.IsSystemRole);
                command.Parameters.AddWithValue("@IsActive", role.IsActive);
                
                // صلاحيات النظام العامة
                command.Parameters.AddWithValue("@CanAccessSystem", role.CanAccessSystem);
                command.Parameters.AddWithValue("@CanManageUsers", role.CanManageUsers);
                command.Parameters.AddWithValue("@CanManageRoles", role.CanManageRoles);
                command.Parameters.AddWithValue("@CanViewReports", role.CanViewReports);
                command.Parameters.AddWithValue("@CanManageSystemSettings", role.CanManageSystemSettings);
                command.Parameters.AddWithValue("@CanViewSystemLogs", role.CanViewSystemLogs);
                
                // صلاحيات المنتجات والمخزون
                command.Parameters.AddWithValue("@CanManageProducts", role.CanManageProducts);
                command.Parameters.AddWithValue("@CanManageCategories", role.CanManageCategories);
                command.Parameters.AddWithValue("@CanManageInventory", role.CanManageInventory);
                command.Parameters.AddWithValue("@CanAdjustInventory", role.CanAdjustInventory);
                command.Parameters.AddWithValue("@CanViewInventoryReports", role.CanViewInventoryReports);
                
                // صلاحيات المبيعات
                command.Parameters.AddWithValue("@CanProcessSales", role.CanProcessSales);
                command.Parameters.AddWithValue("@CanCancelSales", role.CanCancelSales);
                command.Parameters.AddWithValue("@CanApplyDiscounts", role.CanApplyDiscounts);
                command.Parameters.AddWithValue("@MaxDiscountPercentage", role.MaxDiscountPercentage);
                command.Parameters.AddWithValue("@CanProcessReturns", role.CanProcessReturns);
                command.Parameters.AddWithValue("@CanViewSalesReports", role.CanViewSalesReports);
                
                // صلاحيات العملاء
                command.Parameters.AddWithValue("@CanManageCustomers", role.CanManageCustomers);
                command.Parameters.AddWithValue("@CanViewCustomerData", role.CanViewCustomerData);
                command.Parameters.AddWithValue("@CanManageCustomerDebts", role.CanManageCustomerDebts);
                command.Parameters.AddWithValue("@CanViewCustomerReports", role.CanViewCustomerReports);
                
                // صلاحيات المالية
                command.Parameters.AddWithValue("@CanManageCashRegister", role.CanManageCashRegister);
                command.Parameters.AddWithValue("@CanViewFinancialReports", role.CanViewFinancialReports);
                command.Parameters.AddWithValue("@CanManagePaymentMethods", role.CanManagePaymentMethods);
                command.Parameters.AddWithValue("@CanViewProfitLoss", role.CanViewProfitLoss);
                
                command.Parameters.AddWithValue("@CreatedBy", role.CreatedBy);

                // معامل الإخراج
                var outputParam = new SqlParameter("@NewRoleID", SqlDbType.Int)
                {
                    Direction = ParameterDirection.Output
                };
                command.Parameters.Add(outputParam);

                connection.Open();
                command.ExecuteNonQuery();

                var roleId = Convert.ToInt32(outputParam.Value);
                _logger?.LogInformation($"تم إضافة دور جديد برقم {roleId}");
                return roleId;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في إضافة الدور");
                throw;
            }
        }

        /// <summary>
        /// تحديث بيانات الدور
        /// </summary>
        /// <param name="role">بيانات الدور المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateRole(Role role)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_UpdateRole", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                // إضافة المعاملات
                command.Parameters.AddWithValue("@RoleID", role.RoleID);
                command.Parameters.AddWithValue("@RoleName", role.RoleName);
                command.Parameters.AddWithValue("@RoleNameEn", role.RoleNameEn ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@Description", role.Description ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@PermissionLevel", role.PermissionLevel);
                command.Parameters.AddWithValue("@IsAdminRole", role.IsAdminRole);
                command.Parameters.AddWithValue("@IsActive", role.IsActive);
                
                // صلاحيات النظام العامة
                command.Parameters.AddWithValue("@CanAccessSystem", role.CanAccessSystem);
                command.Parameters.AddWithValue("@CanManageUsers", role.CanManageUsers);
                command.Parameters.AddWithValue("@CanManageRoles", role.CanManageRoles);
                command.Parameters.AddWithValue("@CanViewReports", role.CanViewReports);
                command.Parameters.AddWithValue("@CanManageSystemSettings", role.CanManageSystemSettings);
                command.Parameters.AddWithValue("@CanViewSystemLogs", role.CanViewSystemLogs);
                
                // صلاحيات المنتجات والمخزون
                command.Parameters.AddWithValue("@CanManageProducts", role.CanManageProducts);
                command.Parameters.AddWithValue("@CanManageCategories", role.CanManageCategories);
                command.Parameters.AddWithValue("@CanManageInventory", role.CanManageInventory);
                command.Parameters.AddWithValue("@CanAdjustInventory", role.CanAdjustInventory);
                command.Parameters.AddWithValue("@CanViewInventoryReports", role.CanViewInventoryReports);
                
                // صلاحيات المبيعات
                command.Parameters.AddWithValue("@CanProcessSales", role.CanProcessSales);
                command.Parameters.AddWithValue("@CanCancelSales", role.CanCancelSales);
                command.Parameters.AddWithValue("@CanApplyDiscounts", role.CanApplyDiscounts);
                command.Parameters.AddWithValue("@MaxDiscountPercentage", role.MaxDiscountPercentage);
                command.Parameters.AddWithValue("@CanProcessReturns", role.CanProcessReturns);
                command.Parameters.AddWithValue("@CanViewSalesReports", role.CanViewSalesReports);
                
                // صلاحيات العملاء
                command.Parameters.AddWithValue("@CanManageCustomers", role.CanManageCustomers);
                command.Parameters.AddWithValue("@CanViewCustomerData", role.CanViewCustomerData);
                command.Parameters.AddWithValue("@CanManageCustomerDebts", role.CanManageCustomerDebts);
                command.Parameters.AddWithValue("@CanViewCustomerReports", role.CanViewCustomerReports);
                
                // صلاحيات المالية
                command.Parameters.AddWithValue("@CanManageCashRegister", role.CanManageCashRegister);
                command.Parameters.AddWithValue("@CanViewFinancialReports", role.CanViewFinancialReports);
                command.Parameters.AddWithValue("@CanManagePaymentMethods", role.CanManagePaymentMethods);
                command.Parameters.AddWithValue("@CanViewProfitLoss", role.CanViewProfitLoss);
                
                command.Parameters.AddWithValue("@ModifiedBy", role.ModifiedBy);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم تحديث الدور {role.RoleID}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في تحديث الدور {role.RoleID}");
                throw;
            }
        }

        /// <summary>
        /// حذف دور
        /// </summary>
        /// <param name="roleId">رقم الدور</param>
        /// <param name="deletedBy">من قام بالحذف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public bool DeleteRole(int roleId, string deletedBy)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_DeleteRole", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@RoleID", roleId);
                command.Parameters.AddWithValue("@DeletedBy", deletedBy);

                connection.Open();
                var rowsAffected = command.ExecuteNonQuery();

                var success = rowsAffected > 0;
                if (success)
                {
                    _logger?.LogInformation($"تم حذف الدور {roleId}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في حذف الدور {roleId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على دور بالرقم
        /// </summary>
        /// <param name="roleId">رقم الدور</param>
        /// <returns>بيانات الدور</returns>
        public Role GetRoleById(int roleId)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetRoleById", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@RoleID", roleId);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapRoleFromReader(reader);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الدور {roleId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على دور بالاسم
        /// </summary>
        /// <param name="roleName">اسم الدور</param>
        /// <returns>بيانات الدور</returns>
        public Role GetRoleByName(string roleName)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetRoleByName", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@RoleName", roleName);

                connection.Open();
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return MapRoleFromReader(reader);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في الحصول على الدور {roleName}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع الأدوار
        /// </summary>
        /// <param name="includeInactive">تضمين الأدوار غير النشطة</param>
        /// <returns>قائمة الأدوار</returns>
        public List<Role> GetAllRoles(bool includeInactive = false)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_GetAllRoles", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@IncludeInactive", includeInactive);

                connection.Open();
                using var reader = command.ExecuteReader();

                var roles = new List<Role>();
                while (reader.Read())
                {
                    roles.Add(MapRoleFromReader(reader));
                }

                return roles;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في الحصول على جميع الأدوار");
                throw;
            }
        }

        #endregion

        #region التحقق من الوجود

        /// <summary>
        /// التحقق من وجود اسم الدور
        /// </summary>
        /// <param name="roleName">اسم الدور</param>
        /// <param name="excludeRoleId">استثناء دور معين (للتحديث)</param>
        /// <returns>true إذا كان اسم الدور موجود</returns>
        public bool RoleNameExists(string roleName, int? excludeRoleId = null)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_CheckRoleNameExists", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@RoleName", roleName);
                command.Parameters.AddWithValue("@ExcludeRoleID", excludeRoleId ?? (object)DBNull.Value);

                connection.Open();
                var result = command.ExecuteScalar();

                return Convert.ToBoolean(result);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في التحقق من وجود اسم الدور {roleName}");
                throw;
            }
        }

        /// <summary>
        /// التحقق من إمكانية حذف الدور
        /// </summary>
        /// <param name="roleId">رقم الدور</param>
        /// <returns>true إذا كان يمكن حذف الدور</returns>
        public bool CanDeleteRole(int roleId)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("sp_CanDeleteRole", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                command.Parameters.AddWithValue("@RoleID", roleId);

                connection.Open();
                var result = command.ExecuteScalar();

                return Convert.ToBoolean(result);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطأ في التحقق من إمكانية حذف الدور {roleId}");
                throw;
            }
        }

        #endregion

        #region العمليات المساعدة

        /// <summary>
        /// تحويل بيانات القارئ إلى كائن دور
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن الدور</returns>
        private Role MapRoleFromReader(SqlDataReader reader)
        {
            return new Role
            {
                RoleID = reader.GetInt32("RoleID"),
                RoleName = reader.GetString("RoleName"),
                RoleNameEn = reader.IsDBNull("RoleNameEn") ? null : reader.GetString("RoleNameEn"),
                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                PermissionLevel = reader.GetInt32("PermissionLevel"),
                IsAdminRole = reader.GetBoolean("IsAdminRole"),
                IsSystemRole = reader.GetBoolean("IsSystemRole"),
                IsActive = reader.GetBoolean("IsActive"),
                
                // صلاحيات النظام العامة
                CanAccessSystem = reader.GetBoolean("CanAccessSystem"),
                CanManageUsers = reader.GetBoolean("CanManageUsers"),
                CanManageRoles = reader.GetBoolean("CanManageRoles"),
                CanViewReports = reader.GetBoolean("CanViewReports"),
                CanManageSystemSettings = reader.GetBoolean("CanManageSystemSettings"),
                CanViewSystemLogs = reader.GetBoolean("CanViewSystemLogs"),
                
                // صلاحيات المنتجات والمخزون
                CanManageProducts = reader.GetBoolean("CanManageProducts"),
                CanManageCategories = reader.GetBoolean("CanManageCategories"),
                CanManageInventory = reader.GetBoolean("CanManageInventory"),
                CanAdjustInventory = reader.GetBoolean("CanAdjustInventory"),
                CanViewInventoryReports = reader.GetBoolean("CanViewInventoryReports"),
                
                // صلاحيات المبيعات
                CanProcessSales = reader.GetBoolean("CanProcessSales"),
                CanCancelSales = reader.GetBoolean("CanCancelSales"),
                CanApplyDiscounts = reader.GetBoolean("CanApplyDiscounts"),
                MaxDiscountPercentage = reader.GetDecimal("MaxDiscountPercentage"),
                CanProcessReturns = reader.GetBoolean("CanProcessReturns"),
                CanViewSalesReports = reader.GetBoolean("CanViewSalesReports"),
                
                // صلاحيات العملاء
                CanManageCustomers = reader.GetBoolean("CanManageCustomers"),
                CanViewCustomerData = reader.GetBoolean("CanViewCustomerData"),
                CanManageCustomerDebts = reader.GetBoolean("CanManageCustomerDebts"),
                CanViewCustomerReports = reader.GetBoolean("CanViewCustomerReports"),
                
                // صلاحيات المالية
                CanManageCashRegister = reader.GetBoolean("CanManageCashRegister"),
                CanViewFinancialReports = reader.GetBoolean("CanViewFinancialReports"),
                CanManagePaymentMethods = reader.GetBoolean("CanManagePaymentMethods"),
                CanViewProfitLoss = reader.GetBoolean("CanViewProfitLoss"),
                
                CreatedBy = reader.IsDBNull("CreatedBy") ? null : reader.GetString("CreatedBy"),
                CreatedDate = reader.GetDateTime("CreatedDate"),
                ModifiedBy = reader.IsDBNull("ModifiedBy") ? null : reader.GetString("ModifiedBy"),
                ModifiedDate = reader.GetDateTime("ModifiedDate")
            };
        }

        #endregion
    }
}
