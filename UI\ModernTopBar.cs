using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace AredooCashier.UI
{
    /// <summary>
    /// الشريط العلوي الحديث لتطبيق أريدو الكاشير
    /// </summary>
    public class ModernTopBar : UserControl
    {
        #region المتغيرات

        private string _userName = "أحمد محمد";
        private string _userRole = "كاشير";
        private string _storeName = "متجر الأمل";
        private DateTime _loginTime = DateTime.Now;
        private bool _isOnline = true;

        // عناصر الواجهة
        private Panel _logoPanel;
        private Panel _userPanel;
        private Panel _statusPanel;
        private Label _appNameLabel;
        private Label _storeNameLabel;
        private Label _userNameLabel;
        private Label _userRoleLabel;
        private Label _timeLabel;
        private Label _statusLabel;
        private Button _settingsButton;
        private Button _logoutButton;
        private Timer _clockTimer;

        #endregion

        #region الخصائص

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        public string UserName
        {
            get => _userName;
            set
            {
                _userName = value;
                UpdateUserInfo();
            }
        }

        /// <summary>
        /// دور المستخدم
        /// </summary>
        public string UserRole
        {
            get => _userRole;
            set
            {
                _userRole = value;
                UpdateUserInfo();
            }
        }

        /// <summary>
        /// اسم المتجر
        /// </summary>
        public string StoreName
        {
            get => _storeName;
            set
            {
                _storeName = value;
                UpdateStoreInfo();
            }
        }

        /// <summary>
        /// حالة الاتصال
        /// </summary>
        public bool IsOnline
        {
            get => _isOnline;
            set
            {
                _isOnline = value;
                UpdateStatus();
            }
        }

        #endregion

        #region الأحداث

        /// <summary>
        /// حدث النقر على زر الإعدادات
        /// </summary>
        public event EventHandler SettingsClicked;

        /// <summary>
        /// حدث النقر على زر تسجيل الخروج
        /// </summary>
        public event EventHandler LogoutClicked;

        #endregion

        #region البناء والتهيئة

        /// <summary>
        /// منشئ الشريط العلوي
        /// </summary>
        public ModernTopBar()
        {
            InitializeComponent();
            SetupDesign();
            StartClock();
        }

        /// <summary>
        /// تهيئة المكونات
        /// </summary>
        private void InitializeComponent()
        {
            // إعدادات الشريط العلوي
            Size = new Size(1200, ModernDesignSystem.Dimensions.TopBarHeight);
            BackColor = ModernDesignSystem.Colors.Surface;
            Dock = DockStyle.Top;
            Font = ModernDesignSystem.Fonts.Body;

            // لوحة الشعار والاسم
            _logoPanel = new Panel
            {
                Size = new Size(300, Height),
                Location = new Point(0, 0),
                BackColor = Color.Transparent
            };

            // لوحة المستخدم
            _userPanel = new Panel
            {
                Size = new Size(250, Height),
                Location = new Point(Width - 250, 0),
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };

            // لوحة الحالة
            _statusPanel = new Panel
            {
                Size = new Size(200, Height),
                Location = new Point(Width - 450, 0),
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };

            // تسمية اسم التطبيق
            _appNameLabel = new Label
            {
                Text = "أريدو الكاشير",
                Font = ModernDesignSystem.Fonts.Heading3,
                ForeColor = ModernDesignSystem.Colors.Primary,
                Location = new Point(60, 8),
                Size = new Size(200, 25),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // تسمية اسم المتجر
            _storeNameLabel = new Label
            {
                Text = _storeName,
                Font = ModernDesignSystem.Fonts.Caption,
                ForeColor = ModernDesignSystem.Colors.TextSecondary,
                Location = new Point(60, 33),
                Size = new Size(200, 20),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // تسمية اسم المستخدم
            _userNameLabel = new Label
            {
                Text = _userName,
                Font = ModernDesignSystem.Fonts.BodyBold,
                ForeColor = ModernDesignSystem.Colors.TextPrimary,
                Location = new Point(50, 8),
                Size = new Size(150, 20),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // تسمية دور المستخدم
            _userRoleLabel = new Label
            {
                Text = _userRole,
                Font = ModernDesignSystem.Fonts.Caption,
                ForeColor = ModernDesignSystem.Colors.TextSecondary,
                Location = new Point(50, 28),
                Size = new Size(150, 16),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // تسمية الوقت
            _timeLabel = new Label
            {
                Text = DateTime.Now.ToString("HH:mm:ss"),
                Font = ModernDesignSystem.Fonts.Numbers,
                ForeColor = ModernDesignSystem.Colors.TextPrimary,
                Location = new Point(50, 8),
                Size = new Size(120, 20),
                TextAlign = ContentAlignment.MiddleRight
            };

            // تسمية الحالة
            _statusLabel = new Label
            {
                Text = "متصل",
                Font = ModernDesignSystem.Fonts.Caption,
                ForeColor = ModernDesignSystem.Colors.Success,
                Location = new Point(50, 28),
                Size = new Size(120, 16),
                TextAlign = ContentAlignment.MiddleRight,
                RightToLeft = RightToLeft.Yes
            };

            // زر الإعدادات
            _settingsButton = new Button
            {
                Text = "⚙️",
                Font = new Font("Segoe UI Emoji", 16),
                Size = new Size(40, 40),
                Location = new Point(5, 10),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent,
                ForeColor = ModernDesignSystem.Colors.TextSecondary,
                Cursor = Cursors.Hand
            };
            _settingsButton.FlatAppearance.BorderSize = 0;
            _settingsButton.FlatAppearance.MouseOverBackColor = ModernDesignSystem.Colors.Hover;
            _settingsButton.Click += (s, e) => SettingsClicked?.Invoke(this, e);

            // زر تسجيل الخروج
            _logoutButton = new Button
            {
                Text = "🚪",
                Font = new Font("Segoe UI Emoji", 16),
                Size = new Size(40, 40),
                Location = new Point(205, 10),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent,
                ForeColor = ModernDesignSystem.Colors.Error,
                Cursor = Cursors.Hand
            };
            _logoutButton.FlatAppearance.BorderSize = 0;
            _logoutButton.FlatAppearance.MouseOverBackColor = ModernDesignSystem.Colors.Hover;
            _logoutButton.Click += (s, e) => LogoutClicked?.Invoke(this, e);

            // إضافة العناصر للوحات
            _logoPanel.Controls.AddRange(new Control[] { _appNameLabel, _storeNameLabel });
            _userPanel.Controls.AddRange(new Control[] { _userNameLabel, _userRoleLabel, _settingsButton, _logoutButton });
            _statusPanel.Controls.AddRange(new Control[] { _timeLabel, _statusLabel });

            // إضافة اللوحات للشريط العلوي
            Controls.AddRange(new Control[] { _logoPanel, _userPanel, _statusPanel });
        }

        /// <summary>
        /// إعداد التصميم
        /// </summary>
        private void SetupDesign()
        {
            // تطبيق التخطيط العربي
            RightToLeft = RightToLeft.Yes;

            // إضافة حدود سفلية
            Paint += OnPaint;
        }

        /// <summary>
        /// بدء ساعة الوقت
        /// </summary>
        private void StartClock()
        {
            _clockTimer = new Timer
            {
                Interval = 1000 // تحديث كل ثانية
            };
            _clockTimer.Tick += (s, e) => UpdateTime();
            _clockTimer.Start();
        }

        #endregion

        #region التحديثات

        /// <summary>
        /// تحديث معلومات المستخدم
        /// </summary>
        private void UpdateUserInfo()
        {
            if (_userNameLabel != null)
                _userNameLabel.Text = _userName;
            
            if (_userRoleLabel != null)
                _userRoleLabel.Text = _userRole;
        }

        /// <summary>
        /// تحديث معلومات المتجر
        /// </summary>
        private void UpdateStoreInfo()
        {
            if (_storeNameLabel != null)
                _storeNameLabel.Text = _storeName;
        }

        /// <summary>
        /// تحديث الوقت
        /// </summary>
        private void UpdateTime()
        {
            if (_timeLabel != null)
                _timeLabel.Text = DateTime.Now.ToString("HH:mm:ss");
        }

        /// <summary>
        /// تحديث حالة الاتصال
        /// </summary>
        private void UpdateStatus()
        {
            if (_statusLabel != null)
            {
                _statusLabel.Text = _isOnline ? "متصل" : "غير متصل";
                _statusLabel.ForeColor = _isOnline ? 
                    ModernDesignSystem.Colors.Success : 
                    ModernDesignSystem.Colors.Error;
            }
        }

        #endregion

        #region الرسم

        /// <summary>
        /// رسم الشريط العلوي
        /// </summary>
        private void OnPaint(object sender, PaintEventArgs e)
        {
            var g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // رسم الخلفية
            using (var backgroundBrush = new SolidBrush(ModernDesignSystem.Colors.Surface))
            {
                g.FillRectangle(backgroundBrush, ClientRectangle);
            }

            // رسم الحد السفلي
            using (var borderPen = new Pen(ModernDesignSystem.Colors.Border, 1))
            {
                g.DrawLine(borderPen, 0, Height - 1, Width, Height - 1);
            }

            // رسم أيقونة التطبيق
            var logoRect = new Rectangle(10, 10, 40, 40);
            using (var logoBrush = new SolidBrush(ModernDesignSystem.Colors.Primary))
            using (var logoPath = ModernDesignSystem.CreateRoundedRectangle(logoRect, 8))
            {
                g.FillPath(logoBrush, logoPath);
            }

            // رسم نص الأيقونة
            using (var iconBrush = new SolidBrush(ModernDesignSystem.Colors.TextOnPrimary))
            {
                var iconFont = new Font("Segoe UI Emoji", 20, FontStyle.Bold);
                var iconFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };
                g.DrawString("🏪", iconFont, iconBrush, logoRect, iconFormat);
            }

            // رسم خط فاصل بين الأقسام
            using (var separatorPen = new Pen(ModernDesignSystem.Colors.Border, 1))
            {
                var x1 = _statusPanel.Left - 10;
                var x2 = _userPanel.Left - 10;
                g.DrawLine(separatorPen, x1, 15, x1, Height - 15);
                g.DrawLine(separatorPen, x2, 15, x2, Height - 15);
            }
        }

        #endregion

        #region تنظيف الموارد

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _clockTimer?.Stop();
                _clockTimer?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
