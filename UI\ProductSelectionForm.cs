using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using AridooPOS.Models;

namespace AridooPOS.UI
{
    public partial class ProductSelectionForm : Form
    {
        public Product SelectedProduct { get; private set; }
        private List<Product> _products;

        public ProductSelectionForm(List<Product> products)
        {
            _products = products;
            InitializeComponent();
            InitializeArabicUI();
            LoadProducts();
        }

        private void InitializeComponent()
        {
            this.panel1 = new Panel();
            this.lblTitle = new Label();
            this.dgvProducts = new DataGridView();
            this.panel2 = new Panel();
            this.btnSelect = new Button();
            this.btnCancel = new Button();

            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvProducts)).BeginInit();
            this.panel2.SuspendLayout();
            this.SuspendLayout();

            // panel1
            this.panel1.Controls.Add(this.lblTitle);
            this.panel1.Dock = DockStyle.Top;
            this.panel1.Location = new Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new Size(600, 50);
            this.panel1.TabIndex = 0;
            this.panel1.BackColor = Color.FromArgb(52, 152, 219);

            // lblTitle
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(250, 15);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(100, 23);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "اختيار المنتج";

            // dgvProducts
            this.dgvProducts.AllowUserToAddRows = false;
            this.dgvProducts.AllowUserToDeleteRows = false;
            this.dgvProducts.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvProducts.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvProducts.Dock = DockStyle.Fill;
            this.dgvProducts.Location = new Point(0, 50);
            this.dgvProducts.MultiSelect = false;
            this.dgvProducts.Name = "dgvProducts";
            this.dgvProducts.ReadOnly = true;
            this.dgvProducts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dgvProducts.Size = new Size(600, 350);
            this.dgvProducts.TabIndex = 1;
            this.dgvProducts.DoubleClick += new EventHandler(this.dgvProducts_DoubleClick);

            // panel2
            this.panel2.Controls.Add(this.btnSelect);
            this.panel2.Controls.Add(this.btnCancel);
            this.panel2.Dock = DockStyle.Bottom;
            this.panel2.Location = new Point(0, 400);
            this.panel2.Name = "panel2";
            this.panel2.Size = new Size(600, 50);
            this.panel2.TabIndex = 2;

            // btnSelect
            this.btnSelect.BackColor = Color.FromArgb(46, 204, 113);
            this.btnSelect.FlatStyle = FlatStyle.Flat;
            this.btnSelect.ForeColor = Color.White;
            this.btnSelect.Location = new Point(400, 10);
            this.btnSelect.Name = "btnSelect";
            this.btnSelect.Size = new Size(80, 30);
            this.btnSelect.TabIndex = 0;
            this.btnSelect.Text = "اختيار";
            this.btnSelect.UseVisualStyleBackColor = false;
            this.btnSelect.Click += new EventHandler(this.btnSelect_Click);

            // btnCancel
            this.btnCancel.BackColor = Color.FromArgb(231, 76, 60);
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.Location = new Point(500, 10);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(80, 30);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "إلغاء";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            // ProductSelectionForm
            this.AutoScaleDimensions = new SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(600, 450);
            this.Controls.Add(this.dgvProducts);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel1);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ProductSelectionForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "اختيار المنتج";

            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvProducts)).EndInit();
            this.panel2.ResumeLayout(false);
            this.ResumeLayout(false);
        }

        private void InitializeArabicUI()
        {
            this.Font = new Font("Tahoma", 9F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void LoadProducts()
        {
            dgvProducts.Columns.Clear();
            dgvProducts.Columns.Add("ProductCode", "الكود");
            dgvProducts.Columns.Add("ProductName", "اسم المنتج");
            dgvProducts.Columns.Add("UnitPrice", "السعر");
            dgvProducts.Columns.Add("StockQuantity", "المخزون");
            dgvProducts.Columns.Add("CategoryName", "الفئة");

            dgvProducts.Columns["UnitPrice"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvProducts.Columns["StockQuantity"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvProducts.Columns["UnitPrice"].DefaultCellStyle.Format = "N2";

            foreach (var product in _products)
            {
                dgvProducts.Rows.Add(
                    product.ProductCode,
                    product.ProductName,
                    product.UnitPrice,
                    product.StockQuantity,
                    product.CategoryName
                );
            }

            if (dgvProducts.Rows.Count > 0)
            {
                dgvProducts.Rows[0].Selected = true;
            }
        }

        private void btnSelect_Click(object sender, EventArgs e)
        {
            SelectProduct();
        }

        private void dgvProducts_DoubleClick(object sender, EventArgs e)
        {
            SelectProduct();
        }

        private void SelectProduct()
        {
            if (dgvProducts.SelectedRows.Count > 0)
            {
                int selectedIndex = dgvProducts.SelectedRows[0].Index;
                if (selectedIndex >= 0 && selectedIndex < _products.Count)
                {
                    SelectedProduct = _products[selectedIndex];
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #region Designer Variables
        private Panel panel1;
        private Label lblTitle;
        private DataGridView dgvProducts;
        private Panel panel2;
        private Button btnSelect;
        private Button btnCancel;
        #endregion
    }
}
